import { config as dotenvConfig } from "dotenv";
import { getPayload } from "payload";
import { buildConfig } from "payload";
import { postgresAdapter } from "@payloadcms/db-postgres";
import { lexicalEditor } from "@payloadcms/richtext-lexical";

// Load environment variables
dotenvConfig({ path: ".env.local" });
dotenvConfig({ path: ".env" });

const initializePayload = async () => {
  try {
    console.log("Initializing PayloadCMS...");
    console.log(
      "Database URL:",
      process.env.DATABASE_URL?.substring(0, 50) + "..."
    );
    console.log(
      "Payload Secret:",
      process.env.PAYLOAD_SECRET ? "Found" : "Missing"
    );

    // Create PayloadCMS config inline to ensure env vars are loaded
    const config = buildConfig({
      admin: {
        user: "users",
      },
      graphQL: {
        disable: true,
      },
      collections: [
        {
          slug: "users",
          auth: true,
          admin: {
            useAsTitle: "email",
          },
          fields: [
            {
              name: "name",
              type: "text",
              required: true,
            },
            {
              name: "role",
              type: "select",
              options: [
                { label: "Admin", value: "admin" },
                { label: "Editor", value: "editor" },
              ],
              defaultValue: "editor",
              required: true,
            },
          ],
        },
      ],
      editor: lexicalEditor({}),
      secret: process.env.PAYLOAD_SECRET || "your-secret-key-here",
      typescript: {
        outputFile: "./payload-types.ts",
      },
      db: postgresAdapter({
        pool: {
          connectionString: process.env.DATABASE_URL,
        },
        schemaName: "payload",
        localesSuffix: "_payload_locales",
        relationshipsSuffix: "_payload_rels",
        versionsSuffix: "_payload_v",
      }),
    });

    // Initialize Payload
    const payload = await getPayload({ config });

    console.log("✅ PayloadCMS initialized successfully");
    console.log("✅ Database connection established");
    console.log("✅ Schema migration completed");

    // Create an admin user
    try {
      const adminUser = await payload.create({
        collection: "users",
        data: {
          email: "<EMAIL>",
          password: "temp123",
          name: "Admin User",
          role: "admin",
        },
      });
      console.log("✅ Admin user created:", adminUser.email);
    } catch (userError) {
      console.log(
        "ℹ️ Admin user might already exist or creation failed:",
        userError.message
      );
    }

    console.log("✅ PayloadCMS setup completed successfully!");
    process.exit(0);
  } catch (error) {
    console.error("❌ PayloadCMS initialization failed:", error);
    process.exit(1);
  }
};

initializePayload();
