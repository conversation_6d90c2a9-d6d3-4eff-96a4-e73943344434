{"name": "bordfeed-payload-admin", "version": "1.0.0", "description": "PayloadCMS Admin Panel for Bordfeed", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "payload": "cross-env PAYLOAD_CONFIG_PATH=./payload.config.ts payload"}, "dependencies": {"@payloadcms/db-postgres": "^3.50.0", "@payloadcms/next": "^3.50.0", "@payloadcms/richtext-lexical": "^3.50.0", "@payloadcms/ui": "^3.50.0", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "graphql": "^16.11.0", "next": "14.2.18", "payload": "^3.50.0", "pg": "^8.16.3", "react": "^19.1.1", "react-dom": "^19.1.1", "sharp": "^0.34.3"}, "devDependencies": {"@types/node": "^22.17.0", "@types/pg": "^8.15.5", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "typescript": "^5.9.2"}}