{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/wooorm/decode-named-character-reference/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"character-entities": "^2.0.0"}, "description": "Decode named character references", "devDependencies": {"@types/node": "^24.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^1.0.0"}, "exports": {"deno": "./index.js", "edge-light": "./index.js", "react-native": "./index.js", "worker": "./index.js", "workerd": "./index.js", "browser": "./index.dom.js", "default": "./index.js"}, "files": ["index.d.ts.map", "index.d.ts", "index.dom.d.ts.map", "index.dom.d.ts", "index.dom.js", "index.js"], "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "keywords": ["character", "decode", "named", "references"], "license": "MIT", "main#": "to do: next major: remove field", "main": "index.js", "name": "decode-named-character-reference", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "wooorm/decode-named-character-reference", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "ignoreCatch": true}, "types#": "to do: next major: remove field", "types": "index.d.ts", "type": "module", "version": "1.2.0", "xo": {"prettier": true, "rules": {"unicorn/prefer-code-point": "off"}, "space": true}}