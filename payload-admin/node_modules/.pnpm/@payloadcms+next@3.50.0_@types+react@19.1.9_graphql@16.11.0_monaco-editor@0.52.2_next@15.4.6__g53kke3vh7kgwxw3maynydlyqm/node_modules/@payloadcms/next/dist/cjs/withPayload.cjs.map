{"version": 3, "sources": ["../../src/withPayload.js"], "names": ["withPayload", "nextConfig", "options", "env", "experimental", "staleTimes", "dynamic", "console", "warn", "NEXT_PUBLIC_ENABLE_ROUTER_CACHE_REFRESH", "process", "PAYLOAD_PATCH_TURBOPACK_WARNINGS", "turbopackWarningText", "console<PERSON>arn", "args", "includes", "poweredByHeader", "key", "value", "toReturn", "outputFileTracingExcludes", "outputFileTracingIncludes", "headers", "headersFromConfig", "source", "serverExternalPackages", "NODE_ENV", "devBundleServerPackages", "webpack", "webpackConfig", "webpackOptions", "incomingWebpackConfig", "externals", "ignoreWarnings", "module", "file", "plugins", "IgnorePlugin", "resourceRegExp", "resolve", "alias", "fallback", "aws4", "kerber<PERSON>", "snappy", "basePath", "NEXT_BASE_PATH"], "mappings": "AAAA;;;;;;GAMG;;;;;;;;;;;QA4KH;eAAA;;QA3KaA;eAAAA;;;AAAN,MAAMA,cAAc,CAACC,aAAa,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC;IACvD,MAAMC,MAAMF,YAAYE,OAAO,CAAC;IAEhC,IAAIF,WAAWG,YAAY,EAAEC,YAAYC,SAAS;QAChDC,QAAQC,IAAI,CACV;QAEFL,IAAIM,uCAAuC,GAAG;IAChD;IAEA,IAAIC,QAAQP,GAAG,CAACQ,gCAAgC,KAAK,SAAS;QAC5D,MAAMC,uBACJ;QAEF,MAAMC,cAAcN,QAAQC,IAAI;QAChCD,QAAQC,IAAI,GAAG,CAAC,GAAGM;YACjB,mGAAmG;YACnG,IACE,AAAC,OAAOA,IAAI,CAAC,EAAE,KAAK,YAAYA,IAAI,CAAC,EAAE,CAACC,QAAQ,CAACH,yBAChD,OAAOE,IAAI,CAAC,EAAE,KAAK,YAAYA,IAAI,CAAC,EAAE,CAACC,QAAQ,CAACH,uBACjD;gBACA;YACF;YAEAC,eAAeC;QACjB;IACF;IAEA,MAAME,kBAAkB;QACtBC,KAAK;QACLC,OAAO;IACT;IAEA;;GAEC,GACD,MAAMC,WAAW;QACf,GAAGlB,UAAU;QACbE;QACAiB,2BAA2B;YACzB,GAAInB,YAAYmB,6BAA6B,CAAC,CAAC;YAC/C,QAAQ;mBACFnB,YAAYmB,2BAA2B,CAAC,OAAO,IAAI,EAAE;gBACzD;gBACA;aACD;QACH;QACAC,2BAA2B;YACzB,GAAIpB,YAAYoB,6BAA6B,CAAC,CAAC;YAC/C,QAAQ;mBAAKpB,YAAYoB,2BAA2B,CAAC,OAAO,IAAI,EAAE;gBAAG;aAAiB;QACxF;QACA,+FAA+F;QAC/F,GAAIpB,YAAYe,oBAAoB,QAAQ;YAAEA,iBAAiB;QAAM,IAAI,CAAC,CAAC;QAC3EM,SAAS;YACP,MAAMC,oBAAoB,aAAatB,aAAa,MAAMA,WAAWqB,OAAO,KAAK,EAAE;YAEnF,OAAO;mBACDC,qBAAqB,EAAE;gBAC3B;oBACEC,QAAQ;oBACRF,SAAS;wBACP;4BACEL,KAAK;4BACLC,OAAO;wBACT;wBACA;4BACED,KAAK;4BACLC,OAAO;wBACT;wBACA;4BACED,KAAK;4BACLC,OAAO;wBACT;2BACIjB,YAAYe,oBAAoB,QAAQ;4BAACA;yBAAgB,GAAG,EAAE;qBACnE;gBACH;aACD;QACH;QACAS,wBAAwB;eAClBxB,YAAYwB,0BAA0B,EAAE;YAC5C;YACA;YACA;YACA;YACA;YACA;YACA,yEAAyE;eACrEf,QAAQP,GAAG,CAACuB,QAAQ,KAAK,iBAAiBxB,QAAQyB,uBAAuB,KAAK,QAC9E;gBACE;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aAWD,GACD,EAAE;SACP;QACDC,SAAS,CAACC,eAAeC;YACvB,MAAMC,wBACJ,OAAO9B,WAAW2B,OAAO,KAAK,aAC1B3B,WAAW2B,OAAO,CAACC,eAAeC,kBAClCD;YAEN,OAAO;gBACL,GAAGE,qBAAqB;gBACxBC,WAAW;uBACLD,uBAAuBC,aAAa,EAAE;oBAC1C;oBACA;oBACA;oBACA;oBACA;iBACD;gBACDC,gBAAgB;uBACVF,uBAAuBE,kBAAkB,EAAE;oBAC/C;wBAAEC,QAAQ;oBAAwC;oBAClD;wBAAEC,MAAM;oBAAwC;oBAChD;wBAAED,QAAQ;oBAAuC;oBACjD;wBAAEC,MAAM;oBAAuC;iBAChD;gBACDC,SAAS;uBACHL,uBAAuBK,WAAW,EAAE;oBACxC,oFAAoF;oBACpF,IAAIN,eAAeF,OAAO,CAACS,YAAY,CAAC;wBACtCC,gBAAgB;oBAClB;iBACD;gBACDC,SAAS;oBACP,GAAIR,uBAAuBQ,WAAW,CAAC,CAAC;oBACxCC,OAAO;wBACL,GAAIT,uBAAuBQ,SAASC,SAAS,CAAC,CAAC;oBACjD;oBACAC,UAAU;wBACR,GAAIV,uBAAuBQ,SAASE,YAAY,CAAC,CAAC;wBAClD,iCAAiC;wBACjC,oBAAoB;wBACpBC,MAAM;wBACNC,UAAU;wBACV,6BAA6B;wBAC7BC,QAAQ;wBACR,kBAAkB;wBAClB,eAAe;oBACjB;gBACF;YACF;QACF;IACF;IAEA,IAAI3C,WAAW4C,QAAQ,EAAE;QACvB1B,SAAShB,GAAG,CAAC2C,cAAc,GAAG7C,WAAW4C,QAAQ;IACnD;IAEA,OAAO1B;AACT;MAEA,WAAenB", "file": "withPayload.cjs", "sourcesContent": ["/**\n * @param {import('next').NextConfig} nextConfig\n * @param {Object} [options] - Optional configuration options\n * @param {boolean} [options.devBundleServerPackages] - Whether to bundle server packages in development mode. @default true\n *\n * @returns {import('next').NextConfig}\n * */\nexport const withPayload = (nextConfig = {}, options = {}) => {\n  const env = nextConfig?.env || {}\n\n  if (nextConfig.experimental?.staleTimes?.dynamic) {\n    console.warn(\n      'Payload detected a non-zero value for the `staleTimes.dynamic` option in your Next.js config. This will slow down page transitions and may cause stale data to load within the Admin panel. To clear this warning, remove the `staleTimes.dynamic` option from your Next.js config or set it to 0. In the future, Next.js may support scoping this option to specific routes.',\n    )\n    env.NEXT_PUBLIC_ENABLE_ROUTER_CACHE_REFRESH = 'true'\n  }\n\n  if (process.env.PAYLOAD_PATCH_TURBOPACK_WARNINGS !== 'false') {\n    const turbopackWarningText =\n      'Packages that should be external need to be installed in the project directory, so they can be resolved from the output files.\\nTry to install it into the project directory by running'\n\n    const consoleWarn = console.warn\n    console.warn = (...args) => {\n      // Force to disable serverExternalPackages warnings: https://github.com/vercel/next.js/issues/68805\n      if (\n        (typeof args[1] === 'string' && args[1].includes(turbopackWarningText)) ||\n        (typeof args[0] === 'string' && args[0].includes(turbopackWarningText))\n      ) {\n        return\n      }\n\n      consoleWarn(...args)\n    }\n  }\n\n  const poweredByHeader = {\n    key: 'X-Powered-By',\n    value: 'Next.js, Payload',\n  }\n\n  /**\n   * @type {import('next').NextConfig}\n   */\n  const toReturn = {\n    ...nextConfig,\n    env,\n    outputFileTracingExcludes: {\n      ...(nextConfig?.outputFileTracingExcludes || {}),\n      '**/*': [\n        ...(nextConfig?.outputFileTracingExcludes?.['**/*'] || []),\n        'drizzle-kit',\n        'drizzle-kit/api',\n      ],\n    },\n    outputFileTracingIncludes: {\n      ...(nextConfig?.outputFileTracingIncludes || {}),\n      '**/*': [...(nextConfig?.outputFileTracingIncludes?.['**/*'] || []), '@libsql/client'],\n    },\n    // We disable the poweredByHeader here because we add it manually in the headers function below\n    ...(nextConfig?.poweredByHeader !== false ? { poweredByHeader: false } : {}),\n    headers: async () => {\n      const headersFromConfig = 'headers' in nextConfig ? await nextConfig.headers() : []\n\n      return [\n        ...(headersFromConfig || []),\n        {\n          source: '/:path*',\n          headers: [\n            {\n              key: 'Accept-CH',\n              value: 'Sec-CH-Prefers-Color-Scheme',\n            },\n            {\n              key: 'Vary',\n              value: 'Sec-CH-Prefers-Color-Scheme',\n            },\n            {\n              key: 'Critical-CH',\n              value: 'Sec-CH-Prefers-Color-Scheme',\n            },\n            ...(nextConfig?.poweredByHeader !== false ? [poweredByHeader] : []),\n          ],\n        },\n      ]\n    },\n    serverExternalPackages: [\n      ...(nextConfig?.serverExternalPackages || []),\n      'drizzle-kit',\n      'drizzle-kit/api',\n      'pino',\n      'libsql',\n      'pino-pretty',\n      'graphql',\n      // Do not bundle server-only packages during dev to improve compile speed\n      ...(process.env.NODE_ENV === 'development' && options.devBundleServerPackages === false\n        ? [\n            'payload',\n            '@payloadcms/db-mongodb',\n            '@payloadcms/db-postgres',\n            '@payloadcms/db-sqlite',\n            '@payloadcms/db-vercel-postgres',\n            '@payloadcms/drizzle',\n            '@payloadcms/email-nodemailer',\n            '@payloadcms/email-resend',\n            '@payloadcms/graphql',\n            '@payloadcms/payload-cloud',\n            '@payloadcms/plugin-redirects',\n            // TODO: Add the following packages, excluding their /client subpath exports, once Next.js supports it\n            //'@payloadcms/plugin-cloud-storage',\n            //'@payloadcms/plugin-sentry',\n            //'@payloadcms/plugin-stripe',\n            // @payloadcms/richtext-lexical\n            //'@payloadcms/storage-azure',\n            //'@payloadcms/storage-gcs',\n            //'@payloadcms/storage-s3',\n            //'@payloadcms/storage-uploadthing',\n            //'@payloadcms/storage-vercel-blob',\n          ]\n        : []),\n    ],\n    webpack: (webpackConfig, webpackOptions) => {\n      const incomingWebpackConfig =\n        typeof nextConfig.webpack === 'function'\n          ? nextConfig.webpack(webpackConfig, webpackOptions)\n          : webpackConfig\n\n      return {\n        ...incomingWebpackConfig,\n        externals: [\n          ...(incomingWebpackConfig?.externals || []),\n          'drizzle-kit',\n          'drizzle-kit/api',\n          'sharp',\n          'libsql',\n          'require-in-the-middle',\n        ],\n        ignoreWarnings: [\n          ...(incomingWebpackConfig?.ignoreWarnings || []),\n          { module: /node_modules\\/mongodb\\/lib\\/utils\\.js/ },\n          { file: /node_modules\\/mongodb\\/lib\\/utils\\.js/ },\n          { module: /node_modules\\/mongodb\\/lib\\/bson\\.js/ },\n          { file: /node_modules\\/mongodb\\/lib\\/bson\\.js/ },\n        ],\n        plugins: [\n          ...(incomingWebpackConfig?.plugins || []),\n          // Fix cloudflare:sockets error: https://github.com/vercel/next.js/discussions/50177\n          new webpackOptions.webpack.IgnorePlugin({\n            resourceRegExp: /^pg-native$|^cloudflare:sockets$/,\n          }),\n        ],\n        resolve: {\n          ...(incomingWebpackConfig?.resolve || {}),\n          alias: {\n            ...(incomingWebpackConfig?.resolve?.alias || {}),\n          },\n          fallback: {\n            ...(incomingWebpackConfig?.resolve?.fallback || {}),\n            '@aws-sdk/credential-providers': false,\n            '@mongodb-js/zstd': false,\n            aws4: false,\n            kerberos: false,\n            'mongodb-client-encryption': false,\n            snappy: false,\n            'supports-color': false,\n            'yocto-queue': false,\n          },\n        },\n      }\n    },\n  }\n\n  if (nextConfig.basePath) {\n    toReturn.env.NEXT_BASE_PATH = nextConfig.basePath\n  }\n\n  return toReturn\n}\n\nexport default withPayload\n"]}