{"version": 3, "file": "types.js", "names": [], "sources": ["../../../src/utilities/initPage/types.ts"], "sourcesContent": ["import type { ImportMap, SanitizedConfig } from 'payload'\n\nexport type Args = {\n  /**\n   * Your sanitized Payload config.\n   * If unresolved, this function will await the promise.\n   */\n  config: Promise<SanitizedConfig> | SanitizedConfig\n  importMap: ImportMap\n  /**\n   * If true, redirects unauthenticated users to the admin login page.\n   * If a string is provided, the user will be redirected to that specific URL.\n   */\n  redirectUnauthenticatedUser?: boolean | string\n  /**\n   * The current route, i.e. `/admin/collections/posts`.\n   */\n  route: string\n  /**\n   * The route parameters of the current route\n   *\n   * @example `{ collection: 'posts', id: \"post-id\" }`.\n   */\n  routeParams?: { [key: string]: string }\n  /**\n   * The search parameters of the current route provided to all pages in Next.js.\n   */\n  searchParams: { [key: string]: string | string[] | undefined }\n  /**\n   * If `useLayoutReq` is `true`, this page will use the cached `req` created by the root layout\n   * instead of creating a new one.\n   *\n   * This improves performance for pages that are able to share the same `req` as the root layout,\n   * as permissions do not need to be re-calculated.\n   *\n   * If the page has unique query and url params that need to be part of the `req` object, or if you\n   * need permissions calculation to respect those you should not use this property.\n   */\n  useLayoutReq?: boolean\n}\n"], "mappings": "AAEA", "ignoreList": []}