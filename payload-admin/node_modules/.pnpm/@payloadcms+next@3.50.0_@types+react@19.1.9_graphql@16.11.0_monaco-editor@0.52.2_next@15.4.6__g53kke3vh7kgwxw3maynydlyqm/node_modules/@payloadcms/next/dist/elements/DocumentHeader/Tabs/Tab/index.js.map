{"version": 3, "file": "index.js", "names": ["RenderServerComponent", "Fragment", "DocumentTabLink", "baseClass", "DefaultDocumentTab", "props", "apiURL", "collectionConfig", "globalConfig", "permissions", "req", "tabConfig", "href", "tabHref", "isActive", "tabIsActive", "label", "newTab", "<PERSON>ll", "Pill_Component", "collection", "global", "routes", "payload", "config", "labelToRender", "t", "i18n", "_jsx", "adminRoute", "admin", "aria<PERSON><PERSON><PERSON>", "_jsxs", "className", "Component", "Fallback", "importMap", "serverProps", "user"], "sources": ["../../../../../src/elements/DocumentHeader/Tabs/Tab/index.tsx"], "sourcesContent": ["import type {\n  DocumentTabConfig,\n  DocumentTabServerPropsOnly,\n  PayloadRequest,\n  SanitizedCollectionConfig,\n  SanitizedGlobalConfig,\n  SanitizedPermissions,\n} from 'payload'\nimport type React from 'react'\n\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\nimport { Fragment } from 'react'\n\nimport { DocumentTabLink } from './TabLink.js'\nimport './index.scss'\n\nexport const baseClass = 'doc-tab'\n\nexport const DefaultDocumentTab: React.FC<{\n  apiURL?: string\n  collectionConfig?: SanitizedCollectionConfig\n  globalConfig?: SanitizedGlobalConfig\n  path?: string\n  permissions?: SanitizedPermissions\n  req: PayloadRequest\n  tabConfig: { readonly Pill_Component?: React.FC } & DocumentTabConfig\n}> = (props) => {\n  const {\n    apiURL,\n    collectionConfig,\n    globalConfig,\n    permissions,\n    req,\n    tabConfig: { href: tabHref, isActive: tabIsActive, label, newTab, Pill, Pill_Component },\n  } = props\n\n  let href = typeof tabHref === 'string' ? tabHref : ''\n  let isActive = typeof tabIsActive === 'boolean' ? tabIsActive : false\n\n  if (typeof tabHref === 'function') {\n    href = tabHref({\n      apiURL,\n      collection: collectionConfig,\n      global: globalConfig,\n      routes: req.payload.config.routes,\n    })\n  }\n\n  if (typeof tabIsActive === 'function') {\n    isActive = tabIsActive({\n      href,\n    })\n  }\n\n  const labelToRender =\n    typeof label === 'function'\n      ? label({\n          t: req.i18n.t,\n        })\n      : label\n\n  return (\n    <DocumentTabLink\n      adminRoute={req.payload.config.routes.admin}\n      ariaLabel={labelToRender}\n      baseClass={baseClass}\n      href={href}\n      isActive={isActive}\n      newTab={newTab}\n    >\n      <span className={`${baseClass}__label`}>\n        {labelToRender}\n        {Pill || Pill_Component ? (\n          <Fragment>\n            &nbsp;\n            {RenderServerComponent({\n              Component: Pill,\n              Fallback: Pill_Component,\n              importMap: req.payload.importMap,\n              serverProps: {\n                i18n: req.i18n,\n                payload: req.payload,\n                permissions,\n                req,\n                user: req.user,\n              } satisfies DocumentTabServerPropsOnly,\n            })}\n          </Fragment>\n        ) : null}\n      </span>\n    </DocumentTabLink>\n  )\n}\n"], "mappings": ";AAUA,SAASA,qBAAqB,QAAQ;AACtC,SAASC,QAAQ,QAAQ;AAEzB,SAASC,eAAe,QAAQ;AAGhC,OAAO,MAAMC,SAAA,GAAY;AAEzB,OAAO,MAAMC,kBAAA,GAQPC,KAAA;EACJ,MAAM;IACJC,MAAM;IACNC,gBAAgB;IAChBC,YAAY;IACZC,WAAW;IACXC,GAAG;IACHC,SAAA,EAAW;MAAEC,IAAA,EAAMC,OAAO;MAAEC,QAAA,EAAUC,WAAW;MAAEC,KAAK;MAAEC,MAAM;MAAEC,IAAI;MAAEC;IAAc;EAAE,CACzF,GAAGd,KAAA;EAEJ,IAAIO,IAAA,GAAO,OAAOC,OAAA,KAAY,WAAWA,OAAA,GAAU;EACnD,IAAIC,QAAA,GAAW,OAAOC,WAAA,KAAgB,YAAYA,WAAA,GAAc;EAEhE,IAAI,OAAOF,OAAA,KAAY,YAAY;IACjCD,IAAA,GAAOC,OAAA,CAAQ;MACbP,MAAA;MACAc,UAAA,EAAYb,gBAAA;MACZc,MAAA,EAAQb,YAAA;MACRc,MAAA,EAAQZ,GAAA,CAAIa,OAAO,CAACC,MAAM,CAACF;IAC7B;EACF;EAEA,IAAI,OAAOP,WAAA,KAAgB,YAAY;IACrCD,QAAA,GAAWC,WAAA,CAAY;MACrBH;IACF;EACF;EAEA,MAAMa,aAAA,GACJ,OAAOT,KAAA,KAAU,aACbA,KAAA,CAAM;IACJU,CAAA,EAAGhB,GAAA,CAAIiB,IAAI,CAACD;EACd,KACAV,KAAA;EAEN,oBACEY,IAAA,CAAC1B,eAAA;IACC2B,UAAA,EAAYnB,GAAA,CAAIa,OAAO,CAACC,MAAM,CAACF,MAAM,CAACQ,KAAK;IAC3CC,SAAA,EAAWN,aAAA;IACXtB,SAAA,EAAWA,SAAA;IACXS,IAAA,EAAMA,IAAA;IACNE,QAAA,EAAUA,QAAA;IACVG,MAAA,EAAQA,MAAA;cAER,aAAAe,KAAA,CAAC;MAAKC,SAAA,EAAW,GAAG9B,SAAA,SAAkB;iBACnCsB,aAAA,EACAP,IAAA,IAAQC,cAAA,gBACPa,KAAA,CAAC/B,QAAA;mBAAS,KAEPD,qBAAA,CAAsB;UACrBkC,SAAA,EAAWhB,IAAA;UACXiB,QAAA,EAAUhB,cAAA;UACViB,SAAA,EAAW1B,GAAA,CAAIa,OAAO,CAACa,SAAS;UAChCC,WAAA,EAAa;YACXV,IAAA,EAAMjB,GAAA,CAAIiB,IAAI;YACdJ,OAAA,EAASb,GAAA,CAAIa,OAAO;YACpBd,WAAA;YACAC,GAAA;YACA4B,IAAA,EAAM5B,GAAA,CAAI4B;UACZ;QACF;WAEA;;;AAIZ", "ignoreList": []}