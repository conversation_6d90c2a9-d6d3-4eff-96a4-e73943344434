{"version": 3, "file": "getRequestTheme.js", "names": ["defaultTheme", "acceptedThemes", "getRequestTheme", "config", "cookies", "headers", "admin", "theme", "includes", "<PERSON><PERSON><PERSON><PERSON>", "get", "cookiePrefix", "themeFromCookie", "value", "themeFromHeader"], "sources": ["../../src/utilities/getRequestTheme.ts"], "sourcesContent": ["import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies.js'\nimport type { SanitizedConfig } from 'payload'\n\nimport { defaultTheme, type Theme } from '@payloadcms/ui'\n\ntype GetRequestLanguageArgs = {\n  config: SanitizedConfig\n  cookies: Map<string, string> | ReadonlyRequestCookies\n  headers: Request['headers']\n}\n\nconst acceptedThemes: Theme[] = ['dark', 'light']\n\nexport const getRequestTheme = ({ config, cookies, headers }: GetRequestLanguageArgs): Theme => {\n  if (config.admin.theme !== 'all' && acceptedThemes.includes(config.admin.theme)) {\n    return config.admin.theme\n  }\n\n  const themeCookie = cookies.get(`${config.cookiePrefix || 'payload'}-theme`)\n\n  const themeFromCookie: Theme = (\n    typeof themeCookie === 'string' ? themeCookie : themeCookie?.value\n  ) as Theme\n\n  if (themeFromCookie && acceptedThemes.includes(themeFromCookie)) {\n    return themeFromCookie\n  }\n\n  const themeFromHeader = headers.get('Sec-CH-Prefers-Color-Scheme') as Theme\n\n  if (themeFromHeader && acceptedThemes.includes(themeFromHeader)) {\n    return themeFromHeader\n  }\n\n  return defaultTheme\n}\n"], "mappings": "AAGA,SAASA,YAAY,QAAoB;AAQzC,MAAMC,cAAA,GAA0B,CAAC,QAAQ,QAAQ;AAEjD,OAAO,MAAMC,eAAA,GAAkBA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAO,CAA0B;EAClF,IAAIF,MAAA,CAAOG,KAAK,CAACC,KAAK,KAAK,SAASN,cAAA,CAAeO,QAAQ,CAACL,MAAA,CAAOG,KAAK,CAACC,KAAK,GAAG;IAC/E,OAAOJ,MAAA,CAAOG,KAAK,CAACC,KAAK;EAC3B;EAEA,MAAME,WAAA,GAAcL,OAAA,CAAQM,GAAG,CAAC,GAAGP,MAAA,CAAOQ,YAAY,IAAI,iBAAiB;EAE3E,MAAMC,eAAA,GACJ,OAAOH,WAAA,KAAgB,WAAWA,WAAA,GAAcA,WAAA,EAAaI,KAAA;EAG/D,IAAID,eAAA,IAAmBX,cAAA,CAAeO,QAAQ,CAACI,eAAA,GAAkB;IAC/D,OAAOA,eAAA;EACT;EAEA,MAAME,eAAA,GAAkBT,OAAA,CAAQK,GAAG,CAAC;EAEpC,IAAII,eAAA,IAAmBb,cAAA,CAAeO,QAAQ,CAACM,eAAA,GAAkB;IAC/D,OAAOA,eAAA;EACT;EAEA,OAAOd,YAAA;AACT", "ignoreList": []}