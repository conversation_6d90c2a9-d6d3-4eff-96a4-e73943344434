{"version": 3, "file": "handleServerFunctions.js", "names": ["copyDataFromLocaleHandler", "buildFormStateHandler", "buildTableStateHandler", "getFolderResultsComponentAndDataHandler", "schedulePublishHandler", "renderDocumentHandler", "renderDocumentSlotsHandler", "renderListHandler", "initReq", "serverFunctions", "handleServerFunctions", "args", "name", "fnKey", "fnArgs", "config", "config<PERSON>rom<PERSON>", "importMap", "req", "key", "augmentedArgs", "fn", "Error"], "sources": ["../../src/utilities/handleServerFunctions.ts"], "sourcesContent": ["import type { ServerFunction, ServerFunctionHandler } from 'payload'\n\nimport { copyDataFromLocaleHandler } from '@payloadcms/ui/rsc'\nimport { buildFormStateHandler } from '@payloadcms/ui/utilities/buildFormState'\nimport { buildTableStateHandler } from '@payloadcms/ui/utilities/buildTableState'\nimport { getFolderResultsComponentAndDataHandler } from '@payloadcms/ui/utilities/getFolderResultsComponentAndData'\nimport { schedulePublishHandler } from '@payloadcms/ui/utilities/schedulePublishHandler'\n\nimport { renderDocumentHandler } from '../views/Document/handleServerFunction.js'\nimport { renderDocumentSlotsHandler } from '../views/Document/renderDocumentSlots.js'\nimport { renderListHandler } from '../views/List/handleServerFunction.js'\nimport { initReq } from './initReq.js'\n\nconst serverFunctions: Record<string, ServerFunction> = {\n  'copy-data-from-locale': copyDataFromLocaleHandler,\n  'form-state': buildFormStateHandler,\n  'get-folder-results-component-and-data': getFolderResultsComponentAndDataHandler,\n  'render-document': renderDocumentHandler,\n  'render-document-slots': renderDocumentSlotsHandler,\n  'render-list': renderListHandler,\n  'schedule-publish': schedulePublishHandler,\n  'table-state': buildTableStateHandler,\n}\n\nexport const handleServerFunctions: ServerFunctionHandler = async (args) => {\n  const { name: fnKey, args: fnArgs, config: configPromise, importMap } = args\n\n  const { req } = await initReq({\n    configPromise,\n    importMap,\n    key: 'RootLayout',\n  })\n\n  const augmentedArgs: Parameters<ServerFunction>[0] = {\n    ...fnArgs,\n    importMap,\n    req,\n  }\n\n  const fn = serverFunctions[fnKey]\n\n  if (!fn) {\n    throw new Error(`Unknown Server Function: ${fnKey}`)\n  }\n\n  return fn(augmentedArgs)\n}\n"], "mappings": "AAEA,SAASA,yBAAyB,QAAQ;AAC1C,SAASC,qBAAqB,QAAQ;AACtC,SAASC,sBAAsB,QAAQ;AACvC,SAASC,uCAAuC,QAAQ;AACxD,SAASC,sBAAsB,QAAQ;AAEvC,SAASC,qBAAqB,QAAQ;AACtC,SAASC,0BAA0B,QAAQ;AAC3C,SAASC,iBAAiB,QAAQ;AAClC,SAASC,OAAO,QAAQ;AAExB,MAAMC,eAAA,GAAkD;EACtD,yBAAyBT,yBAAA;EACzB,cAAcC,qBAAA;EACd,yCAAyCE,uCAAA;EACzC,mBAAmBE,qBAAA;EACnB,yBAAyBC,0BAAA;EACzB,eAAeC,iBAAA;EACf,oBAAoBH,sBAAA;EACpB,eAAeF;AACjB;AAEA,OAAO,MAAMQ,qBAAA,GAA+C,MAAOC,IAAA;EACjE,MAAM;IAAEC,IAAA,EAAMC,KAAK;IAAEF,IAAA,EAAMG,MAAM;IAAEC,MAAA,EAAQC,aAAa;IAAEC;EAAS,CAAE,GAAGN,IAAA;EAExE,MAAM;IAAEO;EAAG,CAAE,GAAG,MAAMV,OAAA,CAAQ;IAC5BQ,aAAA;IACAC,SAAA;IACAE,GAAA,EAAK;EACP;EAEA,MAAMC,aAAA,GAA+C;IACnD,GAAGN,MAAM;IACTG,SAAA;IACAC;EACF;EAEA,MAAMG,EAAA,GAAKZ,eAAe,CAACI,KAAA,CAAM;EAEjC,IAAI,CAACQ,EAAA,EAAI;IACP,MAAM,IAAIC,KAAA,CAAM,4BAA4BT,KAAA,EAAO;EACrD;EAEA,OAAOQ,EAAA,CAAGD,aAAA;AACZ", "ignoreList": []}