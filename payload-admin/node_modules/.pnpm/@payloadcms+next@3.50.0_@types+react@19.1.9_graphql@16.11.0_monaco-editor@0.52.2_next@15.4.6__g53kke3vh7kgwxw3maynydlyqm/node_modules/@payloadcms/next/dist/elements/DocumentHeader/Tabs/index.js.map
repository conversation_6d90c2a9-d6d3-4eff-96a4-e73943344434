{"version": 3, "file": "index.js", "names": ["RenderServerComponent", "React", "ShouldRenderTabs", "DefaultDocumentTab", "getTabs", "baseClass", "DocumentTabs", "collectionConfig", "globalConfig", "permissions", "req", "config", "payload", "tabs", "_jsx", "className", "map", "tab", "tabConfig", "viewPath", "index", "condition", "meetsCondition", "Component", "clientProps", "path", "importMap", "key", "serverProps", "i18n", "user"], "sources": ["../../../../src/elements/DocumentHeader/Tabs/index.tsx"], "sourcesContent": ["import type {\n  DocumentTabClientProps,\n  DocumentTabServerPropsOnly,\n  PayloadRequest,\n  SanitizedCollectionConfig,\n  SanitizedGlobalConfig,\n  SanitizedPermissions,\n} from 'payload'\n\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\nimport React from 'react'\n\nimport { ShouldRenderTabs } from './ShouldRenderTabs.js'\nimport { DefaultDocumentTab } from './Tab/index.js'\nimport { getTabs } from './tabs/index.js'\nimport './index.scss'\n\nconst baseClass = 'doc-tabs'\n\nexport const DocumentTabs: React.FC<{\n  collectionConfig: SanitizedCollectionConfig\n  globalConfig: SanitizedGlobalConfig\n  permissions: SanitizedPermissions\n  req: PayloadRequest\n}> = ({ collectionConfig, globalConfig, permissions, req }) => {\n  const { config } = req.payload\n\n  const tabs = getTabs({\n    collectionConfig,\n    globalConfig,\n  })\n\n  return (\n    <ShouldRenderTabs>\n      <div className={baseClass}>\n        <div className={`${baseClass}__tabs-container`}>\n          <ul className={`${baseClass}__tabs`}>\n            {tabs?.map(({ tab: tabConfig, viewPath }, index) => {\n              const { condition } = tabConfig || {}\n\n              const meetsCondition =\n                !condition ||\n                condition({ collectionConfig, config, globalConfig, permissions, req })\n\n              if (!meetsCondition) {\n                return null\n              }\n\n              if (tabConfig?.Component) {\n                return RenderServerComponent({\n                  clientProps: {\n                    path: viewPath,\n                  } satisfies DocumentTabClientProps,\n                  Component: tabConfig.Component,\n                  importMap: req.payload.importMap,\n                  key: `tab-${index}`,\n                  serverProps: {\n                    collectionConfig,\n                    globalConfig,\n                    i18n: req.i18n,\n                    payload: req.payload,\n                    permissions,\n                    req,\n                    user: req.user,\n                  } satisfies DocumentTabServerPropsOnly,\n                })\n              }\n\n              return (\n                <DefaultDocumentTab\n                  collectionConfig={collectionConfig}\n                  globalConfig={globalConfig}\n                  key={`tab-${index}`}\n                  path={viewPath}\n                  permissions={permissions}\n                  req={req}\n                  tabConfig={tabConfig}\n                />\n              )\n            })}\n          </ul>\n        </div>\n      </div>\n    </ShouldRenderTabs>\n  )\n}\n"], "mappings": ";AASA,SAASA,qBAAqB,QAAQ;AACtC,OAAOC,KAAA,MAAW;AAElB,SAASC,gBAAgB,QAAQ;AACjC,SAASC,kBAAkB,QAAQ;AACnC,SAASC,OAAO,QAAQ;AAGxB,MAAMC,SAAA,GAAY;AAElB,OAAO,MAAMC,YAAA,GAKRA,CAAC;EAAEC,gBAAgB;EAAEC,YAAY;EAAEC,WAAW;EAAEC;AAAG,CAAE;EACxD,MAAM;IAAEC;EAAM,CAAE,GAAGD,GAAA,CAAIE,OAAO;EAE9B,MAAMC,IAAA,GAAOT,OAAA,CAAQ;IACnBG,gBAAA;IACAC;EACF;EAEA,oBACEM,IAAA,CAACZ,gBAAA;cACC,aAAAY,IAAA,CAAC;MAAIC,SAAA,EAAWV,SAAA;gBACd,aAAAS,IAAA,CAAC;QAAIC,SAAA,EAAW,GAAGV,SAAA,kBAA2B;kBAC5C,aAAAS,IAAA,CAAC;UAAGC,SAAA,EAAW,GAAGV,SAAA,QAAiB;oBAChCQ,IAAA,EAAMG,GAAA,CAAI,CAAC;YAAEC,GAAA,EAAKC,SAAS;YAAEC;UAAQ,CAAE,EAAEC,KAAA;YACxC,MAAM;cAAEC;YAAS,CAAE,GAAGH,SAAA,IAAa,CAAC;YAEpC,MAAMI,cAAA,GACJ,CAACD,SAAA,IACDA,SAAA,CAAU;cAAEd,gBAAA;cAAkBI,MAAA;cAAQH,YAAA;cAAcC,WAAA;cAAaC;YAAI;YAEvE,IAAI,CAACY,cAAA,EAAgB;cACnB,OAAO;YACT;YAEA,IAAIJ,SAAA,EAAWK,SAAA,EAAW;cACxB,OAAOvB,qBAAA,CAAsB;gBAC3BwB,WAAA,EAAa;kBACXC,IAAA,EAAMN;gBACR;gBACAI,SAAA,EAAWL,SAAA,CAAUK,SAAS;gBAC9BG,SAAA,EAAWhB,GAAA,CAAIE,OAAO,CAACc,SAAS;gBAChCC,GAAA,EAAK,OAAOP,KAAA,EAAO;gBACnBQ,WAAA,EAAa;kBACXrB,gBAAA;kBACAC,YAAA;kBACAqB,IAAA,EAAMnB,GAAA,CAAImB,IAAI;kBACdjB,OAAA,EAASF,GAAA,CAAIE,OAAO;kBACpBH,WAAA;kBACAC,GAAA;kBACAoB,IAAA,EAAMpB,GAAA,CAAIoB;gBACZ;cACF;YACF;YAEA,oBACEhB,IAAA,CAACX,kBAAA;cACCI,gBAAA,EAAkBA,gBAAA;cAClBC,YAAA,EAAcA,YAAA;cAEdiB,IAAA,EAAMN,QAAA;cACNV,WAAA,EAAaA,WAAA;cACbC,GAAA,EAAKA,GAAA;cACLQ,SAAA,EAAWA;eAJN,OAAOE,KAAA,EAAO;UAOzB;;;;;AAMZ", "ignoreList": []}