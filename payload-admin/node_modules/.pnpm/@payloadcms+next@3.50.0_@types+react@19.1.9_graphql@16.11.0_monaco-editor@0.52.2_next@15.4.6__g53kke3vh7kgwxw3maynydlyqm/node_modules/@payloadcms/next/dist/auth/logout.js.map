{"version": 3, "file": "logout.js", "names": ["cookies", "getCookies", "headers", "nextHeaders", "createLocalReq", "getPayload", "logoutOperation", "getExistingAuthToken", "logout", "allSessions", "config", "payload", "cron", "authResult", "auth", "user", "message", "success", "req", "collection", "collections", "logoutResult", "existingCookie", "cookiePrefix", "delete", "name"], "sources": ["../../src/auth/logout.ts"], "sourcesContent": ["'use server'\n\nimport type { SanitizedConfig } from 'payload'\n\nimport { cookies as getCookies, headers as nextHeaders } from 'next/headers.js'\nimport { createLocalReq, getPayload, logoutOperation } from 'payload'\n\nimport { getExistingAuthToken } from '../utilities/getExistingAuthToken.js'\n\nexport async function logout({\n  allSessions = false,\n  config,\n}: {\n  allSessions?: boolean\n  config: Promise<SanitizedConfig> | SanitizedConfig\n}) {\n  const payload = await getPayload({ config, cron: true })\n  const headers = await nextHeaders()\n  const authResult = await payload.auth({ headers })\n\n  if (!authResult.user) {\n    return { message: 'User already logged out', success: true }\n  }\n\n  const { user } = authResult\n  const req = await createLocalReq({ user }, payload)\n  const collection = payload.collections[user.collection]\n\n  const logoutResult = await logoutOperation({\n    allSessions,\n    collection,\n    req,\n  })\n\n  if (!logoutResult) {\n    return { message: 'Logout failed', success: false }\n  }\n\n  const existingCookie = await getExistingAuthToken(payload.config.cookiePrefix)\n  if (existingCookie) {\n    const cookies = await getCookies()\n    cookies.delete(existingCookie.name)\n  }\n\n  return { message: 'User logged out successfully', success: true }\n}\n"], "mappings": "AAAA;;AAIA,SAASA,OAAA,IAAWC,UAAU,EAAEC,OAAA,IAAWC,WAAW,QAAQ;AAC9D,SAASC,cAAc,EAAEC,UAAU,EAAEC,eAAe,QAAQ;AAE5D,SAASC,oBAAoB,QAAQ;AAErC,OAAO,eAAeC,OAAO;EAC3BC,WAAA,GAAc,KAAK;EACnBC;AAAM,CAIP;EACC,MAAMC,OAAA,GAAU,MAAMN,UAAA,CAAW;IAAEK,MAAA;IAAQE,IAAA,EAAM;EAAK;EACtD,MAAMV,OAAA,GAAU,MAAMC,WAAA;EACtB,MAAMU,UAAA,GAAa,MAAMF,OAAA,CAAQG,IAAI,CAAC;IAAEZ;EAAQ;EAEhD,IAAI,CAACW,UAAA,CAAWE,IAAI,EAAE;IACpB,OAAO;MAAEC,OAAA,EAAS;MAA2BC,OAAA,EAAS;IAAK;EAC7D;EAEA,MAAM;IAAEF;EAAI,CAAE,GAAGF,UAAA;EACjB,MAAMK,GAAA,GAAM,MAAMd,cAAA,CAAe;IAAEW;EAAK,GAAGJ,OAAA;EAC3C,MAAMQ,UAAA,GAAaR,OAAA,CAAQS,WAAW,CAACL,IAAA,CAAKI,UAAU,CAAC;EAEvD,MAAME,YAAA,GAAe,MAAMf,eAAA,CAAgB;IACzCG,WAAA;IACAU,UAAA;IACAD;EACF;EAEA,IAAI,CAACG,YAAA,EAAc;IACjB,OAAO;MAAEL,OAAA,EAAS;MAAiBC,OAAA,EAAS;IAAM;EACpD;EAEA,MAAMK,cAAA,GAAiB,MAAMf,oBAAA,CAAqBI,OAAA,CAAQD,MAAM,CAACa,YAAY;EAC7E,IAAID,cAAA,EAAgB;IAClB,MAAMtB,OAAA,GAAU,MAAMC,UAAA;IACtBD,OAAA,CAAQwB,MAAM,CAACF,cAAA,CAAeG,IAAI;EACpC;EAEA,OAAO;IAAET,OAAA,EAAS;IAAgCC,OAAA,EAAS;EAAK;AAClE", "ignoreList": []}