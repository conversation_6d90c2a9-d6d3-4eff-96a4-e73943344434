{"version": 3, "file": "index.js", "names": ["rtlLanguages", "ProgressBar", "RootProvider", "getClientConfig", "cookies", "nextCookies", "React", "getNavPrefs", "getRequestTheme", "initReq", "checkDependencies", "NestProviders", "metadata", "description", "title", "RootLayout", "children", "config", "config<PERSON>rom<PERSON>", "htmlProps", "importMap", "serverFunction", "headers", "languageCode", "permissions", "req", "payload", "key", "theme", "dir", "includes", "languageOptions", "Object", "entries", "i18n", "supportedLanguages", "reduce", "acc", "language", "languageConfig", "keys", "push", "label", "translations", "general", "thisLanguage", "value", "switchLanguageServerAction", "lang", "set", "name", "cookiePrefix", "path", "navPrefs", "clientConfig", "localization", "filterAvailableLocales", "locales", "map", "toString", "rest", "localeCodes", "code", "_jsxs", "suppressHydrationWarning", "admin", "_jsx", "dateFNS<PERSON>ey", "fallback<PERSON><PERSON>", "fallbackLanguage", "isNavOpen", "open", "locale", "user", "Array", "isArray", "components", "providers", "length", "serverProps", "id"], "sources": ["../../../src/layouts/Root/index.tsx"], "sourcesContent": ["import type { AcceptedLanguages } from '@payloadcms/translations'\nimport type { ImportMap, LanguageOptions, SanitizedConfig, ServerFunctionClient } from 'payload'\n\nimport { rtlLanguages } from '@payloadcms/translations'\nimport { ProgressBar, RootProvider } from '@payloadcms/ui'\nimport { getClientConfig } from '@payloadcms/ui/utilities/getClientConfig'\nimport { cookies as nextCookies } from 'next/headers.js'\nimport React from 'react'\n\nimport { getNavPrefs } from '../../elements/Nav/getNavPrefs.js'\nimport { getRequestTheme } from '../../utilities/getRequestTheme.js'\nimport { initReq } from '../../utilities/initReq.js'\nimport { checkDependencies } from './checkDependencies.js'\nimport { NestProviders } from './NestProviders.js'\n\nimport '@payloadcms/ui/scss/app.scss'\n\nexport const metadata = {\n  description: 'Generated by Next.js',\n  title: 'Next.js',\n}\n\nexport const RootLayout = async ({\n  children,\n  config: configPromise,\n  htmlProps = {},\n  importMap,\n  serverFunction,\n}: {\n  readonly children: React.ReactNode\n  readonly config: Promise<SanitizedConfig>\n  readonly htmlProps?: React.HtmlHTMLAttributes<HTMLHtmlElement>\n  readonly importMap: ImportMap\n  readonly serverFunction: ServerFunctionClient\n}) => {\n  checkDependencies()\n\n  const {\n    cookies,\n    headers,\n    languageCode,\n    permissions,\n    req,\n    req: {\n      payload: { config },\n    },\n  } = await initReq({ configPromise, importMap, key: 'RootLayout' })\n\n  const theme = getRequestTheme({\n    config,\n    cookies,\n    headers,\n  })\n\n  const dir = (rtlLanguages as unknown as AcceptedLanguages[]).includes(languageCode)\n    ? 'RTL'\n    : 'LTR'\n\n  const languageOptions: LanguageOptions = Object.entries(\n    config.i18n.supportedLanguages || {},\n  ).reduce((acc, [language, languageConfig]) => {\n    if (Object.keys(config.i18n.supportedLanguages).includes(language)) {\n      acc.push({\n        label: languageConfig.translations.general.thisLanguage,\n        value: language,\n      })\n    }\n\n    return acc\n  }, [])\n\n  async function switchLanguageServerAction(lang: string): Promise<void> {\n    'use server'\n    const cookies = await nextCookies()\n    cookies.set({\n      name: `${config.cookiePrefix || 'payload'}-lng`,\n      path: '/',\n      value: lang,\n    })\n  }\n\n  const navPrefs = await getNavPrefs(req)\n\n  const clientConfig = getClientConfig({\n    config,\n    i18n: req.i18n,\n    importMap,\n  })\n\n  if (\n    clientConfig.localization &&\n    config.localization &&\n    typeof config.localization.filterAvailableLocales === 'function'\n  ) {\n    clientConfig.localization.locales = (\n      await config.localization.filterAvailableLocales({\n        locales: config.localization.locales,\n        req,\n      })\n    ).map(({ toString, ...rest }) => rest)\n    clientConfig.localization.localeCodes = config.localization.locales.map(({ code }) => code)\n  }\n\n  return (\n    <html\n      data-theme={theme}\n      dir={dir}\n      lang={languageCode}\n      suppressHydrationWarning={config?.admin?.suppressHydrationWarning ?? false}\n      {...htmlProps}\n    >\n      <head>\n        <style>{`@layer payload-default, payload;`}</style>\n      </head>\n      <body>\n        <RootProvider\n          config={clientConfig}\n          dateFNSKey={req.i18n.dateFNSKey}\n          fallbackLang={config.i18n.fallbackLanguage}\n          isNavOpen={navPrefs?.open ?? true}\n          languageCode={languageCode}\n          languageOptions={languageOptions}\n          locale={req.locale}\n          permissions={permissions}\n          serverFunction={serverFunction}\n          switchLanguageServerAction={switchLanguageServerAction}\n          theme={theme}\n          translations={req.i18n.translations}\n          user={req.user}\n        >\n          <ProgressBar />\n          {Array.isArray(config.admin?.components?.providers) &&\n          config.admin?.components?.providers.length > 0 ? (\n            <NestProviders\n              importMap={req.payload.importMap}\n              providers={config.admin?.components?.providers}\n              serverProps={{\n                i18n: req.i18n,\n                payload: req.payload,\n                permissions,\n                user: req.user,\n              }}\n            >\n              {children}\n            </NestProviders>\n          ) : (\n            children\n          )}\n        </RootProvider>\n        <div id=\"portal\" />\n      </body>\n    </html>\n  )\n}\n"], "mappings": ";AAGA,SAASA,YAAY,QAAQ;AAC7B,SAASC,WAAW,EAAEC,YAAY,QAAQ;AAC1C,SAASC,eAAe,QAAQ;AAChC,SAASC,OAAA,IAAWC,WAAW,QAAQ;AACvC,OAAOC,KAAA,MAAW;AAElB,SAASC,WAAW,QAAQ;AAC5B,SAASC,eAAe,QAAQ;AAChC,SAASC,OAAO,QAAQ;AACxB,SAASC,iBAAiB,QAAQ;AAClC,SAASC,aAAa,QAAQ;AAI9B,OAAO,MAAMC,QAAA,GAAW;EACtBC,WAAA,EAAa;EACbC,KAAA,EAAO;AACT;AAEA,OAAO,MAAMC,UAAA,GAAa,MAAAA,CAAO;EAC/BC,QAAQ;EACRC,MAAA,EAAQC,aAAa;EACrBC,SAAA,GAAY,CAAC,CAAC;EACdC,SAAS;EACTC;AAAc,CAOf;EACCX,iBAAA;EAEA,MAAM;IACJN,OAAO;IACPkB,OAAO;IACPC,YAAY;IACZC,WAAW;IACXC,GAAG;IACHA,GAAA,EAAK;MACHC,OAAA,EAAS;QAAET;MAAM;IAAE;EACpB,CACF,GAAG,MAAMR,OAAA,CAAQ;IAAES,aAAA;IAAeE,SAAA;IAAWO,GAAA,EAAK;EAAa;EAEhE,MAAMC,KAAA,GAAQpB,eAAA,CAAgB;IAC5BS,MAAA;IACAb,OAAA;IACAkB;EACF;EAEA,MAAMO,GAAA,GAAM7B,YAAC,CAAgD8B,QAAQ,CAACP,YAAA,IAClE,QACA;EAEJ,MAAMQ,eAAA,GAAmCC,MAAA,CAAOC,OAAO,CACrDhB,MAAA,CAAOiB,IAAI,CAACC,kBAAkB,IAAI,CAAC,GACnCC,MAAM,CAAC,CAACC,GAAA,EAAK,CAACC,QAAA,EAAUC,cAAA,CAAe;IACvC,IAAIP,MAAA,CAAOQ,IAAI,CAACvB,MAAA,CAAOiB,IAAI,CAACC,kBAAkB,EAAEL,QAAQ,CAACQ,QAAA,GAAW;MAClED,GAAA,CAAII,IAAI,CAAC;QACPC,KAAA,EAAOH,cAAA,CAAeI,YAAY,CAACC,OAAO,CAACC,YAAY;QACvDC,KAAA,EAAOR;MACT;IACF;IAEA,OAAOD,GAAA;EACT,GAAG,EAAE;EAEL,eAAeU,2BAA2BC,IAAY;IACpD;;IACA,MAAM5C,OAAA,GAAU,MAAMC,WAAA;IACtBD,OAAA,CAAQ6C,GAAG,CAAC;MACVC,IAAA,EAAM,GAAGjC,MAAA,CAAOkC,YAAY,IAAI,eAAe;MAC/CC,IAAA,EAAM;MACNN,KAAA,EAAOE;IACT;EACF;EAEA,MAAMK,QAAA,GAAW,MAAM9C,WAAA,CAAYkB,GAAA;EAEnC,MAAM6B,YAAA,GAAenD,eAAA,CAAgB;IACnCc,MAAA;IACAiB,IAAA,EAAMT,GAAA,CAAIS,IAAI;IACdd;EACF;EAEA,IACEkC,YAAA,CAAaC,YAAY,IACzBtC,MAAA,CAAOsC,YAAY,IACnB,OAAOtC,MAAA,CAAOsC,YAAY,CAACC,sBAAsB,KAAK,YACtD;IACAF,YAAA,CAAaC,YAAY,CAACE,OAAO,GAAG,CAClC,MAAMxC,MAAA,CAAOsC,YAAY,CAACC,sBAAsB,CAAC;MAC/CC,OAAA,EAASxC,MAAA,CAAOsC,YAAY,CAACE,OAAO;MACpChC;IACF,EAAC,EACDiC,GAAG,CAAC,CAAC;MAAEC,QAAQ;MAAE,GAAGC;IAAA,CAAM,KAAKA,IAAA;IACjCN,YAAA,CAAaC,YAAY,CAACM,WAAW,GAAG5C,MAAA,CAAOsC,YAAY,CAACE,OAAO,CAACC,GAAG,CAAC,CAAC;MAAEI;IAAI,CAAE,KAAKA,IAAA;EACxF;EAEA,oBACEC,KAAA,CAAC;IACC,cAAYnC,KAAA;IACZC,GAAA,EAAKA,GAAA;IACLmB,IAAA,EAAMzB,YAAA;IACNyC,wBAAA,EAA0B/C,MAAA,EAAQgD,KAAA,EAAOD,wBAAA,IAA4B;IACpE,GAAG7C,SAAS;4BAEb+C,IAAA,CAAC;gBACC,aAAAA,IAAA,CAAC;kBAAO;;qBAEVH,KAAA,CAAC;8BACCA,KAAA,CAAC7D,YAAA;QACCe,MAAA,EAAQqC,YAAA;QACRa,UAAA,EAAY1C,GAAA,CAAIS,IAAI,CAACiC,UAAU;QAC/BC,YAAA,EAAcnD,MAAA,CAAOiB,IAAI,CAACmC,gBAAgB;QAC1CC,SAAA,EAAWjB,QAAA,EAAUkB,IAAA,IAAQ;QAC7BhD,YAAA,EAAcA,YAAA;QACdQ,eAAA,EAAiBA,eAAA;QACjByC,MAAA,EAAQ/C,GAAA,CAAI+C,MAAM;QAClBhD,WAAA,EAAaA,WAAA;QACbH,cAAA,EAAgBA,cAAA;QAChB0B,0BAAA,EAA4BA,0BAAA;QAC5BnB,KAAA,EAAOA,KAAA;QACPe,YAAA,EAAclB,GAAA,CAAIS,IAAI,CAACS,YAAY;QACnC8B,IAAA,EAAMhD,GAAA,CAAIgD,IAAI;gCAEdP,IAAA,CAACjE,WAAA,OACAyE,KAAA,CAAMC,OAAO,CAAC1D,MAAA,CAAOgD,KAAK,EAAEW,UAAA,EAAYC,SAAA,KACzC5D,MAAA,CAAOgD,KAAK,EAAEW,UAAA,EAAYC,SAAA,CAAUC,MAAA,GAAS,iBAC3CZ,IAAA,CAACvD,aAAA;UACCS,SAAA,EAAWK,GAAA,CAAIC,OAAO,CAACN,SAAS;UAChCyD,SAAA,EAAW5D,MAAA,CAAOgD,KAAK,EAAEW,UAAA,EAAYC,SAAA;UACrCE,WAAA,EAAa;YACX7C,IAAA,EAAMT,GAAA,CAAIS,IAAI;YACdR,OAAA,EAASD,GAAA,CAAIC,OAAO;YACpBF,WAAA;YACAiD,IAAA,EAAMhD,GAAA,CAAIgD;UACZ;oBAECzD;aAGHA,QAAA;uBAGJkD,IAAA,CAAC;QAAIc,EAAA,EAAG;;;;AAIhB", "ignoreList": []}