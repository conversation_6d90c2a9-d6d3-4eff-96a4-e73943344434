{"version": 3, "file": "index.js", "names": ["ActionsProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BulkUploadProvider", "EntityVisibilityProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RenderServerComponent", "React", "DefaultNav", "NavHamburger", "Wrapper", "baseClass", "DefaultTemplate", "children", "className", "collectionSlug", "docID", "documentSubViewType", "globalSlug", "i18n", "locale", "params", "payload", "permissions", "req", "searchParams", "user", "viewActions", "viewType", "visibleEntities", "admin", "avatar", "components", "header", "CustomHeader", "Nav", "CustomNav", "undefined", "config", "clientProps", "useMemo", "serverProps", "Actions", "reduce", "acc", "action", "path", "Component", "importMap", "NavComponent", "Fallback", "_jsx", "drawerSlugPrefix", "_jsxs", "style", "position", "id", "CustomAvatar", "CustomIcon", "graphics", "Icon"], "sources": ["../../../src/templates/Default/index.tsx"], "sourcesContent": ["import type {\n  CustomComponent,\n  DocumentSubViewTypes,\n  PayloadRequest,\n  ServerProps,\n  ViewTypes,\n  VisibleEntities,\n} from 'payload'\n\nimport {\n  ActionsProvider,\n  AppHeader,\n  BulkUploadProvider,\n  EntityVisibilityProvider,\n  NavToggler,\n} from '@payloadcms/ui'\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\n\nimport './index.scss'\n\nimport React from 'react'\n\nimport { DefaultNav } from '../../elements/Nav/index.js'\nimport { NavHamburger } from './NavHamburger/index.js'\nimport { Wrapper } from './Wrapper/index.js'\n\nconst baseClass = 'template-default'\n\nexport type DefaultTemplateProps = {\n  children?: React.ReactNode\n  className?: string\n  collectionSlug?: string\n  docID?: number | string\n  documentSubViewType?: DocumentSubViewTypes\n  globalSlug?: string\n  req?: PayloadRequest\n  viewActions?: CustomComponent[]\n  viewType?: ViewTypes\n  visibleEntities: VisibleEntities\n} & ServerProps\n\nexport const DefaultTemplate: React.FC<DefaultTemplateProps> = ({\n  children,\n  className,\n  collectionSlug,\n  docID,\n  documentSubViewType,\n  globalSlug,\n  i18n,\n  locale,\n  params,\n  payload,\n  permissions,\n  req,\n  searchParams,\n  user,\n  viewActions,\n  viewType,\n  visibleEntities,\n}) => {\n  const {\n    admin: {\n      avatar,\n      components,\n      components: { header: CustomHeader, Nav: CustomNav } = {\n        header: undefined,\n        Nav: undefined,\n      },\n    } = {},\n  } = payload.config || {}\n\n  const clientProps = React.useMemo(() => {\n    return {\n      documentSubViewType,\n      viewType,\n      visibleEntities,\n    }\n  }, [documentSubViewType, viewType, visibleEntities])\n\n  const serverProps = React.useMemo<ServerProps>(\n    () => ({\n      collectionSlug,\n      docID,\n      globalSlug,\n      i18n,\n      locale,\n      params,\n      payload,\n      permissions,\n      req,\n      searchParams,\n      user,\n    }),\n    [\n      i18n,\n      locale,\n      params,\n      payload,\n      permissions,\n      searchParams,\n      user,\n      globalSlug,\n      collectionSlug,\n      docID,\n      req,\n    ],\n  )\n\n  const { Actions } = React.useMemo<{\n    Actions: Record<string, React.ReactNode>\n  }>(() => {\n    return {\n      Actions: viewActions\n        ? viewActions.reduce((acc, action) => {\n            if (action) {\n              if (typeof action === 'object') {\n                acc[action.path] = RenderServerComponent({\n                  clientProps,\n                  Component: action,\n                  importMap: payload.importMap,\n                  serverProps,\n                })\n              } else {\n                acc[action] = RenderServerComponent({\n                  clientProps,\n                  Component: action,\n                  importMap: payload.importMap,\n                  serverProps,\n                })\n              }\n            }\n\n            return acc\n          }, {})\n        : undefined,\n    }\n  }, [payload, serverProps, viewActions, clientProps])\n\n  const NavComponent = RenderServerComponent({\n    clientProps,\n    Component: CustomNav,\n    Fallback: DefaultNav,\n    importMap: payload.importMap,\n    serverProps,\n  })\n\n  return (\n    <EntityVisibilityProvider visibleEntities={visibleEntities}>\n      <BulkUploadProvider drawerSlugPrefix={collectionSlug}>\n        <ActionsProvider Actions={Actions}>\n          {RenderServerComponent({\n            clientProps,\n            Component: CustomHeader,\n            importMap: payload.importMap,\n            serverProps,\n          })}\n          <div style={{ position: 'relative' }}>\n            <div className={`${baseClass}__nav-toggler-wrapper`} id=\"nav-toggler\">\n              <div className={`${baseClass}__nav-toggler-container`} id=\"nav-toggler\">\n                <NavToggler className={`${baseClass}__nav-toggler`}>\n                  <NavHamburger />\n                </NavToggler>\n              </div>\n            </div>\n            <Wrapper baseClass={baseClass} className={className}>\n              {NavComponent}\n              <div className={`${baseClass}__wrap`}>\n                <AppHeader\n                  CustomAvatar={\n                    avatar !== 'gravatar' && avatar !== 'default'\n                      ? RenderServerComponent({\n                          Component: avatar.Component,\n                          importMap: payload.importMap,\n                          serverProps,\n                        })\n                      : undefined\n                  }\n                  CustomIcon={\n                    components?.graphics?.Icon\n                      ? RenderServerComponent({\n                          Component: components.graphics.Icon,\n                          importMap: payload.importMap,\n                          serverProps,\n                        })\n                      : undefined\n                  }\n                />\n                {children}\n              </div>\n            </Wrapper>\n          </div>\n        </ActionsProvider>\n      </BulkUploadProvider>\n    </EntityVisibilityProvider>\n  )\n}\n"], "mappings": ";AASA,SACEA,eAAe,EACfC,SAAS,EACTC,kBAAkB,EAClBC,wBAAwB,EACxBC,UAAU,QACL;AACP,SAASC,qBAAqB,QAAQ;AAItC,OAAOC,KAAA,MAAW;AAElB,SAASC,UAAU,QAAQ;AAC3B,SAASC,YAAY,QAAQ;AAC7B,SAASC,OAAO,QAAQ;AAExB,MAAMC,SAAA,GAAY;AAelB,OAAO,MAAMC,eAAA,GAAkDA,CAAC;EAC9DC,QAAQ;EACRC,SAAS;EACTC,cAAc;EACdC,KAAK;EACLC,mBAAmB;EACnBC,UAAU;EACVC,IAAI;EACJC,MAAM;EACNC,MAAM;EACNC,OAAO;EACPC,WAAW;EACXC,GAAG;EACHC,YAAY;EACZC,IAAI;EACJC,WAAW;EACXC,QAAQ;EACRC;AAAe,CAChB;EACC,MAAM;IACJC,KAAA,EAAO;MACLC,MAAM;MACNC,UAAU;MACVA,UAAA,EAAY;QAAEC,MAAA,EAAQC,YAAY;QAAEC,GAAA,EAAKC;MAAS,CAAE,GAAG;QACrDH,MAAA,EAAQI,SAAA;QACRF,GAAA,EAAKE;MACP;IAAC,CACF,GAAG,CAAC;EAAC,CACP,GAAGf,OAAA,CAAQgB,MAAM,IAAI,CAAC;EAEvB,MAAMC,WAAA,GAAchC,KAAA,CAAMiC,OAAO,CAAC;IAChC,OAAO;MACLvB,mBAAA;MACAW,QAAA;MACAC;IACF;EACF,GAAG,CAACZ,mBAAA,EAAqBW,QAAA,EAAUC,eAAA,CAAgB;EAEnD,MAAMY,WAAA,GAAclC,KAAA,CAAMiC,OAAO,CAC/B,OAAO;IACLzB,cAAA;IACAC,KAAA;IACAE,UAAA;IACAC,IAAA;IACAC,MAAA;IACAC,MAAA;IACAC,OAAA;IACAC,WAAA;IACAC,GAAA;IACAC,YAAA;IACAC;EACF,IACA,CACEP,IAAA,EACAC,MAAA,EACAC,MAAA,EACAC,OAAA,EACAC,WAAA,EACAE,YAAA,EACAC,IAAA,EACAR,UAAA,EACAH,cAAA,EACAC,KAAA,EACAQ,GAAA,CACD;EAGH,MAAM;IAAEkB;EAAO,CAAE,GAAGnC,KAAA,CAAMiC,OAAO,CAE9B;IACD,OAAO;MACLE,OAAA,EAASf,WAAA,GACLA,WAAA,CAAYgB,MAAM,CAAC,CAACC,GAAA,EAAKC,MAAA;QACvB,IAAIA,MAAA,EAAQ;UACV,IAAI,OAAOA,MAAA,KAAW,UAAU;YAC9BD,GAAG,CAACC,MAAA,CAAOC,IAAI,CAAC,GAAGxC,qBAAA,CAAsB;cACvCiC,WAAA;cACAQ,SAAA,EAAWF,MAAA;cACXG,SAAA,EAAW1B,OAAA,CAAQ0B,SAAS;cAC5BP;YACF;UACF,OAAO;YACLG,GAAG,CAACC,MAAA,CAAO,GAAGvC,qBAAA,CAAsB;cAClCiC,WAAA;cACAQ,SAAA,EAAWF,MAAA;cACXG,SAAA,EAAW1B,OAAA,CAAQ0B,SAAS;cAC5BP;YACF;UACF;QACF;QAEA,OAAOG,GAAA;MACT,GAAG,CAAC,KACJP;IACN;EACF,GAAG,CAACf,OAAA,EAASmB,WAAA,EAAad,WAAA,EAAaY,WAAA,CAAY;EAEnD,MAAMU,YAAA,GAAe3C,qBAAA,CAAsB;IACzCiC,WAAA;IACAQ,SAAA,EAAWX,SAAA;IACXc,QAAA,EAAU1C,UAAA;IACVwC,SAAA,EAAW1B,OAAA,CAAQ0B,SAAS;IAC5BP;EACF;EAEA,oBACEU,IAAA,CAAC/C,wBAAA;IAAyByB,eAAA,EAAiBA,eAAA;cACzC,aAAAsB,IAAA,CAAChD,kBAAA;MAAmBiD,gBAAA,EAAkBrC,cAAA;gBACpC,aAAAsC,KAAA,CAACpD,eAAA;QAAgByC,OAAA,EAASA,OAAA;mBACvBpC,qBAAA,CAAsB;UACrBiC,WAAA;UACAQ,SAAA,EAAWb,YAAA;UACXc,SAAA,EAAW1B,OAAA,CAAQ0B,SAAS;UAC5BP;QACF,I,aACAY,KAAA,CAAC;UAAIC,KAAA,EAAO;YAAEC,QAAA,EAAU;UAAW;kCACjCJ,IAAA,CAAC;YAAIrC,SAAA,EAAW,GAAGH,SAAA,uBAAgC;YAAE6C,EAAA,EAAG;sBACtD,aAAAL,IAAA,CAAC;cAAIrC,SAAA,EAAW,GAAGH,SAAA,yBAAkC;cAAE6C,EAAA,EAAG;wBACxD,aAAAL,IAAA,CAAC9C,UAAA;gBAAWS,SAAA,EAAW,GAAGH,SAAA,eAAwB;0BAChD,aAAAwC,IAAA,CAAC1C,YAAA;;;2BAIP4C,KAAA,CAAC3C,OAAA;YAAQC,SAAA,EAAWA,SAAA;YAAWG,SAAA,EAAWA,SAAA;uBACvCmC,YAAA,E,aACDI,KAAA,CAAC;cAAIvC,SAAA,EAAW,GAAGH,SAAA,QAAiB;sCAClCwC,IAAA,CAACjD,SAAA;gBACCuD,YAAA,EACE1B,MAAA,KAAW,cAAcA,MAAA,KAAW,YAChCzB,qBAAA,CAAsB;kBACpByC,SAAA,EAAWhB,MAAA,CAAOgB,SAAS;kBAC3BC,SAAA,EAAW1B,OAAA,CAAQ0B,SAAS;kBAC5BP;gBACF,KACAJ,SAAA;gBAENqB,UAAA,EACE1B,UAAA,EAAY2B,QAAA,EAAUC,IAAA,GAClBtD,qBAAA,CAAsB;kBACpByC,SAAA,EAAWf,UAAA,CAAW2B,QAAQ,CAACC,IAAI;kBACnCZ,SAAA,EAAW1B,OAAA,CAAQ0B,SAAS;kBAC5BP;gBACF,KACAJ;kBAGPxB,QAAA;;;;;;;AAQjB", "ignoreList": []}