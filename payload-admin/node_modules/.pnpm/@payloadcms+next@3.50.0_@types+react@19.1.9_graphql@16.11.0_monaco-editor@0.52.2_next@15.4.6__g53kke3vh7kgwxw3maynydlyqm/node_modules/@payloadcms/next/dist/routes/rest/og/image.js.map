{"version": 3, "file": "image.js", "names": ["RenderServerComponent", "React", "OGImage", "description", "Fallback", "fontFamily", "Icon", "importMap", "leader", "title", "IconComponent", "clientProps", "fill", "Component", "_jsxs", "style", "backgroundColor", "color", "display", "flexDirection", "height", "justifyContent", "padding", "width", "flexGrow", "fontSize", "_jsx", "marginBottom", "lineHeight", "marginTop", "textOverflow", "WebkitBoxOrient", "WebkitLineClamp", "alignItems", "flexShrink"], "sources": ["../../../../src/routes/rest/og/image.tsx"], "sourcesContent": ["import type { ImportMap, PayloadComponent } from 'payload'\n\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\nimport React from 'react'\n\nexport const OGImage: React.FC<{\n  description?: string\n  Fallback: React.ComponentType\n  fontFamily?: string\n  Icon: PayloadComponent\n  importMap: ImportMap\n  leader?: string\n  title?: string\n}> = ({\n  description,\n  Fallback,\n  fontFamily = 'Arial, sans-serif',\n  Icon,\n  importMap,\n  leader,\n  title,\n}) => {\n  const IconComponent = RenderServerComponent({\n    clientProps: {\n      fill: 'white',\n    },\n    Component: Icon,\n    Fallback,\n    importMap,\n  })\n  return (\n    <div\n      style={{\n        backgroundColor: '#000',\n        color: '#fff',\n        display: 'flex',\n        flexDirection: 'column',\n        fontFamily,\n        height: '100%',\n        justifyContent: 'space-between',\n        padding: '100px',\n        width: '100%',\n      }}\n    >\n      <div\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          flexGrow: 1,\n          fontSize: 50,\n          height: '100%',\n        }}\n      >\n        {leader && (\n          <div\n            style={{\n              fontSize: 30,\n              marginBottom: 10,\n            }}\n          >\n            {leader}\n          </div>\n        )}\n        <p\n          style={{\n            display: '-webkit-box',\n            fontSize: 90,\n            lineHeight: 1,\n            marginBottom: 0,\n            marginTop: 0,\n            textOverflow: 'ellipsis',\n            WebkitBoxOrient: 'vertical',\n            WebkitLineClamp: 2,\n          }}\n        >\n          {title}\n        </p>\n        {description && (\n          <p\n            style={{\n              display: '-webkit-box',\n              flexGrow: 1,\n              fontSize: 30,\n              lineHeight: 1,\n              marginBottom: 0,\n              marginTop: 40,\n              textOverflow: 'ellipsis',\n              WebkitBoxOrient: 'vertical',\n              WebkitLineClamp: 2,\n            }}\n          >\n            {description}\n          </p>\n        )}\n      </div>\n      <div\n        style={{\n          alignItems: 'flex-end',\n          display: 'flex',\n          flexShrink: 0,\n          height: '38px',\n          justifyContent: 'center',\n          width: '38px',\n        }}\n      >\n        {IconComponent}\n      </div>\n    </div>\n  )\n}\n"], "mappings": ";AAEA,SAASA,qBAAqB,QAAQ;AACtC,OAAOC,KAAA,MAAW;AAElB,OAAO,MAAMC,OAAA,GAQRA,CAAC;EACJC,WAAW;EACXC,QAAQ;EACRC,UAAA,GAAa,mBAAmB;EAChCC,IAAI;EACJC,SAAS;EACTC,MAAM;EACNC;AAAK,CACN;EACC,MAAMC,aAAA,GAAgBV,qBAAA,CAAsB;IAC1CW,WAAA,EAAa;MACXC,IAAA,EAAM;IACR;IACAC,SAAA,EAAWP,IAAA;IACXF,QAAA;IACAG;EACF;EACA,oBACEO,KAAA,CAAC;IACCC,KAAA,EAAO;MACLC,eAAA,EAAiB;MACjBC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTC,aAAA,EAAe;MACfd,UAAA;MACAe,MAAA,EAAQ;MACRC,cAAA,EAAgB;MAChBC,OAAA,EAAS;MACTC,KAAA,EAAO;IACT;4BAEAT,KAAA,CAAC;MACCC,KAAA,EAAO;QACLG,OAAA,EAAS;QACTC,aAAA,EAAe;QACfK,QAAA,EAAU;QACVC,QAAA,EAAU;QACVL,MAAA,EAAQ;MACV;iBAECZ,MAAA,iBACCkB,IAAA,CAAC;QACCX,KAAA,EAAO;UACLU,QAAA,EAAU;UACVE,YAAA,EAAc;QAChB;kBAECnB;uBAGLkB,IAAA,CAAC;QACCX,KAAA,EAAO;UACLG,OAAA,EAAS;UACTO,QAAA,EAAU;UACVG,UAAA,EAAY;UACZD,YAAA,EAAc;UACdE,SAAA,EAAW;UACXC,YAAA,EAAc;UACdC,eAAA,EAAiB;UACjBC,eAAA,EAAiB;QACnB;kBAECvB;UAEFN,WAAA,iBACCuB,IAAA,CAAC;QACCX,KAAA,EAAO;UACLG,OAAA,EAAS;UACTM,QAAA,EAAU;UACVC,QAAA,EAAU;UACVG,UAAA,EAAY;UACZD,YAAA,EAAc;UACdE,SAAA,EAAW;UACXC,YAAA,EAAc;UACdC,eAAA,EAAiB;UACjBC,eAAA,EAAiB;QACnB;kBAEC7B;;qBAIPuB,IAAA,CAAC;MACCX,KAAA,EAAO;QACLkB,UAAA,EAAY;QACZf,OAAA,EAAS;QACTgB,UAAA,EAAY;QACZd,MAAA,EAAQ;QACRC,cAAA,EAAgB;QAChBE,KAAA,EAAO;MACT;gBAECb;;;AAIT", "ignoreList": []}