@import '~@payloadcms/ui/scss';

@layer payload-default {
  .doc-header {
    width: 100%;
    margin-top: base(0.4);
    padding-bottom: calc(var(--base) * 1.2);
    display: flex;
    align-items: center;
    position: relative;
    display: flex;
    gap: calc(var(--base) / 2);

    &::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      background: var(--theme-elevation-100);
      width: 100%;
      left: 0;
      top: calc(100% - 1px);
    }

    &__title {
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
      padding-bottom: base(0.4);
      line-height: 1;
      vertical-align: top;
    }

    @include mid-break {
      margin-top: base(0.25);
      padding-bottom: calc(var(--base) / 1.5);
      flex-direction: column;
      gap: calc(var(--base) / 2);
      padding-bottom: calc(var(--base) / 2);

      &__title {
        width: 100%;
      }
    }

    @include small-break {
      margin-top: 0;
    }
  }
}
