{"version": 3, "file": "shared.js", "names": ["publicAdminRoutes", "isAdminRoute", "adminRoute", "route", "startsWith", "isPublicAdminRoute", "config", "some", "routeSegment", "segment", "admin", "routes", "routeWithoutAdmin", "getRouteWithoutAdmin", "includes", "replace"], "sources": ["../../../src/utilities/initPage/shared.ts"], "sourcesContent": ["import type { SanitizedConfig } from 'payload'\n\n// Routes that require admin authentication\nconst publicAdminRoutes: (keyof Pick<\n  SanitizedConfig['admin']['routes'],\n  'createFirstUser' | 'forgot' | 'inactivity' | 'login' | 'logout' | 'reset' | 'unauthorized'\n>)[] = [\n  'createFirstUser',\n  'forgot',\n  'login',\n  'logout',\n  'forgot',\n  'inactivity',\n  'unauthorized',\n  'reset',\n]\n\nexport const isAdminRoute = ({\n  adminRoute,\n  route,\n}: {\n  adminRoute: string\n  config: SanitizedConfig\n  route: string\n}): boolean => {\n  return route.startsWith(adminRoute)\n}\n\nexport const isPublicAdminRoute = ({\n  adminRoute,\n  config,\n  route,\n}: {\n  adminRoute: string\n  config: SanitizedConfig\n  route: string\n}): boolean => {\n  const isPublicAdminRoute = publicAdminRoutes.some((routeSegment) => {\n    const segment = config.admin?.routes?.[routeSegment] || routeSegment\n    const routeWithoutAdmin = getRouteWithoutAdmin({ adminRoute, route })\n\n    if (routeWithoutAdmin.startsWith(segment)) {\n      return true\n    } else if (routeWithoutAdmin.includes('/verify/')) {\n      return true\n    } else {\n      return false\n    }\n  })\n\n  return isPublicAdminRoute\n}\n\nexport const getRouteWithoutAdmin = ({\n  adminRoute,\n  route,\n}: {\n  adminRoute: string\n  route: string\n}): string => {\n  return adminRoute && adminRoute !== '/' ? route.replace(adminRoute, '') : route\n}\n"], "mappings": "AAEA;AACA,MAAMA,iBAAA,GAGC,CACL,mBACA,UACA,SACA,UACA,UACA,cACA,gBACA,QACD;AAED,OAAO,MAAMC,YAAA,GAAeA,CAAC;EAC3BC,UAAU;EACVC;AAAK,CAKN;EACC,OAAOA,KAAA,CAAMC,UAAU,CAACF,UAAA;AAC1B;AAEA,OAAO,MAAMG,kBAAA,GAAqBA,CAAC;EACjCH,UAAU;EACVI,MAAM;EACNH;AAAK,CAKN;EACC,MAAME,kBAAA,GAAqBL,iBAAA,CAAkBO,IAAI,CAAEC,YAAA;IACjD,MAAMC,OAAA,GAAUH,MAAA,CAAOI,KAAK,EAAEC,MAAA,GAASH,YAAA,CAAa,IAAIA,YAAA;IACxD,MAAMI,iBAAA,GAAoBC,oBAAA,CAAqB;MAAEX,UAAA;MAAYC;IAAM;IAEnE,IAAIS,iBAAA,CAAkBR,UAAU,CAACK,OAAA,GAAU;MACzC,OAAO;IACT,OAAO,IAAIG,iBAAA,CAAkBE,QAAQ,CAAC,aAAa;MACjD,OAAO;IACT,OAAO;MACL,OAAO;IACT;EACF;EAEA,OAAOT,kBAAA;AACT;AAEA,OAAO,MAAMQ,oBAAA,GAAuBA,CAAC;EACnCX,UAAU;EACVC;AAAK,CAIN;EACC,OAAOD,UAAA,IAAcA,UAAA,KAAe,MAAMC,KAAA,CAAMY,OAAO,CAACb,UAAA,EAAY,MAAMC,KAAA;AAC5E", "ignoreList": []}