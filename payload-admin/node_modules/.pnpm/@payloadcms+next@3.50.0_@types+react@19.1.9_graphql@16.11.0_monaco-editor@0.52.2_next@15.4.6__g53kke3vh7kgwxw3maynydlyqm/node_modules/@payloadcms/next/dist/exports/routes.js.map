{"version": 3, "file": "routes.js", "names": ["GRAPHQL_PLAYGROUND_GET", "GRAPHQL_POST", "DELETE", "REST_DELETE", "GET", "REST_GET", "OPTIONS", "REST_OPTIONS", "PATCH", "REST_PATCH", "POST", "REST_POST", "PUT", "REST_PUT"], "sources": ["../../src/exports/routes.ts"], "sourcesContent": ["export { GRAPHQL_PLAYGROUND_GET, GRAPHQL_POST } from '../routes/graphql/index.js'\n\nexport {\n  DELETE as REST_DELETE,\n  GET as REST_GET,\n  OPTIONS as REST_OPTIONS,\n  PATCH as REST_PATCH,\n  POST as REST_POST,\n  PUT as REST_PUT,\n} from '../routes/rest/index.js'\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,YAAY,QAAQ;AAErD,SACEC,MAAA,IAAUC,WAAW,EACrBC,GAAA,IAAOC,QAAQ,EACfC,OAAA,IAAWC,YAAY,EACvBC,KAAA,IAASC,UAAU,EACnBC,IAAA,IAAQC,SAAS,EACjBC,GAAA,IAAOC,QAAQ,QACV", "ignoreList": []}