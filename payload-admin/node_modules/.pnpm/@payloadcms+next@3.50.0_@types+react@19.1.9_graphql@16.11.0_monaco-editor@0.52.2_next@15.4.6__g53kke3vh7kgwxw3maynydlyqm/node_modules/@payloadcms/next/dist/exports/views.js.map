{"version": 3, "file": "views.js", "names": ["NotFoundPage", "RootPage", "generatePageMetadata"], "sources": ["../../src/exports/views.ts"], "sourcesContent": ["export { NotFoundPage } from '../views/NotFound/index.js'\nexport { type GenerateViewMetadata, RootPage } from '../views/Root/index.js'\nexport { generatePageMetadata } from '../views/Root/metadata.js'\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ;AAC7B,SAAoCC,QAAQ,QAAQ;AACpD,SAASC,oBAAoB,QAAQ", "ignoreList": []}