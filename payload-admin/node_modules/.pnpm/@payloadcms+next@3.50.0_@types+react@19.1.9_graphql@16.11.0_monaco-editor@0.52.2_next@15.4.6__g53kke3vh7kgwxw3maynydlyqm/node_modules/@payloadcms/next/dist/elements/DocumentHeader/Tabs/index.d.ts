import type { PayloadRequest, SanitizedCollectionConfig, SanitizedGlobalConfig, SanitizedPermissions } from 'payload';
import React from 'react';
import './index.scss';
export declare const DocumentTabs: React.FC<{
    collectionConfig: SanitizedCollectionConfig;
    globalConfig: SanitizedGlobalConfig;
    permissions: SanitizedPermissions;
    req: PayloadRequest;
}>;
//# sourceMappingURL=index.d.ts.map