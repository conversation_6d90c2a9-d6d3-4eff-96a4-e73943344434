{"version": 3, "file": "initReq.js", "names": ["initI18n", "headers", "getHeaders", "createLocalReq", "executeAuthStrategies", "getAccessResults", "getPayload", "getRequestLanguage", "parseCookies", "getRequestLocale", "selectiveCache", "partialReqCache", "req<PERSON><PERSON>", "initReq", "canSetHeaders", "config<PERSON>rom<PERSON>", "importMap", "key", "overrides", "cookies", "partialResult", "get", "config", "payload", "cron", "languageCode", "i18n", "context", "language", "responseHeaders", "user", "req", "reqOverrides", "optionsOverrides", "host", "locale", "code", "permissions"], "sources": ["../../src/utilities/initReq.ts"], "sourcesContent": ["import type { AcceptedLanguages, I18n, I18nClient } from '@payloadcms/translations'\nimport type {\n  ImportMap,\n  Locale,\n  Payload,\n  PayloadRequest,\n  SanitizedConfig,\n  SanitizedPermissions,\n  TypedUser,\n} from 'payload'\n\nimport { initI18n } from '@payloadcms/translations'\nimport { headers as getHeaders } from 'next/headers.js'\nimport {\n  createLocalReq,\n  executeAuthStrategies,\n  getAccessResults,\n  getPayload,\n  getRequestLanguage,\n  parseCookies,\n} from 'payload'\n\nimport { getRequestLocale } from './getRequestLocale.js'\nimport { selectiveCache } from './selectiveCache.js'\n\ntype Result = {\n  cookies: Map<string, string>\n  headers: Awaited<ReturnType<typeof getHeaders>>\n  languageCode: AcceptedLanguages\n  locale?: Locale\n  permissions: SanitizedPermissions\n  req: PayloadRequest\n}\n\ntype PartialResult = {\n  i18n: I18nClient\n  languageCode: AcceptedLanguages\n  payload: Payload\n  responseHeaders: Headers\n  user: null | TypedUser\n}\n\n// Create cache instances for different parts of our application\nconst partialReqCache = selectiveCache<PartialResult>('partialReq')\nconst reqCache = selectiveCache<Result>('req')\n\n/**\n * Initializes a full request object, including the `req` object and access control.\n * As access control and getting the request locale is dependent on the current URL and\n */\nexport const initReq = async function ({\n  canSetHeaders,\n  configPromise,\n  importMap,\n  key,\n  overrides,\n}: {\n  canSetHeaders?: boolean\n  configPromise: Promise<SanitizedConfig> | SanitizedConfig\n  importMap: ImportMap\n  key: string\n  overrides?: Parameters<typeof createLocalReq>[0]\n}): Promise<Result> {\n  const headers = await getHeaders()\n  const cookies = parseCookies(headers)\n\n  const partialResult = await partialReqCache.get(async () => {\n    const config = await configPromise\n    const payload = await getPayload({ config, cron: true, importMap })\n    const languageCode = getRequestLanguage({\n      config,\n      cookies,\n      headers,\n    })\n    const i18n: I18nClient = await initI18n({\n      config: config.i18n,\n      context: 'client',\n      language: languageCode,\n    })\n\n    const { responseHeaders, user } = await executeAuthStrategies({\n      canSetHeaders,\n      headers,\n      payload,\n    })\n\n    return {\n      i18n,\n      languageCode,\n      payload,\n      responseHeaders,\n      user,\n    }\n  }, 'global')\n\n  return reqCache.get(async () => {\n    const { i18n, languageCode, payload, responseHeaders, user } = partialResult\n\n    const { req: reqOverrides, ...optionsOverrides } = overrides || {}\n\n    const req = await createLocalReq(\n      {\n        req: {\n          headers,\n          host: headers.get('host'),\n          i18n: i18n as I18n,\n          responseHeaders,\n          user,\n          ...(reqOverrides || {}),\n        },\n        ...(optionsOverrides || {}),\n      },\n      payload,\n    )\n\n    const locale = await getRequestLocale({\n      req,\n    })\n\n    req.locale = locale?.code\n\n    const permissions = await getAccessResults({\n      req,\n    })\n\n    return {\n      cookies,\n      headers,\n      languageCode,\n      locale,\n      permissions,\n      req,\n    }\n  }, key)\n}\n"], "mappings": "AAWA,SAASA,QAAQ,QAAQ;AACzB,SAASC,OAAA,IAAWC,UAAU,QAAQ;AACtC,SACEC,cAAc,EACdC,qBAAqB,EACrBC,gBAAgB,EAChBC,UAAU,EACVC,kBAAkB,EAClBC,YAAY,QACP;AAEP,SAASC,gBAAgB,QAAQ;AACjC,SAASC,cAAc,QAAQ;AAmB/B;AACA,MAAMC,eAAA,GAAkBD,cAAA,CAA8B;AACtD,MAAME,QAAA,GAAWF,cAAA,CAAuB;AAExC;;;;AAIA,OAAO,MAAMG,OAAA,GAAU,eAAAA,CAAgB;EACrCC,aAAa;EACbC,aAAa;EACbC,SAAS;EACTC,GAAG;EACHC;AAAS,CAOV;EACC,MAAMjB,OAAA,GAAU,MAAMC,UAAA;EACtB,MAAMiB,OAAA,GAAUX,YAAA,CAAaP,OAAA;EAE7B,MAAMmB,aAAA,GAAgB,MAAMT,eAAA,CAAgBU,GAAG,CAAC;IAC9C,MAAMC,MAAA,GAAS,MAAMP,aAAA;IACrB,MAAMQ,OAAA,GAAU,MAAMjB,UAAA,CAAW;MAAEgB,MAAA;MAAQE,IAAA,EAAM;MAAMR;IAAU;IACjE,MAAMS,YAAA,GAAelB,kBAAA,CAAmB;MACtCe,MAAA;MACAH,OAAA;MACAlB;IACF;IACA,MAAMyB,IAAA,GAAmB,MAAM1B,QAAA,CAAS;MACtCsB,MAAA,EAAQA,MAAA,CAAOI,IAAI;MACnBC,OAAA,EAAS;MACTC,QAAA,EAAUH;IACZ;IAEA,MAAM;MAAEI,eAAe;MAAEC;IAAI,CAAE,GAAG,MAAM1B,qBAAA,CAAsB;MAC5DU,aAAA;MACAb,OAAA;MACAsB;IACF;IAEA,OAAO;MACLG,IAAA;MACAD,YAAA;MACAF,OAAA;MACAM,eAAA;MACAC;IACF;EACF,GAAG;EAEH,OAAOlB,QAAA,CAASS,GAAG,CAAC;IAClB,MAAM;MAAEK,IAAI;MAAED,YAAY;MAAEF,OAAO;MAAEM,eAAe;MAAEC;IAAI,CAAE,GAAGV,aAAA;IAE/D,MAAM;MAAEW,GAAA,EAAKC,YAAY;MAAE,GAAGC;IAAA,CAAkB,GAAGf,SAAA,IAAa,CAAC;IAEjE,MAAMa,GAAA,GAAM,MAAM5B,cAAA,CAChB;MACE4B,GAAA,EAAK;QACH9B,OAAA;QACAiC,IAAA,EAAMjC,OAAA,CAAQoB,GAAG,CAAC;QAClBK,IAAA,EAAMA,IAAA;QACNG,eAAA;QACAC,IAAA;QACA,IAAIE,YAAA,IAAgB,CAAC,CAAC;MACxB;MACA,IAAIC,gBAAA,IAAoB,CAAC,CAAC;IAC5B,GACAV,OAAA;IAGF,MAAMY,MAAA,GAAS,MAAM1B,gBAAA,CAAiB;MACpCsB;IACF;IAEAA,GAAA,CAAII,MAAM,GAAGA,MAAA,EAAQC,IAAA;IAErB,MAAMC,WAAA,GAAc,MAAMhC,gBAAA,CAAiB;MACzC0B;IACF;IAEA,OAAO;MACLZ,OAAA;MACAlB,OAAA;MACAwB,YAAA;MACAU,MAAA;MACAE,WAAA;MACAN;IACF;EACF,GAAGd,GAAA;AACL", "ignoreList": []}