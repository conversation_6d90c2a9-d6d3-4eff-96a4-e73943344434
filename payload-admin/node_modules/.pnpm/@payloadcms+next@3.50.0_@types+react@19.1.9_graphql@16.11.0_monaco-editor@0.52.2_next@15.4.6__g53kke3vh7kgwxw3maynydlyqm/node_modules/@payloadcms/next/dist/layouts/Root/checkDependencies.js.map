{"version": 3, "file": "checkDependencies.js", "names": ["checkDependencies", "payloadCheckDependencies", "customReactVersionParser", "version", "mainVersion", "preReleases", "split", "length", "date", "parts", "map", "Number", "checkedDependencies", "process", "env", "NODE_ENV", "PAYLOAD_DISABLE_DEPENDENCY_CHECKER", "dependencyGroups", "name", "dependencies", "targetVersionDependency", "dependencyVersions", "next", "required", "react", "customVersionParser"], "sources": ["../../../src/layouts/Root/checkDependencies.ts"], "sourcesContent": ["import { type CustomVersionParser, checkDependencies as payloadCheckDependencies } from 'payload'\n\nconst customReactVersionParser: CustomVersionParser = (version) => {\n  const [mainVersion, ...preReleases] = version.split('-')\n\n  if (preReleases?.length === 3) {\n    // Needs different handling, as it's in a format like 19.0.0-rc-06d0b89e-20240801 format\n    const date = preReleases[2]\n\n    const parts = mainVersion.split('.').map(Number)\n    return { parts, preReleases: [date] }\n  }\n\n  const parts = mainVersion.split('.').map(Number)\n  return { parts, preReleases }\n}\n\nlet checkedDependencies = false\n\nexport const checkDependencies = () => {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    process.env.PAYLOAD_DISABLE_DEPENDENCY_CHECKER !== 'true' &&\n    !checkedDependencies\n  ) {\n    checkedDependencies = true\n\n    // First check if there are mismatching dependency versions of next / react packages\n    void payloadCheckDependencies({\n      dependencyGroups: [\n        {\n          name: 'react',\n          dependencies: ['react', 'react-dom'],\n          targetVersionDependency: 'react',\n        },\n      ],\n      dependencyVersions: {\n        next: {\n          required: false,\n          version: '>=15.0.0',\n        },\n        react: {\n          customVersionParser: customReactVersionParser,\n          required: false,\n          version: '>=19.0.0-rc-65a56d0e-20241020',\n        },\n        'react-dom': {\n          customVersionParser: customReactVersionParser,\n          required: false,\n          version: '>=19.0.0-rc-65a56d0e-20241020',\n        },\n      },\n    })\n  }\n}\n"], "mappings": "AAAA,SAAmCA,iBAAA,IAAqBC,wBAAwB,QAAQ;AAExF,MAAMC,wBAAA,GAAiDC,OAAA;EACrD,MAAM,CAACC,WAAA,EAAa,GAAGC,WAAA,CAAY,GAAGF,OAAA,CAAQG,KAAK,CAAC;EAEpD,IAAID,WAAA,EAAaE,MAAA,KAAW,GAAG;IAC7B;IACA,MAAMC,IAAA,GAAOH,WAAW,CAAC,EAAE;IAE3B,MAAMI,KAAA,GAAQL,WAAA,CAAYE,KAAK,CAAC,KAAKI,GAAG,CAACC,MAAA;IACzC,OAAO;MAAEF,KAAA;MAAOJ,WAAA,EAAa,CAACG,IAAA;IAAM;EACtC;EAEA,MAAMC,KAAA,GAAQL,WAAA,CAAYE,KAAK,CAAC,KAAKI,GAAG,CAACC,MAAA;EACzC,OAAO;IAAEF,KAAA;IAAOJ;EAAY;AAC9B;AAEA,IAAIO,mBAAA,GAAsB;AAE1B,OAAO,MAAMZ,iBAAA,GAAoBA,CAAA;EAC/B,IACEa,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,OAAA,CAAQC,GAAG,CAACE,kCAAkC,KAAK,UACnD,CAACJ,mBAAA,EACD;IACAA,mBAAA,GAAsB;IAEtB;IACA,KAAKX,wBAAA,CAAyB;MAC5BgB,gBAAA,EAAkB,CAChB;QACEC,IAAA,EAAM;QACNC,YAAA,EAAc,CAAC,SAAS,YAAY;QACpCC,uBAAA,EAAyB;MAC3B,EACD;MACDC,kBAAA,EAAoB;QAClBC,IAAA,EAAM;UACJC,QAAA,EAAU;UACVpB,OAAA,EAAS;QACX;QACAqB,KAAA,EAAO;UACLC,mBAAA,EAAqBvB,wBAAA;UACrBqB,QAAA,EAAU;UACVpB,OAAA,EAAS;QACX;QACA,aAAa;UACXsB,mBAAA,EAAqBvB,wBAAA;UACrBqB,QAAA,EAAU;UACVpB,OAAA,EAAS;QACX;MACF;IACF;EACF;AACF", "ignoreList": []}