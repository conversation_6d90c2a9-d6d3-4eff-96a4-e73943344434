@import '~@payloadcms/ui/scss';

@layer payload-default {
  .doc-tab {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;

    &:hover {
      .pill-version-count {
        background-color: var(--theme-elevation-150);
      }
    }

    &--active {
      .pill-version-count {
        background-color: var(--theme-elevation-250);
      }

      &:hover {
        .pill-version-count {
          background-color: var(--theme-elevation-250);
        }
      }
    }

    &__label {
      display: flex;
      position: relative;
      align-items: center;
      gap: 4px;
      width: 100%;
      height: 100%;
      line-height: calc(var(--base) * 1.2);
    }
  }
}
