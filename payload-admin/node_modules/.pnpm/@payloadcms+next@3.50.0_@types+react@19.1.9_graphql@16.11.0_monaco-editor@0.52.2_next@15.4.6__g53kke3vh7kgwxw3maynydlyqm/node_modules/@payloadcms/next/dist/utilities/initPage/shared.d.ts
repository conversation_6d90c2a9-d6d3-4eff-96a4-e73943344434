import type { SanitizedConfig } from 'payload';
export declare const isAdminRoute: ({ adminRoute, route, }: {
    adminRoute: string;
    config: SanitizedConfig;
    route: string;
}) => boolean;
export declare const isPublicAdminRoute: ({ adminRoute, config, route, }: {
    adminRoute: string;
    config: SanitizedConfig;
    route: string;
}) => boolean;
export declare const getRouteWithoutAdmin: ({ adminRoute, route, }: {
    adminRoute: string;
    route: string;
}) => string;
//# sourceMappingURL=shared.d.ts.map