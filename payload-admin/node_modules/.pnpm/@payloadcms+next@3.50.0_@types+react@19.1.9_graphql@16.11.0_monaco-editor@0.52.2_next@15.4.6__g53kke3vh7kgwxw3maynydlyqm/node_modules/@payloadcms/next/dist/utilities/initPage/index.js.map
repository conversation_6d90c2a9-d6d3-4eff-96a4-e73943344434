{"version": 3, "file": "index.js", "names": ["notFound", "isEntityHidden", "qs", "initReq", "getRouteInfo", "handleAuthRedirect", "isCustomAdminView", "isPublicAdminRoute", "initPage", "config", "config<PERSON>rom<PERSON>", "importMap", "route", "routeParams", "searchParams", "useLayoutReq", "queryString", "stringify", "addQueryPrefix", "cookies", "locale", "permissions", "req", "payload", "key", "overrides", "fallback<PERSON><PERSON><PERSON>", "query", "parse", "depth", "ignoreQueryPrefix", "urlSuffix", "collections", "globals", "routes", "admin", "adminRoute", "languageOptions", "Object", "entries", "i18n", "supportedLanguages", "reduce", "acc", "language", "languageConfig", "keys", "includes", "push", "label", "translations", "general", "thisLanguage", "value", "visibleEntities", "map", "slug", "hidden", "user", "filter", "Boolean", "redirectTo", "canAccessAdmin", "collectionConfig", "collectionSlug", "docID", "globalConfig", "globalSlug", "defaultIDType", "db"], "sources": ["../../../src/utilities/initPage/index.ts"], "sourcesContent": ["import type { InitPageResult, VisibleEntities } from 'payload'\n\nimport { notFound } from 'next/navigation.js'\nimport { isEntityHidden } from 'payload'\nimport * as qs from 'qs-esm'\n\nimport type { Args } from './types.js'\n\nimport { initReq } from '../initReq.js'\nimport { getRouteInfo } from './handleAdminPage.js'\nimport { handleAuthRedirect } from './handleAuthRedirect.js'\nimport { isCustomAdminView } from './isCustomAdminView.js'\nimport { isPublicAdminRoute } from './shared.js'\n\nexport const initPage = async ({\n  config: configPromise,\n  importMap,\n  route,\n  routeParams = {},\n  searchParams,\n  useLayoutReq,\n}: Args): Promise<InitPageResult> => {\n  const queryString = `${qs.stringify(searchParams ?? {}, { addQueryPrefix: true })}`\n\n  const {\n    cookies,\n    locale,\n    permissions,\n    req,\n    req: { payload },\n  } = await initReq({\n    configPromise,\n    importMap,\n    key: useLayoutReq ? 'RootLayout' : 'initPage',\n    overrides: {\n      fallbackLocale: false,\n      req: {\n        query: qs.parse(queryString, {\n          depth: 10,\n          ignoreQueryPrefix: true,\n        }),\n        routeParams,\n      },\n      urlSuffix: `${route}${searchParams ? queryString : ''}`,\n    },\n  })\n\n  const {\n    collections,\n    globals,\n    routes: { admin: adminRoute },\n  } = payload.config\n\n  const languageOptions = Object.entries(payload.config.i18n.supportedLanguages || {}).reduce(\n    (acc, [language, languageConfig]) => {\n      if (Object.keys(payload.config.i18n.supportedLanguages).includes(language)) {\n        acc.push({\n          label: languageConfig.translations.general.thisLanguage,\n          value: language,\n        })\n      }\n\n      return acc\n    },\n    [],\n  )\n\n  const visibleEntities: VisibleEntities = {\n    collections: collections\n      .map(({ slug, admin: { hidden } }) =>\n        !isEntityHidden({ hidden, user: req.user }) ? slug : null,\n      )\n      .filter(Boolean),\n    globals: globals\n      .map(({ slug, admin: { hidden } }) =>\n        !isEntityHidden({ hidden, user: req.user }) ? slug : null,\n      )\n      .filter(Boolean),\n  }\n\n  let redirectTo = null\n\n  if (\n    !permissions.canAccessAdmin &&\n    !isPublicAdminRoute({ adminRoute, config: payload.config, route }) &&\n    !isCustomAdminView({ adminRoute, config: payload.config, route })\n  ) {\n    redirectTo = handleAuthRedirect({\n      config: payload.config,\n      route,\n      searchParams,\n      user: req.user,\n    })\n  }\n\n  const { collectionConfig, collectionSlug, docID, globalConfig, globalSlug } = getRouteInfo({\n    adminRoute,\n    config: payload.config,\n    defaultIDType: payload.db.defaultIDType,\n    payload,\n    route,\n  })\n\n  if ((collectionSlug && !collectionConfig) || (globalSlug && !globalConfig)) {\n    return notFound()\n  }\n\n  return {\n    collectionConfig,\n    cookies,\n    docID,\n    globalConfig,\n    languageOptions,\n    locale,\n    permissions,\n    redirectTo,\n    req,\n    translations: req.i18n.translations,\n    visibleEntities,\n  }\n}\n"], "mappings": "AAEA,SAASA,QAAQ,QAAQ;AACzB,SAASC,cAAc,QAAQ;AAC/B,YAAYC,EAAA,MAAQ;AAIpB,SAASC,OAAO,QAAQ;AACxB,SAASC,YAAY,QAAQ;AAC7B,SAASC,kBAAkB,QAAQ;AACnC,SAASC,iBAAiB,QAAQ;AAClC,SAASC,kBAAkB,QAAQ;AAEnC,OAAO,MAAMC,QAAA,GAAW,MAAAA,CAAO;EAC7BC,MAAA,EAAQC,aAAa;EACrBC,SAAS;EACTC,KAAK;EACLC,WAAA,GAAc,CAAC,CAAC;EAChBC,YAAY;EACZC;AAAY,CACP;EACL,MAAMC,WAAA,GAAc,GAAGd,EAAA,CAAGe,SAAS,CAACH,YAAA,IAAgB,CAAC,GAAG;IAAEI,cAAA,EAAgB;EAAK,IAAI;EAEnF,MAAM;IACJC,OAAO;IACPC,MAAM;IACNC,WAAW;IACXC,GAAG;IACHA,GAAA,EAAK;MAAEC;IAAO;EAAE,CACjB,GAAG,MAAMpB,OAAA,CAAQ;IAChBO,aAAA;IACAC,SAAA;IACAa,GAAA,EAAKT,YAAA,GAAe,eAAe;IACnCU,SAAA,EAAW;MACTC,cAAA,EAAgB;MAChBJ,GAAA,EAAK;QACHK,KAAA,EAAOzB,EAAA,CAAG0B,KAAK,CAACZ,WAAA,EAAa;UAC3Ba,KAAA,EAAO;UACPC,iBAAA,EAAmB;QACrB;QACAjB;MACF;MACAkB,SAAA,EAAW,GAAGnB,KAAA,GAAQE,YAAA,GAAeE,WAAA,GAAc;IACrD;EACF;EAEA,MAAM;IACJgB,WAAW;IACXC,OAAO;IACPC,MAAA,EAAQ;MAAEC,KAAA,EAAOC;IAAU;EAAE,CAC9B,GAAGb,OAAA,CAAQd,MAAM;EAElB,MAAM4B,eAAA,GAAkBC,MAAA,CAAOC,OAAO,CAAChB,OAAA,CAAQd,MAAM,CAAC+B,IAAI,CAACC,kBAAkB,IAAI,CAAC,GAAGC,MAAM,CACzF,CAACC,GAAA,EAAK,CAACC,QAAA,EAAUC,cAAA,CAAe;IAC9B,IAAIP,MAAA,CAAOQ,IAAI,CAACvB,OAAA,CAAQd,MAAM,CAAC+B,IAAI,CAACC,kBAAkB,EAAEM,QAAQ,CAACH,QAAA,GAAW;MAC1ED,GAAA,CAAIK,IAAI,CAAC;QACPC,KAAA,EAAOJ,cAAA,CAAeK,YAAY,CAACC,OAAO,CAACC,YAAY;QACvDC,KAAA,EAAOT;MACT;IACF;IAEA,OAAOD,GAAA;EACT,GACA,EAAE;EAGJ,MAAMW,eAAA,GAAmC;IACvCtB,WAAA,EAAaA,WAAA,CACVuB,GAAG,CAAC,CAAC;MAAEC,IAAI;MAAErB,KAAA,EAAO;QAAEsB;MAAM;IAAE,CAAE,KAC/B,CAACxD,cAAA,CAAe;MAAEwD,MAAA;MAAQC,IAAA,EAAMpC,GAAA,CAAIoC;IAAK,KAAKF,IAAA,GAAO,MAEtDG,MAAM,CAACC,OAAA;IACV3B,OAAA,EAASA,OAAA,CACNsB,GAAG,CAAC,CAAC;MAAEC,IAAI;MAAErB,KAAA,EAAO;QAAEsB;MAAM;IAAE,CAAE,KAC/B,CAACxD,cAAA,CAAe;MAAEwD,MAAA;MAAQC,IAAA,EAAMpC,GAAA,CAAIoC;IAAK,KAAKF,IAAA,GAAO,MAEtDG,MAAM,CAACC,OAAA;EACZ;EAEA,IAAIC,UAAA,GAAa;EAEjB,IACE,CAACxC,WAAA,CAAYyC,cAAc,IAC3B,CAACvD,kBAAA,CAAmB;IAAE6B,UAAA;IAAY3B,MAAA,EAAQc,OAAA,CAAQd,MAAM;IAAEG;EAAM,MAChE,CAACN,iBAAA,CAAkB;IAAE8B,UAAA;IAAY3B,MAAA,EAAQc,OAAA,CAAQd,MAAM;IAAEG;EAAM,IAC/D;IACAiD,UAAA,GAAaxD,kBAAA,CAAmB;MAC9BI,MAAA,EAAQc,OAAA,CAAQd,MAAM;MACtBG,KAAA;MACAE,YAAA;MACA4C,IAAA,EAAMpC,GAAA,CAAIoC;IACZ;EACF;EAEA,MAAM;IAAEK,gBAAgB;IAAEC,cAAc;IAAEC,KAAK;IAAEC,YAAY;IAAEC;EAAU,CAAE,GAAG/D,YAAA,CAAa;IACzFgC,UAAA;IACA3B,MAAA,EAAQc,OAAA,CAAQd,MAAM;IACtB2D,aAAA,EAAe7C,OAAA,CAAQ8C,EAAE,CAACD,aAAa;IACvC7C,OAAA;IACAX;EACF;EAEA,IAAIoD,cAAC,IAAkB,CAACD,gBAAA,IAAsBI,UAAA,IAAc,CAACD,YAAA,EAAe;IAC1E,OAAOlE,QAAA;EACT;EAEA,OAAO;IACL+D,gBAAA;IACA5C,OAAA;IACA8C,KAAA;IACAC,YAAA;IACA7B,eAAA;IACAjB,MAAA;IACAC,WAAA;IACAwC,UAAA;IACAvC,GAAA;IACA4B,YAAA,EAAc5B,GAAA,CAAIkB,IAAI,CAACU,YAAY;IACnCI;EACF;AACF", "ignoreList": []}