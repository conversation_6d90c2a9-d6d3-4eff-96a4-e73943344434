{"version": 3, "file": "index.js", "names": ["c", "_c", "useNav", "React", "NavWrapper", "props", "$", "baseClass", "children", "hydrated", "navOpen", "navRef", "shouldAnimate", "t0", "t1", "t2", "t3", "filter", "Boolean", "t4", "join", "t5", "undefined", "t6", "t7", "_jsx", "className", "inert", "ref"], "sources": ["../../../../src/elements/Nav/NavWrapper/index.tsx"], "sourcesContent": ["'use client'\nimport { useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\nexport const NavWrapper: React.FC<{\n  baseClass?: string\n  children: React.ReactNode\n}> = (props) => {\n  const { baseClass, children } = props\n\n  const { hydrated, navOpen, navRef, shouldAnimate } = useNav()\n\n  return (\n    <aside\n      className={[\n        baseClass,\n        navOpen && `${baseClass}--nav-open`,\n        shouldAnimate && `${baseClass}--nav-animate`,\n        hydrated && `${baseClass}--nav-hydrated`,\n      ]\n        .filter(Boolean)\n        .join(' ')}\n      inert={!navOpen ? true : undefined}\n    >\n      <div className={`${baseClass}__scroll`} ref={navRef}>\n        {children}\n      </div>\n    </aside>\n  )\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,MAAM,QAAQ;AACvB,OAAOC,KAAA,MAAW;AAIlB,OAAO,MAAMC,UAAA,GAGRC,KAAA;EAAA,MAAAC,CAAA,GAAAL,EAAA;EACH;IAAAM,SAAA;IAAAC;EAAA,IAAgCH,KAAA;EAEhC;IAAAI,QAAA;IAAAC,OAAA;IAAAC,MAAA;IAAAC;EAAA,IAAqDV,MAAA;EAM/C,MAAAW,EAAA,GAAAH,OAAA,IAAW,GAAGH,SAAA,YAAqB;EACnC,MAAAO,EAAA,GAAAF,aAAA,IAAiB,GAAGL,SAAA,eAAwB;EAC5C,MAAAQ,EAAA,GAAAN,QAAA,IAAY,GAAGF,SAAA,gBAAyB;EAAA,IAAAS,EAAA;EAAA,IAAAV,CAAA,QAAAC,SAAA,IAAAD,CAAA,QAAAO,EAAA,IAAAP,CAAA,QAAAQ,EAAA,IAAAR,CAAA,QAAAS,EAAA;IAJ/BC,EAAA,IACTT,SAAA,EACAM,EAAmC,EACnCC,EAA4C,EAC5CC,EAAwC,EAAAE,MAAA,CAAAC,OAEhC;IAAAZ,CAAA,MAAAC,SAAA;IAAAD,CAAA,MAAAO,EAAA;IAAAP,CAAA,MAAAQ,EAAA;IAAAR,CAAA,MAAAS,EAAA;IAAAT,CAAA,MAAAU,EAAA;EAAA;IAAAA,EAAA,GAAAV,CAAA;EAAA;EANC,MAAAa,EAAA,GAAAH,EAMD,CAAAI,IAAA,CACF;EACD,MAAAC,EAAA,IAACX,OAAA,UAAAY,SAAiB;EAET,MAAAC,EAAA,MAAGhB,SAAA,UAAmB;EAAA,IAAAiB,EAAA;EAAA,IAAAlB,CAAA,QAAAE,QAAA,IAAAF,CAAA,QAAAK,MAAA,IAAAL,CAAA,QAAAa,EAAA,IAAAb,CAAA,QAAAe,EAAA,IAAAf,CAAA,QAAAiB,EAAA;IAXxCC,EAAA,GAAAC,IAAA,CAAC;MAAAC,SAAA,EACYP,EAOH;MAAAQ,KAAA,EACDN,EAAkB;MAAAb,QAAA,EAEzBiB,IAAA,CAAC;QAAAC,SAAA,EAAeH,EAAsB;QAAAK,GAAA,EAAOjB,MAAA;QAAAH;MAAA,C;;;;;;;;;;;SAX/CgB,E;CAgBJ", "ignoreList": []}