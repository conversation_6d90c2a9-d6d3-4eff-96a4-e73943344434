{"version": 3, "file": "index.js", "names": ["c", "_c", "<PERSON><PERSON>", "useNav", "React", "NavHamburger", "$", "navOpen", "t0", "_jsx", "closeIcon", "isActive"], "sources": ["../../../../src/templates/Default/NavHamburger/index.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON><PERSON>, useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const NavHamburger: React.FC = () => {\n  const { navOpen } = useNav()\n  return <Hamburger closeIcon=\"collapse\" isActive={navOpen} />\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,SAAS,EAAEC,MAAM,QAAQ;AAClC,OAAOC,KAAA,MAAW;AAElB,OAAO,MAAMC,YAAA,GAAyBA,CAAA;EAAA,MAAAC,CAAA,GAAAL,EAAA;EACpC;IAAAM;EAAA,IAAoBJ,MAAA;EAAA,IAAAK,EAAA;EAAA,IAAAF,CAAA,QAAAC,OAAA;IACbC,EAAA,GAAAC,IAAA,CAAAP,SAAA;MAAAQ,SAAA,EAAqB;MAAAC,QAAA,EAAqBJ;IAAA,C;;;;;;SAA1CC,E;CACT", "ignoreList": []}