{"version": 3, "file": "index.client.js", "names": ["c", "_c", "getTranslation", "BrowseByFolderButton", "Link", "NavGroup", "useConfig", "useTranslation", "EntityType", "usePathname", "formatAdminURL", "React", "Fragment", "baseClass", "DefaultNavClient", "t0", "$", "groups", "navPreferences", "pathname", "config", "t1", "admin", "t2", "folders", "routes", "t3", "t4", "browseByFolder", "foldersRoute", "adminRoute", "i18n", "t5", "t6", "t7", "folderURL", "path", "viewingRootFolderView", "startsWith", "_jsxs", "_jsx", "active", "t9", "t10", "key", "entities", "label", "isOpen", "open", "children", "map", "t11", "i", "slug", "type", "label_0", "href", "id", "collection", "global", "isActive", "undefined", "includes", "length", "Label", "_Fragment", "className", "prefetch", "t8"], "sources": ["../../../src/elements/Nav/index.client.tsx"], "sourcesContent": ["'use client'\n\nimport type { groupNavItems } from '@payloadcms/ui/shared'\nimport type { NavPreferences } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { BrowseByFolderButton, Link, NavGroup, useConfig, useTranslation } from '@payloadcms/ui'\nimport { EntityType } from '@payloadcms/ui/shared'\nimport { usePathname } from 'next/navigation.js'\nimport { formatAdminURL } from 'payload/shared'\nimport React, { Fragment } from 'react'\n\nconst baseClass = 'nav'\n\nexport const DefaultNavClient: React.FC<{\n  groups: ReturnType<typeof groupNavItems>\n  navPreferences: NavPreferences\n}> = ({ groups, navPreferences }) => {\n  const pathname = usePathname()\n\n  const {\n    config: {\n      admin: {\n        routes: { browseByFolder: foldersRoute },\n      },\n      folders,\n      routes: { admin: adminRoute },\n    },\n  } = useConfig()\n\n  const { i18n } = useTranslation()\n\n  const folderURL = formatAdminURL({\n    adminRoute,\n    path: foldersRoute,\n  })\n\n  const viewingRootFolderView = pathname.startsWith(folderURL)\n\n  return (\n    <Fragment>\n      {folders && folders.browseByFolder && <BrowseByFolderButton active={viewingRootFolderView} />}\n      {groups.map(({ entities, label }, key) => {\n        return (\n          <NavGroup isOpen={navPreferences?.groups?.[label]?.open} key={key} label={label}>\n            {entities.map(({ slug, type, label }, i) => {\n              let href: string\n              let id: string\n\n              if (type === EntityType.collection) {\n                href = formatAdminURL({ adminRoute, path: `/collections/${slug}` })\n                id = `nav-${slug}`\n              }\n\n              if (type === EntityType.global) {\n                href = formatAdminURL({ adminRoute, path: `/globals/${slug}` })\n                id = `nav-global-${slug}`\n              }\n\n              const isActive =\n                pathname.startsWith(href) && ['/', undefined].includes(pathname[href.length])\n\n              const Label = (\n                <>\n                  {isActive && <div className={`${baseClass}__link-indicator`} />}\n                  <span className={`${baseClass}__link-label`}>{getTranslation(label, i18n)}</span>\n                </>\n              )\n\n              // If the URL matches the link exactly\n              if (pathname === href) {\n                return (\n                  <div className={`${baseClass}__link`} id={id} key={i}>\n                    {Label}\n                  </div>\n                )\n              }\n\n              return (\n                <Link className={`${baseClass}__link`} href={href} id={id} key={i} prefetch={false}>\n                  {Label}\n                </Link>\n              )\n            })}\n          </NavGroup>\n        )\n      })}\n    </Fragment>\n  )\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAKA,SAASC,cAAc,QAAQ;AAC/B,SAASC,oBAAoB,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,cAAc,QAAQ;AAChF,SAASC,UAAU,QAAQ;AAC3B,SAASC,WAAW,QAAQ;AAC5B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,IAASC,QAAQ,QAAQ;AAEhC,MAAMC,SAAA,GAAY;AAElB,OAAO,MAAMC,gBAAA,GAGRC,EAAA;EAAA,MAAAC,CAAA,GAAAf,EAAA;EAAC;IAAAgB,MAAA;IAAAC;EAAA,IAAAH,EAA0B;EAC9B,MAAAI,QAAA,GAAiBV,WAAA;EAEjB;IAAAW,MAAA,EAAAC;EAAA,IAQIf,SAAA;EAPM;IAAAgB,KAAA,EAAAC,EAAA;IAAAC,OAAA;IAAAC,MAAA,EAAAC;EAAA,IAAAL,EAMP;EALQ;IAAAI,MAAA,EAAAE;EAAA,IAAAJ,EAEN;EADS;IAAAK,cAAA,EAAAC;EAAA,IAAAF,EAAgC;EAGlC;IAAAL,KAAA,EAAAQ;EAAA,IAAAJ,EAAqB;EAIjC;IAAAK;EAAA,IAAiBxB,cAAA;EAAA,IAAAyB,EAAA;EAAA,IAAAC,EAAA;EAAA,IAAAC,EAAA;EAAA,IAAAlB,CAAA,QAAAc,UAAA,IAAAd,CAAA,QAAAQ,OAAA,IAAAR,CAAA,QAAAa,YAAA,IAAAb,CAAA,QAAAG,QAAA;IAEjB,MAAAgB,SAAA,GAAkBzB,cAAA;MAAAoB,UAAA;MAAAM,IAAA,EAEVP;IAAA,CACR;IAEA,MAAAQ,qBAAA,GAA8BlB,QAAA,CAAAmB,UAAA,CAAoBH,SAAA;IAGhDI,EAAA,CAAAA,CAAA,CAAAA,KAAA;IAAC3B,EAAA,CAAAA,CAAA,CAAAA,QAAA;IACEoB,EAAA,GAAAR,OAAA,IAAWA,OAAA,CAAAI,cAAsB,IAAIY,IAAA,CAAArC,oBAAA;MAAAsC,MAAA,EAA8BJ;IAAA,C;;;;;;;;;;;;;;;;;MACxDK,EAAA,GAAAA,CAAAC,GAAA,EAAAC,GAAA;QAAC;UAAAC,QAAA;UAAAC;QAAA,IAAAH,GAAmB;QAAA,OAE5BH,IAAA,CAAAnC,QAAA;UAAA0C,MAAA,EAAkB7B,cAAA,EAAAD,MAAA,GAAyB6B,KAAA,GAAAE,IAAA;UAAAF,KAAA;UAAAG,QAAA,EACxCJ,QAAA,CAAAK,GAAA,EAAAC,GAAA,EAAAC,CAAA;YAAc;cAAAC,IAAA;cAAAC,IAAA;cAAAR,KAAA,EAAAS;YAAA,IAAAJ,GAAqB;YAC9BK,GAAA,CAAAA,IAAA;YACAC,GAAA,CAAAA,EAAA;YAAA,IAEAH,IAAA,KAAA9C,UAAA,CAAAkD,UAA8B;cAChCF,IAAA,CAAAA,CAAA,CAAOA;gBAAAA;gBAAAA,KAAA,CAAmCA,gBAAgBH,IAAA;cAAM,CAAC;cACjEI,EAAA,CAAAA,CAAA,CAAKA,OAAOJ,IAAA,EAAM;YAAlB;YAAA,IAGEC,IAAA,KAAA9C,UAAA,CAAAmD,MAA0B;cAC5BH,IAAA,CAAAA,CAAA,CAAOA;gBAAAA;gBAAAA,KAAA,CAAmCA,YAAYH,IAAA;cAAM,CAAC;cAC7DI,EAAA,CAAAA,CAAA,CAAKA,cAAcJ,IAAA,EAAM;YAAzB;YAGF,MAAAO,QAAA,GACEzC,QAAA,CAAAmB,UAAA,CAAoBkB,IAAA,KAAS,CAAC,KAAAK,SAAA,EAAAC,QAAA,CAAyB3C,QAAQ,CAACqC,IAAA,CAAAO,MAAA,CAAY;YAE9E,MAAAC,KAAA,GACEzB,KAAA,CAAA0B,SAAA;cAAAhB,QAAA,GACGW,QAAA,IAAYpB,IAAA,CAAC;gBAAA0B,SAAA,EAAe,GAAArD,SAAA;cAA8B,C,GAC3D2B,IAAA,CAAC;gBAAA0B,SAAA,EAAgB,GAAArD,SAAA,cAA0B;gBAAAoC,QAAA,EAAG/C,cAAA,CAAe4C,OAAA,EAAOf,IAAA;cAAA,C;;gBAKpEZ,QAAA,KAAaqC,IAAA;cAAA,OAEbhB,IAAA,CAAC;gBAAA0B,SAAA,EAAe,GAAArD,SAAA,QAAoB;gBAAA4C,EAAA;gBAAAR,QAAA,EACjCe;cAAA,GADgDZ,CAAA;YAAA;YAAA,OAOrDZ,IAAA,CAAApC,IAAA;cAAA8D,SAAA,EAAiB,GAAArD,SAAA,QAAoB;cAAA2C,IAAA;cAAAC,EAAA;cAAAU,QAAA;cAAAlB,QAAA,EAClCe;YAAA,GAD6DZ,CAAA;UAAA,CAIpE;QAAA,GAvC4DR,GAAA;MAAA;MA0ClE5B,CAAA,OAAAc,UAAA;MAAAd,CAAA,OAAAe,IAAA;MAAAf,CAAA,OAAAE,cAAA,EAAAD,MAAA;MAAAD,CAAA,OAAAG,QAAA;MAAAH,CAAA,OAAA0B,EAAA;IAAA;MAAAA,EAAA,GAAA1B,CAAA;IAAA;IA9CFoD,EAAA,GAAA7B,EAAA,CAAC3B,EAAA;MAAAqC,QAAA,GACEjB,E,EACAf,MAAA,CAAAiC,GAAA,CAAWR,EA4CZ;IAAA,C;;;;;;;;;;;;;SA9CF0B,E;CAiDJ", "ignoreList": []}