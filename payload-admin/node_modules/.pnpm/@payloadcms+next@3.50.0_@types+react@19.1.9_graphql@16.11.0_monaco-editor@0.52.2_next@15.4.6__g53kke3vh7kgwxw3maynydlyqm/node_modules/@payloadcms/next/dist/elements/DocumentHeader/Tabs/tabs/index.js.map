{"version": 3, "file": "index.js", "names": ["VersionsPill", "documentViewKeys", "getTabs", "collectionConfig", "globalConfig", "customViews", "admin", "components", "views", "edit", "tab", "href", "label", "t", "order", "viewPath", "condition", "permissions", "Boolean", "versions", "collections", "slug", "readVersions", "globals", "Pill_Component", "hideAPIURL", "concat", "Object", "entries", "reduce", "acc", "key", "value", "includes", "push", "path", "sort", "a", "b", "undefined"], "sources": ["../../../../../src/elements/DocumentHeader/Tabs/tabs/index.tsx"], "sourcesContent": ["import type { DocumentTabConfig, SanitizedCollectionConfig, SanitizedGlobalConfig } from 'payload'\n\nimport { VersionsPill } from './VersionsPill/index.js'\n\nexport const documentViewKeys = ['api', 'default', 'livePreview', 'versions']\n\nexport type DocumentViewKey = (typeof documentViewKeys)[number]\n\nexport const getTabs = ({\n  collectionConfig,\n  globalConfig,\n}: {\n  collectionConfig?: SanitizedCollectionConfig\n  globalConfig?: SanitizedGlobalConfig\n}): { tab: DocumentTabConfig; viewPath: string }[] => {\n  const customViews =\n    collectionConfig?.admin?.components?.views?.edit ||\n    globalConfig?.admin?.components?.views?.edit ||\n    {}\n\n  return [\n    {\n      tab: {\n        href: '',\n        label: ({ t }) => t('general:edit'),\n        order: 100,\n        ...(customViews?.['default']?.tab || {}),\n      },\n      viewPath: '/',\n    },\n    {\n      tab: {\n        condition: ({ collectionConfig, globalConfig, permissions }) =>\n          Boolean(\n            (collectionConfig?.versions &&\n              permissions?.collections?.[collectionConfig?.slug]?.readVersions) ||\n              (globalConfig?.versions && permissions?.globals?.[globalConfig?.slug]?.readVersions),\n          ),\n        href: '/versions',\n        label: ({ t }) => t('version:versions'),\n        order: 300,\n        Pill_Component: VersionsPill,\n        ...(customViews?.['versions']?.tab || {}),\n      },\n      viewPath: '/versions',\n    },\n    {\n      tab: {\n        condition: ({ collectionConfig, globalConfig }) =>\n          (collectionConfig && !collectionConfig?.admin?.hideAPIURL) ||\n          (globalConfig && !globalConfig?.admin?.hideAPIURL),\n        href: '/api',\n        label: 'API',\n        order: 400,\n        ...(customViews?.['api']?.tab || {}),\n      },\n      viewPath: '/api',\n    },\n  ]\n    .concat(\n      Object.entries(customViews).reduce((acc, [key, value]) => {\n        if (documentViewKeys.includes(key)) {\n          return acc\n        }\n\n        if (value?.tab) {\n          acc.push({\n            tab: value.tab,\n            viewPath: 'path' in value ? value.path : '',\n          })\n        }\n\n        return acc\n      }, []),\n    )\n    ?.sort(({ tab: a }, { tab: b }) => {\n      if (a.order === undefined && b.order === undefined) {\n        return 0\n      } else if (a.order === undefined) {\n        return 1\n      } else if (b.order === undefined) {\n        return -1\n      }\n\n      return a.order - b.order\n    })\n}\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ;AAE7B,OAAO,MAAMC,gBAAA,GAAmB,CAAC,OAAO,WAAW,eAAe,WAAW;AAI7E,OAAO,MAAMC,OAAA,GAAUA,CAAC;EACtBC,gBAAgB;EAChBC;AAAY,CAIb;EACC,MAAMC,WAAA,GACJF,gBAAA,EAAkBG,KAAA,EAAOC,UAAA,EAAYC,KAAA,EAAOC,IAAA,IAC5CL,YAAA,EAAcE,KAAA,EAAOC,UAAA,EAAYC,KAAA,EAAOC,IAAA,IACxC,CAAC;EAEH,OAAO,CACL;IACEC,GAAA,EAAK;MACHC,IAAA,EAAM;MACNC,KAAA,EAAOA,CAAC;QAAEC;MAAC,CAAE,KAAKA,CAAA,CAAE;MACpBC,KAAA,EAAO;MACP,IAAIT,WAAA,GAAc,UAAU,EAAEK,GAAA,IAAO,CAAC,CAAC;IACzC;IACAK,QAAA,EAAU;EACZ,GACA;IACEL,GAAA,EAAK;MACHM,SAAA,EAAWA,CAAC;QAAEb,gBAAgB;QAAEC,YAAY;QAAEa;MAAW,CAAE,KACzDC,OAAA,CACEf,gBAAC,EAAkBgB,QAAA,IACjBF,WAAA,EAAaG,WAAA,GAAcjB,gBAAA,EAAkBkB,IAAA,CAAK,EAAEC,YAAA,IACnDlB,YAAA,EAAce,QAAA,IAAYF,WAAA,EAAaM,OAAA,GAAUnB,YAAA,EAAciB,IAAA,CAAK,EAAEC,YAAA;MAE7EX,IAAA,EAAM;MACNC,KAAA,EAAOA,CAAC;QAAEC;MAAC,CAAE,KAAKA,CAAA,CAAE;MACpBC,KAAA,EAAO;MACPU,cAAA,EAAgBxB,YAAA;MAChB,IAAIK,WAAA,GAAc,WAAW,EAAEK,GAAA,IAAO,CAAC,CAAC;IAC1C;IACAK,QAAA,EAAU;EACZ,GACA;IACEL,GAAA,EAAK;MACHM,SAAA,EAAWA,CAAC;QAAEb,gBAAgB;QAAEC;MAAY,CAAE,KAC5CD,gBAAC,IAAoB,CAACA,gBAAA,EAAkBG,KAAA,EAAOmB,UAAA,IAC9CrB,YAAA,IAAgB,CAACA,YAAA,EAAcE,KAAA,EAAOmB,UAAA;MACzCd,IAAA,EAAM;MACNC,KAAA,EAAO;MACPE,KAAA,EAAO;MACP,IAAIT,WAAA,GAAc,MAAM,EAAEK,GAAA,IAAO,CAAC,CAAC;IACrC;IACAK,QAAA,EAAU;EACZ,EACD,CACEW,MAAM,CACLC,MAAA,CAAOC,OAAO,CAACvB,WAAA,EAAawB,MAAM,CAAC,CAACC,GAAA,EAAK,CAACC,GAAA,EAAKC,KAAA,CAAM;IACnD,IAAI/B,gBAAA,CAAiBgC,QAAQ,CAACF,GAAA,GAAM;MAClC,OAAOD,GAAA;IACT;IAEA,IAAIE,KAAA,EAAOtB,GAAA,EAAK;MACdoB,GAAA,CAAII,IAAI,CAAC;QACPxB,GAAA,EAAKsB,KAAA,CAAMtB,GAAG;QACdK,QAAA,EAAU,UAAUiB,KAAA,GAAQA,KAAA,CAAMG,IAAI,GAAG;MAC3C;IACF;IAEA,OAAOL,GAAA;EACT,GAAG,EAAE,IAELM,IAAA,CAAK,CAAC;IAAE1B,GAAA,EAAK2B;EAAC,CAAE,EAAE;IAAE3B,GAAA,EAAK4B;EAAC,CAAE;IAC5B,IAAID,CAAA,CAAEvB,KAAK,KAAKyB,SAAA,IAAaD,CAAA,CAAExB,KAAK,KAAKyB,SAAA,EAAW;MAClD,OAAO;IACT,OAAO,IAAIF,CAAA,CAAEvB,KAAK,KAAKyB,SAAA,EAAW;MAChC,OAAO;IACT,OAAO,IAAID,CAAA,CAAExB,KAAK,KAAKyB,SAAA,EAAW;MAChC,OAAO,CAAC;IACV;IAEA,OAAOF,CAAA,CAAEvB,KAAK,GAAGwB,CAAA,CAAExB,KAAK;EAC1B;AACJ", "ignoreList": []}