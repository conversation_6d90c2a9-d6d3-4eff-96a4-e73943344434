{"version": 3, "file": "handleAdminPage.js", "names": ["getRouteWithoutAdmin", "isAdminRoute", "getRouteInfo", "adminRoute", "config", "defaultIDType", "payload", "route", "routeWithoutAdmin", "routeSegments", "split", "filter", "Boolean", "entityType", "entitySlug", "segment3", "segment4", "collectionSlug", "undefined", "globalSlug", "collectionConfig", "globalConfig", "idType", "collections", "globals", "find", "global", "slug", "customIDType", "docID", "Number"], "sources": ["../../../src/utilities/initPage/handleAdminPage.ts"], "sourcesContent": ["import type {\n  Payload,\n  SanitizedCollectionConfig,\n  SanitizedConfig,\n  SanitizedGlobalConfig,\n} from 'payload'\n\nimport { getRouteWithoutAdmin, isAdminRoute } from './shared.js'\n\ntype Args = {\n  adminRoute: string\n  config: SanitizedConfig\n  defaultIDType: Payload['db']['defaultIDType']\n  payload?: Payload\n  route: string\n}\n\ntype RouteInfo = {\n  collectionConfig?: SanitizedCollectionConfig\n  collectionSlug?: string\n  docID?: number | string\n  globalConfig?: SanitizedGlobalConfig\n  globalSlug?: string\n}\n\nexport function getRouteInfo({\n  adminRoute,\n  config,\n  defaultIDType,\n  payload,\n  route,\n}: Args): RouteInfo {\n  if (isAdminRoute({ adminRoute, config, route })) {\n    const routeWithoutAdmin = getRouteWithoutAdmin({ adminRoute, route })\n    const routeSegments = routeWithoutAdmin.split('/').filter(Boolean)\n    const [entityType, entitySlug, segment3, segment4] = routeSegments\n    const collectionSlug = entityType === 'collections' ? entitySlug : undefined\n    const globalSlug = entityType === 'globals' ? entitySlug : undefined\n\n    let collectionConfig: SanitizedCollectionConfig | undefined\n    let globalConfig: SanitizedGlobalConfig | undefined\n    let idType = defaultIDType\n\n    if (collectionSlug) {\n      collectionConfig = payload.collections?.[collectionSlug]?.config\n    }\n\n    if (globalSlug) {\n      globalConfig = config.globals.find((global) => global.slug === globalSlug)\n    }\n\n    // If the collection is using a custom ID, we need to determine its type\n    if (collectionConfig && payload) {\n      if (payload.collections?.[collectionSlug]?.customIDType) {\n        idType = payload.collections?.[collectionSlug].customIDType\n      }\n    }\n\n    let docID: number | string | undefined\n\n    if (collectionSlug) {\n      if (segment3 === 'trash' && segment4) {\n        // /collections/:slug/trash/:id\n        docID = idType === 'number' ? Number(segment4) : segment4\n      } else if (segment3 && segment3 !== 'create') {\n        // /collections/:slug/:id\n        docID = idType === 'number' ? Number(segment3) : segment3\n      }\n    }\n\n    return {\n      collectionConfig,\n      collectionSlug,\n      docID,\n      globalConfig,\n      globalSlug,\n    }\n  }\n\n  return {}\n}\n"], "mappings": "AAOA,SAASA,oBAAoB,EAAEC,YAAY,QAAQ;AAkBnD,OAAO,SAASC,aAAa;EAC3BC,UAAU;EACVC,MAAM;EACNC,aAAa;EACbC,OAAO;EACPC;AAAK,CACA;EACL,IAAIN,YAAA,CAAa;IAAEE,UAAA;IAAYC,MAAA;IAAQG;EAAM,IAAI;IAC/C,MAAMC,iBAAA,GAAoBR,oBAAA,CAAqB;MAAEG,UAAA;MAAYI;IAAM;IACnE,MAAME,aAAA,GAAgBD,iBAAA,CAAkBE,KAAK,CAAC,KAAKC,MAAM,CAACC,OAAA;IAC1D,MAAM,CAACC,UAAA,EAAYC,UAAA,EAAYC,QAAA,EAAUC,QAAA,CAAS,GAAGP,aAAA;IACrD,MAAMQ,cAAA,GAAiBJ,UAAA,KAAe,gBAAgBC,UAAA,GAAaI,SAAA;IACnE,MAAMC,UAAA,GAAaN,UAAA,KAAe,YAAYC,UAAA,GAAaI,SAAA;IAE3D,IAAIE,gBAAA;IACJ,IAAIC,YAAA;IACJ,IAAIC,MAAA,GAASjB,aAAA;IAEb,IAAIY,cAAA,EAAgB;MAClBG,gBAAA,GAAmBd,OAAA,CAAQiB,WAAW,GAAGN,cAAA,CAAe,EAAEb,MAAA;IAC5D;IAEA,IAAIe,UAAA,EAAY;MACdE,YAAA,GAAejB,MAAA,CAAOoB,OAAO,CAACC,IAAI,CAAEC,MAAA,IAAWA,MAAA,CAAOC,IAAI,KAAKR,UAAA;IACjE;IAEA;IACA,IAAIC,gBAAA,IAAoBd,OAAA,EAAS;MAC/B,IAAIA,OAAA,CAAQiB,WAAW,GAAGN,cAAA,CAAe,EAAEW,YAAA,EAAc;QACvDN,MAAA,GAAShB,OAAA,CAAQiB,WAAW,GAAGN,cAAA,CAAe,CAACW,YAAA;MACjD;IACF;IAEA,IAAIC,KAAA;IAEJ,IAAIZ,cAAA,EAAgB;MAClB,IAAIF,QAAA,KAAa,WAAWC,QAAA,EAAU;QACpC;QACAa,KAAA,GAAQP,MAAA,KAAW,WAAWQ,MAAA,CAAOd,QAAA,IAAYA,QAAA;MACnD,OAAO,IAAID,QAAA,IAAYA,QAAA,KAAa,UAAU;QAC5C;QACAc,KAAA,GAAQP,MAAA,KAAW,WAAWQ,MAAA,CAAOf,QAAA,IAAYA,QAAA;MACnD;IACF;IAEA,OAAO;MACLK,gBAAA;MACAH,cAAA;MACAY,KAAA;MACAR,YAAA;MACAF;IACF;EACF;EAEA,OAAO,CAAC;AACV", "ignoreList": []}