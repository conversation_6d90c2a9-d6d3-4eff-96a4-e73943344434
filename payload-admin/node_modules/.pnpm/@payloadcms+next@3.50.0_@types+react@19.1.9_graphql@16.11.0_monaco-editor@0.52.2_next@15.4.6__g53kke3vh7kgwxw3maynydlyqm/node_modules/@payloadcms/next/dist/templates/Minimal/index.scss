@import '~@payloadcms/ui/scss';

@layer payload-default {
  .template-minimal {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    padding: base(3) $baseline;
    margin-left: auto;
    margin-right: auto;
    min-height: 100%;
    background-color: var(--theme-bg-color);
    color: var(--theme-text);

    &--width-normal {
      .template-minimal__wrap {
        max-width: base(24);
        width: 100%;
      }
    }

    &--width-wide {
      .template-minimal__wrap {
        max-width: base(48);
        width: 100%;
      }
    }
  }
}
