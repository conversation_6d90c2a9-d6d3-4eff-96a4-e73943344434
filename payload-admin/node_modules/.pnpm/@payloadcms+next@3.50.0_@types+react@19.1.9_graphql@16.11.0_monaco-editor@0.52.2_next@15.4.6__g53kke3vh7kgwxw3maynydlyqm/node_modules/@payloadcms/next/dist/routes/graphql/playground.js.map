{"version": 3, "file": "playground.js", "names": ["renderPlaygroundPage", "createPayloadRequest", "GET", "config", "request", "req", "payload", "graphQL", "disable", "disablePlaygroundInProduction", "process", "env", "NODE_ENV", "Response", "endpoint", "routes", "api", "settings", "headers", "status"], "sources": ["../../../src/routes/graphql/playground.ts"], "sourcesContent": ["import { renderPlaygroundPage } from 'graphql-playground-html'\nimport { createPayloadRequest, type SanitizedConfig } from 'payload'\n\nexport const GET = (config: Promise<SanitizedConfig>) => async (request: Request) => {\n  const req = await createPayloadRequest({\n    config,\n    request,\n  })\n\n  if (\n    (!req.payload.config.graphQL.disable &&\n      !req.payload.config.graphQL.disablePlaygroundInProduction &&\n      process.env.NODE_ENV === 'production') ||\n    process.env.NODE_ENV !== 'production'\n  ) {\n    return new Response(\n      renderPlaygroundPage({\n        endpoint: `${req.payload.config.routes.api}${req.payload.config.routes.graphQL}`,\n        settings: {\n          'request.credentials': 'include',\n        },\n      }),\n      {\n        headers: {\n          'Content-Type': 'text/html',\n        },\n        status: 200,\n      },\n    )\n  } else {\n    return new Response('Route Not Found', { status: 404 })\n  }\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ;AACrC,SAASC,oBAAoB,QAA8B;AAE3D,OAAO,MAAMC,GAAA,GAAOC,MAAA,IAAqC,MAAOC,OAAA;EAC9D,MAAMC,GAAA,GAAM,MAAMJ,oBAAA,CAAqB;IACrCE,MAAA;IACAC;EACF;EAEA,IACE,CAAEC,GAAA,CAAIC,OAAO,CAACH,MAAM,CAACI,OAAO,CAACC,OAAO,IAClC,CAACH,GAAA,CAAIC,OAAO,CAACH,MAAM,CAACI,OAAO,CAACE,6BAA6B,IACzDC,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,gBAC3BF,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cACzB;IACA,OAAO,IAAIC,QAAA,CACTb,oBAAA,CAAqB;MACnBc,QAAA,EAAU,GAAGT,GAAA,CAAIC,OAAO,CAACH,MAAM,CAACY,MAAM,CAACC,GAAG,GAAGX,GAAA,CAAIC,OAAO,CAACH,MAAM,CAACY,MAAM,CAACR,OAAO,EAAE;MAChFU,QAAA,EAAU;QACR,uBAAuB;MACzB;IACF,IACA;MACEC,OAAA,EAAS;QACP,gBAAgB;MAClB;MACAC,MAAA,EAAQ;IACV;EAEJ,OAAO;IACL,OAAO,IAAIN,QAAA,CAAS,mBAAmB;MAAEM,MAAA,EAAQ;IAAI;EACvD;AACF", "ignoreList": []}