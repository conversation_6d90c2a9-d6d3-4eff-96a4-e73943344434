{"version": 3, "file": "getNavPrefs.js", "names": ["cache", "getNavPrefs", "req", "user", "collection", "payload", "find", "depth", "limit", "pagination", "where", "and", "key", "equals", "id", "then", "res", "docs", "value"], "sources": ["../../../src/elements/Nav/getNavPrefs.ts"], "sourcesContent": ["import type { NavPreferences, PayloadRequest } from 'payload'\n\nimport { cache } from 'react'\n\nexport const getNavPrefs = cache(async (req: PayloadRequest): Promise<NavPreferences> => {\n  return req?.user?.collection\n    ? await req.payload\n        .find({\n          collection: 'payload-preferences',\n          depth: 0,\n          limit: 1,\n          pagination: false,\n          req,\n          where: {\n            and: [\n              {\n                key: {\n                  equals: 'nav',\n                },\n              },\n              {\n                'user.relationTo': {\n                  equals: req.user.collection,\n                },\n              },\n              {\n                'user.value': {\n                  equals: req?.user?.id,\n                },\n              },\n            ],\n          },\n        })\n        ?.then((res) => res?.docs?.[0]?.value)\n    : null\n})\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ;AAEtB,OAAO,MAAMC,WAAA,GAAcD,KAAA,CAAM,MAAOE,GAAA;EACtC,OAAOA,GAAA,EAAKC,IAAA,EAAMC,UAAA,GACd,MAAMF,GAAA,CAAIG,OAAO,CACdC,IAAI,CAAC;IACJF,UAAA,EAAY;IACZG,KAAA,EAAO;IACPC,KAAA,EAAO;IACPC,UAAA,EAAY;IACZP,GAAA;IACAQ,KAAA,EAAO;MACLC,GAAA,EAAK,CACH;QACEC,GAAA,EAAK;UACHC,MAAA,EAAQ;QACV;MACF,GACA;QACE,mBAAmB;UACjBA,MAAA,EAAQX,GAAA,CAAIC,IAAI,CAACC;QACnB;MACF,GACA;QACE,cAAc;UACZS,MAAA,EAAQX,GAAA,EAAKC,IAAA,EAAMW;QACrB;MACF;IAEJ;EACF,IACEC,IAAA,CAAMC,GAAA,IAAQA,GAAA,EAAKC,IAAA,GAAO,EAAE,EAAEC,KAAA,IAClC;AACN", "ignoreList": []}