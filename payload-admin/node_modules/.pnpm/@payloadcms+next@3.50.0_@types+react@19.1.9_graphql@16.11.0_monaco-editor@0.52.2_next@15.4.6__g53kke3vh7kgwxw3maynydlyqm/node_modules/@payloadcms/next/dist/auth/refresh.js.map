{"version": 3, "file": "refresh.js", "names": ["headers", "nextHeaders", "createLocalReq", "getPayload", "refreshOperation", "getExistingAuthToken", "setPayloadAuthCookie", "refresh", "config", "payload", "cron", "result", "auth", "user", "Error", "collection", "collectionConfig", "collections", "req", "refreshResult", "message", "success", "existingCookie", "cookiePrefix", "authConfig", "token", "value"], "sources": ["../../src/auth/refresh.ts"], "sourcesContent": ["'use server'\n\nimport type { CollectionSlug } from 'payload'\n\nimport { headers as nextHeaders } from 'next/headers.js'\nimport { createLocalReq, getPayload, refreshOperation } from 'payload'\n\nimport { getExistingAuthToken } from '../utilities/getExistingAuthToken.js'\nimport { setPayloadAuthCookie } from '../utilities/setPayloadAuthCookie.js'\n\nexport async function refresh({ config }: { config: any }) {\n  const payload = await getPayload({ config, cron: true })\n  const headers = await nextHeaders()\n  const result = await payload.auth({ headers })\n\n  if (!result.user) {\n    throw new Error('Cannot refresh token: user not authenticated')\n  }\n\n  const collection: CollectionSlug | undefined = result.user.collection\n  const collectionConfig = payload.collections[collection]\n\n  if (!collectionConfig?.config.auth) {\n    throw new Error(`No auth config found for collection: ${collection}`)\n  }\n\n  const req = await createLocalReq({ user: result.user }, payload)\n\n  const refreshResult = await refreshOperation({\n    collection: collectionConfig,\n    req,\n  })\n\n  if (!refreshResult) {\n    return { message: 'Token refresh failed', success: false }\n  }\n\n  const existingCookie = await getExistingAuthToken(payload.config.cookiePrefix)\n  if (!existingCookie) {\n    return { message: 'No valid token found to refresh', success: false }\n  }\n\n  await setPayloadAuthCookie({\n    authConfig: collectionConfig.config.auth,\n    cookiePrefix: payload.config.cookiePrefix,\n    token: existingCookie.value,\n  })\n\n  return { message: 'Token refreshed successfully', success: true }\n}\n"], "mappings": "AAAA;;AAIA,SAASA,OAAA,IAAWC,WAAW,QAAQ;AACvC,SAASC,cAAc,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ;AAE7D,SAASC,oBAAoB,QAAQ;AACrC,SAASC,oBAAoB,QAAQ;AAErC,OAAO,eAAeC,QAAQ;EAAEC;AAAM,CAAmB;EACvD,MAAMC,OAAA,GAAU,MAAMN,UAAA,CAAW;IAAEK,MAAA;IAAQE,IAAA,EAAM;EAAK;EACtD,MAAMV,OAAA,GAAU,MAAMC,WAAA;EACtB,MAAMU,MAAA,GAAS,MAAMF,OAAA,CAAQG,IAAI,CAAC;IAAEZ;EAAQ;EAE5C,IAAI,CAACW,MAAA,CAAOE,IAAI,EAAE;IAChB,MAAM,IAAIC,KAAA,CAAM;EAClB;EAEA,MAAMC,UAAA,GAAyCJ,MAAA,CAAOE,IAAI,CAACE,UAAU;EACrE,MAAMC,gBAAA,GAAmBP,OAAA,CAAQQ,WAAW,CAACF,UAAA,CAAW;EAExD,IAAI,CAACC,gBAAA,EAAkBR,MAAA,CAAOI,IAAA,EAAM;IAClC,MAAM,IAAIE,KAAA,CAAM,wCAAwCC,UAAA,EAAY;EACtE;EAEA,MAAMG,GAAA,GAAM,MAAMhB,cAAA,CAAe;IAAEW,IAAA,EAAMF,MAAA,CAAOE;EAAK,GAAGJ,OAAA;EAExD,MAAMU,aAAA,GAAgB,MAAMf,gBAAA,CAAiB;IAC3CW,UAAA,EAAYC,gBAAA;IACZE;EACF;EAEA,IAAI,CAACC,aAAA,EAAe;IAClB,OAAO;MAAEC,OAAA,EAAS;MAAwBC,OAAA,EAAS;IAAM;EAC3D;EAEA,MAAMC,cAAA,GAAiB,MAAMjB,oBAAA,CAAqBI,OAAA,CAAQD,MAAM,CAACe,YAAY;EAC7E,IAAI,CAACD,cAAA,EAAgB;IACnB,OAAO;MAAEF,OAAA,EAAS;MAAmCC,OAAA,EAAS;IAAM;EACtE;EAEA,MAAMf,oBAAA,CAAqB;IACzBkB,UAAA,EAAYR,gBAAA,CAAiBR,MAAM,CAACI,IAAI;IACxCW,YAAA,EAAcd,OAAA,CAAQD,MAAM,CAACe,YAAY;IACzCE,KAAA,EAAOH,cAAA,CAAeI;EACxB;EAEA,OAAO;IAAEN,OAAA,EAAS;IAAgCC,OAAA,EAAS;EAAK;AAClE", "ignoreList": []}