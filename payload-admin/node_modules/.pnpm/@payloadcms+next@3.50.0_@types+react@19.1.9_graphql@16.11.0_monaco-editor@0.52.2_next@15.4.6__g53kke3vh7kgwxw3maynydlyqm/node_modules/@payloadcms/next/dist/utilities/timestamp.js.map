{"version": 3, "file": "timestamp.js", "names": ["timestamp", "label", "process", "env", "PAYLOAD_TIME", "String", "Date", "getTime", "now", "console", "log", "Number"], "sources": ["../../src/utilities/timestamp.ts"], "sourcesContent": ["export const timestamp = (label: string) => {\n  if (!process.env.PAYLOAD_TIME) {\n    process.env.PAYLOAD_TIME = String(new Date().getTime())\n  }\n  const now = new Date()\n  console.log(`[${now.getTime() - Number(process.env.PAYLOAD_TIME)}ms] ${label}`)\n}\n"], "mappings": "AAAA,OAAO,MAAMA,SAAA,GAAaC,KAAA;EACxB,IAAI,CAACC,OAAA,CAAQC,GAAG,CAACC,YAAY,EAAE;IAC7BF,OAAA,CAAQC,GAAG,CAACC,YAAY,GAAGC,MAAA,CAAO,IAAIC,IAAA,GAAOC,OAAO;EACtD;EACA,MAAMC,GAAA,GAAM,IAAIF,IAAA;EAChBG,OAAA,CAAQC,GAAG,CAAC,IAAIF,GAAA,CAAID,OAAO,KAAKI,MAAA,CAAOT,OAAA,CAAQC,GAAG,CAACC,YAAY,QAAQH,KAAA,EAAO;AAChF", "ignoreList": []}