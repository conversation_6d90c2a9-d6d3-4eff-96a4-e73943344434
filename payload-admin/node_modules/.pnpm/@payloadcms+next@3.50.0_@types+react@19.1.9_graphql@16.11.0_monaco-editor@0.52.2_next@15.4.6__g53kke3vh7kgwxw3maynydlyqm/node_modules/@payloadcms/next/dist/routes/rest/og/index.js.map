{"version": 3, "file": "index.js", "names": ["PayloadIcon", "fs", "ImageResponse", "path", "React", "fileURLToPath", "OGImage", "filename", "import", "meta", "url", "dirname", "runtime", "contentType", "generateOGImage", "req", "config", "payload", "admin", "defaultOGImageType", "Response", "json", "error", "status", "searchParams", "URL", "hasTitle", "has", "title", "get", "slice", "<PERSON><PERSON><PERSON><PERSON>", "leader", "replace", "description", "fontData", "readFile", "join", "e", "logger", "message", "fontFamily", "_jsx", "Fallback", "Icon", "components", "graphics", "importMap", "fonts", "name", "data", "style", "weight", "height", "width"], "sources": ["../../../../src/routes/rest/og/index.tsx"], "sourcesContent": ["import type { PayloadHandler } from 'payload'\n\nimport { PayloadIcon } from '@payloadcms/ui/shared'\nimport fs from 'fs/promises'\nimport { ImageResponse } from 'next/og.js'\nimport path from 'path'\nimport React from 'react'\nimport { fileURLToPath } from 'url'\n\nimport { OGImage } from './image.js'\n\nconst filename = fileURLToPath(import.meta.url)\nconst dirname = path.dirname(filename)\n\nexport const runtime = 'nodejs'\n\nexport const contentType = 'image/png'\n\nexport const generateOGImage: PayloadHandler = async (req) => {\n  const config = req.payload.config\n\n  if (config.admin.meta.defaultOGImageType === 'off') {\n    return Response.json({ error: `Open Graph images are disabled` }, { status: 400 })\n  }\n\n  try {\n    const { searchParams } = new URL(req.url)\n\n    const hasTitle = searchParams.has('title')\n    const title = hasTitle ? searchParams.get('title')?.slice(0, 100) : ''\n    const hasLeader = searchParams.has('leader')\n    const leader = hasLeader ? searchParams.get('leader')?.slice(0, 100).replace('-', ' ') : ''\n    const description = searchParams.has('description') ? searchParams.get('description') : ''\n\n    let fontData\n\n    try {\n      // TODO: replace with `.woff2` file when supported\n      // See https://github.com/vercel/next.js/issues/63935\n      // Or better yet, use a CDN like Google Fonts if ever supported\n      fontData = fs.readFile(path.join(dirname, 'roboto-regular.woff'))\n    } catch (e) {\n      req.payload.logger.error(`Error reading font file or not readable: ${e.message}`)\n    }\n\n    const fontFamily = 'Roboto, sans-serif'\n\n    return new ImageResponse(\n      (\n        <OGImage\n          description={description}\n          Fallback={PayloadIcon}\n          fontFamily={fontFamily}\n          Icon={config.admin?.components?.graphics?.Icon}\n          importMap={req.payload.importMap}\n          leader={leader}\n          title={title}\n        />\n      ),\n      {\n        ...(fontData\n          ? {\n              fonts: [\n                {\n                  name: 'Roboto',\n                  data: await fontData,\n                  style: 'normal',\n                  weight: 400,\n                },\n              ],\n            }\n          : {}),\n        height: 630,\n        width: 1200,\n      },\n    )\n  } catch (e: any) {\n    req.payload.logger.error(`Error generating Open Graph image: ${e.message}`)\n    return Response.json({ error: `Internal Server Error: ${e.message}` }, { status: 500 })\n  }\n}\n"], "mappings": ";AAEA,SAASA,WAAW,QAAQ;AAC5B,OAAOC,EAAA,MAAQ;AACf,SAASC,aAAa,QAAQ;AAC9B,OAAOC,IAAA,MAAU;AACjB,OAAOC,KAAA,MAAW;AAClB,SAASC,aAAa,QAAQ;AAE9B,SAASC,OAAO,QAAQ;AAExB,MAAMC,QAAA,GAAWF,aAAA,CAAcG,MAAA,CAAAC,IAAA,CAAYC,GAAG;AAC9C,MAAMC,OAAA,GAAUR,IAAA,CAAKQ,OAAO,CAACJ,QAAA;AAE7B,OAAO,MAAMK,OAAA,GAAU;AAEvB,OAAO,MAAMC,WAAA,GAAc;AAE3B,OAAO,MAAMC,eAAA,GAAkC,MAAOC,GAAA;EACpD,MAAMC,MAAA,GAASD,GAAA,CAAIE,OAAO,CAACD,MAAM;EAEjC,IAAIA,MAAA,CAAOE,KAAK,CAACT,IAAI,CAACU,kBAAkB,KAAK,OAAO;IAClD,OAAOC,QAAA,CAASC,IAAI,CAAC;MAAEC,KAAA,EAAO;IAAiC,GAAG;MAAEC,MAAA,EAAQ;IAAI;EAClF;EAEA,IAAI;IACF,MAAM;MAAEC;IAAY,CAAE,GAAG,IAAIC,GAAA,CAAIV,GAAA,CAAIL,GAAG;IAExC,MAAMgB,QAAA,GAAWF,YAAA,CAAaG,GAAG,CAAC;IAClC,MAAMC,KAAA,GAAQF,QAAA,GAAWF,YAAA,CAAaK,GAAG,CAAC,UAAUC,KAAA,CAAM,GAAG,OAAO;IACpE,MAAMC,SAAA,GAAYP,YAAA,CAAaG,GAAG,CAAC;IACnC,MAAMK,MAAA,GAASD,SAAA,GAAYP,YAAA,CAAaK,GAAG,CAAC,WAAWC,KAAA,CAAM,GAAG,KAAKG,OAAA,CAAQ,KAAK,OAAO;IACzF,MAAMC,WAAA,GAAcV,YAAA,CAAaG,GAAG,CAAC,iBAAiBH,YAAA,CAAaK,GAAG,CAAC,iBAAiB;IAExF,IAAIM,QAAA;IAEJ,IAAI;MACF;MACA;MACA;MACAA,QAAA,GAAWlC,EAAA,CAAGmC,QAAQ,CAACjC,IAAA,CAAKkC,IAAI,CAAC1B,OAAA,EAAS;IAC5C,EAAE,OAAO2B,CAAA,EAAG;MACVvB,GAAA,CAAIE,OAAO,CAACsB,MAAM,CAACjB,KAAK,CAAC,4CAA4CgB,CAAA,CAAEE,OAAO,EAAE;IAClF;IAEA,MAAMC,UAAA,GAAa;IAEnB,OAAO,IAAIvC,aAAA,cAEPwC,IAAA,CAACpC,OAAA;MACC4B,WAAA,EAAaA,WAAA;MACbS,QAAA,EAAU3C,WAAA;MACVyC,UAAA,EAAYA,UAAA;MACZG,IAAA,EAAM5B,MAAA,CAAOE,KAAK,EAAE2B,UAAA,EAAYC,QAAA,EAAUF,IAAA;MAC1CG,SAAA,EAAWhC,GAAA,CAAIE,OAAO,CAAC8B,SAAS;MAChCf,MAAA,EAAQA,MAAA;MACRJ,KAAA,EAAOA;QAGX;MACE,IAAIO,QAAA,GACA;QACEa,KAAA,EAAO,CACL;UACEC,IAAA,EAAM;UACNC,IAAA,EAAM,MAAMf,QAAA;UACZgB,KAAA,EAAO;UACPC,MAAA,EAAQ;QACV;MAEJ,IACA,CAAC,CAAC;MACNC,MAAA,EAAQ;MACRC,KAAA,EAAO;IACT;EAEJ,EAAE,OAAOhB,CAAA,EAAQ;IACfvB,GAAA,CAAIE,OAAO,CAACsB,MAAM,CAACjB,KAAK,CAAC,sCAAsCgB,CAAA,CAAEE,OAAO,EAAE;IAC1E,OAAOpB,QAAA,CAASC,IAAI,CAAC;MAAEC,KAAA,EAAO,0BAA0BgB,CAAA,CAAEE,OAAO;IAAG,GAAG;MAAEjB,MAAA,EAAQ;IAAI;EACvF;AACF", "ignoreList": []}