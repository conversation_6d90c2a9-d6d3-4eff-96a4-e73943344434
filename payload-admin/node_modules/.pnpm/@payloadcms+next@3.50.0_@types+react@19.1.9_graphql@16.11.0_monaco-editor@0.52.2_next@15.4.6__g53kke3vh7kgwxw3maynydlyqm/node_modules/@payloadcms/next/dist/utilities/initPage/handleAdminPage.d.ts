import type { Payload, SanitizedCollectionConfig, SanitizedConfig, SanitizedGlobalConfig } from 'payload';
type Args = {
    adminRoute: string;
    config: SanitizedConfig;
    defaultIDType: Payload['db']['defaultIDType'];
    payload?: Payload;
    route: string;
};
type RouteInfo = {
    collectionConfig?: SanitizedCollectionConfig;
    collectionSlug?: string;
    docID?: number | string;
    globalConfig?: SanitizedGlobalConfig;
    globalSlug?: string;
};
export declare function getRouteInfo({ adminRoute, config, defaultIDType, payload, route, }: Args): RouteInfo;
export {};
//# sourceMappingURL=handleAdminPage.d.ts.map