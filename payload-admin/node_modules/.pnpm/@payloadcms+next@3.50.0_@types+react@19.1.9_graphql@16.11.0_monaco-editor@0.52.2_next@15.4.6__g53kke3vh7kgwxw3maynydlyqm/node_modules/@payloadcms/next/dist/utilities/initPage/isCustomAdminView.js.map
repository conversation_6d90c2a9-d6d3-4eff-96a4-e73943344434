{"version": 3, "file": "isCustomAdminView.js", "names": ["getRouteWithoutAdmin", "isCustomAdminView", "adminRoute", "config", "route", "admin", "components", "views", "isPublicAdminRoute", "Object", "entries", "some", "_", "view", "routeWithoutAdmin", "exact", "path", "startsWith"], "sources": ["../../../src/utilities/initPage/isCustomAdminView.ts"], "sourcesContent": ["import type { AdminViewConfig, PayloadRequest, SanitizedConfig } from 'payload'\n\nimport { getRouteWithoutAdmin } from './shared.js'\n\n/**\n * Returns an array of views marked with 'public: true' in the config\n */\nexport const isCustomAdminView = ({\n  adminRoute,\n  config,\n  route,\n}: {\n  adminRoute: string\n  config: SanitizedConfig\n  route: string\n}): boolean => {\n  if (config.admin?.components?.views) {\n    const isPublicAdminRoute = Object.entries(config.admin.components.views).some(([_, view]) => {\n      const routeWithoutAdmin = getRouteWithoutAdmin({ adminRoute, route })\n\n      if (view.exact) {\n        if (routeWithoutAdmin === view.path) {\n          return true\n        }\n      } else {\n        if (routeWithoutAdmin.startsWith(view.path)) {\n          return true\n        }\n      }\n      return false\n    })\n    return isPublicAdminRoute\n  }\n  return false\n}\n"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ;AAErC;;;AAGA,OAAO,MAAMC,iBAAA,GAAoBA,CAAC;EAChCC,UAAU;EACVC,MAAM;EACNC;AAAK,CAKN;EACC,IAAID,MAAA,CAAOE,KAAK,EAAEC,UAAA,EAAYC,KAAA,EAAO;IACnC,MAAMC,kBAAA,GAAqBC,MAAA,CAAOC,OAAO,CAACP,MAAA,CAAOE,KAAK,CAACC,UAAU,CAACC,KAAK,EAAEI,IAAI,CAAC,CAAC,CAACC,CAAA,EAAGC,IAAA,CAAK;MACtF,MAAMC,iBAAA,GAAoBd,oBAAA,CAAqB;QAAEE,UAAA;QAAYE;MAAM;MAEnE,IAAIS,IAAA,CAAKE,KAAK,EAAE;QACd,IAAID,iBAAA,KAAsBD,IAAA,CAAKG,IAAI,EAAE;UACnC,OAAO;QACT;MACF,OAAO;QACL,IAAIF,iBAAA,CAAkBG,UAAU,CAACJ,IAAA,CAAKG,IAAI,GAAG;UAC3C,OAAO;QACT;MACF;MACA,OAAO;IACT;IACA,OAAOR,kBAAA;EACT;EACA,OAAO;AACT", "ignoreList": []}