{"version": 3, "file": "index.js", "names": ["React", "baseClass", "MinimalTemplate", "props", "children", "className", "style", "width", "classes", "filter", "Boolean", "join", "_jsx"], "sources": ["../../../src/templates/Minimal/index.tsx"], "sourcesContent": ["import React from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'template-minimal'\n\nexport type MinimalTemplateProps = {\n  children?: React.ReactNode\n  className?: string\n  style?: React.CSSProperties\n  width?: 'normal' | 'wide'\n}\n\nexport const MinimalTemplate: React.FC<MinimalTemplateProps> = (props) => {\n  const { children, className, style = {}, width = 'normal' } = props\n\n  const classes = [className, baseClass, `${baseClass}--width-${width}`].filter(Boolean).join(' ')\n\n  return (\n    <section className={classes} style={style}>\n      <div className={`${baseClass}__wrap`}>{children}</div>\n    </section>\n  )\n}\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAIlB,MAAMC,SAAA,GAAY;AASlB,OAAO,MAAMC,eAAA,GAAmDC,KAAA;EAC9D,MAAM;IAAEC,QAAQ;IAAEC,SAAS;IAAEC,KAAA,GAAQ,CAAC,CAAC;IAAEC,KAAA,GAAQ;EAAQ,CAAE,GAAGJ,KAAA;EAE9D,MAAMK,OAAA,GAAU,CAACH,SAAA,EAAWJ,SAAA,EAAW,GAAGA,SAAA,WAAoBM,KAAA,EAAO,CAAC,CAACE,MAAM,CAACC,OAAA,EAASC,IAAI,CAAC;EAE5F,oBACEC,IAAA,CAAC;IAAQP,SAAA,EAAWG,OAAA;IAASF,KAAA,EAAOA,KAAA;cAClC,aAAAM,IAAA,CAAC;MAAIP,SAAA,EAAW,GAAGJ,SAAA,QAAiB;gBAAGG;;;AAG7C", "ignoreList": []}