@import '~@payloadcms/ui/scss';

@layer payload-default {
  .template-default {
    min-height: 100vh;
    display: grid;
    position: relative;
    isolation: isolate;

    @media (prefers-reduced-motion) {
      transition: none;
    }

    &--nav-animate {
      transition: grid-template-columns var(--nav-trans-time) linear;
    }

    &--nav-open {
      .template-default {
        &__nav-overlay {
          transition: opacity var(--nav-trans-time) linear;
        }
      }
    }
  }

  @media (min-width: 1441px) {
    .template-default {
      grid-template-columns: 0 auto;

      &--nav-open {
        grid-template-columns: var(--nav-width) auto;
      }
    }
  }

  @media (max-width: 1440px) {
    .template-default--nav-hydrated.template-default--nav-open {
      grid-template-columns: var(--nav-width) auto;
    }

    .template-default {
      grid-template-columns: 1fr auto;

      .nav {
        display: none;
      }

      &--nav-hydrated {
        grid-template-columns: 0 auto;

        .nav {
          display: unset;
        }
      }
    }
  }
}
