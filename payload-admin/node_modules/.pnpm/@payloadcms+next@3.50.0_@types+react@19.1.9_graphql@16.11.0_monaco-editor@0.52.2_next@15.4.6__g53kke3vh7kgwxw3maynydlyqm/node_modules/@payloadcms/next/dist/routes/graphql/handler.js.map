{"version": 3, "file": "handler.js", "names": ["configToSchema", "createHandler", "status", "httpStatus", "addDataAndFileToRequest", "addLocalesToRequestFromData", "createPayloadRequest", "headersWithCors", "logError", "mergeHeaders", "handleError", "err", "payload", "req", "originalError", "INTERNAL_SERVER_ERROR", "errorMessage", "message", "config", "debug", "response", "extensions", "name", "undefined", "data", "stack", "statusCode", "locations", "path", "hooks", "afterError", "reduce", "promise", "hook", "result", "context", "error", "graphqlResult", "Promise", "resolve", "cached", "global", "_payload_graphql", "graphql", "getGraphql", "process", "env", "NODE_ENV", "resolvedConfig", "schema", "e", "POST", "request", "originalRequest", "clone", "canSetHeaders", "validationRules", "headers", "apiResponse", "onOperation", "args", "errors", "all", "map", "_", "defaultRules", "concat", "resHeaders", "Headers", "key", "append", "Response", "body", "responseHeaders"], "sources": ["../../../src/routes/graphql/handler.ts"], "sourcesContent": ["import type { GraphQLError, GraphQLFormattedError } from 'graphql'\nimport type { APIError, Payload, PayloadRequest, SanitizedConfig } from 'payload'\n\nimport { configToSchema } from '@payloadcms/graphql'\nimport { createHandler } from 'graphql-http/lib/use/fetch'\nimport { status as httpStatus } from 'http-status'\nimport {\n  addDataAndFileToRequest,\n  addLocalesToRequestFromData,\n  createPayloadRequest,\n  headersWithCors,\n  logError,\n  mergeHeaders,\n} from 'payload'\n\nconst handleError = async ({\n  err,\n  payload,\n  req,\n}: {\n  err: GraphQLError\n  payload: Payload\n  req: PayloadRequest\n}): Promise<GraphQLFormattedError> => {\n  const status = (err.originalError as APIError).status || httpStatus.INTERNAL_SERVER_ERROR\n  let errorMessage = err.message\n  logError({ err, payload })\n\n  // Internal server errors can contain anything, including potentially sensitive data.\n  // Therefore, error details will be hidden from the response unless `config.debug` is `true`\n  if (!payload.config.debug && status === httpStatus.INTERNAL_SERVER_ERROR) {\n    errorMessage = 'Something went wrong.'\n  }\n\n  let response: GraphQLFormattedError = {\n    extensions: {\n      name: err?.originalError?.name || undefined,\n      data: (err && err.originalError && (err.originalError as APIError).data) || undefined,\n      stack: payload.config.debug ? err.stack : undefined,\n      statusCode: status,\n    },\n    locations: err.locations,\n    message: errorMessage,\n    path: err.path,\n  }\n\n  await payload.config.hooks.afterError?.reduce(async (promise, hook) => {\n    await promise\n\n    const result = await hook({\n      context: req.context,\n      error: err,\n      graphqlResult: response,\n      req,\n    })\n\n    if (result) {\n      response = result.graphqlResult || response\n    }\n  }, Promise.resolve())\n\n  return response\n}\n\nlet cached = global._payload_graphql\n\nif (!cached) {\n  cached = global._payload_graphql = { graphql: null, promise: null }\n}\n\nexport const getGraphql = async (config: Promise<SanitizedConfig> | SanitizedConfig) => {\n  if (process.env.NODE_ENV === 'development') {\n    cached = global._payload_graphql = { graphql: null, promise: null }\n  }\n\n  if (cached.graphql) {\n    return cached.graphql\n  }\n\n  if (!cached.promise) {\n    const resolvedConfig = await config\n    cached.promise = new Promise((resolve) => {\n      const schema = configToSchema(resolvedConfig)\n      resolve(cached.graphql || schema)\n    })\n  }\n\n  try {\n    cached.graphql = await cached.promise\n  } catch (e) {\n    cached.promise = null\n    throw e\n  }\n\n  return cached.graphql\n}\n\nexport const POST =\n  (config: Promise<SanitizedConfig> | SanitizedConfig) => async (request: Request) => {\n    const originalRequest = request.clone()\n    const req = await createPayloadRequest({\n      canSetHeaders: true,\n      config,\n      request,\n    })\n\n    await addDataAndFileToRequest(req)\n    addLocalesToRequestFromData(req)\n\n    const { schema, validationRules } = await getGraphql(config)\n\n    const { payload } = req\n\n    const headers = {}\n    const apiResponse = await createHandler({\n      context: { headers, req },\n      onOperation: async (request, args, result) => {\n        const response =\n          typeof payload.extensions === 'function'\n            ? await payload.extensions({\n                args,\n                req: request,\n                result,\n              })\n            : result\n        if (response.errors) {\n          const errors = (await Promise.all(\n            result.errors.map((error) => {\n              return handleError({ err: error, payload, req })\n            }),\n          )) as GraphQLError[]\n          // errors type should be FormattedGraphQLError[] but onOperation has a return type of ExecutionResult instead of FormattedExecutionResult\n          return { ...response, errors }\n        }\n        return response\n      },\n      schema,\n      validationRules: (_, args, defaultRules) => defaultRules.concat(validationRules(args)),\n    })(originalRequest)\n\n    const resHeaders = headersWithCors({\n      headers: new Headers(apiResponse.headers),\n      req,\n    })\n\n    for (const key in headers) {\n      resHeaders.append(key, headers[key])\n    }\n\n    return new Response(apiResponse.body, {\n      headers: req.responseHeaders ? mergeHeaders(req.responseHeaders, resHeaders) : resHeaders,\n      status: apiResponse.status,\n    })\n  }\n"], "mappings": "AAGA,SAASA,cAAc,QAAQ;AAC/B,SAASC,aAAa,QAAQ;AAC9B,SAASC,MAAA,IAAUC,UAAU,QAAQ;AACrC,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,oBAAoB,EACpBC,eAAe,EACfC,QAAQ,EACRC,YAAY,QACP;AAEP,MAAMC,WAAA,GAAc,MAAAA,CAAO;EACzBC,GAAG;EACHC,OAAO;EACPC;AAAG,CAKJ;EACC,MAAMX,MAAA,GAASS,GAAC,CAAIG,aAAa,CAAcZ,MAAM,IAAIC,UAAA,CAAWY,qBAAqB;EACzF,IAAIC,YAAA,GAAeL,GAAA,CAAIM,OAAO;EAC9BT,QAAA,CAAS;IAAEG,GAAA;IAAKC;EAAQ;EAExB;EACA;EACA,IAAI,CAACA,OAAA,CAAQM,MAAM,CAACC,KAAK,IAAIjB,MAAA,KAAWC,UAAA,CAAWY,qBAAqB,EAAE;IACxEC,YAAA,GAAe;EACjB;EAEA,IAAII,QAAA,GAAkC;IACpCC,UAAA,EAAY;MACVC,IAAA,EAAMX,GAAA,EAAKG,aAAA,EAAeQ,IAAA,IAAQC,SAAA;MAClCC,IAAA,EAAMb,GAAC,IAAOA,GAAA,CAAIG,aAAa,IAAIH,GAAC,CAAIG,aAAa,CAAcU,IAAI,IAAKD,SAAA;MAC5EE,KAAA,EAAOb,OAAA,CAAQM,MAAM,CAACC,KAAK,GAAGR,GAAA,CAAIc,KAAK,GAAGF,SAAA;MAC1CG,UAAA,EAAYxB;IACd;IACAyB,SAAA,EAAWhB,GAAA,CAAIgB,SAAS;IACxBV,OAAA,EAASD,YAAA;IACTY,IAAA,EAAMjB,GAAA,CAAIiB;EACZ;EAEA,MAAMhB,OAAA,CAAQM,MAAM,CAACW,KAAK,CAACC,UAAU,EAAEC,MAAA,CAAO,OAAOC,OAAA,EAASC,IAAA;IAC5D,MAAMD,OAAA;IAEN,MAAME,MAAA,GAAS,MAAMD,IAAA,CAAK;MACxBE,OAAA,EAAStB,GAAA,CAAIsB,OAAO;MACpBC,KAAA,EAAOzB,GAAA;MACP0B,aAAA,EAAejB,QAAA;MACfP;IACF;IAEA,IAAIqB,MAAA,EAAQ;MACVd,QAAA,GAAWc,MAAA,CAAOG,aAAa,IAAIjB,QAAA;IACrC;EACF,GAAGkB,OAAA,CAAQC,OAAO;EAElB,OAAOnB,QAAA;AACT;AAEA,IAAIoB,MAAA,GAASC,MAAA,CAAOC,gBAAgB;AAEpC,IAAI,CAACF,MAAA,EAAQ;EACXA,MAAA,GAASC,MAAA,CAAOC,gBAAgB,GAAG;IAAEC,OAAA,EAAS;IAAMX,OAAA,EAAS;EAAK;AACpE;AAEA,OAAO,MAAMY,UAAA,GAAa,MAAO1B,MAAA;EAC/B,IAAI2B,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1CP,MAAA,GAASC,MAAA,CAAOC,gBAAgB,GAAG;MAAEC,OAAA,EAAS;MAAMX,OAAA,EAAS;IAAK;EACpE;EAEA,IAAIQ,MAAA,CAAOG,OAAO,EAAE;IAClB,OAAOH,MAAA,CAAOG,OAAO;EACvB;EAEA,IAAI,CAACH,MAAA,CAAOR,OAAO,EAAE;IACnB,MAAMgB,cAAA,GAAiB,MAAM9B,MAAA;IAC7BsB,MAAA,CAAOR,OAAO,GAAG,IAAIM,OAAA,CAASC,OAAA;MAC5B,MAAMU,MAAA,GAASjD,cAAA,CAAegD,cAAA;MAC9BT,OAAA,CAAQC,MAAA,CAAOG,OAAO,IAAIM,MAAA;IAC5B;EACF;EAEA,IAAI;IACFT,MAAA,CAAOG,OAAO,GAAG,MAAMH,MAAA,CAAOR,OAAO;EACvC,EAAE,OAAOkB,CAAA,EAAG;IACVV,MAAA,CAAOR,OAAO,GAAG;IACjB,MAAMkB,CAAA;EACR;EAEA,OAAOV,MAAA,CAAOG,OAAO;AACvB;AAEA,OAAO,MAAMQ,IAAA,GACVjC,MAAA,IAAuD,MAAOkC,OAAA;EAC7D,MAAMC,eAAA,GAAkBD,OAAA,CAAQE,KAAK;EACrC,MAAMzC,GAAA,GAAM,MAAMP,oBAAA,CAAqB;IACrCiD,aAAA,EAAe;IACfrC,MAAA;IACAkC;EACF;EAEA,MAAMhD,uBAAA,CAAwBS,GAAA;EAC9BR,2BAAA,CAA4BQ,GAAA;EAE5B,MAAM;IAAEoC,MAAM;IAAEO;EAAe,CAAE,GAAG,MAAMZ,UAAA,CAAW1B,MAAA;EAErD,MAAM;IAAEN;EAAO,CAAE,GAAGC,GAAA;EAEpB,MAAM4C,OAAA,GAAU,CAAC;EACjB,MAAMC,WAAA,GAAc,MAAMzD,aAAA,CAAc;IACtCkC,OAAA,EAAS;MAAEsB,OAAA;MAAS5C;IAAI;IACxB8C,WAAA,EAAa,MAAAA,CAAOP,OAAA,EAASQ,IAAA,EAAM1B,MAAA;MACjC,MAAMd,QAAA,GACJ,OAAOR,OAAA,CAAQS,UAAU,KAAK,aAC1B,MAAMT,OAAA,CAAQS,UAAU,CAAC;QACvBuC,IAAA;QACA/C,GAAA,EAAKuC,OAAA;QACLlB;MACF,KACAA,MAAA;MACN,IAAId,QAAA,CAASyC,MAAM,EAAE;QACnB,MAAMA,MAAA,GAAU,MAAMvB,OAAA,CAAQwB,GAAG,CAC/B5B,MAAA,CAAO2B,MAAM,CAACE,GAAG,CAAE3B,KAAA;UACjB,OAAO1B,WAAA,CAAY;YAAEC,GAAA,EAAKyB,KAAA;YAAOxB,OAAA;YAASC;UAAI;QAChD;QAEF;QACA,OAAO;UAAE,GAAGO,QAAQ;UAAEyC;QAAO;MAC/B;MACA,OAAOzC,QAAA;IACT;IACA6B,MAAA;IACAO,eAAA,EAAiBA,CAACQ,CAAA,EAAGJ,IAAA,EAAMK,YAAA,KAAiBA,YAAA,CAAaC,MAAM,CAACV,eAAA,CAAgBI,IAAA;EAClF,GAAGP,eAAA;EAEH,MAAMc,UAAA,GAAa5D,eAAA,CAAgB;IACjCkD,OAAA,EAAS,IAAIW,OAAA,CAAQV,WAAA,CAAYD,OAAO;IACxC5C;EACF;EAEA,KAAK,MAAMwD,GAAA,IAAOZ,OAAA,EAAS;IACzBU,UAAA,CAAWG,MAAM,CAACD,GAAA,EAAKZ,OAAO,CAACY,GAAA,CAAI;EACrC;EAEA,OAAO,IAAIE,QAAA,CAASb,WAAA,CAAYc,IAAI,EAAE;IACpCf,OAAA,EAAS5C,GAAA,CAAI4D,eAAe,GAAGhE,YAAA,CAAaI,GAAA,CAAI4D,eAAe,EAAEN,UAAA,IAAcA,UAAA;IAC/EjE,MAAA,EAAQwD,WAAA,CAAYxD;EACtB;AACF", "ignoreList": []}