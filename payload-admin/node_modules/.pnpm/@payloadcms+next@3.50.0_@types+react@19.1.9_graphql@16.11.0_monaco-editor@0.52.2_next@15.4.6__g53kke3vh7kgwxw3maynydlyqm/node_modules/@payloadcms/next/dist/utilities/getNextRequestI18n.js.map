{"version": 3, "file": "getNextRequestI18n.js", "names": ["initI18n", "cookies", "headers", "getRequestLanguage", "getNextRequestI18n", "config", "i18n", "context", "language"], "sources": ["../../src/utilities/getNextRequestI18n.ts"], "sourcesContent": ["import type { ClientTranslationsObject, I18nClient } from '@payloadcms/translations'\n\nimport { initI18n } from '@payloadcms/translations'\nimport { cookies, headers } from 'next/headers.js'\nimport { getRequestLanguage, type SanitizedConfig } from 'payload'\n\n/**\n * In the context of Next.js, this function initializes the i18n object for the current request.\n *\n * It must be called on the server side, and within the lifecycle of a request since it relies on the request headers and cookies.\n */\nexport const getNextRequestI18n = async <\n  // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n  TAdditionalTranslations = {},\n  TAdditionalClientTranslationKeys extends string = never,\n>({\n  config,\n}: {\n  config: SanitizedConfig\n}): Promise<\n  [TAdditionalClientTranslationKeys] extends [never]\n    ? I18nClient\n    : TAdditionalTranslations extends object\n      ? I18nClient<TAdditionalTranslations, TAdditionalClientTranslationKeys>\n      : I18nClient<ClientTranslationsObject, TAdditionalClientTranslationKeys>\n> => {\n  return (await initI18n({\n    config: config.i18n,\n    context: 'client',\n    language: getRequestLanguage({ config, cookies: await cookies(), headers: await headers() }),\n  })) as any\n}\n"], "mappings": "AAEA,SAASA,QAAQ,QAAQ;AACzB,SAASC,OAAO,EAAEC,OAAO,QAAQ;AACjC,SAASC,kBAAkB,QAA8B;AAEzD;;;;;AAKA,OAAO,MAAMC,kBAAA,GAAqB,MAAAA,CAIhC;EACAC;AAAM,CAGP;EAOC,OAAQ,MAAML,QAAA,CAAS;IACrBK,MAAA,EAAQA,MAAA,CAAOC,IAAI;IACnBC,OAAA,EAAS;IACTC,QAAA,EAAUL,kBAAA,CAAmB;MAAEE,MAAA;MAAQJ,OAAA,EAAS,MAAMA,OAAA;MAAWC,OAAA,EAAS,MAAMA,OAAA;IAAU;EAC5F;AACF", "ignoreList": []}