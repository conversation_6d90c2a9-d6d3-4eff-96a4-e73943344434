import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { rtlLanguages } from '@payloadcms/translations';
import { ProgressBar, RootProvider } from '@payloadcms/ui';
import { getClientConfig } from '@payloadcms/ui/utilities/getClientConfig';
import { cookies as nextCookies } from 'next/headers.js';
import React from 'react';
import { getNavPrefs } from '../../elements/Nav/getNavPrefs.js';
import { getRequestTheme } from '../../utilities/getRequestTheme.js';
import { initReq } from '../../utilities/initReq.js';
import { checkDependencies } from './checkDependencies.js';
import { NestProviders } from './NestProviders.js';
export const metadata = {
  description: 'Generated by Next.js',
  title: 'Next.js'
};
export const RootLayout = async ({
  children,
  config: configPromise,
  htmlProps = {},
  importMap,
  serverFunction
}) => {
  checkDependencies();
  const {
    cookies,
    headers,
    languageCode,
    permissions,
    req,
    req: {
      payload: {
        config
      }
    }
  } = await initReq({
    configPromise,
    importMap,
    key: 'RootLayout'
  });
  const theme = getRequestTheme({
    config,
    cookies,
    headers
  });
  const dir = rtlLanguages.includes(languageCode) ? 'RTL' : 'LTR';
  const languageOptions = Object.entries(config.i18n.supportedLanguages || {}).reduce((acc, [language, languageConfig]) => {
    if (Object.keys(config.i18n.supportedLanguages).includes(language)) {
      acc.push({
        label: languageConfig.translations.general.thisLanguage,
        value: language
      });
    }
    return acc;
  }, []);
  async function switchLanguageServerAction(lang) {
    'use server';

    const cookies = await nextCookies();
    cookies.set({
      name: `${config.cookiePrefix || 'payload'}-lng`,
      path: '/',
      value: lang
    });
  }
  const navPrefs = await getNavPrefs(req);
  const clientConfig = getClientConfig({
    config,
    i18n: req.i18n,
    importMap
  });
  if (clientConfig.localization && config.localization && typeof config.localization.filterAvailableLocales === 'function') {
    clientConfig.localization.locales = (await config.localization.filterAvailableLocales({
      locales: config.localization.locales,
      req
    })).map(({
      toString,
      ...rest
    }) => rest);
    clientConfig.localization.localeCodes = config.localization.locales.map(({
      code
    }) => code);
  }
  return /*#__PURE__*/_jsxs("html", {
    "data-theme": theme,
    dir: dir,
    lang: languageCode,
    suppressHydrationWarning: config?.admin?.suppressHydrationWarning ?? false,
    ...htmlProps,
    children: [/*#__PURE__*/_jsx("head", {
      children: /*#__PURE__*/_jsx("style", {
        children: `@layer payload-default, payload;`
      })
    }), /*#__PURE__*/_jsxs("body", {
      children: [/*#__PURE__*/_jsxs(RootProvider, {
        config: clientConfig,
        dateFNSKey: req.i18n.dateFNSKey,
        fallbackLang: config.i18n.fallbackLanguage,
        isNavOpen: navPrefs?.open ?? true,
        languageCode: languageCode,
        languageOptions: languageOptions,
        locale: req.locale,
        permissions: permissions,
        serverFunction: serverFunction,
        switchLanguageServerAction: switchLanguageServerAction,
        theme: theme,
        translations: req.i18n.translations,
        user: req.user,
        children: [/*#__PURE__*/_jsx(ProgressBar, {}), Array.isArray(config.admin?.components?.providers) && config.admin?.components?.providers.length > 0 ? /*#__PURE__*/_jsx(NestProviders, {
          importMap: req.payload.importMap,
          providers: config.admin?.components?.providers,
          serverProps: {
            i18n: req.i18n,
            payload: req.payload,
            permissions,
            user: req.user
          },
          children: children
        }) : children]
      }), /*#__PURE__*/_jsx("div", {
        id: "portal"
      })]
    })]
  });
};
//# sourceMappingURL=index.js.map