{"version": 3, "file": "index.js", "names": ["c", "_c", "useDocumentInfo", "React", "baseClass", "VersionsPill", "$", "versionCount", "t0", "_jsx", "className", "children"], "sources": ["../../../../../../src/elements/DocumentHeader/Tabs/tabs/VersionsPill/index.tsx"], "sourcesContent": ["'use client'\nimport { useDocumentInfo } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'pill-version-count'\n\nexport const VersionsPill: React.FC = () => {\n  const { versionCount } = useDocumentInfo()\n\n  if (!versionCount) {\n    return null\n  }\n\n  return <span className={baseClass}>{versionCount}</span>\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,eAAe,QAAQ;AAChC,OAAOC,KAAA,MAAW;AAIlB,MAAMC,SAAA,GAAY;AAElB,OAAO,MAAMC,YAAA,GAAyBA,CAAA;EAAA,MAAAC,CAAA,GAAAL,EAAA;EACpC;IAAAM;EAAA,IAAyBL,eAAA;EAAA,KAEpBK,YAAA;IAAA;EAAA;EAAA,IAAAC,EAAA;EAAA,IAAAF,CAAA,QAAAC,YAAA;IAIEC,EAAA,GAAAC,IAAA,CAAC;MAAAC,SAAA,EAAAN,SAAA;MAAAO,QAAA,EAA4BJ;IAAA,C;;;;;;SAA7BC,E;CACT", "ignoreList": []}