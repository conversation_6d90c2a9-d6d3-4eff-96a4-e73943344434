{"version": 3, "file": "getPayloadHMR.js", "names": ["getPayload", "getPayloadHMR", "options", "result", "logger", "warn"], "sources": ["../../src/utilities/getPayloadHMR.ts"], "sourcesContent": ["import type { InitOptions, Payload } from 'payload'\n\nimport { getPayload } from 'payload'\n\n/**\n *  getPayloadHMR is no longer preferred.\n *  You can now use in all contexts:\n *  ```ts\n *   import { getPayload } from 'payload'\n *  ```\n * @deprecated\n */\nexport const getPayloadHMR = async (\n  options: Pick<InitOptions, 'config' | 'importMap'>,\n): Promise<Payload> => {\n  const result = await getPayload(options)\n\n  result.logger.warn(\n    \"Deprecation warning: getPayloadHMR is no longer preferred. You can now use `import { getPayload } from 'payload' in all contexts.\",\n  )\n\n  return result\n}\n"], "mappings": "AAEA,SAASA,UAAU,QAAQ;AAE3B;;;;;;;;AAQA,OAAO,MAAMC,aAAA,GAAgB,MAC3BC,OAAA;EAEA,MAAMC,MAAA,GAAS,MAAMH,UAAA,CAAWE,OAAA;EAEhCC,MAAA,CAAOC,MAAM,CAACC,IAAI,CAChB;EAGF,OAAOF,MAAA;AACT", "ignoreList": []}