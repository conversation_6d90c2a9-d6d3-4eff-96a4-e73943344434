{"version": 3, "file": "index.js", "names": ["Logout", "RenderServerComponent", "EntityType", "groupNavItems", "React", "NavHamburger", "NavWrapper", "baseClass", "getNavPrefs", "DefaultNavClient", "DefaultNav", "props", "documentSubViewType", "i18n", "locale", "params", "payload", "permissions", "req", "searchParams", "user", "viewType", "visibleEntities", "config", "admin", "components", "afterNavLinks", "beforeNavLinks", "logout", "collections", "globals", "groups", "filter", "slug", "includes", "map", "collection", "type", "entity", "global", "navPreferences", "LogoutComponent", "clientProps", "Component", "<PERSON><PERSON>", "Fallback", "importMap", "serverProps", "_jsxs", "className", "_jsx"], "sources": ["../../../src/elements/Nav/index.tsx"], "sourcesContent": ["import type { EntityToGroup } from '@payloadcms/ui/shared'\nimport type { PayloadRequest, ServerProps } from 'payload'\n\nimport { Logout } from '@payloadcms/ui'\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\nimport { EntityType, groupNavItems } from '@payloadcms/ui/shared'\nimport React from 'react'\n\nimport { NavHamburger } from './NavHamburger/index.js'\nimport { NavWrapper } from './NavWrapper/index.js'\nimport './index.scss'\n\nconst baseClass = 'nav'\n\nimport { getNavPrefs } from './getNavPrefs.js'\nimport { DefaultNavClient } from './index.client.js'\n\nexport type NavProps = {\n  req?: PayloadRequest\n} & ServerProps\n\nexport const DefaultNav: React.FC<NavProps> = async (props) => {\n  const {\n    documentSubViewType,\n    i18n,\n    locale,\n    params,\n    payload,\n    permissions,\n    req,\n    searchParams,\n    user,\n    viewType,\n    visibleEntities,\n  } = props\n\n  if (!payload?.config) {\n    return null\n  }\n\n  const {\n    admin: {\n      components: { afterNavLinks, beforeNavLinks, logout },\n    },\n    collections,\n    globals,\n  } = payload.config\n\n  const groups = groupNavItems(\n    [\n      ...collections\n        .filter(({ slug }) => visibleEntities.collections.includes(slug))\n        .map(\n          (collection) =>\n            ({\n              type: EntityType.collection,\n              entity: collection,\n            }) satisfies EntityToGroup,\n        ),\n      ...globals\n        .filter(({ slug }) => visibleEntities.globals.includes(slug))\n        .map(\n          (global) =>\n            ({\n              type: EntityType.global,\n              entity: global,\n            }) satisfies EntityToGroup,\n        ),\n    ],\n    permissions,\n    i18n,\n  )\n\n  const navPreferences = await getNavPrefs(req)\n\n  const LogoutComponent = RenderServerComponent({\n    clientProps: {\n      documentSubViewType,\n      viewType,\n    },\n    Component: logout?.Button,\n    Fallback: Logout,\n    importMap: payload.importMap,\n    serverProps: {\n      i18n,\n      locale,\n      params,\n      payload,\n      permissions,\n      searchParams,\n      user,\n    },\n  })\n\n  return (\n    <NavWrapper baseClass={baseClass}>\n      <nav className={`${baseClass}__wrap`}>\n        {RenderServerComponent({\n          clientProps: {\n            documentSubViewType,\n            viewType,\n          },\n          Component: beforeNavLinks,\n          importMap: payload.importMap,\n          serverProps: {\n            i18n,\n            locale,\n            params,\n            payload,\n            permissions,\n            searchParams,\n            user,\n          },\n        })}\n        <DefaultNavClient groups={groups} navPreferences={navPreferences} />\n        {RenderServerComponent({\n          clientProps: {\n            documentSubViewType,\n            viewType,\n          },\n          Component: afterNavLinks,\n          importMap: payload.importMap,\n          serverProps: {\n            i18n,\n            locale,\n            params,\n            payload,\n            permissions,\n            searchParams,\n            user,\n          },\n        })}\n        <div className={`${baseClass}__controls`}>{LogoutComponent}</div>\n      </nav>\n      <div className={`${baseClass}__header`}>\n        <div className={`${baseClass}__header-content`}>\n          <NavHamburger baseClass={baseClass} />\n        </div>\n      </div>\n    </NavWrapper>\n  )\n}\n"], "mappings": ";AAGA,SAASA,MAAM,QAAQ;AACvB,SAASC,qBAAqB,QAAQ;AACtC,SAASC,UAAU,EAAEC,aAAa,QAAQ;AAC1C,OAAOC,KAAA,MAAW;AAElB,SAASC,YAAY,QAAQ;AAC7B,SAASC,UAAU,QAAQ;AAG3B,MAAMC,SAAA,GAAY;AAElB,SAASC,WAAW,QAAQ;AAC5B,SAASC,gBAAgB,QAAQ;AAMjC,OAAO,MAAMC,UAAA,GAAiC,MAAOC,KAAA;EACnD,MAAM;IACJC,mBAAmB;IACnBC,IAAI;IACJC,MAAM;IACNC,MAAM;IACNC,OAAO;IACPC,WAAW;IACXC,GAAG;IACHC,YAAY;IACZC,IAAI;IACJC,QAAQ;IACRC;EAAe,CAChB,GAAGX,KAAA;EAEJ,IAAI,CAACK,OAAA,EAASO,MAAA,EAAQ;IACpB,OAAO;EACT;EAEA,MAAM;IACJC,KAAA,EAAO;MACLC,UAAA,EAAY;QAAEC,aAAa;QAAEC,cAAc;QAAEC;MAAM;IAAE,CACtD;IACDC,WAAW;IACXC;EAAO,CACR,GAAGd,OAAA,CAAQO,MAAM;EAElB,MAAMQ,MAAA,GAAS5B,aAAA,CACb,C,GACK0B,WAAA,CACAG,MAAM,CAAC,CAAC;IAAEC;EAAI,CAAE,KAAKX,eAAA,CAAgBO,WAAW,CAACK,QAAQ,CAACD,IAAA,GAC1DE,GAAG,CACDC,UAAA,KACE;IACCC,IAAA,EAAMnC,UAAA,CAAWkC,UAAU;IAC3BE,MAAA,EAAQF;EACV,K,GAEHN,OAAA,CACAE,MAAM,CAAC,CAAC;IAAEC;EAAI,CAAE,KAAKX,eAAA,CAAgBQ,OAAO,CAACI,QAAQ,CAACD,IAAA,GACtDE,GAAG,CACDI,MAAA,KACE;IACCF,IAAA,EAAMnC,UAAA,CAAWqC,MAAM;IACvBD,MAAA,EAAQC;EACV,IAEP,EACDtB,WAAA,EACAJ,IAAA;EAGF,MAAM2B,cAAA,GAAiB,MAAMhC,WAAA,CAAYU,GAAA;EAEzC,MAAMuB,eAAA,GAAkBxC,qBAAA,CAAsB;IAC5CyC,WAAA,EAAa;MACX9B,mBAAA;MACAS;IACF;IACAsB,SAAA,EAAWf,MAAA,EAAQgB,MAAA;IACnBC,QAAA,EAAU7C,MAAA;IACV8C,SAAA,EAAW9B,OAAA,CAAQ8B,SAAS;IAC5BC,WAAA,EAAa;MACXlC,IAAA;MACAC,MAAA;MACAC,MAAA;MACAC,OAAA;MACAC,WAAA;MACAE,YAAA;MACAC;IACF;EACF;EAEA,oBACE4B,KAAA,CAAC1C,UAAA;IAAWC,SAAA,EAAWA,SAAA;4BACrByC,KAAA,CAAC;MAAIC,SAAA,EAAW,GAAG1C,SAAA,QAAiB;iBACjCN,qBAAA,CAAsB;QACrByC,WAAA,EAAa;UACX9B,mBAAA;UACAS;QACF;QACAsB,SAAA,EAAWhB,cAAA;QACXmB,SAAA,EAAW9B,OAAA,CAAQ8B,SAAS;QAC5BC,WAAA,EAAa;UACXlC,IAAA;UACAC,MAAA;UACAC,MAAA;UACAC,OAAA;UACAC,WAAA;UACAE,YAAA;UACAC;QACF;MACF,I,aACA8B,IAAA,CAACzC,gBAAA;QAAiBsB,MAAA,EAAQA,MAAA;QAAQS,cAAA,EAAgBA;UACjDvC,qBAAA,CAAsB;QACrByC,WAAA,EAAa;UACX9B,mBAAA;UACAS;QACF;QACAsB,SAAA,EAAWjB,aAAA;QACXoB,SAAA,EAAW9B,OAAA,CAAQ8B,SAAS;QAC5BC,WAAA,EAAa;UACXlC,IAAA;UACAC,MAAA;UACAC,MAAA;UACAC,OAAA;UACAC,WAAA;UACAE,YAAA;UACAC;QACF;MACF,I,aACA8B,IAAA,CAAC;QAAID,SAAA,EAAW,GAAG1C,SAAA,YAAqB;kBAAGkC;;qBAE7CS,IAAA,CAAC;MAAID,SAAA,EAAW,GAAG1C,SAAA,UAAmB;gBACpC,aAAA2C,IAAA,CAAC;QAAID,SAAA,EAAW,GAAG1C,SAAA,kBAA2B;kBAC5C,aAAA2C,IAAA,CAAC7C,YAAA;UAAaE,SAAA,EAAWA;;;;;AAKnC", "ignoreList": []}