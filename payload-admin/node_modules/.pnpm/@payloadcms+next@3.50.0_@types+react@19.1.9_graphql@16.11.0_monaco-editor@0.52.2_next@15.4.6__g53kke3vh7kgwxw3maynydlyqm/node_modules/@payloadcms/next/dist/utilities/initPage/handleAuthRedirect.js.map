{"version": 3, "file": "handleAuthRedirect.js", "names": ["formatAdminURL", "qs", "handleAuthRedirect", "config", "route", "searchParams", "user", "admin", "routes", "login", "loginRouteFromConfig", "unauthorized", "unauthorizedRoute", "adminRoute", "redirect", "redirectRoute", "Object", "keys", "length", "stringify", "addQueryPrefix", "redirectTo", "path", "parsedLoginRouteSearchParams", "parse", "split", "searchParamsWithRedirect"], "sources": ["../../../src/utilities/initPage/handleAuthRedirect.ts"], "sourcesContent": ["import type { TypedUser } from 'payload'\n\nimport { formatAdminURL } from 'payload/shared'\nimport * as qs from 'qs-esm'\n\ntype Args = {\n  config\n  route: string\n  searchParams: { [key: string]: string | string[] }\n  user?: TypedUser\n}\n\nexport const handleAuthRedirect = ({ config, route, searchParams, user }: Args): string => {\n  const {\n    admin: {\n      routes: { login: loginRouteFromConfig, unauthorized: unauthorizedRoute },\n    },\n    routes: { admin: adminRoute },\n  } = config\n\n  if (searchParams && 'redirect' in searchParams) {\n    delete searchParams.redirect\n  }\n\n  const redirectRoute =\n    (route !== adminRoute ? route : '') +\n    (Object.keys(searchParams ?? {}).length > 0\n      ? `${qs.stringify(searchParams, { addQueryPrefix: true })}`\n      : '')\n\n  const redirectTo = formatAdminURL({\n    adminRoute,\n    path: user ? unauthorizedRoute : loginRouteFromConfig,\n  })\n\n  const parsedLoginRouteSearchParams = qs.parse(redirectTo.split('?')[1] ?? '')\n\n  const searchParamsWithRedirect = `${qs.stringify(\n    {\n      ...parsedLoginRouteSearchParams,\n      ...(redirectRoute ? { redirect: redirectRoute } : {}),\n    },\n    { addQueryPrefix: true },\n  )}`\n\n  return `${redirectTo.split('?')[0]}${searchParamsWithRedirect}`\n}\n"], "mappings": "AAEA,SAASA,cAAc,QAAQ;AAC/B,YAAYC,EAAA,MAAQ;AASpB,OAAO,MAAMC,kBAAA,GAAqBA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC,YAAY;EAAEC;AAAI,CAAQ;EAC5E,MAAM;IACJC,KAAA,EAAO;MACLC,MAAA,EAAQ;QAAEC,KAAA,EAAOC,oBAAoB;QAAEC,YAAA,EAAcC;MAAiB;IAAE,CACzE;IACDJ,MAAA,EAAQ;MAAED,KAAA,EAAOM;IAAU;EAAE,CAC9B,GAAGV,MAAA;EAEJ,IAAIE,YAAA,IAAgB,cAAcA,YAAA,EAAc;IAC9C,OAAOA,YAAA,CAAaS,QAAQ;EAC9B;EAEA,MAAMC,aAAA,GACJ,CAACX,KAAA,KAAUS,UAAA,GAAaT,KAAA,GAAQ,EAAC,KAChCY,MAAA,CAAOC,IAAI,CAACZ,YAAA,IAAgB,CAAC,GAAGa,MAAM,GAAG,IACtC,GAAGjB,EAAA,CAAGkB,SAAS,CAACd,YAAA,EAAc;IAAEe,cAAA,EAAgB;EAAK,IAAI,GACzD,EAAC;EAEP,MAAMC,UAAA,GAAarB,cAAA,CAAe;IAChCa,UAAA;IACAS,IAAA,EAAMhB,IAAA,GAAOM,iBAAA,GAAoBF;EACnC;EAEA,MAAMa,4BAAA,GAA+BtB,EAAA,CAAGuB,KAAK,CAACH,UAAA,CAAWI,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;EAE1E,MAAMC,wBAAA,GAA2B,GAAGzB,EAAA,CAAGkB,SAAS,CAC9C;IACE,GAAGI,4BAA4B;IAC/B,IAAIR,aAAA,GAAgB;MAAED,QAAA,EAAUC;IAAc,IAAI,CAAC,CAAC;EACtD,GACA;IAAEK,cAAA,EAAgB;EAAK,IACtB;EAEH,OAAO,GAAGC,UAAA,CAAWI,KAAK,CAAC,IAAI,CAAC,EAAE,GAAGC,wBAAA,EAA0B;AACjE", "ignoreList": []}