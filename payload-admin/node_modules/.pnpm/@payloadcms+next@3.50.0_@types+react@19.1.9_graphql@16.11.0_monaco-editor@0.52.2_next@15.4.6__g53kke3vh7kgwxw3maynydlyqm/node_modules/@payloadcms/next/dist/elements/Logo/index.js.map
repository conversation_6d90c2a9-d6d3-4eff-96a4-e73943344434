{"version": 3, "file": "index.js", "names": ["RenderServerComponent", "PayloadLogo", "Logo", "props", "i18n", "locale", "params", "payload", "permissions", "searchParams", "user", "admin", "components", "graphics", "CustomLogo", "undefined", "config", "Component", "Fallback", "importMap", "serverProps"], "sources": ["../../../src/elements/Logo/index.tsx"], "sourcesContent": ["import type { ServerProps } from 'payload'\nimport type React from 'react'\n\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\nimport { PayloadLogo } from '@payloadcms/ui/shared'\n\nexport const Logo: React.FC<ServerProps> = (props) => {\n  const { i18n, locale, params, payload, permissions, searchParams, user } = props\n\n  const {\n    admin: {\n      components: {\n        graphics: { Logo: CustomLogo } = {\n          Logo: undefined,\n        },\n      } = {},\n    } = {},\n  } = payload.config\n\n  return RenderServerComponent({\n    Component: CustomLogo,\n    Fallback: PayloadLogo,\n    importMap: payload.importMap,\n    serverProps: {\n      i18n,\n      locale,\n      params,\n      payload,\n      permissions,\n      searchParams,\n      user,\n    },\n  })\n}\n"], "mappings": "AAGA,SAASA,qBAAqB,QAAQ;AACtC,SAASC,WAAW,QAAQ;AAE5B,OAAO,MAAMC,IAAA,GAA+BC,KAAA;EAC1C,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAI,CAAE,GAAGP,KAAA;EAE3E,MAAM;IACJQ,KAAA,EAAO;MACLC,UAAA,EAAY;QACVC,QAAA,EAAU;UAAEX,IAAA,EAAMY;QAAU,CAAE,GAAG;UAC/BZ,IAAA,EAAMa;QACR;MAAC,CACF,GAAG,CAAC;IAAC,CACP,GAAG,CAAC;EAAC,CACP,GAAGR,OAAA,CAAQS,MAAM;EAElB,OAAOhB,qBAAA,CAAsB;IAC3BiB,SAAA,EAAWH,UAAA;IACXI,QAAA,EAAUjB,WAAA;IACVkB,SAAA,EAAWZ,OAAA,CAAQY,SAAS;IAC5BC,WAAA,EAAa;MACXhB,IAAA;MACAC,MAAA;MACAC,MAAA;MACAC,OAAA;MACAC,WAAA;MACAC,YAAA;MACAC;IACF;EACF;AACF", "ignoreList": []}