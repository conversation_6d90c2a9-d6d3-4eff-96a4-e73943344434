{"version": 3, "file": "utilities.js", "names": ["getNextRequestI18n", "getPayloadHMR", "addDataAndFileToRequest", "_addDataAndFileToRequest", "addLocalesToRequestFromData", "_addLocalesToRequestFromData", "createPayloadRequest", "_createPayloadRequest", "headersWithCors", "_headersWithCors", "mergeHeaders", "_mergeHeaders", "sanitizeLocales", "_sanitizeLocales"], "sources": ["../../src/exports/utilities.ts"], "sourcesContent": ["// NOTICE: Server-only utilities, do not import anything client-side here.\n\nexport { getNextRequestI18n } from '../utilities/getNextRequestI18n.js'\nexport { getPayloadHMR } from '../utilities/getPayloadHMR.js'\n\nimport {\n  addDataAndFileToRequest as _addDataAndFileToRequest,\n  addLocalesToRequestFromData as _addLocalesToRequestFromData,\n  createPayloadRequest as _createPayloadRequest,\n  headersWithCors as _headersWithCors,\n  mergeHeaders as _mergeHeaders,\n  sanitizeLocales as _sanitizeLocales,\n} from 'payload'\n\n/**\n * Use:\n * ```ts\n * import { mergeHeaders } from 'payload'\n * ```\n * @deprecated\n */\nexport const mergeHeaders = _mergeHeaders\n\n/**\n * @deprecated\n * Use:\n * ```ts\n * import { headersWithCors } from 'payload'\n * ```\n */\nexport const headersWithCors = _headersWithCors\n\n/**\n * @deprecated\n * Use:\n * ```ts\n * import { createPayloadRequest } from 'payload'\n * ```\n */\nexport const createPayloadRequest = _createPayloadRequest\n\n/**\n * @deprecated\n * Use:\n * ```ts\n * import { addDataAndFileToRequest } from 'payload'\n * ```\n */\nexport const addDataAndFileToRequest = _addDataAndFileToRequest\n\n/**\n * @deprecated\n * Use:\n * ```ts\n * import { sanitizeLocales } from 'payload'\n * ```\n */\nexport const sanitizeLocales = _sanitizeLocales\n\n/**\n * @deprecated\n * Use:\n * ```ts\n * import { addLocalesToRequestFromData } from 'payload'\n * ```\n */\nexport const addLocalesToRequestFromData = _addLocalesToRequestFromData\n"], "mappings": "AAAA;AAEA,SAASA,kBAAkB,QAAQ;AACnC,SAASC,aAAa,QAAQ;AAE9B,SACEC,uBAAA,IAA2BC,wBAAwB,EACnDC,2BAAA,IAA+BC,4BAA4B,EAC3DC,oBAAA,IAAwBC,qBAAqB,EAC7CC,eAAA,IAAmBC,gBAAgB,EACnCC,YAAA,IAAgBC,aAAa,EAC7BC,eAAA,IAAmBC,gBAAgB,QAC9B;AAEP;;;;;;;AAOA,OAAO,MAAMH,YAAA,GAAeC,aAAA;AAE5B;;;;;;;AAOA,OAAO,MAAMH,eAAA,GAAkBC,gBAAA;AAE/B;;;;;;;AAOA,OAAO,MAAMH,oBAAA,GAAuBC,qBAAA;AAEpC;;;;;;;AAOA,OAAO,MAAML,uBAAA,GAA0BC,wBAAA;AAEvC;;;;;;;AAOA,OAAO,MAAMS,eAAA,GAAkBC,gBAAA;AAE/B;;;;;;;AAOA,OAAO,MAAMT,2BAAA,GAA8BC,4BAAA", "ignoreList": []}