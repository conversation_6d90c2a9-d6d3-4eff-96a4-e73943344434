@import '~@payloadcms/ui/scss';

@layer payload-default {
  .nav {
    position: sticky;
    top: 0;
    left: 0;
    flex-shrink: 0;
    height: 100vh;
    width: var(--nav-width);
    border-right: 1px solid var(--theme-elevation-100);
    opacity: 0;
    overflow: hidden;

    [dir='rtl'] & {
      border-right: none;
      border-left: 1px solid var(--theme-elevation-100);
    }

    &--nav-animate {
      transition: opacity var(--nav-trans-time) ease-in-out;
    }

    &--nav-open {
      opacity: 1;
    }

    &__header {
      position: absolute;
      top: 0;
      width: 100vw;
      height: var(--app-header-height);
    }

    &__header-content {
      z-index: 1;
      position: relative;
      height: 100%;
      width: 100%;
    }

    &__mobile-close {
      display: none;
      background: none;
      border: 0;
      outline: 0;
      padding: base(0.8) 0;
    }

    &__scroll {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: var(--app-header-height) base(1) base(2) base(1);
      overflow-y: auto;

      // remove the scrollbar here to prevent layout shift as nav groups are toggled
      &::-webkit-scrollbar {
        display: none;
      }
    }

    &__wrap {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex-grow: 1;
    }

    &__label {
      color: var(--theme-elevation-400);
    }

    &__controls {
      margin-top: auto;
      margin-bottom: 0;

      > * {
        margin-top: base(1);
      }

      a:focus-visible {
        outline: var(--accessibility-outline);
      }
    }

    &__log-out {
      &:hover {
        g {
          transform: translateX(-#{base(0.125)});
        }
      }
    }

    &__link {
      display: flex;
      align-items: center;
      position: relative;
      padding-block: base(0.125);
      padding-inline-start: 0;
      padding-inline-end: base(1.5);
      text-decoration: none;

      &:focus:not(:focus-visible) {
        box-shadow: none;
        font-weight: 600;
      }

      &.active {
        font-weight: normal;
        padding-left: 0;
        font-weight: 600;
      }
    }

    a.nav__link {
      &:hover,
      &:focus-visible {
        text-decoration: underline;
      }
    }

    &__link:has(.nav__link-indicator) {
      font-weight: 600;
      padding-left: 0;
    }

    &__link-indicator {
      position: absolute;
      display: block;
      // top: 0;
      inset-inline-start: base(-1);
      width: 2px;
      height: 16px;
      border-start-end-radius: 2px;
      border-end-end-radius: 2px;
      background: var(--theme-text);
    }

    @include mid-break {
      &__scroll {
        padding: var(--app-header-height) base(0.5) base(2);
      }
    }

    @include small-break {
      &__scroll {
        padding: var(--app-header-height) var(--gutter-h) base(2);
      }

      &__link {
        font-size: base(0.875);
        line-height: base(1.5);
      }

      &__mobile-close {
        display: flex;
        align-items: center;
      }
    }
  }
}
