{"version": 3, "file": "index.js", "names": ["<PERSON><PERSON>", "RenderTitle", "React", "DocumentTabs", "baseClass", "DocumentHeader", "props", "collectionConfig", "globalConfig", "hideTabs", "permissions", "req", "_jsxs", "className", "_jsx"], "sources": ["../../../src/elements/DocumentHeader/index.tsx"], "sourcesContent": ["import type { I18n } from '@payloadcms/translations'\nimport type {\n  PayloadRequest,\n  SanitizedCollectionConfig,\n  SanitizedGlobalConfig,\n  SanitizedPermissions,\n} from 'payload'\n\nimport { Gutter, RenderTitle } from '@payloadcms/ui'\nimport React from 'react'\n\nimport { DocumentTabs } from './Tabs/index.js'\nimport './index.scss'\n\nconst baseClass = `doc-header`\n\nexport const DocumentHeader: React.FC<{\n  collectionConfig?: SanitizedCollectionConfig\n  globalConfig?: SanitizedGlobalConfig\n  hideTabs?: boolean\n  permissions: SanitizedPermissions\n  req: PayloadRequest\n}> = (props) => {\n  const { collectionConfig, globalConfig, hideTabs, permissions, req } = props\n\n  return (\n    <Gutter className={baseClass}>\n      <RenderTitle className={`${baseClass}__title`} />\n      {!hideTabs && (\n        <DocumentTabs\n          collectionConfig={collectionConfig}\n          globalConfig={globalConfig}\n          permissions={permissions}\n          req={req}\n        />\n      )}\n    </Gutter>\n  )\n}\n"], "mappings": ";AAQA,SAASA,MAAM,EAAEC,WAAW,QAAQ;AACpC,OAAOC,KAAA,MAAW;AAElB,SAASC,YAAY,QAAQ;AAG7B,MAAMC,SAAA,GAAY,YAAY;AAE9B,OAAO,MAAMC,cAAA,GAMPC,KAAA;EACJ,MAAM;IAAEC,gBAAgB;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAG,CAAE,GAAGL,KAAA;EAEvE,oBACEM,KAAA,CAACZ,MAAA;IAAOa,SAAA,EAAWT,SAAA;4BACjBU,IAAA,CAACb,WAAA;MAAYY,SAAA,EAAW,GAAGT,SAAA;QAC1B,CAACK,QAAA,iBACAK,IAAA,CAACX,YAAA;MACCI,gBAAA,EAAkBA,gBAAA;MAClBC,YAAA,EAAcA,YAAA;MACdE,WAAA,EAAaA,WAAA;MACbC,GAAA,EAAKA;;;AAKf", "ignoreList": []}