{"version": 3, "file": "TabLink.js", "names": ["c", "_c", "<PERSON><PERSON>", "useParams", "usePathname", "useSearchParams", "formatAdminURL", "React", "DocumentTabLink", "t0", "$", "adminRoute", "aria<PERSON><PERSON><PERSON>", "baseClass", "children", "href", "hrefFromProps", "isActive", "isActiveFromProps", "newTab", "pathname", "params", "searchParams", "locale", "get", "entityType", "entitySlug", "segmentThree", "segmentFour", "segments", "isCollection", "t1", "t2", "path", "<PERSON><PERSON><PERSON><PERSON>", "hrefWithLocale", "t3", "startsWith", "t4", "t5", "filter", "Boolean", "t6", "join", "t7", "t8", "undefined", "t9", "_jsx", "buttonStyle", "className", "disabled", "el", "margin", "size", "to"], "sources": ["../../../../../src/elements/DocumentHeader/Tabs/Tab/TabLink.tsx"], "sourcesContent": ["'use client'\nimport type { SanitizedConfig } from 'payload'\n\nimport { Button } from '@payloadcms/ui'\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js'\nimport { formatAdminURL } from 'payload/shared'\nimport React from 'react'\n\nexport const DocumentTabLink: React.FC<{\n  adminRoute: SanitizedConfig['routes']['admin']\n  ariaLabel?: string\n  baseClass: string\n  children?: React.ReactNode\n  href: string\n  isActive?: boolean\n  newTab?: boolean\n}> = ({\n  adminRoute,\n  ariaLabel,\n  baseClass,\n  children,\n  href: hrefFromProps,\n  isActive: isActiveFromProps,\n  newTab,\n}) => {\n  const pathname = usePathname()\n  const params = useParams()\n\n  const searchParams = useSearchParams()\n\n  const locale = searchParams.get('locale')\n\n  const [entityType, entitySlug, segmentThree, segmentFour, ...rest] = params.segments || []\n  const isCollection = entityType === 'collections'\n\n  let docPath = formatAdminURL({\n    adminRoute,\n    path: `/${isCollection ? 'collections' : 'globals'}/${entitySlug}`,\n  })\n\n  if (isCollection) {\n    if (segmentThree === 'trash' && segmentFour) {\n      docPath += `/trash/${segmentFour}`\n    } else if (segmentThree) {\n      docPath += `/${segmentThree}`\n    }\n  }\n\n  const href = `${docPath}${hrefFromProps}`\n  // separated the two so it doesn't break checks against pathname\n  const hrefWithLocale = `${href}${locale ? `?locale=${locale}` : ''}`\n\n  const isActive =\n    (href === docPath && pathname === docPath) ||\n    (href !== docPath && pathname.startsWith(href)) ||\n    isActiveFromProps\n\n  return (\n    <Button\n      aria-label={ariaLabel}\n      buttonStyle=\"tab\"\n      className={[baseClass, isActive && `${baseClass}--active`].filter(Boolean).join(' ')}\n      disabled={isActive}\n      el={!isActive || href !== pathname ? 'link' : 'div'}\n      margin={false}\n      newTab={newTab}\n      size=\"medium\"\n      to={!isActive || href !== pathname ? hrefWithLocale : undefined}\n    >\n      {children}\n    </Button>\n  )\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,MAAM,QAAQ;AACvB,SAASC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ;AACxD,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AAElB,OAAO,MAAMC,eAAA,GAQRC,EAAA;EAAA,MAAAC,CAAA,GAAAT,EAAA;EAAC;IAAAU,UAAA;IAAAC,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,IAAA,EAAAC,aAAA;IAAAC,QAAA,EAAAC,iBAAA;IAAAC;EAAA,IAAAV,EAQL;EACC,MAAAW,QAAA,GAAiBhB,WAAA;EACjB,MAAAiB,MAAA,GAAelB,SAAA;EAEf,MAAAmB,YAAA,GAAqBjB,eAAA;EAErB,MAAAkB,MAAA,GAAeD,YAAA,CAAAE,GAAA,CAAiB;EAEhC,OAAAC,UAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,WAAA,IAAqEP,MAAA,CAAAQ,QAAA,MAAqB;EAC1F,MAAAC,YAAA,GAAqBL,UAAA,KAAe;EAI5B,MAAAM,EAAA,OAAID,YAAA,GAAe,gBAAgB,aAAaJ,UAAA,EAAY;EAAA,IAAAM,EAAA;EAAA,IAAAtB,CAAA,QAAAC,UAAA,IAAAD,CAAA,QAAAqB,EAAA;IAFtDC,EAAA,GAAA1B,cAAA;MAAAK,UAAA;MAAAsB,IAAA,EAENF;IAA4D,CACpE;IAAArB,CAAA,MAAAC,UAAA;IAAAD,CAAA,MAAAqB,EAAA;IAAArB,CAAA,MAAAsB,EAAA;EAAA;IAAAA,EAAA,GAAAtB,CAAA;EAAA;EAHA,IAAAwB,OAAA,GAAcF,EAGd;EAAA,IAEIF,YAAA;IAAA,IACEH,YAAA,KAAiB,WAAWC,WAAA;MAC9BM,OAAA,GAAAA,OAAA,GAAW,UAAUN,WAAA,EAAa;IAAA;MAAA,IACzBD,YAAA;QACTO,OAAA,GAAAA,OAAA,GAAW,IAAIP,YAAA,EAAc;MAAA;IAAA;EAAA;EAIjC,MAAAZ,IAAA,GAAa,GAAGmB,OAAA,GAAUlB,aAAA,EAAe;EAEzC,MAAAmB,cAAA,GAAuB,GAAGpB,IAAA,GAAOQ,MAAA,GAAS,WAAWA,MAAA,EAAQ,GAAG,IAAI;EAAA,IAAAa,EAAA;EAAA,IAAA1B,CAAA,QAAAwB,OAAA,IAAAxB,CAAA,QAAAK,IAAA,IAAAL,CAAA,QAAAQ,iBAAA,IAAAR,CAAA,QAAAU,QAAA;IAGlEgB,EAAA,GAAArB,IAAC,KAASmB,OAAA,IAAWd,QAAA,KAAac,OAAA,IACjCnB,IAAA,KAASmB,OAAA,IAAWd,QAAA,CAAAiB,UAAA,CAAoBtB,IAAA,KACzCG,iBAAA;IAAAR,CAAA,MAAAwB,OAAA;IAAAxB,CAAA,MAAAK,IAAA;IAAAL,CAAA,MAAAQ,iBAAA;IAAAR,CAAA,MAAAU,QAAA;IAAAV,CAAA,MAAA0B,EAAA;EAAA;IAAAA,EAAA,GAAA1B,CAAA;EAAA;EAHF,MAAAO,QAAA,GACEmB,EAEA;EAMyB,MAAAE,EAAA,GAAArB,QAAA,IAAY,GAAGJ,SAAA,UAAmB;EAAA,IAAA0B,EAAA;EAAA,IAAA7B,CAAA,QAAAG,SAAA,IAAAH,CAAA,QAAA4B,EAAA;IAA9CC,EAAA,IAAC1B,SAAA,EAAWyB,EAAkC,EAAAE,MAAA,CAAAC,OAAS;IAAA/B,CAAA,MAAAG,SAAA;IAAAH,CAAA,MAAA4B,EAAA;IAAA5B,CAAA,OAAA6B,EAAA;EAAA;IAAAA,EAAA,GAAA7B,CAAA;EAAA;EAAvD,MAAAgC,EAAA,GAAAH,EAAuD,CAAAI,IAAA,CAAc;EAE5E,MAAAC,EAAA,IAAC3B,QAAA,IAAYF,IAAA,KAASK,QAAA,GAAW,SAAS;EAI1C,MAAAyB,EAAA,IAAC5B,QAAA,IAAYF,IAAA,KAASK,QAAA,GAAWe,cAAA,GAAAW,SAAiB;EAAA,IAAAC,EAAA;EAAA,IAAArC,CAAA,SAAAE,SAAA,IAAAF,CAAA,SAAAI,QAAA,IAAAJ,CAAA,SAAAO,QAAA,IAAAP,CAAA,SAAAS,MAAA,IAAAT,CAAA,SAAAgC,EAAA,IAAAhC,CAAA,SAAAkC,EAAA,IAAAlC,CAAA,SAAAmC,EAAA;IATxDE,EAAA,GAAAC,IAAA,CAAA9C,MAAA;MAAA,cACcU,SAAA;MAAAqC,WAAA,EACA;MAAAC,SAAA,EACDR,EAAqE;MAAAS,QAAA,EACtElC,QAAA;MAAAmC,EAAA,EACNR,EAA0C;MAAAS,MAAA;MAAAlC,MAAA;MAAAmC,IAAA,EAGzC;MAAAC,EAAA,EACDV,EAAkD;MAAA/B;IAAA,C;;;;;;;;;;;;SATxDiC,E;CAcJ", "ignoreList": []}