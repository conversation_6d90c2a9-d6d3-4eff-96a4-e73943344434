{"version": 3, "file": "NestProviders.js", "names": ["RenderServerComponent", "React", "NestProviders", "children", "importMap", "providers", "serverProps", "clientProps", "length", "_jsx", "slice", "Component"], "sources": ["../../../src/layouts/Root/NestProviders.tsx"], "sourcesContent": ["import type { Config, ImportMap, ServerProps } from 'payload'\n\nimport { RenderServerComponent } from '@payloadcms/ui/elements/RenderServerComponent'\nimport '@payloadcms/ui/scss/app.scss'\nimport React from 'react'\n\ntype Args = {\n  readonly children: React.ReactNode\n  readonly importMap: ImportMap\n  readonly providers: Config['admin']['components']['providers']\n  readonly serverProps: ServerProps\n}\n\nexport function NestProviders({\n  children,\n  importMap,\n  providers,\n  serverProps,\n}: Args): React.ReactNode {\n  return RenderServerComponent({\n    clientProps: {\n      children:\n        providers.length > 1 ? (\n          <NestProviders\n            importMap={importMap}\n            providers={providers.slice(1)}\n            serverProps={serverProps}\n          >\n            {children}\n          </NestProviders>\n        ) : (\n          children\n        ),\n    },\n    Component: providers[0],\n    importMap,\n    serverProps,\n  })\n}\n"], "mappings": ";AAEA,SAASA,qBAAqB,QAAQ;AAEtC,OAAOC,KAAA,MAAW;AASlB,OAAO,SAASC,cAAc;EAC5BC,QAAQ;EACRC,SAAS;EACTC,SAAS;EACTC;AAAW,CACN;EACL,OAAON,qBAAA,CAAsB;IAC3BO,WAAA,EAAa;MACXJ,QAAA,EACEE,SAAA,CAAUG,MAAM,GAAG,iBACjBC,IAAA,CAACP,aAAA;QACCE,SAAA,EAAWA,SAAA;QACXC,SAAA,EAAWA,SAAA,CAAUK,KAAK,CAAC;QAC3BJ,WAAA,EAAaA,WAAA;kBAEZH;WAGHA;IAEN;IACAQ,SAAA,EAAWN,SAAS,CAAC,EAAE;IACvBD,SAAA;IACAE;EACF;AACF", "ignoreList": []}