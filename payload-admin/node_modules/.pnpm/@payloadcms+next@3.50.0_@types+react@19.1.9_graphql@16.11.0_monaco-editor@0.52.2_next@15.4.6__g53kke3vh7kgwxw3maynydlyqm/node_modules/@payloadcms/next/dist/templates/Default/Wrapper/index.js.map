{"version": 3, "file": "index.js", "names": ["c", "_c", "useNav", "React", "Wrapper", "props", "$", "baseClass", "children", "className", "hydrated", "navOpen", "shouldAnimate", "t0", "t1", "t2", "t3", "filter", "Boolean", "t4", "join", "t5", "_jsx"], "sources": ["../../../../src/templates/Default/Wrapper/index.tsx"], "sourcesContent": ["'use client'\nimport { useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\nexport const Wrapper: React.FC<{\n  baseClass?: string\n  children?: React.ReactNode\n  className?: string\n}> = (props) => {\n  const { baseClass, children, className } = props\n  const { hydrated, navOpen, shouldAnimate } = useNav()\n\n  return (\n    <div\n      className={[\n        baseClass,\n        className,\n        navOpen && `${baseClass}--nav-open`,\n        shouldAnimate && `${baseClass}--nav-animate`,\n        hydrated && `${baseClass}--nav-hydrated`,\n      ]\n        .filter(Boolean)\n        .join(' ')}\n    >\n      {children}\n    </div>\n  )\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,MAAM,QAAQ;AACvB,OAAOC,KAAA,MAAW;AAIlB,OAAO,MAAMC,OAAA,GAIRC,KAAA;EAAA,MAAAC,CAAA,GAAAL,EAAA;EACH;IAAAM,SAAA;IAAAC,QAAA;IAAAC;EAAA,IAA2CJ,KAAA;EAC3C;IAAAK,QAAA;IAAAC,OAAA;IAAAC;EAAA,IAA6CV,MAAA;EAOvC,MAAAW,EAAA,GAAAF,OAAA,IAAW,GAAGJ,SAAA,YAAqB;EACnC,MAAAO,EAAA,GAAAF,aAAA,IAAiB,GAAGL,SAAA,eAAwB;EAC5C,MAAAQ,EAAA,GAAAL,QAAA,IAAY,GAAGH,SAAA,gBAAyB;EAAA,IAAAS,EAAA;EAAA,IAAAV,CAAA,QAAAC,SAAA,IAAAD,CAAA,QAAAG,SAAA,IAAAH,CAAA,QAAAO,EAAA,IAAAP,CAAA,QAAAQ,EAAA,IAAAR,CAAA,QAAAS,EAAA;IAL/BC,EAAA,IACTT,SAAA,EACAE,SAAA,EACAI,EAAmC,EACnCC,EAA4C,EAC5CC,EAAwC,EAAAE,MAAA,CAAAC,OAEhC;IAAAZ,CAAA,MAAAC,SAAA;IAAAD,CAAA,MAAAG,SAAA;IAAAH,CAAA,MAAAO,EAAA;IAAAP,CAAA,MAAAQ,EAAA;IAAAR,CAAA,MAAAS,EAAA;IAAAT,CAAA,MAAAU,EAAA;EAAA;IAAAA,EAAA,GAAAV,CAAA;EAAA;EAPC,MAAAa,EAAA,GAAAH,EAOD,CAAAI,IAAA,CACF;EAAA,IAAAC,EAAA;EAAA,IAAAf,CAAA,QAAAE,QAAA,IAAAF,CAAA,QAAAa,EAAA;IATVE,EAAA,GAAAC,IAAA,CAAC;MAAAb,SAAA,EACYU,EAQH;MAAAX;IAAA,C;;;;;;;SATVa,E;CAcJ", "ignoreList": []}