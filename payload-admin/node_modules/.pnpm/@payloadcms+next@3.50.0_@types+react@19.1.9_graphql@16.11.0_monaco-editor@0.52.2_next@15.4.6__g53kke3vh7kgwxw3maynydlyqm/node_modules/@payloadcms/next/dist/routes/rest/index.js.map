{"version": 3, "file": "index.js", "names": ["handleEndpoints", "generateOGImage", "initedOGEndpoint", "handlerBuilder", "config", "request", "args", "awaitedConfig", "endpoints", "some", "endpoint", "path", "method", "push", "handler", "awaitedParams", "params", "response", "routes", "api", "slug", "join", "undefined", "OPTIONS", "GET", "POST", "DELETE", "PATCH", "PUT"], "sources": ["../../../src/routes/rest/index.ts"], "sourcesContent": ["import { handleEndpoints, type SanitizedConfig } from 'payload'\n\nimport { generateOGImage } from './og/index.js'\n\nlet initedOGEndpoint = false\n\nconst handlerBuilder =\n  (config: Promise<SanitizedConfig> | SanitizedConfig) =>\n  async (\n    request: Request,\n    args: {\n      params: Promise<{ slug: string[] }>\n    },\n  ): Promise<Response> => {\n    const awaitedConfig = await config\n\n    // Add this endpoint only when using Next.js, still can be overriden.\n    if (\n      initedOGEndpoint === false &&\n      !awaitedConfig.endpoints.some(\n        (endpoint) => endpoint.path === '/og' && endpoint.method === 'get',\n      )\n    ) {\n      awaitedConfig.endpoints.push({\n        handler: generateOGImage,\n        method: 'get',\n        path: '/og',\n      })\n    }\n\n    initedOGEndpoint = true\n\n    const awaitedParams = await args.params\n\n    const response = await handleEndpoints({\n      config,\n      path: awaitedParams\n        ? `${awaitedConfig.routes.api}/${awaitedParams.slug.join('/')}`\n        : undefined,\n      request,\n    })\n\n    return response\n  }\n\nexport const OPTIONS = handlerBuilder\n\nexport const GET = handlerBuilder\n\nexport const POST = handlerBuilder\n\nexport const DELETE = handlerBuilder\n\nexport const PATCH = handlerBuilder\n\nexport const PUT = handlerBuilder\n"], "mappings": "AAAA,SAASA,eAAe,QAA8B;AAEtD,SAASC,eAAe,QAAQ;AAEhC,IAAIC,gBAAA,GAAmB;AAEvB,MAAMC,cAAA,GACHC,MAAA,IACD,OACEC,OAAA,EACAC,IAAA;EAIA,MAAMC,aAAA,GAAgB,MAAMH,MAAA;EAE5B;EACA,IACEF,gBAAA,KAAqB,SACrB,CAACK,aAAA,CAAcC,SAAS,CAACC,IAAI,CAC1BC,QAAA,IAAaA,QAAA,CAASC,IAAI,KAAK,SAASD,QAAA,CAASE,MAAM,KAAK,QAE/D;IACAL,aAAA,CAAcC,SAAS,CAACK,IAAI,CAAC;MAC3BC,OAAA,EAASb,eAAA;MACTW,MAAA,EAAQ;MACRD,IAAA,EAAM;IACR;EACF;EAEAT,gBAAA,GAAmB;EAEnB,MAAMa,aAAA,GAAgB,MAAMT,IAAA,CAAKU,MAAM;EAEvC,MAAMC,QAAA,GAAW,MAAMjB,eAAA,CAAgB;IACrCI,MAAA;IACAO,IAAA,EAAMI,aAAA,GACF,GAAGR,aAAA,CAAcW,MAAM,CAACC,GAAG,IAAIJ,aAAA,CAAcK,IAAI,CAACC,IAAI,CAAC,MAAM,GAC7DC,SAAA;IACJjB;EACF;EAEA,OAAOY,QAAA;AACT;AAEF,OAAO,MAAMM,OAAA,GAAUpB,cAAA;AAEvB,OAAO,MAAMqB,GAAA,GAAMrB,cAAA;AAEnB,OAAO,MAAMsB,IAAA,GAAOtB,cAAA;AAEpB,OAAO,MAAMuB,MAAA,GAASvB,cAAA;AAEtB,OAAO,MAAMwB,KAAA,GAAQxB,cAAA;AAErB,OAAO,MAAMyB,GAAA,GAAMzB,cAAA", "ignoreList": []}