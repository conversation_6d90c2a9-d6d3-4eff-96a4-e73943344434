{"version": 3, "file": "getRequestLocale.js", "names": ["upsertPreferences", "findLocaleFromCode", "getPreferences", "getRequestLocale", "req", "payload", "config", "localization", "localeFromParams", "query", "locale", "key", "value", "user", "id", "collection", "defaultLocale", "undefined"], "sources": ["../../src/utilities/getRequestLocale.ts"], "sourcesContent": ["import type { Locale, PayloadRequest } from 'payload'\n\nimport { upsertPreferences } from '@payloadcms/ui/rsc'\nimport { findLocaleFromCode } from '@payloadcms/ui/shared'\n\nimport { getPreferences } from './getPreferences.js'\n\ntype GetRequestLocalesArgs = {\n  req: PayloadRequest\n}\n\nexport async function getRequestLocale({ req }: GetRequestLocalesArgs): Promise<Locale> {\n  if (req.payload.config.localization) {\n    const localeFromParams = req.query.locale as string | undefined\n\n    if (localeFromParams) {\n      await upsertPreferences<Locale['code']>({ key: 'locale', req, value: localeFromParams })\n    }\n\n    return (\n      (req.user &&\n        findLocaleFromCode(\n          req.payload.config.localization,\n          localeFromParams ||\n            (\n              await getPreferences<Locale['code']>(\n                'locale',\n                req.payload,\n                req.user.id,\n                req.user.collection,\n              )\n            )?.value,\n        )) ||\n      findLocaleFromCode(\n        req.payload.config.localization,\n        req.payload.config.localization.defaultLocale || 'en',\n      )\n    )\n  }\n\n  return undefined\n}\n"], "mappings": "AAEA,SAASA,iBAAiB,QAAQ;AAClC,SAASC,kBAAkB,QAAQ;AAEnC,SAASC,cAAc,QAAQ;AAM/B,OAAO,eAAeC,iBAAiB;EAAEC;AAAG,CAAyB;EACnE,IAAIA,GAAA,CAAIC,OAAO,CAACC,MAAM,CAACC,YAAY,EAAE;IACnC,MAAMC,gBAAA,GAAmBJ,GAAA,CAAIK,KAAK,CAACC,MAAM;IAEzC,IAAIF,gBAAA,EAAkB;MACpB,MAAMR,iBAAA,CAAkC;QAAEW,GAAA,EAAK;QAAUP,GAAA;QAAKQ,KAAA,EAAOJ;MAAiB;IACxF;IAEA,OACEJ,GAAC,CAAIS,IAAI,IACPZ,kBAAA,CACEG,GAAA,CAAIC,OAAO,CAACC,MAAM,CAACC,YAAY,EAC/BC,gBAAA,IAEI,OAAMN,cAAA,CACJ,UACAE,GAAA,CAAIC,OAAO,EACXD,GAAA,CAAIS,IAAI,CAACC,EAAE,EACXV,GAAA,CAAIS,IAAI,CAACE,UAAU,CACrB,GACCH,KAAA,KAETX,kBAAA,CACEG,GAAA,CAAIC,OAAO,CAACC,MAAM,CAACC,YAAY,EAC/BH,GAAA,CAAIC,OAAO,CAACC,MAAM,CAACC,YAAY,CAACS,aAAa,IAAI;EAGvD;EAEA,OAAOC,SAAA;AACT", "ignoreList": []}