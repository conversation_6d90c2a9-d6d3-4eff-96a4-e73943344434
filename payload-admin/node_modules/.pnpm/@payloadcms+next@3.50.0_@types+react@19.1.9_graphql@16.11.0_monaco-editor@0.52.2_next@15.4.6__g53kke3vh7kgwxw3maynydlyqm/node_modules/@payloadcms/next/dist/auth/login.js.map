{"version": 3, "file": "login.js", "names": ["getPayload", "setPayloadAuthCookie", "login", "collection", "config", "email", "password", "username", "payload", "cron", "authConfig", "collections", "auth", "Error", "loginWithUsername", "allowEmailLogin", "loginData", "result", "data", "token", "cookiePrefix", "removeTokenFromResponses"], "sources": ["../../src/auth/login.ts"], "sourcesContent": ["'use server'\n\nimport type { CollectionSlug } from 'payload'\n\nimport { getPayload } from 'payload'\n\nimport { setPayloadAuthCookie } from '../utilities/setPayloadAuthCookie.js'\n\ntype LoginWithEmail = {\n  collection: CollectionSlug\n  config: any\n  email: string\n  password: string\n  username?: never\n}\n\ntype LoginWithUsername = {\n  collection: CollectionSlug\n  config: any\n  email?: never\n  password: string\n  username: string\n}\ntype LoginArgs = LoginWithEmail | LoginWithUsername\n\nexport async function login({ collection, config, email, password, username }: LoginArgs): Promise<{\n  token?: string\n  user: any\n}> {\n  const payload = await getPayload({ config, cron: true })\n\n  const authConfig = payload.collections[collection]?.config.auth\n\n  if (!authConfig) {\n    throw new Error(`No auth config found for collection: ${collection}`)\n  }\n\n  const loginWithUsername = authConfig?.loginWithUsername ?? false\n\n  if (loginWithUsername) {\n    if (loginWithUsername.allowEmailLogin) {\n      if (!email && !username) {\n        throw new Error('Email or username is required.')\n      }\n    } else {\n      if (!username) {\n        throw new Error('Username is required.')\n      }\n    }\n  } else {\n    if (!email) {\n      throw new Error('Email is required.')\n    }\n  }\n\n  let loginData\n\n  if (loginWithUsername) {\n    loginData = username ? { password, username } : { email, password }\n  } else {\n    loginData = { email, password }\n  }\n\n  const result = await payload.login({\n    collection,\n    data: loginData,\n  })\n\n  if (result.token) {\n    await setPayloadAuthCookie({\n      authConfig,\n      cookiePrefix: payload.config.cookiePrefix,\n      token: result.token,\n    })\n  }\n\n  if ('removeTokenFromResponses' in config && config.removeTokenFromResponses) {\n    delete result.token\n  }\n\n  return result\n}\n"], "mappings": "AAAA;;AAIA,SAASA,UAAU,QAAQ;AAE3B,SAASC,oBAAoB,QAAQ;AAmBrC,OAAO,eAAeC,MAAM;EAAEC,UAAU;EAAEC,MAAM;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAQ,CAAa;EAItF,MAAMC,OAAA,GAAU,MAAMR,UAAA,CAAW;IAAEI,MAAA;IAAQK,IAAA,EAAM;EAAK;EAEtD,MAAMC,UAAA,GAAaF,OAAA,CAAQG,WAAW,CAACR,UAAA,CAAW,EAAEC,MAAA,CAAOQ,IAAA;EAE3D,IAAI,CAACF,UAAA,EAAY;IACf,MAAM,IAAIG,KAAA,CAAM,wCAAwCV,UAAA,EAAY;EACtE;EAEA,MAAMW,iBAAA,GAAoBJ,UAAA,EAAYI,iBAAA,IAAqB;EAE3D,IAAIA,iBAAA,EAAmB;IACrB,IAAIA,iBAAA,CAAkBC,eAAe,EAAE;MACrC,IAAI,CAACV,KAAA,IAAS,CAACE,QAAA,EAAU;QACvB,MAAM,IAAIM,KAAA,CAAM;MAClB;IACF,OAAO;MACL,IAAI,CAACN,QAAA,EAAU;QACb,MAAM,IAAIM,KAAA,CAAM;MAClB;IACF;EACF,OAAO;IACL,IAAI,CAACR,KAAA,EAAO;MACV,MAAM,IAAIQ,KAAA,CAAM;IAClB;EACF;EAEA,IAAIG,SAAA;EAEJ,IAAIF,iBAAA,EAAmB;IACrBE,SAAA,GAAYT,QAAA,GAAW;MAAED,QAAA;MAAUC;IAAS,IAAI;MAAEF,KAAA;MAAOC;IAAS;EACpE,OAAO;IACLU,SAAA,GAAY;MAAEX,KAAA;MAAOC;IAAS;EAChC;EAEA,MAAMW,MAAA,GAAS,MAAMT,OAAA,CAAQN,KAAK,CAAC;IACjCC,UAAA;IACAe,IAAA,EAAMF;EACR;EAEA,IAAIC,MAAA,CAAOE,KAAK,EAAE;IAChB,MAAMlB,oBAAA,CAAqB;MACzBS,UAAA;MACAU,YAAA,EAAcZ,OAAA,CAAQJ,MAAM,CAACgB,YAAY;MACzCD,KAAA,EAAOF,MAAA,CAAOE;IAChB;EACF;EAEA,IAAI,8BAA8Bf,MAAA,IAAUA,MAAA,CAAOiB,wBAAwB,EAAE;IAC3E,OAAOJ,MAAA,CAAOE,KAAK;EACrB;EAEA,OAAOF,MAAA;AACT", "ignoreList": []}