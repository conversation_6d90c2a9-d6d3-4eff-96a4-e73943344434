import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { G<PERSON>, RenderTitle } from '@payloadcms/ui';
import React from 'react';
import { DocumentTabs } from './Tabs/index.js';
const baseClass = `doc-header`;
export const DocumentHeader = props => {
  const {
    collectionConfig,
    globalConfig,
    hideTabs,
    permissions,
    req
  } = props;
  return /*#__PURE__*/_jsxs(<PERSON><PERSON>, {
    className: baseClass,
    children: [/*#__PURE__*/_jsx(RenderTitle, {
      className: `${baseClass}__title`
    }), !hideTabs && /*#__PURE__*/_jsx(DocumentTabs, {
      collectionConfig: collectionConfig,
      globalConfig: globalConfig,
      permissions: permissions,
      req: req
    })]
  });
};
//# sourceMappingURL=index.js.map