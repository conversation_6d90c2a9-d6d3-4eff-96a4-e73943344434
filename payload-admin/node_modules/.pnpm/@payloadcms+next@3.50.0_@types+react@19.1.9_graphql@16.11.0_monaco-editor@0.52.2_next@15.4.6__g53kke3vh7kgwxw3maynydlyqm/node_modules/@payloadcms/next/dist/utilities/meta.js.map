{"version": 3, "file": "meta.js", "names": ["payloadFaviconDark", "payloadFaviconLight", "staticOGImage", "qs", "defaultOpenGraph", "description", "siteName", "title", "generateMetadata", "args", "defaultOGImageType", "serverURL", "titleSuffix", "rest", "incomingMetadata", "icons", "type", "rel", "sizes", "url", "src", "media", "metaTitle", "filter", "Boolean", "join", "ogTitle", "openGraph", "mergedOpenGraph", "images", "alt", "height", "stringify", "addQueryPrefix", "width", "Promise", "resolve", "metadataBase", "URL", "process", "env", "PAYLOAD_PUBLIC_SERVER_URL", "PORT"], "sources": ["../../src/utilities/meta.ts"], "sourcesContent": ["import type { Metadata } from 'next'\nimport type { Icon } from 'next/dist/lib/metadata/types/metadata-types.js'\nimport type { MetaConfig } from 'payload'\n\nimport { payloadFaviconDark, payloadFaviconLight, staticOGImage } from '@payloadcms/ui/assets'\nimport * as qs from 'qs-esm'\n\nconst defaultOpenGraph: Metadata['openGraph'] = {\n  description:\n    'Payload is a headless CMS and application framework built with TypeScript, Node.js, and React.',\n  siteName: 'Payload App',\n  title: 'Payload App',\n}\n\nexport const generateMetadata = async (\n  args: { serverURL: string } & MetaConfig,\n): Promise<Metadata> => {\n  const { defaultOGImageType, serverURL, titleSuffix, ...rest } = args\n\n  /**\n   * @todo find a way to remove the type assertion here.\n   * It is a result of needing to `DeepCopy` the `MetaConfig` type from Payload.\n   * This is required for the `DeepRequired` from `Config` to `SanitizedConfig`.\n   */\n  const incomingMetadata = rest as Metadata\n\n  const icons: Metadata['icons'] =\n    incomingMetadata.icons ||\n    ([\n      {\n        type: 'image/png',\n        rel: 'icon',\n        sizes: '32x32',\n        url: typeof payloadFaviconDark === 'object' ? payloadFaviconDark?.src : payloadFaviconDark,\n      },\n      {\n        type: 'image/png',\n        media: '(prefers-color-scheme: dark)',\n        rel: 'icon',\n        sizes: '32x32',\n        url:\n          typeof payloadFaviconLight === 'object' ? payloadFaviconLight?.src : payloadFaviconLight,\n      },\n    ] satisfies Array<Icon>)\n\n  const metaTitle: Metadata['title'] = [incomingMetadata.title, titleSuffix]\n    .filter(Boolean)\n    .join(' ')\n\n  const ogTitle = `${typeof incomingMetadata.openGraph?.title === 'string' ? incomingMetadata.openGraph.title : incomingMetadata.title} ${titleSuffix}`\n\n  const mergedOpenGraph: Metadata['openGraph'] = {\n    ...(defaultOpenGraph || {}),\n    ...(defaultOGImageType === 'dynamic'\n      ? {\n          images: [\n            {\n              alt: ogTitle,\n              height: 630,\n              url: `/api/og${qs.stringify(\n                {\n                  description:\n                    incomingMetadata.openGraph?.description || defaultOpenGraph.description,\n                  title: ogTitle,\n                },\n                {\n                  addQueryPrefix: true,\n                },\n              )}`,\n              width: 1200,\n            },\n          ],\n        }\n      : {}),\n    ...(defaultOGImageType === 'static'\n      ? {\n          images: [\n            {\n              alt: ogTitle,\n              height: 480,\n              url: typeof staticOGImage === 'object' ? staticOGImage?.src : staticOGImage,\n              width: 640,\n            },\n          ],\n        }\n      : {}),\n    title: ogTitle,\n    ...(incomingMetadata.openGraph || {}),\n  }\n\n  return Promise.resolve({\n    ...incomingMetadata,\n    icons,\n    metadataBase: new URL(\n      serverURL ||\n        process.env.PAYLOAD_PUBLIC_SERVER_URL ||\n        `http://localhost:${process.env.PORT || 3000}`,\n    ),\n    openGraph: mergedOpenGraph,\n    title: metaTitle,\n  })\n}\n"], "mappings": "AAIA,SAASA,kBAAkB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ;AACvE,YAAYC,EAAA,MAAQ;AAEpB,MAAMC,gBAAA,GAA0C;EAC9CC,WAAA,EACE;EACFC,QAAA,EAAU;EACVC,KAAA,EAAO;AACT;AAEA,OAAO,MAAMC,gBAAA,GAAmB,MAC9BC,IAAA;EAEA,MAAM;IAAEC,kBAAkB;IAAEC,SAAS;IAAEC,WAAW;IAAE,GAAGC;EAAA,CAAM,GAAGJ,IAAA;EAEhE;;;;;EAKA,MAAMK,gBAAA,GAAmBD,IAAA;EAEzB,MAAME,KAAA,GACJD,gBAAA,CAAiBC,KAAK,IACrB,CACC;IACEC,IAAA,EAAM;IACNC,GAAA,EAAK;IACLC,KAAA,EAAO;IACPC,GAAA,EAAK,OAAOnB,kBAAA,KAAuB,WAAWA,kBAAA,EAAoBoB,GAAA,GAAMpB;EAC1E,GACA;IACEgB,IAAA,EAAM;IACNK,KAAA,EAAO;IACPJ,GAAA,EAAK;IACLC,KAAA,EAAO;IACPC,GAAA,EACE,OAAOlB,mBAAA,KAAwB,WAAWA,mBAAA,EAAqBmB,GAAA,GAAMnB;EACzE,EACD;EAEH,MAAMqB,SAAA,GAA+B,CAACR,gBAAA,CAAiBP,KAAK,EAAEK,WAAA,CAAY,CACvEW,MAAM,CAACC,OAAA,EACPC,IAAI,CAAC;EAER,MAAMC,OAAA,GAAU,GAAG,OAAOZ,gBAAA,CAAiBa,SAAS,EAAEpB,KAAA,KAAU,WAAWO,gBAAA,CAAiBa,SAAS,CAACpB,KAAK,GAAGO,gBAAA,CAAiBP,KAAK,IAAIK,WAAA,EAAa;EAErJ,MAAMgB,eAAA,GAAyC;IAC7C,IAAIxB,gBAAA,IAAoB,CAAC,CAAC;IAC1B,IAAIM,kBAAA,KAAuB,YACvB;MACEmB,MAAA,EAAQ,CACN;QACEC,GAAA,EAAKJ,OAAA;QACLK,MAAA,EAAQ;QACRZ,GAAA,EAAK,UAAUhB,EAAA,CAAG6B,SAAS,CACzB;UACE3B,WAAA,EACES,gBAAA,CAAiBa,SAAS,EAAEtB,WAAA,IAAeD,gBAAA,CAAiBC,WAAW;UACzEE,KAAA,EAAOmB;QACT,GACA;UACEO,cAAA,EAAgB;QAClB,IACC;QACHC,KAAA,EAAO;MACT;IAEJ,IACA,CAAC,CAAC;IACN,IAAIxB,kBAAA,KAAuB,WACvB;MACEmB,MAAA,EAAQ,CACN;QACEC,GAAA,EAAKJ,OAAA;QACLK,MAAA,EAAQ;QACRZ,GAAA,EAAK,OAAOjB,aAAA,KAAkB,WAAWA,aAAA,EAAekB,GAAA,GAAMlB,aAAA;QAC9DgC,KAAA,EAAO;MACT;IAEJ,IACA,CAAC,CAAC;IACN3B,KAAA,EAAOmB,OAAA;IACP,IAAIZ,gBAAA,CAAiBa,SAAS,IAAI,CAAC,CAAC;EACtC;EAEA,OAAOQ,OAAA,CAAQC,OAAO,CAAC;IACrB,GAAGtB,gBAAgB;IACnBC,KAAA;IACAsB,YAAA,EAAc,IAAIC,GAAA,CAChB3B,SAAA,IACE4B,OAAA,CAAQC,GAAG,CAACC,yBAAyB,IACrC,oBAAoBF,OAAA,CAAQC,GAAG,CAACE,IAAI,IAAI,MAAM;IAElDf,SAAA,EAAWC,eAAA;IACXrB,KAAA,EAAOe;EACT;AACF", "ignoreList": []}