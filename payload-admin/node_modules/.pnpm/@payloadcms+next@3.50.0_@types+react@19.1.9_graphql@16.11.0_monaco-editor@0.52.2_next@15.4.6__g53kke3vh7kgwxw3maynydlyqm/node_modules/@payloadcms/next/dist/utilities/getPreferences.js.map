{"version": 3, "file": "getPreferences.js", "names": ["cache", "getPreferences", "key", "payload", "userID", "userSlug", "result", "find", "collection", "depth", "limit", "pagination", "where", "and", "equals", "then", "res", "docs"], "sources": ["../../src/utilities/getPreferences.ts"], "sourcesContent": ["import type { DefaultDocumentIDType, Payload } from 'payload'\n\nimport { cache } from 'react'\n\nexport const getPreferences = cache(\n  async <T>(\n    key: string,\n    payload: Payload,\n    userID: DefaultDocumentIDType,\n    userSlug: string,\n  ): Promise<{ id: DefaultDocumentIDType; value: T }> => {\n    const result = (await payload\n      .find({\n        collection: 'payload-preferences',\n        depth: 0,\n        limit: 1,\n        pagination: false,\n        where: {\n          and: [\n            {\n              key: {\n                equals: key,\n              },\n            },\n            {\n              'user.relationTo': {\n                equals: userSlug,\n              },\n            },\n            {\n              'user.value': {\n                equals: userID,\n              },\n            },\n          ],\n        },\n      })\n      .then((res) => res.docs?.[0])) as { id: DefaultDocumentIDType; value: T }\n\n    return result\n  },\n)\n"], "mappings": "AAEA,SAASA,KAAK,QAAQ;AAEtB,OAAO,MAAMC,cAAA,GAAiBD,KAAA,CAC5B,OACEE,GAAA,EACAC,OAAA,EACAC,MAAA,EACAC,QAAA;EAEA,MAAMC,MAAA,GAAU,MAAMH,OAAA,CACnBI,IAAI,CAAC;IACJC,UAAA,EAAY;IACZC,KAAA,EAAO;IACPC,KAAA,EAAO;IACPC,UAAA,EAAY;IACZC,KAAA,EAAO;MACLC,GAAA,EAAK,CACH;QACEX,GAAA,EAAK;UACHY,MAAA,EAAQZ;QACV;MACF,GACA;QACE,mBAAmB;UACjBY,MAAA,EAAQT;QACV;MACF,GACA;QACE,cAAc;UACZS,MAAA,EAAQV;QACV;MACF;IAEJ;EACF,GACCW,IAAI,CAAEC,GAAA,IAAQA,GAAA,CAAIC,IAAI,GAAG,EAAE;EAE9B,OAAOX,MAAA;AACT", "ignoreList": []}