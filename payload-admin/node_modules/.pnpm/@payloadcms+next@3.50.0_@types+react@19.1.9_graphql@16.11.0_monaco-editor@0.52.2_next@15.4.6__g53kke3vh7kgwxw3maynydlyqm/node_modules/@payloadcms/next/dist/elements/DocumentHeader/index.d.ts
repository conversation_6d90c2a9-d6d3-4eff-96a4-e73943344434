import type { PayloadRequest, SanitizedCollectionConfig, SanitizedGlobalConfig, SanitizedPermissions } from 'payload';
import React from 'react';
import './index.scss';
export declare const DocumentHeader: React.FC<{
    collectionConfig?: SanitizedCollectionConfig;
    globalConfig?: SanitizedGlobalConfig;
    hideTabs?: boolean;
    permissions: SanitizedPermissions;
    req: PayloadRequest;
}>;
//# sourceMappingURL=index.d.ts.map