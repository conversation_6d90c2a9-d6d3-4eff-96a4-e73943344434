{"version": 3, "file": "index.js", "names": ["c", "_c", "<PERSON><PERSON>", "useNav", "React", "NavHamburger", "t0", "$", "baseClass", "navOpen", "setNavOpen", "t1", "t2", "t3", "undefined", "t4", "_jsx", "className", "onClick", "tabIndex", "type", "children", "isActive"], "sources": ["../../../../src/elements/Nav/NavHamburger/index.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON><PERSON>, useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const NavHamburger: React.FC<{\n  baseClass?: string\n}> = ({ baseClass }) => {\n  const { navOpen, setNavOpen } = useNav()\n\n  return (\n    <button\n      className={`${baseClass}__mobile-close`}\n      onClick={() => {\n        setNavOpen(false)\n      }}\n      tabIndex={!navOpen ? -1 : undefined}\n      type=\"button\"\n    >\n      <Hamburger isActive />\n    </button>\n  )\n}\n"], "mappings": "AAAA;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,SAAS,EAAEC,MAAM,QAAQ;AAClC,OAAOC,KAAA,MAAW;AAElB,OAAO,MAAMC,YAAA,GAERC,EAAA;EAAA,MAAAC,CAAA,GAAAN,EAAA;EAAC;IAAAO;EAAA,IAAAF,EAAa;EACjB;IAAAG,OAAA;IAAAC;EAAA,IAAgCP,MAAA;EAIjB,MAAAQ,EAAA,MAAGH,SAAA,gBAAyB;EAAA,IAAAI,EAAA;EAAA,IAAAL,CAAA,QAAAG,UAAA;IAC9BE,EAAA,GAAAA,CAAA;MACPF,UAAA,MAAW;IAAA;IACbH,CAAA,MAAAG,UAAA;IAAAH,CAAA,MAAAK,EAAA;EAAA;IAAAA,EAAA,GAAAL,CAAA;EAAA;EACU,MAAAM,EAAA,IAACJ,OAAA,QAAAK,SAAe;EAAA,IAAAC,EAAA;EAAA,IAAAR,CAAA,QAAAI,EAAA,IAAAJ,CAAA,QAAAK,EAAA,IAAAL,CAAA,QAAAM,EAAA;IAL5BE,EAAA,GAAAC,IAAA,CAAC;MAAAC,SAAA,EACYN,EAA4B;MAAAO,OAAA,EAC9BN,EAET;MAAAO,QAAA,EACUN,EAAgB;MAAAO,IAAA,EACrB;MAAAC,QAAA,EAELL,IAAA,CAAAd,SAAA;QAAAoB,QAAA;MAAA,C;;;;;;;;;SARFP,E;CAWJ", "ignoreList": []}