{"version": 3, "file": "setPayloadAuthCookie.js", "names": ["cookies", "getCookies", "generatePayloadCookie", "setPayloadAuthCookie", "authConfig", "cookiePrefix", "token", "cookieExpiration", "tokenExpiration", "Date", "now", "undefined", "payloadCookie", "collectionAuthConfig", "expires", "returnCookieAsObject", "value", "set", "name", "domain", "httpOnly", "sameSite", "toLowerCase", "secure"], "sources": ["../../src/utilities/setPayloadAuthCookie.ts"], "sourcesContent": ["import type { Auth } from 'payload'\n\nimport { cookies as getCookies } from 'next/headers.js'\nimport { generatePayloadCookie } from 'payload'\n\ntype SetPayloadAuthCookieArgs = {\n  authConfig: Auth\n  cookiePrefix: string\n  token: string\n}\n\nexport async function setPayloadAuthCookie({\n  authConfig,\n  cookiePrefix,\n  token,\n}: SetPayloadAuthCookieArgs): Promise<void> {\n  const cookies = await getCookies()\n\n  const cookieExpiration = authConfig.tokenExpiration\n    ? new Date(Date.now() + authConfig.tokenExpiration)\n    : undefined\n\n  const payloadCookie = generatePayloadCookie({\n    collectionAuthConfig: authConfig,\n    cookiePrefix,\n    expires: cookieExpiration,\n    returnCookieAsObject: true,\n    token,\n  })\n\n  if (payloadCookie.value) {\n    cookies.set(payloadCookie.name, payloadCookie.value, {\n      domain: authConfig.cookies.domain,\n      expires: payloadCookie.expires ? new Date(payloadCookie.expires) : undefined,\n      httpOnly: true,\n      sameSite: (typeof authConfig.cookies.sameSite === 'string'\n        ? authConfig.cookies.sameSite.toLowerCase()\n        : 'lax') as 'lax' | 'none' | 'strict',\n      secure: authConfig.cookies.secure || false,\n    })\n  }\n}\n"], "mappings": "AAEA,SAASA,OAAA,IAAWC,UAAU,QAAQ;AACtC,SAASC,qBAAqB,QAAQ;AAQtC,OAAO,eAAeC,qBAAqB;EACzCC,UAAU;EACVC,YAAY;EACZC;AAAK,CACoB;EACzB,MAAMN,OAAA,GAAU,MAAMC,UAAA;EAEtB,MAAMM,gBAAA,GAAmBH,UAAA,CAAWI,eAAe,GAC/C,IAAIC,IAAA,CAAKA,IAAA,CAAKC,GAAG,KAAKN,UAAA,CAAWI,eAAe,IAChDG,SAAA;EAEJ,MAAMC,aAAA,GAAgBV,qBAAA,CAAsB;IAC1CW,oBAAA,EAAsBT,UAAA;IACtBC,YAAA;IACAS,OAAA,EAASP,gBAAA;IACTQ,oBAAA,EAAsB;IACtBT;EACF;EAEA,IAAIM,aAAA,CAAcI,KAAK,EAAE;IACvBhB,OAAA,CAAQiB,GAAG,CAACL,aAAA,CAAcM,IAAI,EAAEN,aAAA,CAAcI,KAAK,EAAE;MACnDG,MAAA,EAAQf,UAAA,CAAWJ,OAAO,CAACmB,MAAM;MACjCL,OAAA,EAASF,aAAA,CAAcE,OAAO,GAAG,IAAIL,IAAA,CAAKG,aAAA,CAAcE,OAAO,IAAIH,SAAA;MACnES,QAAA,EAAU;MACVC,QAAA,EAAW,OAAOjB,UAAA,CAAWJ,OAAO,CAACqB,QAAQ,KAAK,WAC9CjB,UAAA,CAAWJ,OAAO,CAACqB,QAAQ,CAACC,WAAW,KACvC;MACJC,MAAA,EAAQnB,UAAA,CAAWJ,OAAO,CAACuB,MAAM,IAAI;IACvC;EACF;AACF", "ignoreList": []}