{"version": 3, "file": "ShouldRenderTabs.js", "names": ["useDocumentInfo", "ShouldRenderTabs", "t0", "children", "id", "idFromContext", "collectionSlug", "globalSlug"], "sources": ["../../../../src/elements/DocumentHeader/Tabs/ShouldRenderTabs.tsx"], "sourcesContent": ["'use client'\nimport type React from 'react'\n\nimport { useDocumentInfo } from '@payloadcms/ui'\n\nexport const ShouldRenderTabs: React.FC<{\n  children: React.ReactNode\n}> = ({ children }) => {\n  const { id: idFromContext, collectionSlug, globalSlug } = useDocumentInfo()\n\n  const id = idFromContext !== 'create' ? idFromContext : null\n\n  // Don't show tabs when creating new documents\n  if ((collectionSlug && id) || globalSlug) {\n    return children\n  }\n\n  return null\n}\n"], "mappings": "AAAA;;AAGA,SAASA,eAAe,QAAQ;AAEhC,OAAO,MAAMC,gBAAA,GAERC,EAAA;EAAC;IAAAC;EAAA,IAAAD,EAAY;EAChB;IAAAE,EAAA,EAAAC,aAAA;IAAAC,cAAA;IAAAC;EAAA,IAA0DP,eAAA;EAE1D,MAAAI,EAAA,GAAWC,aAAA,KAAkB,WAAWA,aAAA,OAAgB;EAAA,IAGpDC,cAAC,IAAkBF,EAAA,IAAOG,UAAA;IAAA,OACrBJ,QAAA;EAAA;EAAA;AAAA,CAIX", "ignoreList": []}