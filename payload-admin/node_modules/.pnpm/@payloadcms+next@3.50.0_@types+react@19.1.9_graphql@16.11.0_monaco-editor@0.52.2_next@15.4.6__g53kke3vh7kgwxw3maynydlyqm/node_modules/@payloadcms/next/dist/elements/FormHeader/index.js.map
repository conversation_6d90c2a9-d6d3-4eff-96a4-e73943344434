{"version": 3, "file": "index.js", "names": ["React", "baseClass", "FormHeader", "description", "heading", "_jsxs", "className", "_jsx", "Boolean"], "sources": ["../../../src/elements/FormHeader/index.tsx"], "sourcesContent": ["import React from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'form-header'\n\ntype Props = {\n  description?: React.ReactNode | string\n  heading: string\n}\nexport function FormHeader({ description, heading }: Props) {\n  if (!heading) {\n    return null\n  }\n\n  return (\n    <div className={baseClass}>\n      <h1>{heading}</h1>\n      {<PERSON><PERSON><PERSON>(description) && <p>{description}</p>}\n    </div>\n  )\n}\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAIlB,MAAMC,SAAA,GAAY;AAMlB,OAAO,SAASC,WAAW;EAAEC,WAAW;EAAEC;AAAO,CAAS;EACxD,IAAI,CAACA,OAAA,EAAS;IACZ,OAAO;EACT;EAEA,oBACEC,KAAA,CAAC;IAAIC,SAAA,EAAWL,SAAA;4BACdM,IAAA,CAAC;gBAAIH;QACJI,OAAA,CAAQL,WAAA,kBAAgBI,IAAA,CAAC;gBAAGJ;;;AAGnC", "ignoreList": []}