{"version": 3, "file": "selectiveCache.js", "names": ["cache", "globalCacheContainer", "selectiveCache", "namespace", "args", "value", "getCached", "factory", "cacheArgs", "stableObjectFn", "stableObject", "then", "get"], "sources": ["../../src/utilities/selectiveCache.ts"], "sourcesContent": ["import { cache } from 'react'\n\ntype CachedValue = object\n\n// Module-scoped cache container that holds all cached, stable containers\n// - these may hold the stable value, or a promise to the stable value\nconst globalCacheContainer: Record<\n  string,\n  <TValue extends object = CachedValue>(\n    ...args: unknown[]\n  ) => {\n    value: null | Promise<TValue> | TValue\n  }\n> = {}\n\n/**\n * Creates a selective cache function that provides more control over React's request-level caching behavior.\n *\n * @param namespace - A namespace to group related cached values\n * @returns A function that manages cached values within the specified namespace\n */\nexport function selectiveCache<TValue extends object = CachedValue>(namespace: string) {\n  // Create a stable namespace container if it doesn't exist\n  if (!globalCacheContainer[namespace]) {\n    globalCacheContainer[namespace] = cache((...args) => ({\n      value: null,\n    }))\n  }\n\n  /**\n   * Gets or creates a cached value for a specific key within the namespace\n   *\n   * @param key - The key to identify the cached value\n   * @param factory - A function that produces the value if not cached\n   * @returns The cached or newly created value\n   */\n  const getCached = async (factory: () => Promise<TValue>, ...cacheArgs): Promise<TValue> => {\n    const stableObjectFn = globalCacheContainer[namespace]\n    const stableObject = stableObjectFn<TValue>(...cacheArgs)\n\n    if (\n      stableObject?.value &&\n      'then' in stableObject.value &&\n      typeof stableObject.value?.then === 'function'\n    ) {\n      return await stableObject.value\n    }\n\n    stableObject.value = factory()\n\n    return await stableObject.value\n  }\n\n  return {\n    get: getCached,\n  }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ;AAItB;AACA;AACA,MAAMC,oBAAA,GAOF,CAAC;AAEL;;;;;;AAMA,OAAO,SAASC,eAAoDC,SAAiB;EACnF;EACA,IAAI,CAACF,oBAAoB,CAACE,SAAA,CAAU,EAAE;IACpCF,oBAAoB,CAACE,SAAA,CAAU,GAAGH,KAAA,CAAM,CAAC,GAAGI,IAAA,MAAU;MACpDC,KAAA,EAAO;IACT;EACF;EAEA;;;;;;;EAOA,MAAMC,SAAA,GAAY,MAAAA,CAAOC,OAAA,EAAgC,GAAGC,SAAA;IAC1D,MAAMC,cAAA,GAAiBR,oBAAoB,CAACE,SAAA,CAAU;IACtD,MAAMO,YAAA,GAAeD,cAAA,IAA0BD,SAAA;IAE/C,IACEE,YAAA,EAAcL,KAAA,IACd,UAAUK,YAAA,CAAaL,KAAK,IAC5B,OAAOK,YAAA,CAAaL,KAAK,EAAEM,IAAA,KAAS,YACpC;MACA,OAAO,MAAMD,YAAA,CAAaL,KAAK;IACjC;IAEAK,YAAA,CAAaL,KAAK,GAAGE,OAAA;IAErB,OAAO,MAAMG,YAAA,CAAaL,KAAK;EACjC;EAEA,OAAO;IACLO,GAAA,EAAKN;EACP;AACF", "ignoreList": []}