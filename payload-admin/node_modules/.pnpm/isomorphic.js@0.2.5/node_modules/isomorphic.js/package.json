{"name": "isomorphic.js", "version": "0.2.5", "description": "Isomorphic JavaScript helper functions (performance, crpyto, ..)", "sideEffects": false, "main": "./iso.js", "browser": "./browser.mjs", "unpkg": "./browser.mjs", "module": "./browser.mjs", "exports": {".": {"node": {"import": "./node.mjs", "require": "./iso.js"}, "browser": {"import": "./browser.mjs", "require": "./browser.js"}, "default": {"import": "./browser.mjs", "require": "./iso.js"}}, "./package.json": "./package.json"}, "dependencies": {}, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/dmonad"}, "devDependencies": {"@types/node": "^13.13.9", "standard": "^14.3.4", "typescript": "^3.9.3"}, "scripts": {"test": "npm run lint", "lint": "standard && tsc", "preversion": "npm run test"}, "files": ["browser.js", "browser.mjs", "iso.js", "node.js", "node.mjs"], "repository": {"type": "git", "url": "git+https://github.com/dmonad/isomorphic.js.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dmonad/isomorphic.js/issues"}, "homepage": "https://github.com/dmonad/isomorphic.js#readme", "standard": {"ignore": ["/dist", "/node_modules", "/docs"]}}