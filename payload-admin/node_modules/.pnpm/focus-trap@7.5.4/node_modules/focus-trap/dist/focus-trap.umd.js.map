{"version": 3, "file": "focus-trap.umd.js", "sources": ["../index.js"], "sourcesContent": ["import {\n  tabbable,\n  focusable,\n  isFocusable,\n  isTabbable,\n  getTabIndex,\n} from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e?.key === 'Escape' || e?.key === 'Esc' || e?.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e?.key === 'Tab' || e?.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element, event) {\n    const composedPath =\n      typeof event?.composedPath === 'function'\n        ? event.composedPath()\n        : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        composedPath?.includes(container) ||\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined || !isFocusable(node, config.tabbableOptions)) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      const firstTabbableNode =\n        tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      const lastTabbableNode =\n        tabbableNodes.length > 0\n          ? tabbableNodes[tabbableNodes.length - 1]\n          : undefined;\n\n      const firstDomTabbableNode = focusableNodes.find((node) =>\n        isTabbable(node)\n      );\n      const lastDomTabbableNode = focusableNodes\n        .slice()\n        .reverse()\n        .find((node) => isTabbable(node));\n\n      const posTabIndexesFound = !!tabbableNodes.find(\n        (node) => getTabIndex(node) > 0\n      );\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound,\n\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode,\n\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          const nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes\n                .slice(focusableNodes.indexOf(node) + 1)\n                .find((el) => isTabbable(el));\n            }\n\n            return focusableNodes\n              .slice(0, focusableNodes.indexOf(node))\n              .reverse()\n              .find((el) => isTabbable(el));\n          }\n\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (\n      state.containerGroups.find((g) => g.posTabIndexesFound) &&\n      state.containerGroups.length > 1\n    ) {\n      throw new Error(\n        \"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\"\n      );\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  const getActiveElement = function (el) {\n    const activeElement = el.activeElement;\n\n    if (!activeElement) {\n      return;\n    }\n\n    if (\n      activeElement.shadowRoot &&\n      activeElement.shadowRoot.activeElement !== null\n    ) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n\n    return activeElement;\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === getActiveElement(document)) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  const findNextNavNode = function ({ target, event, isBackward = false }) {\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target, event);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.lastTabbableNode\n              : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.firstTabbableNode\n              : destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate,\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  const checkFocusIn = function (event) {\n    const target = getActualTarget(event);\n    const targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      let nextNode; // next node to focus, if we find one\n      let navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if (getTabIndex(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          const mruContainerIdx = findContainerIndex(\n            state.mostRecentlyFocusedNode\n          );\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          const { tabbableNodes } = state.containerGroups[mruContainerIdx];\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            const mruTabIdx = tabbableNodes.findIndex(\n              (node) => node === state.mostRecentlyFocusedNode\n            );\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (\n            !state.containerGroups.some((g) =>\n              g.tabbableNodes.some((n) => getTabIndex(n) > 0)\n            )\n          ) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent),\n        });\n      }\n\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    state.recentNavEvent = event;\n\n    const destinationNode = findNextNavNode({ event, isBackward });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  const checkDomRemoval = function (mutations) {\n    const isFocusedNodeRemoved = mutations.some(function (mutation) {\n      const removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  const mutationObserver =\n    typeof window !== 'undefined' && 'MutationObserver' in window\n      ? new MutationObserver(checkDomRemoval)\n      : undefined;\n\n  const updateObservedNodes = function () {\n    if (!mutationObserver) {\n      return;\n    }\n\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true,\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      onActivate?.();\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate?.();\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      onDeactivate?.();\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate?.();\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause(pauseOptions) {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      const onPause = getOption(pauseOptions, 'onPause');\n      const onPostPause = getOption(pauseOptions, 'onPostPause');\n\n      state.paused = true;\n      onPause?.();\n\n      removeListeners();\n      updateObservedNodes();\n\n      onPostPause?.();\n      return this;\n    },\n\n    unpause(unpauseOptions) {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      const onUnpause = getOption(unpauseOptions, 'onUnpause');\n      const onPostUnpause = getOption(unpauseOptions, 'onPostUnpause');\n\n      state.paused = false;\n      onUnpause?.();\n\n      updateTabbableNodes();\n      addListeners();\n      updateObservedNodes();\n\n      onPostUnpause?.();\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      updateObservedNodes();\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["activeFocusTraps", "activateTrap", "trapStack", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "recentNavEvent", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "_ref", "container", "tabbableNodes", "contains", "includes", "find", "getNodeForOption", "optionValue", "_len2", "_key2", "Error", "concat", "querySelector", "getInitialFocusNode", "isFocusable", "tabbableOptions", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "focusableNodes", "focusable", "lastTabbableNode", "firstDomTabbableNode", "isTabbable", "lastDomTabbableNode", "slice", "reverse", "posTabIndexesFound", "getTabIndex", "nextTabbableNode", "forward", "nodeIdx", "el", "filter", "group", "g", "getActiveElement", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "findNextNavNode", "_ref2", "_ref2$isBackward", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "_ref3", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "_ref4", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "nextNode", "navAcrossContainers", "mruContainerIdx", "mruTabIdx", "some", "n", "checkKeyNav", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "checkDomRemoval", "mutations", "isFocusedNodeRemoved", "mutation", "removedNodes", "from", "mutationObserver", "window", "MutationObserver", "updateObservedNodes", "disconnect", "observe", "subtree", "childList", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "pauseOptions", "onPause", "onPostPause", "unpauseOptions", "onUnpause", "onPostUnpause", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAQA,IAAMA,gBAAgB,GAAG;EACvBC,EAAAA,YAAY,EAAAA,SAAAA,YAAAA,CAACC,SAAS,EAAEC,IAAI,EAAE;EAC5B,IAAA,IAAID,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;QACxB,IAAMC,UAAU,GAAGH,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAAA;QAClD,IAAIC,UAAU,KAAKF,IAAI,EAAE;UACvBE,UAAU,CAACC,KAAK,EAAE,CAAA;EACpB,OAAA;EACF,KAAA;EAEA,IAAA,IAAMC,SAAS,GAAGL,SAAS,CAACM,OAAO,CAACL,IAAI,CAAC,CAAA;EACzC,IAAA,IAAII,SAAS,KAAK,CAAC,CAAC,EAAE;EACpBL,MAAAA,SAAS,CAACO,IAAI,CAACN,IAAI,CAAC,CAAA;EACtB,KAAC,MAAM;EACL;EACAD,MAAAA,SAAS,CAACQ,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC,CAAA;EAC9BL,MAAAA,SAAS,CAACO,IAAI,CAACN,IAAI,CAAC,CAAA;EACtB,KAAA;KACD;EAEDQ,EAAAA,cAAc,EAAAA,SAAAA,cAAAA,CAACT,SAAS,EAAEC,IAAI,EAAE;EAC9B,IAAA,IAAMI,SAAS,GAAGL,SAAS,CAACM,OAAO,CAACL,IAAI,CAAC,CAAA;EACzC,IAAA,IAAII,SAAS,KAAK,CAAC,CAAC,EAAE;EACpBL,MAAAA,SAAS,CAACQ,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC,CAAA;EAChC,KAAA;EAEA,IAAA,IAAIL,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;QACxBF,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACQ,OAAO,EAAE,CAAA;EAC3C,KAAA;EACF,GAAA;EACF,CAAC,CAAA;EAED,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,IAAI,EAAE;EACxC,EAAA,OACEA,IAAI,CAACC,OAAO,IACZD,IAAI,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IACtC,OAAOF,IAAI,CAACG,MAAM,KAAK,UAAU,CAAA;EAErC,CAAC,CAAA;EAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,CAAC,EAAE;EACjC,EAAA,OAAO,CAAAA,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAC,CAAEC,GAAG,MAAK,QAAQ,IAAI,CAAAD,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAC,CAAEC,GAAG,MAAK,KAAK,IAAI,CAAAD,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAC,CAAEE,OAAO,MAAK,EAAE,CAAA;EACrE,CAAC,CAAA;EAED,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAaH,CAAC,EAAE;IAC9B,OAAO,CAAAA,CAAC,KAADA,IAAAA,IAAAA,CAAC,uBAADA,CAAC,CAAEC,GAAG,MAAK,KAAK,IAAI,CAAAD,CAAC,aAADA,CAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAADA,CAAC,CAAEE,OAAO,MAAK,CAAC,CAAA;EAC7C,CAAC,CAAA;;EAED;EACA,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAaJ,CAAC,EAAE;IAChC,OAAOG,UAAU,CAACH,CAAC,CAAC,IAAI,CAACA,CAAC,CAACK,QAAQ,CAAA;EACrC,CAAC,CAAA;;EAED;EACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaN,CAAC,EAAE;EACjC,EAAA,OAAOG,UAAU,CAACH,CAAC,CAAC,IAAIA,CAAC,CAACK,QAAQ,CAAA;EACpC,CAAC,CAAA;EAED,IAAME,KAAK,GAAG,SAARA,KAAKA,CAAaC,EAAE,EAAE;EAC1B,EAAA,OAAOC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC,CAAA;EAC1B,CAAC,CAAA;;EAED;EACA;EACA,IAAME,SAAS,GAAG,SAAZA,SAASA,CAAaC,GAAG,EAAEH,EAAE,EAAE;IACnC,IAAII,GAAG,GAAG,CAAC,CAAC,CAAA;EAEZD,EAAAA,GAAG,CAACE,KAAK,CAAC,UAAUC,KAAK,EAAEC,CAAC,EAAE;EAC5B,IAAA,IAAIP,EAAE,CAACM,KAAK,CAAC,EAAE;EACbF,MAAAA,GAAG,GAAGG,CAAC,CAAA;QACP,OAAO,KAAK,CAAC;EACf,KAAA;;MAEA,OAAO,IAAI,CAAC;EACd,GAAC,CAAC,CAAA;;EAEF,EAAA,OAAOH,GAAG,CAAA;EACZ,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAMI,cAAc,GAAG,SAAjBA,cAAcA,CAAaF,KAAK,EAAa;IAAA,KAAAG,IAAAA,IAAA,GAAAC,SAAA,CAAAjC,MAAA,EAARkC,MAAM,OAAAC,KAAA,CAAAH,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAI,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA,EAAA,EAAA;EAANF,IAAAA,MAAM,CAAAE,IAAA,GAAAH,CAAAA,CAAAA,GAAAA,SAAA,CAAAG,IAAA,CAAA,CAAA;EAAA,GAAA;EAC/C,EAAA,OAAO,OAAOP,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAAQ,KAAA,CAAIH,KAAAA,CAAAA,EAAAA,MAAM,CAAC,GAAGL,KAAK,CAAA;EAC/D,CAAC,CAAA;EAED,IAAMS,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,KAAK,EAAE;EACvC;EACA;EACA;EACA;EACA;EACA;EACA;IACA,OAAOA,KAAK,CAACC,MAAM,CAACC,UAAU,IAAI,OAAOF,KAAK,CAACG,YAAY,KAAK,UAAU,GACtEH,KAAK,CAACG,YAAY,EAAE,CAAC,CAAC,CAAC,GACvBH,KAAK,CAACC,MAAM,CAAA;EAClB,CAAC,CAAA;;EAED;EACA;EACA,IAAMG,iBAAiB,GAAG,EAAE,CAAA;AAEtBC,MAAAA,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,QAAQ,EAAEC,WAAW,EAAE;EACvD;EACA;IACA,IAAMC,GAAG,GAAG,CAAAD,WAAW,KAAA,IAAA,IAAXA,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAXA,WAAW,CAAEE,QAAQ,KAAIA,QAAQ,CAAA;IAE7C,IAAMlD,SAAS,GAAG,CAAAgD,WAAW,KAAA,IAAA,IAAXA,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAXA,WAAW,CAAEhD,SAAS,KAAI6C,iBAAiB,CAAA;IAE7D,IAAMM,MAAM,GAAAC,cAAA,CAAA;EACVC,IAAAA,uBAAuB,EAAE,IAAI;EAC7BC,IAAAA,iBAAiB,EAAE,IAAI;EACvBC,IAAAA,iBAAiB,EAAE,IAAI;EACvBlC,IAAAA,YAAY,EAAZA,YAAY;EACZE,IAAAA,aAAa,EAAbA,aAAAA;EAAa,GAAA,EACVyB,WAAW,CACf,CAAA;EAED,EAAA,IAAMQ,KAAK,GAAG;EACZ;EACA;EACAC,IAAAA,UAAU,EAAE,EAAE;EAEd;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,IAAAA,eAAe,EAAE,EAAE;EAAE;;EAErB;EACA;EACA;EACA;EACAC,IAAAA,cAAc,EAAE,EAAE;EAElBC,IAAAA,2BAA2B,EAAE,IAAI;EACjCC,IAAAA,uBAAuB,EAAE,IAAI;EAC7BC,IAAAA,MAAM,EAAE,KAAK;EACbC,IAAAA,MAAM,EAAE,KAAK;EAEb;EACA;EACAC,IAAAA,sBAAsB,EAAEC,SAAS;EAEjC;EACAC,IAAAA,cAAc,EAAED,SAAAA;KACjB,CAAA;IAED,IAAIhE,IAAI,CAAC;;EAET;EACF;EACA;EACA;EACA;EACA;EACA;EACA;IACE,IAAMkE,SAAS,GAAG,SAAZA,SAASA,CAAIC,qBAAqB,EAAEC,UAAU,EAAEC,gBAAgB,EAAK;EACzE,IAAA,OAAOF,qBAAqB,IAC1BA,qBAAqB,CAACC,UAAU,CAAC,KAAKJ,SAAS,GAC7CG,qBAAqB,CAACC,UAAU,CAAC,GACjClB,MAAM,CAACmB,gBAAgB,IAAID,UAAU,CAAC,CAAA;KAC3C,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACE,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,OAAO,EAAE/B,KAAK,EAAE;EACnD,IAAA,IAAMG,YAAY,GAChB,QAAOH,KAAK,KAALA,IAAAA,IAAAA,KAAK,uBAALA,KAAK,CAAEG,YAAY,CAAK,KAAA,UAAU,GACrCH,KAAK,CAACG,YAAY,EAAE,GACpBqB,SAAS,CAAA;EACf;EACA;EACA;EACA,IAAA,OAAOT,KAAK,CAACE,eAAe,CAAC/B,SAAS,CACpC,UAAA8C,IAAA,EAAA;EAAA,MAAA,IAAGC,SAAS,GAAAD,IAAA,CAATC,SAAS;UAAEC,aAAa,GAAAF,IAAA,CAAbE,aAAa,CAAA;EAAA,MAAA,OACzBD,SAAS,CAACE,QAAQ,CAACJ,OAAO,CAAC;EAE3B;EACA;EACA;EACA5B,MAAAA,YAAY,KAAZA,IAAAA,IAAAA,YAAY,KAAZA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,YAAY,CAAEiC,QAAQ,CAACH,SAAS,CAAC,KACjCC,aAAa,CAACG,IAAI,CAAC,UAAClE,IAAI,EAAA;UAAA,OAAKA,IAAI,KAAK4D,OAAO,CAAA;SAAC,CAAA,CAAA;EAAA,KAClD,CAAC,CAAA;KACF,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMO,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaV,UAAU,EAAa;EACxD,IAAA,IAAIW,WAAW,GAAG7B,MAAM,CAACkB,UAAU,CAAC,CAAA;EAEpC,IAAA,IAAI,OAAOW,WAAW,KAAK,UAAU,EAAE;QAAA,KAAAC,IAAAA,KAAA,GAAA9C,SAAA,CAAAjC,MAAA,EAHSkC,MAAM,OAAAC,KAAA,CAAA4C,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAN9C,QAAAA,MAAM,CAAA8C,KAAA,GAAA/C,CAAAA,CAAAA,GAAAA,SAAA,CAAA+C,KAAA,CAAA,CAAA;EAAA,OAAA;EAIpDF,MAAAA,WAAW,GAAGA,WAAW,CAAAzC,KAAA,CAAA,KAAA,CAAA,EAAIH,MAAM,CAAC,CAAA;EACtC,KAAA;MAEA,IAAI4C,WAAW,KAAK,IAAI,EAAE;QACxBA,WAAW,GAAGf,SAAS,CAAC;EAC1B,KAAA;;MAEA,IAAI,CAACe,WAAW,EAAE;EAChB,MAAA,IAAIA,WAAW,KAAKf,SAAS,IAAIe,WAAW,KAAK,KAAK,EAAE;EACtD,QAAA,OAAOA,WAAW,CAAA;EACpB,OAAA;EACA;;EAEA,MAAA,MAAM,IAAIG,KAAK,CAAA,GAAA,CAAAC,MAAA,CACRf,UAAU,iEACjB,CAAC,CAAA;EACH,KAAA;EAEA,IAAA,IAAIzD,IAAI,GAAGoE,WAAW,CAAC;;EAEvB,IAAA,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QACnCpE,IAAI,GAAGqC,GAAG,CAACoC,aAAa,CAACL,WAAW,CAAC,CAAC;QACtC,IAAI,CAACpE,IAAI,EAAE;EACT,QAAA,MAAM,IAAIuE,KAAK,CAAA,GAAA,CAAAC,MAAA,CACRf,UAAU,0CACjB,CAAC,CAAA;EACH,OAAA;EACF,KAAA;EAEA,IAAA,OAAOzD,IAAI,CAAA;KACZ,CAAA;EAED,EAAA,IAAM0E,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;EACtC,IAAA,IAAI1E,IAAI,GAAGmE,gBAAgB,CAAC,cAAc,CAAC,CAAA;;EAE3C;MACA,IAAInE,IAAI,KAAK,KAAK,EAAE;EAClB,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EAEA,IAAA,IAAIA,IAAI,KAAKqD,SAAS,IAAI,CAACsB,oBAAW,CAAC3E,IAAI,EAAEuC,MAAM,CAACqC,eAAe,CAAC,EAAE;EACpE;QACA,IAAIjB,kBAAkB,CAACtB,GAAG,CAACwC,aAAa,CAAC,IAAI,CAAC,EAAE;UAC9C7E,IAAI,GAAGqC,GAAG,CAACwC,aAAa,CAAA;EAC1B,OAAC,MAAM;EACL,QAAA,IAAMC,kBAAkB,GAAGlC,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC,CAAA;EAClD,QAAA,IAAMgC,iBAAiB,GACrBD,kBAAkB,IAAIA,kBAAkB,CAACC,iBAAiB,CAAA;;EAE5D;EACA/E,QAAAA,IAAI,GAAG+E,iBAAiB,IAAIZ,gBAAgB,CAAC,eAAe,CAAC,CAAA;EAC/D,OAAA;EACF,KAAA;MAEA,IAAI,CAACnE,IAAI,EAAE;EACT,MAAA,MAAM,IAAIuE,KAAK,CACb,8DACF,CAAC,CAAA;EACH,KAAA;EAEA,IAAA,OAAOvE,IAAI,CAAA;KACZ,CAAA;EAED,EAAA,IAAMgF,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;MACtCpC,KAAK,CAACE,eAAe,GAAGF,KAAK,CAACC,UAAU,CAACoC,GAAG,CAAC,UAACnB,SAAS,EAAK;QAC1D,IAAMC,aAAa,GAAGmB,iBAAQ,CAACpB,SAAS,EAAEvB,MAAM,CAACqC,eAAe,CAAC,CAAA;;EAEjE;EACA;EACA;QACA,IAAMO,cAAc,GAAGC,kBAAS,CAACtB,SAAS,EAAEvB,MAAM,CAACqC,eAAe,CAAC,CAAA;EAEnE,MAAA,IAAMG,iBAAiB,GACrBhB,aAAa,CAACzE,MAAM,GAAG,CAAC,GAAGyE,aAAa,CAAC,CAAC,CAAC,GAAGV,SAAS,CAAA;EACzD,MAAA,IAAMgC,gBAAgB,GACpBtB,aAAa,CAACzE,MAAM,GAAG,CAAC,GACpByE,aAAa,CAACA,aAAa,CAACzE,MAAM,GAAG,CAAC,CAAC,GACvC+D,SAAS,CAAA;EAEf,MAAA,IAAMiC,oBAAoB,GAAGH,cAAc,CAACjB,IAAI,CAAC,UAAClE,IAAI,EAAA;UAAA,OACpDuF,mBAAU,CAACvF,IAAI,CAAC,CAAA;EAAA,OAClB,CAAC,CAAA;EACD,MAAA,IAAMwF,mBAAmB,GAAGL,cAAc,CACvCM,KAAK,EAAE,CACPC,OAAO,EAAE,CACTxB,IAAI,CAAC,UAAClE,IAAI,EAAA;UAAA,OAAKuF,mBAAU,CAACvF,IAAI,CAAC,CAAA;SAAC,CAAA,CAAA;QAEnC,IAAM2F,kBAAkB,GAAG,CAAC,CAAC5B,aAAa,CAACG,IAAI,CAC7C,UAAClE,IAAI,EAAA;EAAA,QAAA,OAAK4F,oBAAW,CAAC5F,IAAI,CAAC,GAAG,CAAC,CAAA;EAAA,OACjC,CAAC,CAAA;QAED,OAAO;EACL8D,QAAAA,SAAS,EAATA,SAAS;EACTC,QAAAA,aAAa,EAAbA,aAAa;EACboB,QAAAA,cAAc,EAAdA,cAAc;EAEd;EACAQ,QAAAA,kBAAkB,EAAlBA,kBAAkB;EAElB;EACAZ,QAAAA,iBAAiB,EAAjBA,iBAAiB;EACjB;EACAM,QAAAA,gBAAgB,EAAhBA,gBAAgB;EAEhB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,QAAAA,oBAAoB,EAApBA,oBAAoB;EACpB;EACAE,QAAAA,mBAAmB,EAAnBA,mBAAmB;EAEnB;EACR;EACA;EACA;EACA;EACA;EACA;EACA;UACQK,gBAAgB,EAAA,SAAAA,gBAAC7F,CAAAA,IAAI,EAAkB;EAAA,UAAA,IAAhB8F,OAAO,GAAAvE,SAAA,CAAAjC,MAAA,GAAA,CAAA,IAAAiC,SAAA,CAAA,CAAA,CAAA,KAAA8B,SAAA,GAAA9B,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI,CAAA;EACnC,UAAA,IAAMwE,OAAO,GAAGhC,aAAa,CAACrE,OAAO,CAACM,IAAI,CAAC,CAAA;YAC3C,IAAI+F,OAAO,GAAG,CAAC,EAAE;EACf;EACA;EACA;EACA;EACA;EACA;EACA,YAAA,IAAID,OAAO,EAAE;EACX,cAAA,OAAOX,cAAc,CAClBM,KAAK,CAACN,cAAc,CAACzF,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAC,CACvCkE,IAAI,CAAC,UAAC8B,EAAE,EAAA;kBAAA,OAAKT,mBAAU,CAACS,EAAE,CAAC,CAAA;iBAAC,CAAA,CAAA;EACjC,aAAA;cAEA,OAAOb,cAAc,CAClBM,KAAK,CAAC,CAAC,EAAEN,cAAc,CAACzF,OAAO,CAACM,IAAI,CAAC,CAAC,CACtC0F,OAAO,EAAE,CACTxB,IAAI,CAAC,UAAC8B,EAAE,EAAA;gBAAA,OAAKT,mBAAU,CAACS,EAAE,CAAC,CAAA;eAAC,CAAA,CAAA;EACjC,WAAA;YAEA,OAAOjC,aAAa,CAACgC,OAAO,IAAID,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;EACpD,SAAA;SACD,CAAA;EACH,KAAC,CAAC,CAAA;MAEFlD,KAAK,CAACG,cAAc,GAAGH,KAAK,CAACE,eAAe,CAACmD,MAAM,CACjD,UAACC,KAAK,EAAA;EAAA,MAAA,OAAKA,KAAK,CAACnC,aAAa,CAACzE,MAAM,GAAG,CAAC,CAAA;EAAA,KAC3C,CAAC,CAAA;;EAED;EACA,IAAA,IACEsD,KAAK,CAACG,cAAc,CAACzD,MAAM,IAAI,CAAC,IAChC,CAAC6E,gBAAgB,CAAC,eAAe,CAAC;QAClC;EACA,MAAA,MAAM,IAAII,KAAK,CACb,qGACF,CAAC,CAAA;EACH,KAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IACE3B,KAAK,CAACE,eAAe,CAACoB,IAAI,CAAC,UAACiC,CAAC,EAAA;QAAA,OAAKA,CAAC,CAACR,kBAAkB,CAAA;OAAC,CAAA,IACvD/C,KAAK,CAACE,eAAe,CAACxD,MAAM,GAAG,CAAC,EAChC;EACA,MAAA,MAAM,IAAIiF,KAAK,CACb,+KACF,CAAC,CAAA;EACH,KAAA;KACD,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAM6B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaJ,EAAE,EAAE;EACrC,IAAA,IAAMnB,aAAa,GAAGmB,EAAE,CAACnB,aAAa,CAAA;MAEtC,IAAI,CAACA,aAAa,EAAE;EAClB,MAAA,OAAA;EACF,KAAA;MAEA,IACEA,aAAa,CAAC9C,UAAU,IACxB8C,aAAa,CAAC9C,UAAU,CAAC8C,aAAa,KAAK,IAAI,EAC/C;EACA,MAAA,OAAOuB,gBAAgB,CAACvB,aAAa,CAAC9C,UAAU,CAAC,CAAA;EACnD,KAAA;EAEA,IAAA,OAAO8C,aAAa,CAAA;KACrB,CAAA;EAED,EAAA,IAAMwB,QAAQ,GAAG,SAAXA,QAAQA,CAAarG,IAAI,EAAE;MAC/B,IAAIA,IAAI,KAAK,KAAK,EAAE;EAClB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAIA,IAAI,KAAKoG,gBAAgB,CAAC9D,QAAQ,CAAC,EAAE;EACvC,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI,CAACtC,IAAI,IAAI,CAACA,IAAI,CAACsG,KAAK,EAAE;EACxBD,MAAAA,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;EAC/B,MAAA,OAAA;EACF,KAAA;MAEA1E,IAAI,CAACsG,KAAK,CAAC;EAAEC,MAAAA,aAAa,EAAE,CAAC,CAAChE,MAAM,CAACgE,aAAAA;EAAc,KAAC,CAAC,CAAA;EACrD;MACA3D,KAAK,CAACK,uBAAuB,GAAGjD,IAAI,CAAA;EAEpC,IAAA,IAAID,iBAAiB,CAACC,IAAI,CAAC,EAAE;QAC3BA,IAAI,CAACG,MAAM,EAAE,CAAA;EACf,KAAA;KACD,CAAA;EAED,EAAA,IAAMqG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,qBAAqB,EAAE;EAC1D,IAAA,IAAMzG,IAAI,GAAGmE,gBAAgB,CAAC,gBAAgB,EAAEsC,qBAAqB,CAAC,CAAA;MACtE,OAAOzG,IAAI,GAAGA,IAAI,GAAGA,IAAI,KAAK,KAAK,GAAG,KAAK,GAAGyG,qBAAqB,CAAA;KACpE,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,KAAA,EAAoD;EAAA,IAAA,IAArC7E,MAAM,GAAA6E,KAAA,CAAN7E,MAAM;QAAED,KAAK,GAAA8E,KAAA,CAAL9E,KAAK;QAAA+E,gBAAA,GAAAD,KAAA,CAAEE,UAAU;EAAVA,MAAAA,UAAU,GAAAD,gBAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,gBAAA,CAAA;EACnE9E,IAAAA,MAAM,GAAGA,MAAM,IAAIF,eAAe,CAACC,KAAK,CAAC,CAAA;EACzCmD,IAAAA,mBAAmB,EAAE,CAAA;MAErB,IAAI8B,eAAe,GAAG,IAAI,CAAA;EAE1B,IAAA,IAAIlE,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,EAAE;EACnC;EACA;EACA;EACA,MAAA,IAAMyH,cAAc,GAAGpD,kBAAkB,CAAC7B,MAAM,EAAED,KAAK,CAAC,CAAA;EACxD,MAAA,IAAMmF,cAAc,GAClBD,cAAc,IAAI,CAAC,GAAGnE,KAAK,CAACE,eAAe,CAACiE,cAAc,CAAC,GAAG1D,SAAS,CAAA;QAEzE,IAAI0D,cAAc,GAAG,CAAC,EAAE;EACtB;EACA;EACA,QAAA,IAAIF,UAAU,EAAE;EACd;EACAC,UAAAA,eAAe,GACblE,KAAK,CAACG,cAAc,CAACH,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,CAAC,CAClD+F,gBAAgB,CAAA;EACvB,SAAC,MAAM;EACL;YACAyB,eAAe,GAAGlE,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC,CAACgC,iBAAiB,CAAA;EAC7D,SAAA;SACD,MAAM,IAAI8B,UAAU,EAAE;EACrB;;EAEA;UACA,IAAII,iBAAiB,GAAGlG,SAAS,CAC/B6B,KAAK,CAACG,cAAc,EACpB,UAAAmE,KAAA,EAAA;EAAA,UAAA,IAAGnC,iBAAiB,GAAAmC,KAAA,CAAjBnC,iBAAiB,CAAA;YAAA,OAAOjD,MAAM,KAAKiD,iBAAiB,CAAA;EAAA,SACzD,CAAC,CAAA;EAED,QAAA,IACEkC,iBAAiB,GAAG,CAAC,KACpBD,cAAc,CAAClD,SAAS,KAAKhC,MAAM,IACjC6C,oBAAW,CAAC7C,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC1C,CAACW,mBAAU,CAACzD,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC3C,CAACoC,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,EAAE,KAAK,CAAE,CAAC,EACrD;EACA;EACA;EACA;EACA;EACA;EACA;EACAmF,UAAAA,iBAAiB,GAAGF,cAAc,CAAA;EACpC,SAAA;UAEA,IAAIE,iBAAiB,IAAI,CAAC,EAAE;EAC1B;EACA;EACA;EACA,UAAA,IAAME,qBAAqB,GACzBF,iBAAiB,KAAK,CAAC,GACnBrE,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,GAC/B2H,iBAAiB,GAAG,CAAC,CAAA;EAE3B,UAAA,IAAMG,gBAAgB,GAAGxE,KAAK,CAACG,cAAc,CAACoE,qBAAqB,CAAC,CAAA;EAEpEL,UAAAA,eAAe,GACblB,oBAAW,CAAC9D,MAAM,CAAC,IAAI,CAAC,GACpBsF,gBAAgB,CAAC/B,gBAAgB,GACjC+B,gBAAgB,CAAC5B,mBAAmB,CAAA;EAC5C,SAAC,MAAM,IAAI,CAAChF,UAAU,CAACqB,KAAK,CAAC,EAAE;EAC7B;EACA;YACAiF,eAAe,GAAGE,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,EAAE,KAAK,CAAC,CAAA;EAClE,SAAA;EACF,OAAC,MAAM;EACL;;EAEA;UACA,IAAIuF,gBAAgB,GAAGtG,SAAS,CAC9B6B,KAAK,CAACG,cAAc,EACpB,UAAAuE,KAAA,EAAA;EAAA,UAAA,IAAGjC,gBAAgB,GAAAiC,KAAA,CAAhBjC,gBAAgB,CAAA;YAAA,OAAOvD,MAAM,KAAKuD,gBAAgB,CAAA;EAAA,SACvD,CAAC,CAAA;EAED,QAAA,IACEgC,gBAAgB,GAAG,CAAC,KACnBL,cAAc,CAAClD,SAAS,KAAKhC,MAAM,IACjC6C,oBAAW,CAAC7C,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC1C,CAACW,mBAAU,CAACzD,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC3C,CAACoC,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,CAAE,CAAC,EAC9C;EACA;EACA;EACA;EACA;EACA;EACA;EACAuF,UAAAA,gBAAgB,GAAGN,cAAc,CAAA;EACnC,SAAA;UAEA,IAAIM,gBAAgB,IAAI,CAAC,EAAE;EACzB;EACA;EACA;EACA,UAAA,IAAMF,sBAAqB,GACzBE,gBAAgB,KAAKzE,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,GAChD,CAAC,GACD+H,gBAAgB,GAAG,CAAC,CAAA;EAE1B,UAAA,IAAMD,iBAAgB,GAAGxE,KAAK,CAACG,cAAc,CAACoE,sBAAqB,CAAC,CAAA;EAEpEL,UAAAA,eAAe,GACblB,oBAAW,CAAC9D,MAAM,CAAC,IAAI,CAAC,GACpBsF,iBAAgB,CAACrC,iBAAiB,GAClCqC,iBAAgB,CAAC9B,oBAAoB,CAAA;EAC7C,SAAC,MAAM,IAAI,CAAC9E,UAAU,CAACqB,KAAK,CAAC,EAAE;EAC7B;EACA;EACAiF,UAAAA,eAAe,GAAGE,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,CAAC,CAAA;EAC3D,SAAA;EACF,OAAA;EACF,KAAC,MAAM;EACL;EACA;EACAgF,MAAAA,eAAe,GAAG3C,gBAAgB,CAAC,eAAe,CAAC,CAAA;EACrD,KAAA;EAEA,IAAA,OAAO2C,eAAe,CAAA;KACvB,CAAA;;EAED;EACA;EACA,EAAA,IAAMS,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAalH,CAAC,EAAE;EACpC,IAAA,IAAMyB,MAAM,GAAGF,eAAe,CAACvB,CAAC,CAAC,CAAA;MAEjC,IAAIsD,kBAAkB,CAAC7B,MAAM,EAAEzB,CAAC,CAAC,IAAI,CAAC,EAAE;EACtC;EACA,MAAA,OAAA;EACF,KAAA;MAEA,IAAIgB,cAAc,CAACkB,MAAM,CAACiF,uBAAuB,EAAEnH,CAAC,CAAC,EAAE;EACrD;QACAhB,IAAI,CAACoI,UAAU,CAAC;EACd;EACA;EACA;EACA;EACA;EACA;UACAC,WAAW,EAAEnF,MAAM,CAACE,uBAAAA;EACtB,OAAC,CAAC,CAAA;EACF,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA;MACA,IAAIpB,cAAc,CAACkB,MAAM,CAACoF,iBAAiB,EAAEtH,CAAC,CAAC,EAAE;EAC/C;EACA,MAAA,OAAA;EACF,KAAA;;EAEA;MACAA,CAAC,CAACuH,cAAc,EAAE,CAAA;KACnB,CAAA;;EAED;EACA;EACA;EACA;EACA,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAahG,KAAK,EAAE;EACpC,IAAA,IAAMC,MAAM,GAAGF,eAAe,CAACC,KAAK,CAAC,CAAA;MACrC,IAAMiG,eAAe,GAAGnE,kBAAkB,CAAC7B,MAAM,EAAED,KAAK,CAAC,IAAI,CAAC,CAAA;;EAE9D;EACA,IAAA,IAAIiG,eAAe,IAAIhG,MAAM,YAAYiG,QAAQ,EAAE;EACjD,MAAA,IAAID,eAAe,EAAE;UACnBlF,KAAK,CAACK,uBAAuB,GAAGnB,MAAM,CAAA;EACxC,OAAA;EACF,KAAC,MAAM;EACL;QACAD,KAAK,CAACmG,wBAAwB,EAAE,CAAA;;EAEhC;EACA;EACA;QACA,IAAIC,QAAQ,CAAC;QACb,IAAIC,mBAAmB,GAAG,IAAI,CAAA;QAC9B,IAAItF,KAAK,CAACK,uBAAuB,EAAE;UACjC,IAAI2C,oBAAW,CAAChD,KAAK,CAACK,uBAAuB,CAAC,GAAG,CAAC,EAAE;EAClD;EACA,UAAA,IAAMkF,eAAe,GAAGxE,kBAAkB,CACxCf,KAAK,CAACK,uBACR,CAAC,CAAA;EACD;EACA;EACA;EACA;YACA,IAAQc,aAAa,GAAKnB,KAAK,CAACE,eAAe,CAACqF,eAAe,CAAC,CAAxDpE,aAAa,CAAA;EACrB,UAAA,IAAIA,aAAa,CAACzE,MAAM,GAAG,CAAC,EAAE;EAC5B;EACA,YAAA,IAAM8I,SAAS,GAAGrE,aAAa,CAAChD,SAAS,CACvC,UAACf,IAAI,EAAA;EAAA,cAAA,OAAKA,IAAI,KAAK4C,KAAK,CAACK,uBAAuB,CAAA;EAAA,aAClD,CAAC,CAAA;cACD,IAAImF,SAAS,IAAI,CAAC,EAAE;gBAClB,IAAI7F,MAAM,CAAC9B,YAAY,CAACmC,KAAK,CAACU,cAAc,CAAC,EAAE;EAC7C,gBAAA,IAAI8E,SAAS,GAAG,CAAC,GAAGrE,aAAa,CAACzE,MAAM,EAAE;EACxC2I,kBAAAA,QAAQ,GAAGlE,aAAa,CAACqE,SAAS,GAAG,CAAC,CAAC,CAAA;EACvCF,kBAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,iBAAA;EACA;EACA;EACF,eAAC,MAAM;EACL,gBAAA,IAAIE,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE;EACtBH,kBAAAA,QAAQ,GAAGlE,aAAa,CAACqE,SAAS,GAAG,CAAC,CAAC,CAAA;EACvCF,kBAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,iBAAA;EACA;EACA;EACF,eAAA;EACA;EACF,aAAA;EACF,WAAA;EACA;EACA;EACA;EACA;EACF,SAAC,MAAM;EACL;EACA;EACA;EACA;YACA,IACE,CAACtF,KAAK,CAACE,eAAe,CAACuF,IAAI,CAAC,UAAClC,CAAC,EAAA;EAAA,YAAA,OAC5BA,CAAC,CAACpC,aAAa,CAACsE,IAAI,CAAC,UAACC,CAAC,EAAA;EAAA,cAAA,OAAK1C,oBAAW,CAAC0C,CAAC,CAAC,GAAG,CAAC,CAAA;eAAC,CAAA,CAAA;EAAA,WACjD,CAAC,EACD;EACA;EACA;EACA;EACAJ,YAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,WAAA;EACF,SAAA;EACF,OAAC,MAAM;EACL;EACA;EACA;EACA;EACAA,QAAAA,mBAAmB,GAAG,KAAK,CAAA;EAC7B,OAAA;EAEA,MAAA,IAAIA,mBAAmB,EAAE;UACvBD,QAAQ,GAAGvB,eAAe,CAAC;EACzB;EACA;YACA5E,MAAM,EAAEc,KAAK,CAACK,uBAAuB;EACrC4D,UAAAA,UAAU,EAAEtE,MAAM,CAAC5B,aAAa,CAACiC,KAAK,CAACU,cAAc,CAAA;EACvD,SAAC,CAAC,CAAA;EACJ,OAAA;EAEA,MAAA,IAAI2E,QAAQ,EAAE;UACZ5B,QAAQ,CAAC4B,QAAQ,CAAC,CAAA;EACpB,OAAC,MAAM;UACL5B,QAAQ,CAACzD,KAAK,CAACK,uBAAuB,IAAIyB,mBAAmB,EAAE,CAAC,CAAA;EAClE,OAAA;EACF,KAAA;EAEA9B,IAAAA,KAAK,CAACU,cAAc,GAAGD,SAAS,CAAC;KAClC,CAAA;;EAED;EACA;EACA;EACA;EACA,EAAA,IAAMkF,WAAW,GAAG,SAAdA,WAAWA,CAAa1G,KAAK,EAAsB;EAAA,IAAA,IAApBgF,UAAU,GAAAtF,SAAA,CAAAjC,MAAA,GAAA,CAAA,IAAAiC,SAAA,CAAA,CAAA,CAAA,KAAA8B,SAAA,GAAA9B,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;MACrDqB,KAAK,CAACU,cAAc,GAAGzB,KAAK,CAAA;MAE5B,IAAMiF,eAAe,GAAGJ,eAAe,CAAC;EAAE7E,MAAAA,KAAK,EAALA,KAAK;EAAEgF,MAAAA,UAAU,EAAVA,UAAAA;EAAW,KAAC,CAAC,CAAA;EAC9D,IAAA,IAAIC,eAAe,EAAE;EACnB,MAAA,IAAItG,UAAU,CAACqB,KAAK,CAAC,EAAE;EACrB;EACA;EACA;EACA;UACAA,KAAK,CAAC+F,cAAc,EAAE,CAAA;EACxB,OAAA;QACAvB,QAAQ,CAACS,eAAe,CAAC,CAAA;EAC3B,KAAA;EACA;KACD,CAAA;;EAED,EAAA,IAAM0B,QAAQ,GAAG,SAAXA,QAAQA,CAAa3G,KAAK,EAAE;EAChC,IAAA,IACEzB,aAAa,CAACyB,KAAK,CAAC,IACpBR,cAAc,CAACkB,MAAM,CAACG,iBAAiB,EAAEb,KAAK,CAAC,KAAK,KAAK,EACzD;QACAA,KAAK,CAAC+F,cAAc,EAAE,CAAA;QACtBvI,IAAI,CAACoI,UAAU,EAAE,CAAA;EACjB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAIlF,MAAM,CAAC9B,YAAY,CAACoB,KAAK,CAAC,IAAIU,MAAM,CAAC5B,aAAa,CAACkB,KAAK,CAAC,EAAE;QAC7D0G,WAAW,CAAC1G,KAAK,EAAEU,MAAM,CAAC5B,aAAa,CAACkB,KAAK,CAAC,CAAC,CAAA;EACjD,KAAA;KACD,CAAA;EAED,EAAA,IAAM4G,UAAU,GAAG,SAAbA,UAAUA,CAAapI,CAAC,EAAE;EAC9B,IAAA,IAAMyB,MAAM,GAAGF,eAAe,CAACvB,CAAC,CAAC,CAAA;MAEjC,IAAIsD,kBAAkB,CAAC7B,MAAM,EAAEzB,CAAC,CAAC,IAAI,CAAC,EAAE;EACtC,MAAA,OAAA;EACF,KAAA;MAEA,IAAIgB,cAAc,CAACkB,MAAM,CAACiF,uBAAuB,EAAEnH,CAAC,CAAC,EAAE;EACrD,MAAA,OAAA;EACF,KAAA;MAEA,IAAIgB,cAAc,CAACkB,MAAM,CAACoF,iBAAiB,EAAEtH,CAAC,CAAC,EAAE;EAC/C,MAAA,OAAA;EACF,KAAA;MAEAA,CAAC,CAACuH,cAAc,EAAE,CAAA;MAClBvH,CAAC,CAAC2H,wBAAwB,EAAE,CAAA;KAC7B,CAAA;;EAED;EACA;EACA;;EAEA,EAAA,IAAMU,YAAY,GAAG,SAAfA,YAAYA,GAAe;EAC/B,IAAA,IAAI,CAAC9F,KAAK,CAACM,MAAM,EAAE;EACjB,MAAA,OAAA;EACF,KAAA;;EAEA;EACAhE,IAAAA,gBAAgB,CAACC,YAAY,CAACC,SAAS,EAAEC,IAAI,CAAC,CAAA;;EAE9C;EACA;MACAuD,KAAK,CAACQ,sBAAsB,GAAGb,MAAM,CAACI,iBAAiB,GACnD/B,KAAK,CAAC,YAAY;EAChByF,MAAAA,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;EACjC,KAAC,CAAC,GACF2B,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;MAEnCrC,GAAG,CAACsG,gBAAgB,CAAC,SAAS,EAAEd,YAAY,EAAE,IAAI,CAAC,CAAA;EACnDxF,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,WAAW,EAAEpB,gBAAgB,EAAE;EAClDqB,MAAAA,OAAO,EAAE,IAAI;EACbC,MAAAA,OAAO,EAAE,KAAA;EACX,KAAC,CAAC,CAAA;EACFxG,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,YAAY,EAAEpB,gBAAgB,EAAE;EACnDqB,MAAAA,OAAO,EAAE,IAAI;EACbC,MAAAA,OAAO,EAAE,KAAA;EACX,KAAC,CAAC,CAAA;EACFxG,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,OAAO,EAAEF,UAAU,EAAE;EACxCG,MAAAA,OAAO,EAAE,IAAI;EACbC,MAAAA,OAAO,EAAE,KAAA;EACX,KAAC,CAAC,CAAA;EACFxG,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,SAAS,EAAEH,QAAQ,EAAE;EACxCI,MAAAA,OAAO,EAAE,IAAI;EACbC,MAAAA,OAAO,EAAE,KAAA;EACX,KAAC,CAAC,CAAA;EAEF,IAAA,OAAOxJ,IAAI,CAAA;KACZ,CAAA;EAED,EAAA,IAAMyJ,eAAe,GAAG,SAAlBA,eAAeA,GAAe;EAClC,IAAA,IAAI,CAAClG,KAAK,CAACM,MAAM,EAAE;EACjB,MAAA,OAAA;EACF,KAAA;MAEAb,GAAG,CAAC0G,mBAAmB,CAAC,SAAS,EAAElB,YAAY,EAAE,IAAI,CAAC,CAAA;MACtDxF,GAAG,CAAC0G,mBAAmB,CAAC,WAAW,EAAExB,gBAAgB,EAAE,IAAI,CAAC,CAAA;MAC5DlF,GAAG,CAAC0G,mBAAmB,CAAC,YAAY,EAAExB,gBAAgB,EAAE,IAAI,CAAC,CAAA;MAC7DlF,GAAG,CAAC0G,mBAAmB,CAAC,OAAO,EAAEN,UAAU,EAAE,IAAI,CAAC,CAAA;MAClDpG,GAAG,CAAC0G,mBAAmB,CAAC,SAAS,EAAEP,QAAQ,EAAE,IAAI,CAAC,CAAA;EAElD,IAAA,OAAOnJ,IAAI,CAAA;KACZ,CAAA;;EAED;EACA;EACA;;EAEA,EAAA,IAAM2J,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,SAAS,EAAE;MAC3C,IAAMC,oBAAoB,GAAGD,SAAS,CAACZ,IAAI,CAAC,UAAUc,QAAQ,EAAE;QAC9D,IAAMC,YAAY,GAAG3H,KAAK,CAAC4H,IAAI,CAACF,QAAQ,CAACC,YAAY,CAAC,CAAA;EACtD,MAAA,OAAOA,YAAY,CAACf,IAAI,CAAC,UAAUrI,IAAI,EAAE;EACvC,QAAA,OAAOA,IAAI,KAAK4C,KAAK,CAACK,uBAAuB,CAAA;EAC/C,OAAC,CAAC,CAAA;EACJ,KAAC,CAAC,CAAA;;EAEF;EACA;EACA,IAAA,IAAIiG,oBAAoB,EAAE;EACxB7C,MAAAA,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;EACjC,KAAA;KACD,CAAA;;EAED;EACA;EACA,EAAA,IAAM4E,gBAAgB,GACpB,OAAOC,MAAM,KAAK,WAAW,IAAI,kBAAkB,IAAIA,MAAM,GACzD,IAAIC,gBAAgB,CAACR,eAAe,CAAC,GACrC3F,SAAS,CAAA;EAEf,EAAA,IAAMoG,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;MACtC,IAAI,CAACH,gBAAgB,EAAE;EACrB,MAAA,OAAA;EACF,KAAA;MAEAA,gBAAgB,CAACI,UAAU,EAAE,CAAA;MAC7B,IAAI9G,KAAK,CAACM,MAAM,IAAI,CAACN,KAAK,CAACO,MAAM,EAAE;EACjCP,MAAAA,KAAK,CAACC,UAAU,CAACoC,GAAG,CAAC,UAAUnB,SAAS,EAAE;EACxCwF,QAAAA,gBAAgB,CAACK,OAAO,CAAC7F,SAAS,EAAE;EAClC8F,UAAAA,OAAO,EAAE,IAAI;EACbC,UAAAA,SAAS,EAAE,IAAA;EACb,SAAC,CAAC,CAAA;EACJ,OAAC,CAAC,CAAA;EACJ,KAAA;KACD,CAAA;;EAED;EACA;EACA;;EAEAxK,EAAAA,IAAI,GAAG;MACL,IAAI6D,MAAMA,GAAG;QACX,OAAON,KAAK,CAACM,MAAM,CAAA;OACpB;MAED,IAAIC,MAAMA,GAAG;QACX,OAAOP,KAAK,CAACO,MAAM,CAAA;OACpB;MAED2G,QAAQ,EAAA,SAAAA,QAACC,CAAAA,eAAe,EAAE;QACxB,IAAInH,KAAK,CAACM,MAAM,EAAE;EAChB,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;EAEA,MAAA,IAAM8G,UAAU,GAAGzG,SAAS,CAACwG,eAAe,EAAE,YAAY,CAAC,CAAA;EAC3D,MAAA,IAAME,cAAc,GAAG1G,SAAS,CAACwG,eAAe,EAAE,gBAAgB,CAAC,CAAA;EACnE,MAAA,IAAMG,iBAAiB,GAAG3G,SAAS,CAACwG,eAAe,EAAE,mBAAmB,CAAC,CAAA;QAEzE,IAAI,CAACG,iBAAiB,EAAE;EACtBlF,QAAAA,mBAAmB,EAAE,CAAA;EACvB,OAAA;QAEApC,KAAK,CAACM,MAAM,GAAG,IAAI,CAAA;QACnBN,KAAK,CAACO,MAAM,GAAG,KAAK,CAAA;EACpBP,MAAAA,KAAK,CAACI,2BAA2B,GAAGX,GAAG,CAACwC,aAAa,CAAA;EAErDmF,MAAAA,UAAU,KAAVA,IAAAA,IAAAA,UAAU,KAAVA,KAAAA,CAAAA,IAAAA,UAAU,EAAI,CAAA;EAEd,MAAA,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,GAAS;EAC7B,QAAA,IAAID,iBAAiB,EAAE;EACrBlF,UAAAA,mBAAmB,EAAE,CAAA;EACvB,SAAA;EACA0D,QAAAA,YAAY,EAAE,CAAA;EACde,QAAAA,mBAAmB,EAAE,CAAA;EACrBQ,QAAAA,cAAc,KAAdA,IAAAA,IAAAA,cAAc,KAAdA,KAAAA,CAAAA,IAAAA,cAAc,EAAI,CAAA;SACnB,CAAA;EAED,MAAA,IAAIC,iBAAiB,EAAE;EACrBA,QAAAA,iBAAiB,CAACtH,KAAK,CAACC,UAAU,CAAC2B,MAAM,EAAE,CAAC,CAAC4F,IAAI,CAC/CD,gBAAgB,EAChBA,gBACF,CAAC,CAAA;EACD,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;EAEAA,MAAAA,gBAAgB,EAAE,CAAA;EAClB,MAAA,OAAO,IAAI,CAAA;OACZ;MAED1C,UAAU,EAAA,SAAAA,UAAC4C,CAAAA,iBAAiB,EAAE;EAC5B,MAAA,IAAI,CAACzH,KAAK,CAACM,MAAM,EAAE;EACjB,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;QAEA,IAAMoH,OAAO,GAAA9H,cAAA,CAAA;UACX+H,YAAY,EAAEhI,MAAM,CAACgI,YAAY;UACjCC,gBAAgB,EAAEjI,MAAM,CAACiI,gBAAgB;UACzCC,mBAAmB,EAAElI,MAAM,CAACkI,mBAAAA;EAAmB,OAAA,EAC5CJ,iBAAiB,CACrB,CAAA;EAEDK,MAAAA,YAAY,CAAC9H,KAAK,CAACQ,sBAAsB,CAAC,CAAC;QAC3CR,KAAK,CAACQ,sBAAsB,GAAGC,SAAS,CAAA;EAExCyF,MAAAA,eAAe,EAAE,CAAA;QACjBlG,KAAK,CAACM,MAAM,GAAG,KAAK,CAAA;QACpBN,KAAK,CAACO,MAAM,GAAG,KAAK,CAAA;EACpBsG,MAAAA,mBAAmB,EAAE,CAAA;EAErBvK,MAAAA,gBAAgB,CAACW,cAAc,CAACT,SAAS,EAAEC,IAAI,CAAC,CAAA;EAEhD,MAAA,IAAMkL,YAAY,GAAGhH,SAAS,CAAC+G,OAAO,EAAE,cAAc,CAAC,CAAA;EACvD,MAAA,IAAME,gBAAgB,GAAGjH,SAAS,CAAC+G,OAAO,EAAE,kBAAkB,CAAC,CAAA;EAC/D,MAAA,IAAMG,mBAAmB,GAAGlH,SAAS,CAAC+G,OAAO,EAAE,qBAAqB,CAAC,CAAA;QACrE,IAAM5C,WAAW,GAAGnE,SAAS,CAC3B+G,OAAO,EACP,aAAa,EACb,yBACF,CAAC,CAAA;EAEDC,MAAAA,YAAY,KAAZA,IAAAA,IAAAA,YAAY,KAAZA,KAAAA,CAAAA,IAAAA,YAAY,EAAI,CAAA;EAEhB,MAAA,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,GAAS;EAC/B/J,QAAAA,KAAK,CAAC,YAAM;EACV,UAAA,IAAI8G,WAAW,EAAE;EACfrB,YAAAA,QAAQ,CAACG,kBAAkB,CAAC5D,KAAK,CAACI,2BAA2B,CAAC,CAAC,CAAA;EACjE,WAAA;EACAwH,UAAAA,gBAAgB,KAAhBA,IAAAA,IAAAA,gBAAgB,KAAhBA,KAAAA,CAAAA,IAAAA,gBAAgB,EAAI,CAAA;EACtB,SAAC,CAAC,CAAA;SACH,CAAA;QAED,IAAI9C,WAAW,IAAI+C,mBAAmB,EAAE;EACtCA,QAAAA,mBAAmB,CACjBjE,kBAAkB,CAAC5D,KAAK,CAACI,2BAA2B,CACtD,CAAC,CAACoH,IAAI,CAACO,kBAAkB,EAAEA,kBAAkB,CAAC,CAAA;EAC9C,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;EAEAA,MAAAA,kBAAkB,EAAE,CAAA;EACpB,MAAA,OAAO,IAAI,CAAA;OACZ;MAEDnL,KAAK,EAAA,SAAAA,KAACoL,CAAAA,YAAY,EAAE;QAClB,IAAIhI,KAAK,CAACO,MAAM,IAAI,CAACP,KAAK,CAACM,MAAM,EAAE;EACjC,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;EAEA,MAAA,IAAM2H,OAAO,GAAGtH,SAAS,CAACqH,YAAY,EAAE,SAAS,CAAC,CAAA;EAClD,MAAA,IAAME,WAAW,GAAGvH,SAAS,CAACqH,YAAY,EAAE,aAAa,CAAC,CAAA;QAE1DhI,KAAK,CAACO,MAAM,GAAG,IAAI,CAAA;EACnB0H,MAAAA,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAPA,KAAAA,CAAAA,IAAAA,OAAO,EAAI,CAAA;EAEX/B,MAAAA,eAAe,EAAE,CAAA;EACjBW,MAAAA,mBAAmB,EAAE,CAAA;EAErBqB,MAAAA,WAAW,KAAXA,IAAAA,IAAAA,WAAW,KAAXA,KAAAA,CAAAA,IAAAA,WAAW,EAAI,CAAA;EACf,MAAA,OAAO,IAAI,CAAA;OACZ;MAEDhL,OAAO,EAAA,SAAAA,OAACiL,CAAAA,cAAc,EAAE;QACtB,IAAI,CAACnI,KAAK,CAACO,MAAM,IAAI,CAACP,KAAK,CAACM,MAAM,EAAE;EAClC,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;EAEA,MAAA,IAAM8H,SAAS,GAAGzH,SAAS,CAACwH,cAAc,EAAE,WAAW,CAAC,CAAA;EACxD,MAAA,IAAME,aAAa,GAAG1H,SAAS,CAACwH,cAAc,EAAE,eAAe,CAAC,CAAA;QAEhEnI,KAAK,CAACO,MAAM,GAAG,KAAK,CAAA;EACpB6H,MAAAA,SAAS,KAATA,IAAAA,IAAAA,SAAS,KAATA,KAAAA,CAAAA,IAAAA,SAAS,EAAI,CAAA;EAEbhG,MAAAA,mBAAmB,EAAE,CAAA;EACrB0D,MAAAA,YAAY,EAAE,CAAA;EACde,MAAAA,mBAAmB,EAAE,CAAA;EAErBwB,MAAAA,aAAa,KAAbA,IAAAA,IAAAA,aAAa,KAAbA,KAAAA,CAAAA,IAAAA,aAAa,EAAI,CAAA;EACjB,MAAA,OAAO,IAAI,CAAA;OACZ;MAEDC,uBAAuB,EAAA,SAAAA,uBAACC,CAAAA,iBAAiB,EAAE;EACzC,MAAA,IAAMC,eAAe,GAAG,EAAE,CAAC5G,MAAM,CAAC2G,iBAAiB,CAAC,CAAClF,MAAM,CAACoF,OAAO,CAAC,CAAA;QAEpEzI,KAAK,CAACC,UAAU,GAAGuI,eAAe,CAACnG,GAAG,CAAC,UAACrB,OAAO,EAAA;EAAA,QAAA,OAC7C,OAAOA,OAAO,KAAK,QAAQ,GAAGvB,GAAG,CAACoC,aAAa,CAACb,OAAO,CAAC,GAAGA,OAAO,CAAA;EAAA,OACpE,CAAC,CAAA;QAED,IAAIhB,KAAK,CAACM,MAAM,EAAE;EAChB8B,QAAAA,mBAAmB,EAAE,CAAA;EACvB,OAAA;EAEAyE,MAAAA,mBAAmB,EAAE,CAAA;EAErB,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;KACD,CAAA;;EAED;EACApK,EAAAA,IAAI,CAAC6L,uBAAuB,CAAC/I,QAAQ,CAAC,CAAA;EAEtC,EAAA,OAAO9C,IAAI,CAAA;EACb;;;;;;;;;;"}