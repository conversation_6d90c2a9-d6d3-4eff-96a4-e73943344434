/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("tabbable")):"function"==typeof define&&define.amd?define(["exports","tabbable"],t):(e="undefined"!=typeof globalThis?globalThis:e||self,function(){var n=e.focusTrap,o=e.focusTrap={};t(o,e.tabbable),o.noConflict=function(){return e.focusTrap=n,o}}())}(this,(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function o(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?n(Object(o),!0).forEach((function(t){a(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):n(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function a(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var r=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n.pause()}var o=e.indexOf(t);-1===o||e.splice(o,1),e.push(t)},i=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&e[e.length-1].unpause()},u=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},c=function(e){return u(e)&&!e.shiftKey},s=function(e){return u(e)&&e.shiftKey},l=function(e){return setTimeout(e,0)},b=function(e,t){var n=-1;return e.every((function(e,o){return!t(e)||(n=o,!1)})),n},d=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"==typeof e?e.apply(void 0,n):e},f=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},v=[];e.createFocusTrap=function(e,n){var a,p=(null==n?void 0:n.document)||document,m=(null==n?void 0:n.trapStack)||v,y=o({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:c,isKeyBackward:s},n),h={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},w=function(e,t,n){return e&&void 0!==e[t]?e[t]:y[n||t]},g=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return h.containerGroups.findIndex((function(t){var o=t.container,a=t.tabbableNodes;return o.contains(e)||(null==n?void 0:n.includes(o))||a.find((function(t){return t===e}))}))},T=function(e){var t=y[e];if("function"==typeof t){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];t=t.apply(void 0,o)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var r=t;if("string"==typeof t&&!(r=p.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return r},F=function(){var e=T("initialFocus");if(!1===e)return!1;if(void 0===e||!t.isFocusable(e,y.tabbableOptions))if(g(p.activeElement)>=0)e=p.activeElement;else{var n=h.tabbableGroups[0];e=n&&n.firstTabbableNode||T("fallbackFocus")}if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},N=function(){if(h.containerGroups=h.containers.map((function(e){var n=t.tabbable(e,y.tabbableOptions),o=t.focusable(e,y.tabbableOptions),a=n.length>0?n[0]:void 0,r=n.length>0?n[n.length-1]:void 0,i=o.find((function(e){return t.isTabbable(e)})),u=o.slice().reverse().find((function(e){return t.isTabbable(e)})),c=!!n.find((function(e){return t.getTabIndex(e)>0}));return{container:e,tabbableNodes:n,focusableNodes:o,posTabIndexesFound:c,firstTabbableNode:a,lastTabbableNode:r,firstDomTabbableNode:i,lastDomTabbableNode:u,nextTabbableNode:function(e){var a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=n.indexOf(e);return r<0?a?o.slice(o.indexOf(e)+1).find((function(e){return t.isTabbable(e)})):o.slice(0,o.indexOf(e)).reverse().find((function(e){return t.isTabbable(e)})):n[r+(a?1:-1)]}}})),h.tabbableGroups=h.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),h.tabbableGroups.length<=0&&!T("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(h.containerGroups.find((function(e){return e.posTabIndexesFound}))&&h.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},O=function e(t){var n=t.activeElement;if(n)return n.shadowRoot&&null!==n.shadowRoot.activeElement?e(n.shadowRoot):n},E=function e(t){!1!==t&&t!==O(document)&&(t&&t.focus?(t.focus({preventScroll:!!y.preventScroll}),h.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(t)&&t.select()):e(F()))},k=function(e){var t=T("setReturnFocus",e);return t||!1!==t&&e},P=function(e){var n=e.target,o=e.event,a=e.isBackward,r=void 0!==a&&a;n=n||f(o),N();var i=null;if(h.tabbableGroups.length>0){var c=g(n,o),s=c>=0?h.containerGroups[c]:void 0;if(c<0)i=r?h.tabbableGroups[h.tabbableGroups.length-1].lastTabbableNode:h.tabbableGroups[0].firstTabbableNode;else if(r){var l=b(h.tabbableGroups,(function(e){var t=e.firstTabbableNode;return n===t}));if(l<0&&(s.container===n||t.isFocusable(n,y.tabbableOptions)&&!t.isTabbable(n,y.tabbableOptions)&&!s.nextTabbableNode(n,!1))&&(l=c),l>=0){var d=0===l?h.tabbableGroups.length-1:l-1,v=h.tabbableGroups[d];i=t.getTabIndex(n)>=0?v.lastTabbableNode:v.lastDomTabbableNode}else u(o)||(i=s.nextTabbableNode(n,!1))}else{var p=b(h.tabbableGroups,(function(e){var t=e.lastTabbableNode;return n===t}));if(p<0&&(s.container===n||t.isFocusable(n,y.tabbableOptions)&&!t.isTabbable(n,y.tabbableOptions)&&!s.nextTabbableNode(n))&&(p=c),p>=0){var m=p===h.tabbableGroups.length-1?0:p+1,w=h.tabbableGroups[m];i=t.getTabIndex(n)>=0?w.firstTabbableNode:w.firstDomTabbableNode}else u(o)||(i=s.nextTabbableNode(n))}}else i=T("fallbackFocus");return i},D=function(e){var t=f(e);g(t,e)>=0||(d(y.clickOutsideDeactivates,e)?a.deactivate({returnFocus:y.returnFocusOnDeactivate}):d(y.allowOutsideClick,e)||e.preventDefault())},x=function(e){var n=f(e),o=g(n,e)>=0;if(o||n instanceof Document)o&&(h.mostRecentlyFocusedNode=n);else{var a;e.stopImmediatePropagation();var r=!0;if(h.mostRecentlyFocusedNode)if(t.getTabIndex(h.mostRecentlyFocusedNode)>0){var i=g(h.mostRecentlyFocusedNode),u=h.containerGroups[i].tabbableNodes;if(u.length>0){var c=u.findIndex((function(e){return e===h.mostRecentlyFocusedNode}));c>=0&&(y.isKeyForward(h.recentNavEvent)?c+1<u.length&&(a=u[c+1],r=!1):c-1>=0&&(a=u[c-1],r=!1))}}else h.containerGroups.some((function(e){return e.tabbableNodes.some((function(e){return t.getTabIndex(e)>0}))}))||(r=!1);else r=!1;r&&(a=P({target:h.mostRecentlyFocusedNode,isBackward:y.isKeyBackward(h.recentNavEvent)})),E(a||(h.mostRecentlyFocusedNode||F()))}h.recentNavEvent=void 0},G=function(e){if(!(t=e,"Escape"!==(null==t?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===d(y.escapeDeactivates,e)))return e.preventDefault(),void a.deactivate();var t;(y.isKeyForward(e)||y.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];h.recentNavEvent=e;var n=P({event:e,isBackward:t});n&&(u(e)&&e.preventDefault(),E(n))}(e,y.isKeyBackward(e))},R=function(e){var t=f(e);g(t,e)>=0||d(y.clickOutsideDeactivates,e)||d(y.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},I=function(){if(h.active)return r(m,a),h.delayInitialFocusTimer=y.delayInitialFocus?l((function(){E(F())})):E(F()),p.addEventListener("focusin",x,!0),p.addEventListener("mousedown",D,{capture:!0,passive:!1}),p.addEventListener("touchstart",D,{capture:!0,passive:!1}),p.addEventListener("click",R,{capture:!0,passive:!1}),p.addEventListener("keydown",G,{capture:!0,passive:!1}),a},j=function(){if(h.active)return p.removeEventListener("focusin",x,!0),p.removeEventListener("mousedown",D,!0),p.removeEventListener("touchstart",D,!0),p.removeEventListener("click",R,!0),p.removeEventListener("keydown",G,!0),a},B="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===h.mostRecentlyFocusedNode}))}))&&E(F())})):void 0,C=function(){B&&(B.disconnect(),h.active&&!h.paused&&h.containers.map((function(e){B.observe(e,{subtree:!0,childList:!0})})))};return(a={get active(){return h.active},get paused(){return h.paused},activate:function(e){if(h.active)return this;var t=w(e,"onActivate"),n=w(e,"onPostActivate"),o=w(e,"checkCanFocusTrap");o||N(),h.active=!0,h.paused=!1,h.nodeFocusedBeforeActivation=p.activeElement,null==t||t();var a=function(){o&&N(),I(),C(),null==n||n()};return o?(o(h.containers.concat()).then(a,a),this):(a(),this)},deactivate:function(e){if(!h.active)return this;var t=o({onDeactivate:y.onDeactivate,onPostDeactivate:y.onPostDeactivate,checkCanReturnFocus:y.checkCanReturnFocus},e);clearTimeout(h.delayInitialFocusTimer),h.delayInitialFocusTimer=void 0,j(),h.active=!1,h.paused=!1,C(),i(m,a);var n=w(t,"onDeactivate"),r=w(t,"onPostDeactivate"),u=w(t,"checkCanReturnFocus"),c=w(t,"returnFocus","returnFocusOnDeactivate");null==n||n();var s=function(){l((function(){c&&E(k(h.nodeFocusedBeforeActivation)),null==r||r()}))};return c&&u?(u(k(h.nodeFocusedBeforeActivation)).then(s,s),this):(s(),this)},pause:function(e){if(h.paused||!h.active)return this;var t=w(e,"onPause"),n=w(e,"onPostPause");return h.paused=!0,null==t||t(),j(),C(),null==n||n(),this},unpause:function(e){if(!h.paused||!h.active)return this;var t=w(e,"onUnpause"),n=w(e,"onPostUnpause");return h.paused=!1,null==t||t(),N(),I(),C(),null==n||n(),this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return h.containers=t.map((function(e){return"string"==typeof e?p.querySelector(e):e})),h.active&&N(),C(),this}}).updateContainerElements(e),a},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=focus-trap.umd.min.js.map
