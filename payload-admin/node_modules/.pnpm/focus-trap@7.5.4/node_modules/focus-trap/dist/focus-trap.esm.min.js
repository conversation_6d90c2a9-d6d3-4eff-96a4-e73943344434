/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
import{isFocusable as e,tabbable as t,focusable as n,isTabbable as o,getTabIndex as r}from"tabbable";function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var c=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n.pause()}var o=e.indexOf(t);-1===o||e.splice(o,1),e.push(t)},s=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&e[e.length-1].unpause()},l=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},d=function(e){return l(e)&&!e.shiftKey},f=function(e){return l(e)&&e.shiftKey},b=function(e){return setTimeout(e,0)},v=function(e,t){var n=-1;return e.every((function(e,o){return!t(e)||(n=o,!1)})),n},p=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"==typeof e?e.apply(void 0,n):e},m=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},y=[],h=function(a,u){var h,w=(null==u?void 0:u.document)||document,g=(null==u?void 0:u.trapStack)||y,N=i({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:d,isKeyBackward:f},u),F={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},O=function(e,t,n){return e&&void 0!==e[t]?e[t]:N[n||t]},E=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return F.containerGroups.findIndex((function(t){var o=t.container,r=t.tabbableNodes;return o.contains(e)||(null==n?void 0:n.includes(o))||r.find((function(t){return t===e}))}))},k=function(e){var t=N[e];if("function"==typeof t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];t=t.apply(void 0,o)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var a=t;if("string"==typeof t&&!(a=w.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return a},T=function(){var t=k("initialFocus");if(!1===t)return!1;if(void 0===t||!e(t,N.tabbableOptions))if(E(w.activeElement)>=0)t=w.activeElement;else{var n=F.tabbableGroups[0];t=n&&n.firstTabbableNode||k("fallbackFocus")}if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},D=function(){if(F.containerGroups=F.containers.map((function(e){var a=t(e,N.tabbableOptions),i=n(e,N.tabbableOptions),u=a.length>0?a[0]:void 0,c=a.length>0?a[a.length-1]:void 0,s=i.find((function(e){return o(e)})),l=i.slice().reverse().find((function(e){return o(e)})),d=!!a.find((function(e){return r(e)>0}));return{container:e,tabbableNodes:a,focusableNodes:i,posTabIndexesFound:d,firstTabbableNode:u,lastTabbableNode:c,firstDomTabbableNode:s,lastDomTabbableNode:l,nextTabbableNode:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=a.indexOf(e);return n<0?t?i.slice(i.indexOf(e)+1).find((function(e){return o(e)})):i.slice(0,i.indexOf(e)).reverse().find((function(e){return o(e)})):a[n+(t?1:-1)]}}})),F.tabbableGroups=F.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),F.tabbableGroups.length<=0&&!k("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(F.containerGroups.find((function(e){return e.posTabIndexesFound}))&&F.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},P=function e(t){var n=t.activeElement;if(n)return n.shadowRoot&&null!==n.shadowRoot.activeElement?e(n.shadowRoot):n},G=function e(t){!1!==t&&t!==P(document)&&(t&&t.focus?(t.focus({preventScroll:!!N.preventScroll}),F.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(t)&&t.select()):e(T()))},R=function(e){var t=k("setReturnFocus",e);return t||!1!==t&&e},x=function(t){var n=t.target,a=t.event,i=t.isBackward,u=void 0!==i&&i;n=n||m(a),D();var c=null;if(F.tabbableGroups.length>0){var s=E(n,a),d=s>=0?F.containerGroups[s]:void 0;if(s<0)c=u?F.tabbableGroups[F.tabbableGroups.length-1].lastTabbableNode:F.tabbableGroups[0].firstTabbableNode;else if(u){var f=v(F.tabbableGroups,(function(e){var t=e.firstTabbableNode;return n===t}));if(f<0&&(d.container===n||e(n,N.tabbableOptions)&&!o(n,N.tabbableOptions)&&!d.nextTabbableNode(n,!1))&&(f=s),f>=0){var b=0===f?F.tabbableGroups.length-1:f-1,p=F.tabbableGroups[b];c=r(n)>=0?p.lastTabbableNode:p.lastDomTabbableNode}else l(a)||(c=d.nextTabbableNode(n,!1))}else{var y=v(F.tabbableGroups,(function(e){var t=e.lastTabbableNode;return n===t}));if(y<0&&(d.container===n||e(n,N.tabbableOptions)&&!o(n,N.tabbableOptions)&&!d.nextTabbableNode(n))&&(y=s),y>=0){var h=y===F.tabbableGroups.length-1?0:y+1,w=F.tabbableGroups[h];c=r(n)>=0?w.firstTabbableNode:w.firstDomTabbableNode}else l(a)||(c=d.nextTabbableNode(n))}}else c=k("fallbackFocus");return c},j=function(e){var t=m(e);E(t,e)>=0||(p(N.clickOutsideDeactivates,e)?h.deactivate({returnFocus:N.returnFocusOnDeactivate}):p(N.allowOutsideClick,e)||e.preventDefault())},B=function(e){var t=m(e),n=E(t,e)>=0;if(n||t instanceof Document)n&&(F.mostRecentlyFocusedNode=t);else{var o;e.stopImmediatePropagation();var a=!0;if(F.mostRecentlyFocusedNode)if(r(F.mostRecentlyFocusedNode)>0){var i=E(F.mostRecentlyFocusedNode),u=F.containerGroups[i].tabbableNodes;if(u.length>0){var c=u.findIndex((function(e){return e===F.mostRecentlyFocusedNode}));c>=0&&(N.isKeyForward(F.recentNavEvent)?c+1<u.length&&(o=u[c+1],a=!1):c-1>=0&&(o=u[c-1],a=!1))}}else F.containerGroups.some((function(e){return e.tabbableNodes.some((function(e){return r(e)>0}))}))||(a=!1);else a=!1;a&&(o=x({target:F.mostRecentlyFocusedNode,isBackward:N.isKeyBackward(F.recentNavEvent)})),G(o||(F.mostRecentlyFocusedNode||T()))}F.recentNavEvent=void 0},I=function(e){if(!(t=e,"Escape"!==(null==t?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===p(N.escapeDeactivates,e)))return e.preventDefault(),void h.deactivate();var t;(N.isKeyForward(e)||N.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];F.recentNavEvent=e;var n=x({event:e,isBackward:t});n&&(l(e)&&e.preventDefault(),G(n))}(e,N.isKeyBackward(e))},L=function(e){var t=m(e);E(t,e)>=0||p(N.clickOutsideDeactivates,e)||p(N.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},C=function(){if(F.active)return c(g,h),F.delayInitialFocusTimer=N.delayInitialFocus?b((function(){G(T())})):G(T()),w.addEventListener("focusin",B,!0),w.addEventListener("mousedown",j,{capture:!0,passive:!1}),w.addEventListener("touchstart",j,{capture:!0,passive:!1}),w.addEventListener("click",L,{capture:!0,passive:!1}),w.addEventListener("keydown",I,{capture:!0,passive:!1}),h},A=function(){if(F.active)return w.removeEventListener("focusin",B,!0),w.removeEventListener("mousedown",j,!0),w.removeEventListener("touchstart",j,!0),w.removeEventListener("click",L,!0),w.removeEventListener("keydown",I,!0),h},S="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===F.mostRecentlyFocusedNode}))}))&&G(T())})):void 0,K=function(){S&&(S.disconnect(),F.active&&!F.paused&&F.containers.map((function(e){S.observe(e,{subtree:!0,childList:!0})})))};return(h={get active(){return F.active},get paused(){return F.paused},activate:function(e){if(F.active)return this;var t=O(e,"onActivate"),n=O(e,"onPostActivate"),o=O(e,"checkCanFocusTrap");o||D(),F.active=!0,F.paused=!1,F.nodeFocusedBeforeActivation=w.activeElement,null==t||t();var r=function(){o&&D(),C(),K(),null==n||n()};return o?(o(F.containers.concat()).then(r,r),this):(r(),this)},deactivate:function(e){if(!F.active)return this;var t=i({onDeactivate:N.onDeactivate,onPostDeactivate:N.onPostDeactivate,checkCanReturnFocus:N.checkCanReturnFocus},e);clearTimeout(F.delayInitialFocusTimer),F.delayInitialFocusTimer=void 0,A(),F.active=!1,F.paused=!1,K(),s(g,h);var n=O(t,"onDeactivate"),o=O(t,"onPostDeactivate"),r=O(t,"checkCanReturnFocus"),a=O(t,"returnFocus","returnFocusOnDeactivate");null==n||n();var u=function(){b((function(){a&&G(R(F.nodeFocusedBeforeActivation)),null==o||o()}))};return a&&r?(r(R(F.nodeFocusedBeforeActivation)).then(u,u),this):(u(),this)},pause:function(e){if(F.paused||!F.active)return this;var t=O(e,"onPause"),n=O(e,"onPostPause");return F.paused=!0,null==t||t(),A(),K(),null==n||n(),this},unpause:function(e){if(!F.paused||!F.active)return this;var t=O(e,"onUnpause"),n=O(e,"onPostUnpause");return F.paused=!1,null==t||t(),D(),C(),K(),null==n||n(),this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return F.containers=t.map((function(e){return"string"==typeof e?w.querySelector(e):e})),F.active&&D(),K(),this}}).updateContainerElements(a),h};export{h as createFocusTrap};
//# sourceMappingURL=focus-trap.esm.min.js.map
