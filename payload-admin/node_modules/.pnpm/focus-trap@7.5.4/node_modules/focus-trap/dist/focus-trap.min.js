/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tabbable");function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){o(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function o(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n.pause()}var o=e.indexOf(t);-1===o||e.splice(o,1),e.push(t)},r=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&e[e.length-1].unpause()},i=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},u=function(e){return i(e)&&!e.shiftKey},s=function(e){return i(e)&&e.shiftKey},c=function(e){return setTimeout(e,0)},l=function(e,t){var n=-1;return e.every((function(e,o){return!t(e)||(n=o,!1)})),n},b=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"==typeof e?e.apply(void 0,n):e},d=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},f=[];exports.createFocusTrap=function(t,o){var v,p=(null==o?void 0:o.document)||document,m=(null==o?void 0:o.trapStack)||f,y=n({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:u,isKeyBackward:s},o),h={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},w=function(e,t,n){return e&&void 0!==e[t]?e[t]:y[n||t]},g=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return h.containerGroups.findIndex((function(t){var o=t.container,a=t.tabbableNodes;return o.contains(e)||(null==n?void 0:n.includes(o))||a.find((function(t){return t===e}))}))},F=function(e){var t=y[e];if("function"==typeof t){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];t=t.apply(void 0,o)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var r=t;if("string"==typeof t&&!(r=p.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return r},N=function(){var t=F("initialFocus");if(!1===t)return!1;if(void 0===t||!e.isFocusable(t,y.tabbableOptions))if(g(p.activeElement)>=0)t=p.activeElement;else{var n=h.tabbableGroups[0];t=n&&n.firstTabbableNode||F("fallbackFocus")}if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},T=function(){if(h.containerGroups=h.containers.map((function(t){var n=e.tabbable(t,y.tabbableOptions),o=e.focusable(t,y.tabbableOptions),a=n.length>0?n[0]:void 0,r=n.length>0?n[n.length-1]:void 0,i=o.find((function(t){return e.isTabbable(t)})),u=o.slice().reverse().find((function(t){return e.isTabbable(t)})),s=!!n.find((function(t){return e.getTabIndex(t)>0}));return{container:t,tabbableNodes:n,focusableNodes:o,posTabIndexesFound:s,firstTabbableNode:a,lastTabbableNode:r,firstDomTabbableNode:i,lastDomTabbableNode:u,nextTabbableNode:function(t){var a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=n.indexOf(t);return r<0?a?o.slice(o.indexOf(t)+1).find((function(t){return e.isTabbable(t)})):o.slice(0,o.indexOf(t)).reverse().find((function(t){return e.isTabbable(t)})):n[r+(a?1:-1)]}}})),h.tabbableGroups=h.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),h.tabbableGroups.length<=0&&!F("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(h.containerGroups.find((function(e){return e.posTabIndexesFound}))&&h.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},O=function e(t){var n=t.activeElement;if(n)return n.shadowRoot&&null!==n.shadowRoot.activeElement?e(n.shadowRoot):n},E=function e(t){!1!==t&&t!==O(document)&&(t&&t.focus?(t.focus({preventScroll:!!y.preventScroll}),h.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(t)&&t.select()):e(N()))},k=function(e){var t=F("setReturnFocus",e);return t||!1!==t&&e},P=function(t){var n=t.target,o=t.event,a=t.isBackward,r=void 0!==a&&a;n=n||d(o),T();var u=null;if(h.tabbableGroups.length>0){var s=g(n,o),c=s>=0?h.containerGroups[s]:void 0;if(s<0)u=r?h.tabbableGroups[h.tabbableGroups.length-1].lastTabbableNode:h.tabbableGroups[0].firstTabbableNode;else if(r){var b=l(h.tabbableGroups,(function(e){var t=e.firstTabbableNode;return n===t}));if(b<0&&(c.container===n||e.isFocusable(n,y.tabbableOptions)&&!e.isTabbable(n,y.tabbableOptions)&&!c.nextTabbableNode(n,!1))&&(b=s),b>=0){var f=0===b?h.tabbableGroups.length-1:b-1,v=h.tabbableGroups[f];u=e.getTabIndex(n)>=0?v.lastTabbableNode:v.lastDomTabbableNode}else i(o)||(u=c.nextTabbableNode(n,!1))}else{var p=l(h.tabbableGroups,(function(e){var t=e.lastTabbableNode;return n===t}));if(p<0&&(c.container===n||e.isFocusable(n,y.tabbableOptions)&&!e.isTabbable(n,y.tabbableOptions)&&!c.nextTabbableNode(n))&&(p=s),p>=0){var m=p===h.tabbableGroups.length-1?0:p+1,w=h.tabbableGroups[m];u=e.getTabIndex(n)>=0?w.firstTabbableNode:w.firstDomTabbableNode}else i(o)||(u=c.nextTabbableNode(n))}}else u=F("fallbackFocus");return u},D=function(e){var t=d(e);g(t,e)>=0||(b(y.clickOutsideDeactivates,e)?v.deactivate({returnFocus:y.returnFocusOnDeactivate}):b(y.allowOutsideClick,e)||e.preventDefault())},x=function(t){var n=d(t),o=g(n,t)>=0;if(o||n instanceof Document)o&&(h.mostRecentlyFocusedNode=n);else{var a;t.stopImmediatePropagation();var r=!0;if(h.mostRecentlyFocusedNode)if(e.getTabIndex(h.mostRecentlyFocusedNode)>0){var i=g(h.mostRecentlyFocusedNode),u=h.containerGroups[i].tabbableNodes;if(u.length>0){var s=u.findIndex((function(e){return e===h.mostRecentlyFocusedNode}));s>=0&&(y.isKeyForward(h.recentNavEvent)?s+1<u.length&&(a=u[s+1],r=!1):s-1>=0&&(a=u[s-1],r=!1))}}else h.containerGroups.some((function(t){return t.tabbableNodes.some((function(t){return e.getTabIndex(t)>0}))}))||(r=!1);else r=!1;r&&(a=P({target:h.mostRecentlyFocusedNode,isBackward:y.isKeyBackward(h.recentNavEvent)})),E(a||(h.mostRecentlyFocusedNode||N()))}h.recentNavEvent=void 0},G=function(e){if(!(t=e,"Escape"!==(null==t?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===b(y.escapeDeactivates,e)))return e.preventDefault(),void v.deactivate();var t;(y.isKeyForward(e)||y.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];h.recentNavEvent=e;var n=P({event:e,isBackward:t});n&&(i(e)&&e.preventDefault(),E(n))}(e,y.isKeyBackward(e))},R=function(e){var t=d(e);g(t,e)>=0||b(y.clickOutsideDeactivates,e)||b(y.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},I=function(){if(h.active)return a(m,v),h.delayInitialFocusTimer=y.delayInitialFocus?c((function(){E(N())})):E(N()),p.addEventListener("focusin",x,!0),p.addEventListener("mousedown",D,{capture:!0,passive:!1}),p.addEventListener("touchstart",D,{capture:!0,passive:!1}),p.addEventListener("click",R,{capture:!0,passive:!1}),p.addEventListener("keydown",G,{capture:!0,passive:!1}),v},j=function(){if(h.active)return p.removeEventListener("focusin",x,!0),p.removeEventListener("mousedown",D,!0),p.removeEventListener("touchstart",D,!0),p.removeEventListener("click",R,!0),p.removeEventListener("keydown",G,!0),v},B="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===h.mostRecentlyFocusedNode}))}))&&E(N())})):void 0,L=function(){B&&(B.disconnect(),h.active&&!h.paused&&h.containers.map((function(e){B.observe(e,{subtree:!0,childList:!0})})))};return(v={get active(){return h.active},get paused(){return h.paused},activate:function(e){if(h.active)return this;var t=w(e,"onActivate"),n=w(e,"onPostActivate"),o=w(e,"checkCanFocusTrap");o||T(),h.active=!0,h.paused=!1,h.nodeFocusedBeforeActivation=p.activeElement,null==t||t();var a=function(){o&&T(),I(),L(),null==n||n()};return o?(o(h.containers.concat()).then(a,a),this):(a(),this)},deactivate:function(e){if(!h.active)return this;var t=n({onDeactivate:y.onDeactivate,onPostDeactivate:y.onPostDeactivate,checkCanReturnFocus:y.checkCanReturnFocus},e);clearTimeout(h.delayInitialFocusTimer),h.delayInitialFocusTimer=void 0,j(),h.active=!1,h.paused=!1,L(),r(m,v);var o=w(t,"onDeactivate"),a=w(t,"onPostDeactivate"),i=w(t,"checkCanReturnFocus"),u=w(t,"returnFocus","returnFocusOnDeactivate");null==o||o();var s=function(){c((function(){u&&E(k(h.nodeFocusedBeforeActivation)),null==a||a()}))};return u&&i?(i(k(h.nodeFocusedBeforeActivation)).then(s,s),this):(s(),this)},pause:function(e){if(h.paused||!h.active)return this;var t=w(e,"onPause"),n=w(e,"onPostPause");return h.paused=!0,null==t||t(),j(),L(),null==n||n(),this},unpause:function(e){if(!h.paused||!h.active)return this;var t=w(e,"onUnpause"),n=w(e,"onPostUnpause");return h.paused=!1,null==t||t(),T(),I(),L(),null==n||n(),this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return h.containers=t.map((function(e){return"string"==typeof e?p.querySelector(e):e})),h.active&&T(),L(),this}}).updateContainerElements(t),v};
//# sourceMappingURL=focus-trap.min.js.map
