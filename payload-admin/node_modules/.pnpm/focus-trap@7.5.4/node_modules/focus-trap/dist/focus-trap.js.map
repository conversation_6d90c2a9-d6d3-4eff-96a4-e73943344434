{"version": 3, "file": "focus-trap.js", "sources": ["../index.js"], "sourcesContent": ["import {\n  tabbable,\n  focusable,\n  isFocusable,\n  isTabbable,\n  getTabIndex,\n} from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e?.key === 'Escape' || e?.key === 'Esc' || e?.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e?.key === 'Tab' || e?.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element, event) {\n    const composedPath =\n      typeof event?.composedPath === 'function'\n        ? event.composedPath()\n        : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        composedPath?.includes(container) ||\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined || !isFocusable(node, config.tabbableOptions)) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      const firstTabbableNode =\n        tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      const lastTabbableNode =\n        tabbableNodes.length > 0\n          ? tabbableNodes[tabbableNodes.length - 1]\n          : undefined;\n\n      const firstDomTabbableNode = focusableNodes.find((node) =>\n        isTabbable(node)\n      );\n      const lastDomTabbableNode = focusableNodes\n        .slice()\n        .reverse()\n        .find((node) => isTabbable(node));\n\n      const posTabIndexesFound = !!tabbableNodes.find(\n        (node) => getTabIndex(node) > 0\n      );\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound,\n\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode,\n\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          const nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes\n                .slice(focusableNodes.indexOf(node) + 1)\n                .find((el) => isTabbable(el));\n            }\n\n            return focusableNodes\n              .slice(0, focusableNodes.indexOf(node))\n              .reverse()\n              .find((el) => isTabbable(el));\n          }\n\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (\n      state.containerGroups.find((g) => g.posTabIndexesFound) &&\n      state.containerGroups.length > 1\n    ) {\n      throw new Error(\n        \"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\"\n      );\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  const getActiveElement = function (el) {\n    const activeElement = el.activeElement;\n\n    if (!activeElement) {\n      return;\n    }\n\n    if (\n      activeElement.shadowRoot &&\n      activeElement.shadowRoot.activeElement !== null\n    ) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n\n    return activeElement;\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === getActiveElement(document)) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  const findNextNavNode = function ({ target, event, isBackward = false }) {\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target, event);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.lastTabbableNode\n              : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.firstTabbableNode\n              : destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate,\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  const checkFocusIn = function (event) {\n    const target = getActualTarget(event);\n    const targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      let nextNode; // next node to focus, if we find one\n      let navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if (getTabIndex(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          const mruContainerIdx = findContainerIndex(\n            state.mostRecentlyFocusedNode\n          );\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          const { tabbableNodes } = state.containerGroups[mruContainerIdx];\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            const mruTabIdx = tabbableNodes.findIndex(\n              (node) => node === state.mostRecentlyFocusedNode\n            );\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (\n            !state.containerGroups.some((g) =>\n              g.tabbableNodes.some((n) => getTabIndex(n) > 0)\n            )\n          ) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent),\n        });\n      }\n\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    state.recentNavEvent = event;\n\n    const destinationNode = findNextNavNode({ event, isBackward });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  const checkDomRemoval = function (mutations) {\n    const isFocusedNodeRemoved = mutations.some(function (mutation) {\n      const removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  const mutationObserver =\n    typeof window !== 'undefined' && 'MutationObserver' in window\n      ? new MutationObserver(checkDomRemoval)\n      : undefined;\n\n  const updateObservedNodes = function () {\n    if (!mutationObserver) {\n      return;\n    }\n\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true,\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      onActivate?.();\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate?.();\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      onDeactivate?.();\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate?.();\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause(pauseOptions) {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      const onPause = getOption(pauseOptions, 'onPause');\n      const onPostPause = getOption(pauseOptions, 'onPostPause');\n\n      state.paused = true;\n      onPause?.();\n\n      removeListeners();\n      updateObservedNodes();\n\n      onPostPause?.();\n      return this;\n    },\n\n    unpause(unpauseOptions) {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      const onUnpause = getOption(unpauseOptions, 'onUnpause');\n      const onPostUnpause = getOption(unpauseOptions, 'onPostUnpause');\n\n      state.paused = false;\n      onUnpause?.();\n\n      updateTabbableNodes();\n      addListeners();\n      updateObservedNodes();\n\n      onPostUnpause?.();\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      updateObservedNodes();\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["activeFocusTraps", "activateTrap", "trapStack", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "recentNavEvent", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "_ref", "container", "tabbableNodes", "contains", "includes", "find", "getNodeForOption", "optionValue", "_len2", "_key2", "Error", "concat", "querySelector", "getInitialFocusNode", "isFocusable", "tabbableOptions", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "focusableNodes", "focusable", "lastTabbableNode", "firstDomTabbableNode", "isTabbable", "lastDomTabbableNode", "slice", "reverse", "posTabIndexesFound", "getTabIndex", "nextTabbableNode", "forward", "nodeIdx", "el", "filter", "group", "g", "getActiveElement", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "findNextNavNode", "_ref2", "_ref2$isBackward", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "_ref3", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "_ref4", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "nextNode", "navAcrossContainers", "mruContainerIdx", "mruTabIdx", "some", "n", "checkKeyNav", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "checkDomRemoval", "mutations", "isFocusedNodeRemoved", "mutation", "removedNodes", "from", "mutationObserver", "window", "MutationObserver", "updateObservedNodes", "disconnect", "observe", "subtree", "childList", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "pauseOptions", "onPause", "onPostPause", "unpauseOptions", "onUnpause", "onPostUnpause", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAMA,gBAAgB,GAAG;AACvBC,EAAAA,YAAY,EAAAA,SAAAA,YAAAA,CAACC,SAAS,EAAEC,IAAI,EAAE;AAC5B,IAAA,IAAID,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;MACxB,IAAMC,UAAU,GAAGH,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAAA;MAClD,IAAIC,UAAU,KAAKF,IAAI,EAAE;QACvBE,UAAU,CAACC,KAAK,EAAE,CAAA;AACpB,OAAA;AACF,KAAA;AAEA,IAAA,IAAMC,SAAS,GAAGL,SAAS,CAACM,OAAO,CAACL,IAAI,CAAC,CAAA;AACzC,IAAA,IAAII,SAAS,KAAK,CAAC,CAAC,EAAE;AACpBL,MAAAA,SAAS,CAACO,IAAI,CAACN,IAAI,CAAC,CAAA;AACtB,KAAC,MAAM;AACL;AACAD,MAAAA,SAAS,CAACQ,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC,CAAA;AAC9BL,MAAAA,SAAS,CAACO,IAAI,CAACN,IAAI,CAAC,CAAA;AACtB,KAAA;GACD;AAEDQ,EAAAA,cAAc,EAAAA,SAAAA,cAAAA,CAACT,SAAS,EAAEC,IAAI,EAAE;AAC9B,IAAA,IAAMI,SAAS,GAAGL,SAAS,CAACM,OAAO,CAACL,IAAI,CAAC,CAAA;AACzC,IAAA,IAAII,SAAS,KAAK,CAAC,CAAC,EAAE;AACpBL,MAAAA,SAAS,CAACQ,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC,CAAA;AAChC,KAAA;AAEA,IAAA,IAAIL,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;MACxBF,SAAS,CAACA,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC,CAACQ,OAAO,EAAE,CAAA;AAC3C,KAAA;AACF,GAAA;AACF,CAAC,CAAA;AAED,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,IAAI,EAAE;AACxC,EAAA,OACEA,IAAI,CAACC,OAAO,IACZD,IAAI,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,OAAO,IACtC,OAAOF,IAAI,CAACG,MAAM,KAAK,UAAU,CAAA;AAErC,CAAC,CAAA;AAED,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,CAAC,EAAE;AACjC,EAAA,OAAO,CAAAA,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAC,CAAEC,GAAG,MAAK,QAAQ,IAAI,CAAAD,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAC,CAAEC,GAAG,MAAK,KAAK,IAAI,CAAAD,CAAC,KAADA,IAAAA,IAAAA,CAAC,KAADA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAC,CAAEE,OAAO,MAAK,EAAE,CAAA;AACrE,CAAC,CAAA;AAED,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAaH,CAAC,EAAE;EAC9B,OAAO,CAAAA,CAAC,KAADA,IAAAA,IAAAA,CAAC,uBAADA,CAAC,CAAEC,GAAG,MAAK,KAAK,IAAI,CAAAD,CAAC,aAADA,CAAC,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAADA,CAAC,CAAEE,OAAO,MAAK,CAAC,CAAA;AAC7C,CAAC,CAAA;;AAED;AACA,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAaJ,CAAC,EAAE;EAChC,OAAOG,UAAU,CAACH,CAAC,CAAC,IAAI,CAACA,CAAC,CAACK,QAAQ,CAAA;AACrC,CAAC,CAAA;;AAED;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaN,CAAC,EAAE;AACjC,EAAA,OAAOG,UAAU,CAACH,CAAC,CAAC,IAAIA,CAAC,CAACK,QAAQ,CAAA;AACpC,CAAC,CAAA;AAED,IAAME,KAAK,GAAG,SAARA,KAAKA,CAAaC,EAAE,EAAE;AAC1B,EAAA,OAAOC,UAAU,CAACD,EAAE,EAAE,CAAC,CAAC,CAAA;AAC1B,CAAC,CAAA;;AAED;AACA;AACA,IAAME,SAAS,GAAG,SAAZA,SAASA,CAAaC,GAAG,EAAEH,EAAE,EAAE;EACnC,IAAII,GAAG,GAAG,CAAC,CAAC,CAAA;AAEZD,EAAAA,GAAG,CAACE,KAAK,CAAC,UAAUC,KAAK,EAAEC,CAAC,EAAE;AAC5B,IAAA,IAAIP,EAAE,CAACM,KAAK,CAAC,EAAE;AACbF,MAAAA,GAAG,GAAGG,CAAC,CAAA;MACP,OAAO,KAAK,CAAC;AACf,KAAA;;IAEA,OAAO,IAAI,CAAC;AACd,GAAC,CAAC,CAAA;;AAEF,EAAA,OAAOH,GAAG,CAAA;AACZ,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMI,cAAc,GAAG,SAAjBA,cAAcA,CAAaF,KAAK,EAAa;EAAA,KAAAG,IAAAA,IAAA,GAAAC,SAAA,CAAAjC,MAAA,EAARkC,MAAM,OAAAC,KAAA,CAAAH,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAI,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA,EAAA,EAAA;AAANF,IAAAA,MAAM,CAAAE,IAAA,GAAAH,CAAAA,CAAAA,GAAAA,SAAA,CAAAG,IAAA,CAAA,CAAA;AAAA,GAAA;AAC/C,EAAA,OAAO,OAAOP,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAAQ,KAAA,CAAIH,KAAAA,CAAAA,EAAAA,MAAM,CAAC,GAAGL,KAAK,CAAA;AAC/D,CAAC,CAAA;AAED,IAAMS,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,KAAK,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;EACA,OAAOA,KAAK,CAACC,MAAM,CAACC,UAAU,IAAI,OAAOF,KAAK,CAACG,YAAY,KAAK,UAAU,GACtEH,KAAK,CAACG,YAAY,EAAE,CAAC,CAAC,CAAC,GACvBH,KAAK,CAACC,MAAM,CAAA;AAClB,CAAC,CAAA;;AAED;AACA;AACA,IAAMG,iBAAiB,GAAG,EAAE,CAAA;AAEtBC,IAAAA,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,QAAQ,EAAEC,WAAW,EAAE;AACvD;AACA;EACA,IAAMC,GAAG,GAAG,CAAAD,WAAW,KAAA,IAAA,IAAXA,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAXA,WAAW,CAAEE,QAAQ,KAAIA,QAAQ,CAAA;EAE7C,IAAMlD,SAAS,GAAG,CAAAgD,WAAW,KAAA,IAAA,IAAXA,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAXA,WAAW,CAAEhD,SAAS,KAAI6C,iBAAiB,CAAA;EAE7D,IAAMM,MAAM,GAAAC,cAAA,CAAA;AACVC,IAAAA,uBAAuB,EAAE,IAAI;AAC7BC,IAAAA,iBAAiB,EAAE,IAAI;AACvBC,IAAAA,iBAAiB,EAAE,IAAI;AACvBlC,IAAAA,YAAY,EAAZA,YAAY;AACZE,IAAAA,aAAa,EAAbA,aAAAA;AAAa,GAAA,EACVyB,WAAW,CACf,CAAA;AAED,EAAA,IAAMQ,KAAK,GAAG;AACZ;AACA;AACAC,IAAAA,UAAU,EAAE,EAAE;AAEd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,IAAAA,eAAe,EAAE,EAAE;AAAE;;AAErB;AACA;AACA;AACA;AACAC,IAAAA,cAAc,EAAE,EAAE;AAElBC,IAAAA,2BAA2B,EAAE,IAAI;AACjCC,IAAAA,uBAAuB,EAAE,IAAI;AAC7BC,IAAAA,MAAM,EAAE,KAAK;AACbC,IAAAA,MAAM,EAAE,KAAK;AAEb;AACA;AACAC,IAAAA,sBAAsB,EAAEC,SAAS;AAEjC;AACAC,IAAAA,cAAc,EAAED,SAAAA;GACjB,CAAA;EAED,IAAIhE,IAAI,CAAC;;AAET;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAMkE,SAAS,GAAG,SAAZA,SAASA,CAAIC,qBAAqB,EAAEC,UAAU,EAAEC,gBAAgB,EAAK;AACzE,IAAA,OAAOF,qBAAqB,IAC1BA,qBAAqB,CAACC,UAAU,CAAC,KAAKJ,SAAS,GAC7CG,qBAAqB,CAACC,UAAU,CAAC,GACjClB,MAAM,CAACmB,gBAAgB,IAAID,UAAU,CAAC,CAAA;GAC3C,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAME,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,OAAO,EAAE/B,KAAK,EAAE;AACnD,IAAA,IAAMG,YAAY,GAChB,QAAOH,KAAK,KAALA,IAAAA,IAAAA,KAAK,uBAALA,KAAK,CAAEG,YAAY,CAAK,KAAA,UAAU,GACrCH,KAAK,CAACG,YAAY,EAAE,GACpBqB,SAAS,CAAA;AACf;AACA;AACA;AACA,IAAA,OAAOT,KAAK,CAACE,eAAe,CAAC/B,SAAS,CACpC,UAAA8C,IAAA,EAAA;AAAA,MAAA,IAAGC,SAAS,GAAAD,IAAA,CAATC,SAAS;QAAEC,aAAa,GAAAF,IAAA,CAAbE,aAAa,CAAA;AAAA,MAAA,OACzBD,SAAS,CAACE,QAAQ,CAACJ,OAAO,CAAC;AAE3B;AACA;AACA;AACA5B,MAAAA,YAAY,KAAZA,IAAAA,IAAAA,YAAY,KAAZA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,YAAY,CAAEiC,QAAQ,CAACH,SAAS,CAAC,KACjCC,aAAa,CAACG,IAAI,CAAC,UAAClE,IAAI,EAAA;QAAA,OAAKA,IAAI,KAAK4D,OAAO,CAAA;OAAC,CAAA,CAAA;AAAA,KAClD,CAAC,CAAA;GACF,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMO,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaV,UAAU,EAAa;AACxD,IAAA,IAAIW,WAAW,GAAG7B,MAAM,CAACkB,UAAU,CAAC,CAAA;AAEpC,IAAA,IAAI,OAAOW,WAAW,KAAK,UAAU,EAAE;MAAA,KAAAC,IAAAA,KAAA,GAAA9C,SAAA,CAAAjC,MAAA,EAHSkC,MAAM,OAAAC,KAAA,CAAA4C,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;AAAN9C,QAAAA,MAAM,CAAA8C,KAAA,GAAA/C,CAAAA,CAAAA,GAAAA,SAAA,CAAA+C,KAAA,CAAA,CAAA;AAAA,OAAA;AAIpDF,MAAAA,WAAW,GAAGA,WAAW,CAAAzC,KAAA,CAAA,KAAA,CAAA,EAAIH,MAAM,CAAC,CAAA;AACtC,KAAA;IAEA,IAAI4C,WAAW,KAAK,IAAI,EAAE;MACxBA,WAAW,GAAGf,SAAS,CAAC;AAC1B,KAAA;;IAEA,IAAI,CAACe,WAAW,EAAE;AAChB,MAAA,IAAIA,WAAW,KAAKf,SAAS,IAAIe,WAAW,KAAK,KAAK,EAAE;AACtD,QAAA,OAAOA,WAAW,CAAA;AACpB,OAAA;AACA;;AAEA,MAAA,MAAM,IAAIG,KAAK,CAAA,GAAA,CAAAC,MAAA,CACRf,UAAU,iEACjB,CAAC,CAAA;AACH,KAAA;AAEA,IAAA,IAAIzD,IAAI,GAAGoE,WAAW,CAAC;;AAEvB,IAAA,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;MACnCpE,IAAI,GAAGqC,GAAG,CAACoC,aAAa,CAACL,WAAW,CAAC,CAAC;MACtC,IAAI,CAACpE,IAAI,EAAE;AACT,QAAA,MAAM,IAAIuE,KAAK,CAAA,GAAA,CAAAC,MAAA,CACRf,UAAU,0CACjB,CAAC,CAAA;AACH,OAAA;AACF,KAAA;AAEA,IAAA,OAAOzD,IAAI,CAAA;GACZ,CAAA;AAED,EAAA,IAAM0E,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;AACtC,IAAA,IAAI1E,IAAI,GAAGmE,gBAAgB,CAAC,cAAc,CAAC,CAAA;;AAE3C;IACA,IAAInE,IAAI,KAAK,KAAK,EAAE;AAClB,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AAEA,IAAA,IAAIA,IAAI,KAAKqD,SAAS,IAAI,CAACsB,oBAAW,CAAC3E,IAAI,EAAEuC,MAAM,CAACqC,eAAe,CAAC,EAAE;AACpE;MACA,IAAIjB,kBAAkB,CAACtB,GAAG,CAACwC,aAAa,CAAC,IAAI,CAAC,EAAE;QAC9C7E,IAAI,GAAGqC,GAAG,CAACwC,aAAa,CAAA;AAC1B,OAAC,MAAM;AACL,QAAA,IAAMC,kBAAkB,GAAGlC,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC,CAAA;AAClD,QAAA,IAAMgC,iBAAiB,GACrBD,kBAAkB,IAAIA,kBAAkB,CAACC,iBAAiB,CAAA;;AAE5D;AACA/E,QAAAA,IAAI,GAAG+E,iBAAiB,IAAIZ,gBAAgB,CAAC,eAAe,CAAC,CAAA;AAC/D,OAAA;AACF,KAAA;IAEA,IAAI,CAACnE,IAAI,EAAE;AACT,MAAA,MAAM,IAAIuE,KAAK,CACb,8DACF,CAAC,CAAA;AACH,KAAA;AAEA,IAAA,OAAOvE,IAAI,CAAA;GACZ,CAAA;AAED,EAAA,IAAMgF,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;IACtCpC,KAAK,CAACE,eAAe,GAAGF,KAAK,CAACC,UAAU,CAACoC,GAAG,CAAC,UAACnB,SAAS,EAAK;MAC1D,IAAMC,aAAa,GAAGmB,iBAAQ,CAACpB,SAAS,EAAEvB,MAAM,CAACqC,eAAe,CAAC,CAAA;;AAEjE;AACA;AACA;MACA,IAAMO,cAAc,GAAGC,kBAAS,CAACtB,SAAS,EAAEvB,MAAM,CAACqC,eAAe,CAAC,CAAA;AAEnE,MAAA,IAAMG,iBAAiB,GACrBhB,aAAa,CAACzE,MAAM,GAAG,CAAC,GAAGyE,aAAa,CAAC,CAAC,CAAC,GAAGV,SAAS,CAAA;AACzD,MAAA,IAAMgC,gBAAgB,GACpBtB,aAAa,CAACzE,MAAM,GAAG,CAAC,GACpByE,aAAa,CAACA,aAAa,CAACzE,MAAM,GAAG,CAAC,CAAC,GACvC+D,SAAS,CAAA;AAEf,MAAA,IAAMiC,oBAAoB,GAAGH,cAAc,CAACjB,IAAI,CAAC,UAAClE,IAAI,EAAA;QAAA,OACpDuF,mBAAU,CAACvF,IAAI,CAAC,CAAA;AAAA,OAClB,CAAC,CAAA;AACD,MAAA,IAAMwF,mBAAmB,GAAGL,cAAc,CACvCM,KAAK,EAAE,CACPC,OAAO,EAAE,CACTxB,IAAI,CAAC,UAAClE,IAAI,EAAA;QAAA,OAAKuF,mBAAU,CAACvF,IAAI,CAAC,CAAA;OAAC,CAAA,CAAA;MAEnC,IAAM2F,kBAAkB,GAAG,CAAC,CAAC5B,aAAa,CAACG,IAAI,CAC7C,UAAClE,IAAI,EAAA;AAAA,QAAA,OAAK4F,oBAAW,CAAC5F,IAAI,CAAC,GAAG,CAAC,CAAA;AAAA,OACjC,CAAC,CAAA;MAED,OAAO;AACL8D,QAAAA,SAAS,EAATA,SAAS;AACTC,QAAAA,aAAa,EAAbA,aAAa;AACboB,QAAAA,cAAc,EAAdA,cAAc;AAEd;AACAQ,QAAAA,kBAAkB,EAAlBA,kBAAkB;AAElB;AACAZ,QAAAA,iBAAiB,EAAjBA,iBAAiB;AACjB;AACAM,QAAAA,gBAAgB,EAAhBA,gBAAgB;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,QAAAA,oBAAoB,EAApBA,oBAAoB;AACpB;AACAE,QAAAA,mBAAmB,EAAnBA,mBAAmB;AAEnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;QACQK,gBAAgB,EAAA,SAAAA,gBAAC7F,CAAAA,IAAI,EAAkB;AAAA,UAAA,IAAhB8F,OAAO,GAAAvE,SAAA,CAAAjC,MAAA,GAAA,CAAA,IAAAiC,SAAA,CAAA,CAAA,CAAA,KAAA8B,SAAA,GAAA9B,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI,CAAA;AACnC,UAAA,IAAMwE,OAAO,GAAGhC,aAAa,CAACrE,OAAO,CAACM,IAAI,CAAC,CAAA;UAC3C,IAAI+F,OAAO,GAAG,CAAC,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;AACA,YAAA,IAAID,OAAO,EAAE;AACX,cAAA,OAAOX,cAAc,CAClBM,KAAK,CAACN,cAAc,CAACzF,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAC,CACvCkE,IAAI,CAAC,UAAC8B,EAAE,EAAA;gBAAA,OAAKT,mBAAU,CAACS,EAAE,CAAC,CAAA;eAAC,CAAA,CAAA;AACjC,aAAA;YAEA,OAAOb,cAAc,CAClBM,KAAK,CAAC,CAAC,EAAEN,cAAc,CAACzF,OAAO,CAACM,IAAI,CAAC,CAAC,CACtC0F,OAAO,EAAE,CACTxB,IAAI,CAAC,UAAC8B,EAAE,EAAA;cAAA,OAAKT,mBAAU,CAACS,EAAE,CAAC,CAAA;aAAC,CAAA,CAAA;AACjC,WAAA;UAEA,OAAOjC,aAAa,CAACgC,OAAO,IAAID,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AACpD,SAAA;OACD,CAAA;AACH,KAAC,CAAC,CAAA;IAEFlD,KAAK,CAACG,cAAc,GAAGH,KAAK,CAACE,eAAe,CAACmD,MAAM,CACjD,UAACC,KAAK,EAAA;AAAA,MAAA,OAAKA,KAAK,CAACnC,aAAa,CAACzE,MAAM,GAAG,CAAC,CAAA;AAAA,KAC3C,CAAC,CAAA;;AAED;AACA,IAAA,IACEsD,KAAK,CAACG,cAAc,CAACzD,MAAM,IAAI,CAAC,IAChC,CAAC6E,gBAAgB,CAAC,eAAe,CAAC;MAClC;AACA,MAAA,MAAM,IAAII,KAAK,CACb,qGACF,CAAC,CAAA;AACH,KAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACE3B,KAAK,CAACE,eAAe,CAACoB,IAAI,CAAC,UAACiC,CAAC,EAAA;MAAA,OAAKA,CAAC,CAACR,kBAAkB,CAAA;KAAC,CAAA,IACvD/C,KAAK,CAACE,eAAe,CAACxD,MAAM,GAAG,CAAC,EAChC;AACA,MAAA,MAAM,IAAIiF,KAAK,CACb,+KACF,CAAC,CAAA;AACH,KAAA;GACD,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAM6B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaJ,EAAE,EAAE;AACrC,IAAA,IAAMnB,aAAa,GAAGmB,EAAE,CAACnB,aAAa,CAAA;IAEtC,IAAI,CAACA,aAAa,EAAE;AAClB,MAAA,OAAA;AACF,KAAA;IAEA,IACEA,aAAa,CAAC9C,UAAU,IACxB8C,aAAa,CAAC9C,UAAU,CAAC8C,aAAa,KAAK,IAAI,EAC/C;AACA,MAAA,OAAOuB,gBAAgB,CAACvB,aAAa,CAAC9C,UAAU,CAAC,CAAA;AACnD,KAAA;AAEA,IAAA,OAAO8C,aAAa,CAAA;GACrB,CAAA;AAED,EAAA,IAAMwB,QAAQ,GAAG,SAAXA,QAAQA,CAAarG,IAAI,EAAE;IAC/B,IAAIA,IAAI,KAAK,KAAK,EAAE;AAClB,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAIA,IAAI,KAAKoG,gBAAgB,CAAC9D,QAAQ,CAAC,EAAE;AACvC,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAI,CAACtC,IAAI,IAAI,CAACA,IAAI,CAACsG,KAAK,EAAE;AACxBD,MAAAA,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;AAC/B,MAAA,OAAA;AACF,KAAA;IAEA1E,IAAI,CAACsG,KAAK,CAAC;AAAEC,MAAAA,aAAa,EAAE,CAAC,CAAChE,MAAM,CAACgE,aAAAA;AAAc,KAAC,CAAC,CAAA;AACrD;IACA3D,KAAK,CAACK,uBAAuB,GAAGjD,IAAI,CAAA;AAEpC,IAAA,IAAID,iBAAiB,CAACC,IAAI,CAAC,EAAE;MAC3BA,IAAI,CAACG,MAAM,EAAE,CAAA;AACf,KAAA;GACD,CAAA;AAED,EAAA,IAAMqG,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,qBAAqB,EAAE;AAC1D,IAAA,IAAMzG,IAAI,GAAGmE,gBAAgB,CAAC,gBAAgB,EAAEsC,qBAAqB,CAAC,CAAA;IACtE,OAAOzG,IAAI,GAAGA,IAAI,GAAGA,IAAI,KAAK,KAAK,GAAG,KAAK,GAAGyG,qBAAqB,CAAA;GACpE,CAAA;;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,EAAA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAAC,KAAA,EAAoD;AAAA,IAAA,IAArC7E,MAAM,GAAA6E,KAAA,CAAN7E,MAAM;MAAED,KAAK,GAAA8E,KAAA,CAAL9E,KAAK;MAAA+E,gBAAA,GAAAD,KAAA,CAAEE,UAAU;AAAVA,MAAAA,UAAU,GAAAD,gBAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,gBAAA,CAAA;AACnE9E,IAAAA,MAAM,GAAGA,MAAM,IAAIF,eAAe,CAACC,KAAK,CAAC,CAAA;AACzCmD,IAAAA,mBAAmB,EAAE,CAAA;IAErB,IAAI8B,eAAe,GAAG,IAAI,CAAA;AAE1B,IAAA,IAAIlE,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,EAAE;AACnC;AACA;AACA;AACA,MAAA,IAAMyH,cAAc,GAAGpD,kBAAkB,CAAC7B,MAAM,EAAED,KAAK,CAAC,CAAA;AACxD,MAAA,IAAMmF,cAAc,GAClBD,cAAc,IAAI,CAAC,GAAGnE,KAAK,CAACE,eAAe,CAACiE,cAAc,CAAC,GAAG1D,SAAS,CAAA;MAEzE,IAAI0D,cAAc,GAAG,CAAC,EAAE;AACtB;AACA;AACA,QAAA,IAAIF,UAAU,EAAE;AACd;AACAC,UAAAA,eAAe,GACblE,KAAK,CAACG,cAAc,CAACH,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,CAAC,CAClD+F,gBAAgB,CAAA;AACvB,SAAC,MAAM;AACL;UACAyB,eAAe,GAAGlE,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC,CAACgC,iBAAiB,CAAA;AAC7D,SAAA;OACD,MAAM,IAAI8B,UAAU,EAAE;AACrB;;AAEA;QACA,IAAII,iBAAiB,GAAGlG,SAAS,CAC/B6B,KAAK,CAACG,cAAc,EACpB,UAAAmE,KAAA,EAAA;AAAA,UAAA,IAAGnC,iBAAiB,GAAAmC,KAAA,CAAjBnC,iBAAiB,CAAA;UAAA,OAAOjD,MAAM,KAAKiD,iBAAiB,CAAA;AAAA,SACzD,CAAC,CAAA;AAED,QAAA,IACEkC,iBAAiB,GAAG,CAAC,KACpBD,cAAc,CAAClD,SAAS,KAAKhC,MAAM,IACjC6C,oBAAW,CAAC7C,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC1C,CAACW,mBAAU,CAACzD,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC3C,CAACoC,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,EAAE,KAAK,CAAE,CAAC,EACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACAmF,UAAAA,iBAAiB,GAAGF,cAAc,CAAA;AACpC,SAAA;QAEA,IAAIE,iBAAiB,IAAI,CAAC,EAAE;AAC1B;AACA;AACA;AACA,UAAA,IAAME,qBAAqB,GACzBF,iBAAiB,KAAK,CAAC,GACnBrE,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,GAC/B2H,iBAAiB,GAAG,CAAC,CAAA;AAE3B,UAAA,IAAMG,gBAAgB,GAAGxE,KAAK,CAACG,cAAc,CAACoE,qBAAqB,CAAC,CAAA;AAEpEL,UAAAA,eAAe,GACblB,oBAAW,CAAC9D,MAAM,CAAC,IAAI,CAAC,GACpBsF,gBAAgB,CAAC/B,gBAAgB,GACjC+B,gBAAgB,CAAC5B,mBAAmB,CAAA;AAC5C,SAAC,MAAM,IAAI,CAAChF,UAAU,CAACqB,KAAK,CAAC,EAAE;AAC7B;AACA;UACAiF,eAAe,GAAGE,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,EAAE,KAAK,CAAC,CAAA;AAClE,SAAA;AACF,OAAC,MAAM;AACL;;AAEA;QACA,IAAIuF,gBAAgB,GAAGtG,SAAS,CAC9B6B,KAAK,CAACG,cAAc,EACpB,UAAAuE,KAAA,EAAA;AAAA,UAAA,IAAGjC,gBAAgB,GAAAiC,KAAA,CAAhBjC,gBAAgB,CAAA;UAAA,OAAOvD,MAAM,KAAKuD,gBAAgB,CAAA;AAAA,SACvD,CAAC,CAAA;AAED,QAAA,IACEgC,gBAAgB,GAAG,CAAC,KACnBL,cAAc,CAAClD,SAAS,KAAKhC,MAAM,IACjC6C,oBAAW,CAAC7C,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC1C,CAACW,mBAAU,CAACzD,MAAM,EAAES,MAAM,CAACqC,eAAe,CAAC,IAC3C,CAACoC,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,CAAE,CAAC,EAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACAuF,UAAAA,gBAAgB,GAAGN,cAAc,CAAA;AACnC,SAAA;QAEA,IAAIM,gBAAgB,IAAI,CAAC,EAAE;AACzB;AACA;AACA;AACA,UAAA,IAAMF,sBAAqB,GACzBE,gBAAgB,KAAKzE,KAAK,CAACG,cAAc,CAACzD,MAAM,GAAG,CAAC,GAChD,CAAC,GACD+H,gBAAgB,GAAG,CAAC,CAAA;AAE1B,UAAA,IAAMD,iBAAgB,GAAGxE,KAAK,CAACG,cAAc,CAACoE,sBAAqB,CAAC,CAAA;AAEpEL,UAAAA,eAAe,GACblB,oBAAW,CAAC9D,MAAM,CAAC,IAAI,CAAC,GACpBsF,iBAAgB,CAACrC,iBAAiB,GAClCqC,iBAAgB,CAAC9B,oBAAoB,CAAA;AAC7C,SAAC,MAAM,IAAI,CAAC9E,UAAU,CAACqB,KAAK,CAAC,EAAE;AAC7B;AACA;AACAiF,UAAAA,eAAe,GAAGE,cAAc,CAACnB,gBAAgB,CAAC/D,MAAM,CAAC,CAAA;AAC3D,SAAA;AACF,OAAA;AACF,KAAC,MAAM;AACL;AACA;AACAgF,MAAAA,eAAe,GAAG3C,gBAAgB,CAAC,eAAe,CAAC,CAAA;AACrD,KAAA;AAEA,IAAA,OAAO2C,eAAe,CAAA;GACvB,CAAA;;AAED;AACA;AACA,EAAA,IAAMS,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAalH,CAAC,EAAE;AACpC,IAAA,IAAMyB,MAAM,GAAGF,eAAe,CAACvB,CAAC,CAAC,CAAA;IAEjC,IAAIsD,kBAAkB,CAAC7B,MAAM,EAAEzB,CAAC,CAAC,IAAI,CAAC,EAAE;AACtC;AACA,MAAA,OAAA;AACF,KAAA;IAEA,IAAIgB,cAAc,CAACkB,MAAM,CAACiF,uBAAuB,EAAEnH,CAAC,CAAC,EAAE;AACrD;MACAhB,IAAI,CAACoI,UAAU,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;QACAC,WAAW,EAAEnF,MAAM,CAACE,uBAAAA;AACtB,OAAC,CAAC,CAAA;AACF,MAAA,OAAA;AACF,KAAA;;AAEA;AACA;AACA;IACA,IAAIpB,cAAc,CAACkB,MAAM,CAACoF,iBAAiB,EAAEtH,CAAC,CAAC,EAAE;AAC/C;AACA,MAAA,OAAA;AACF,KAAA;;AAEA;IACAA,CAAC,CAACuH,cAAc,EAAE,CAAA;GACnB,CAAA;;AAED;AACA;AACA;AACA;AACA,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAahG,KAAK,EAAE;AACpC,IAAA,IAAMC,MAAM,GAAGF,eAAe,CAACC,KAAK,CAAC,CAAA;IACrC,IAAMiG,eAAe,GAAGnE,kBAAkB,CAAC7B,MAAM,EAAED,KAAK,CAAC,IAAI,CAAC,CAAA;;AAE9D;AACA,IAAA,IAAIiG,eAAe,IAAIhG,MAAM,YAAYiG,QAAQ,EAAE;AACjD,MAAA,IAAID,eAAe,EAAE;QACnBlF,KAAK,CAACK,uBAAuB,GAAGnB,MAAM,CAAA;AACxC,OAAA;AACF,KAAC,MAAM;AACL;MACAD,KAAK,CAACmG,wBAAwB,EAAE,CAAA;;AAEhC;AACA;AACA;MACA,IAAIC,QAAQ,CAAC;MACb,IAAIC,mBAAmB,GAAG,IAAI,CAAA;MAC9B,IAAItF,KAAK,CAACK,uBAAuB,EAAE;QACjC,IAAI2C,oBAAW,CAAChD,KAAK,CAACK,uBAAuB,CAAC,GAAG,CAAC,EAAE;AAClD;AACA,UAAA,IAAMkF,eAAe,GAAGxE,kBAAkB,CACxCf,KAAK,CAACK,uBACR,CAAC,CAAA;AACD;AACA;AACA;AACA;UACA,IAAQc,aAAa,GAAKnB,KAAK,CAACE,eAAe,CAACqF,eAAe,CAAC,CAAxDpE,aAAa,CAAA;AACrB,UAAA,IAAIA,aAAa,CAACzE,MAAM,GAAG,CAAC,EAAE;AAC5B;AACA,YAAA,IAAM8I,SAAS,GAAGrE,aAAa,CAAChD,SAAS,CACvC,UAACf,IAAI,EAAA;AAAA,cAAA,OAAKA,IAAI,KAAK4C,KAAK,CAACK,uBAAuB,CAAA;AAAA,aAClD,CAAC,CAAA;YACD,IAAImF,SAAS,IAAI,CAAC,EAAE;cAClB,IAAI7F,MAAM,CAAC9B,YAAY,CAACmC,KAAK,CAACU,cAAc,CAAC,EAAE;AAC7C,gBAAA,IAAI8E,SAAS,GAAG,CAAC,GAAGrE,aAAa,CAACzE,MAAM,EAAE;AACxC2I,kBAAAA,QAAQ,GAAGlE,aAAa,CAACqE,SAAS,GAAG,CAAC,CAAC,CAAA;AACvCF,kBAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,iBAAA;AACA;AACA;AACF,eAAC,MAAM;AACL,gBAAA,IAAIE,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE;AACtBH,kBAAAA,QAAQ,GAAGlE,aAAa,CAACqE,SAAS,GAAG,CAAC,CAAC,CAAA;AACvCF,kBAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,iBAAA;AACA;AACA;AACF,eAAA;AACA;AACF,aAAA;AACF,WAAA;AACA;AACA;AACA;AACA;AACF,SAAC,MAAM;AACL;AACA;AACA;AACA;UACA,IACE,CAACtF,KAAK,CAACE,eAAe,CAACuF,IAAI,CAAC,UAAClC,CAAC,EAAA;AAAA,YAAA,OAC5BA,CAAC,CAACpC,aAAa,CAACsE,IAAI,CAAC,UAACC,CAAC,EAAA;AAAA,cAAA,OAAK1C,oBAAW,CAAC0C,CAAC,CAAC,GAAG,CAAC,CAAA;aAAC,CAAA,CAAA;AAAA,WACjD,CAAC,EACD;AACA;AACA;AACA;AACAJ,YAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,WAAA;AACF,SAAA;AACF,OAAC,MAAM;AACL;AACA;AACA;AACA;AACAA,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAA;AAEA,MAAA,IAAIA,mBAAmB,EAAE;QACvBD,QAAQ,GAAGvB,eAAe,CAAC;AACzB;AACA;UACA5E,MAAM,EAAEc,KAAK,CAACK,uBAAuB;AACrC4D,UAAAA,UAAU,EAAEtE,MAAM,CAAC5B,aAAa,CAACiC,KAAK,CAACU,cAAc,CAAA;AACvD,SAAC,CAAC,CAAA;AACJ,OAAA;AAEA,MAAA,IAAI2E,QAAQ,EAAE;QACZ5B,QAAQ,CAAC4B,QAAQ,CAAC,CAAA;AACpB,OAAC,MAAM;QACL5B,QAAQ,CAACzD,KAAK,CAACK,uBAAuB,IAAIyB,mBAAmB,EAAE,CAAC,CAAA;AAClE,OAAA;AACF,KAAA;AAEA9B,IAAAA,KAAK,CAACU,cAAc,GAAGD,SAAS,CAAC;GAClC,CAAA;;AAED;AACA;AACA;AACA;AACA,EAAA,IAAMkF,WAAW,GAAG,SAAdA,WAAWA,CAAa1G,KAAK,EAAsB;AAAA,IAAA,IAApBgF,UAAU,GAAAtF,SAAA,CAAAjC,MAAA,GAAA,CAAA,IAAAiC,SAAA,CAAA,CAAA,CAAA,KAAA8B,SAAA,GAAA9B,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;IACrDqB,KAAK,CAACU,cAAc,GAAGzB,KAAK,CAAA;IAE5B,IAAMiF,eAAe,GAAGJ,eAAe,CAAC;AAAE7E,MAAAA,KAAK,EAALA,KAAK;AAAEgF,MAAAA,UAAU,EAAVA,UAAAA;AAAW,KAAC,CAAC,CAAA;AAC9D,IAAA,IAAIC,eAAe,EAAE;AACnB,MAAA,IAAItG,UAAU,CAACqB,KAAK,CAAC,EAAE;AACrB;AACA;AACA;AACA;QACAA,KAAK,CAAC+F,cAAc,EAAE,CAAA;AACxB,OAAA;MACAvB,QAAQ,CAACS,eAAe,CAAC,CAAA;AAC3B,KAAA;AACA;GACD,CAAA;;AAED,EAAA,IAAM0B,QAAQ,GAAG,SAAXA,QAAQA,CAAa3G,KAAK,EAAE;AAChC,IAAA,IACEzB,aAAa,CAACyB,KAAK,CAAC,IACpBR,cAAc,CAACkB,MAAM,CAACG,iBAAiB,EAAEb,KAAK,CAAC,KAAK,KAAK,EACzD;MACAA,KAAK,CAAC+F,cAAc,EAAE,CAAA;MACtBvI,IAAI,CAACoI,UAAU,EAAE,CAAA;AACjB,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAIlF,MAAM,CAAC9B,YAAY,CAACoB,KAAK,CAAC,IAAIU,MAAM,CAAC5B,aAAa,CAACkB,KAAK,CAAC,EAAE;MAC7D0G,WAAW,CAAC1G,KAAK,EAAEU,MAAM,CAAC5B,aAAa,CAACkB,KAAK,CAAC,CAAC,CAAA;AACjD,KAAA;GACD,CAAA;AAED,EAAA,IAAM4G,UAAU,GAAG,SAAbA,UAAUA,CAAapI,CAAC,EAAE;AAC9B,IAAA,IAAMyB,MAAM,GAAGF,eAAe,CAACvB,CAAC,CAAC,CAAA;IAEjC,IAAIsD,kBAAkB,CAAC7B,MAAM,EAAEzB,CAAC,CAAC,IAAI,CAAC,EAAE;AACtC,MAAA,OAAA;AACF,KAAA;IAEA,IAAIgB,cAAc,CAACkB,MAAM,CAACiF,uBAAuB,EAAEnH,CAAC,CAAC,EAAE;AACrD,MAAA,OAAA;AACF,KAAA;IAEA,IAAIgB,cAAc,CAACkB,MAAM,CAACoF,iBAAiB,EAAEtH,CAAC,CAAC,EAAE;AAC/C,MAAA,OAAA;AACF,KAAA;IAEAA,CAAC,CAACuH,cAAc,EAAE,CAAA;IAClBvH,CAAC,CAAC2H,wBAAwB,EAAE,CAAA;GAC7B,CAAA;;AAED;AACA;AACA;;AAEA,EAAA,IAAMU,YAAY,GAAG,SAAfA,YAAYA,GAAe;AAC/B,IAAA,IAAI,CAAC9F,KAAK,CAACM,MAAM,EAAE;AACjB,MAAA,OAAA;AACF,KAAA;;AAEA;AACAhE,IAAAA,gBAAgB,CAACC,YAAY,CAACC,SAAS,EAAEC,IAAI,CAAC,CAAA;;AAE9C;AACA;IACAuD,KAAK,CAACQ,sBAAsB,GAAGb,MAAM,CAACI,iBAAiB,GACnD/B,KAAK,CAAC,YAAY;AAChByF,MAAAA,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;AACjC,KAAC,CAAC,GACF2B,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;IAEnCrC,GAAG,CAACsG,gBAAgB,CAAC,SAAS,EAAEd,YAAY,EAAE,IAAI,CAAC,CAAA;AACnDxF,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,WAAW,EAAEpB,gBAAgB,EAAE;AAClDqB,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE,KAAA;AACX,KAAC,CAAC,CAAA;AACFxG,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,YAAY,EAAEpB,gBAAgB,EAAE;AACnDqB,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE,KAAA;AACX,KAAC,CAAC,CAAA;AACFxG,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,OAAO,EAAEF,UAAU,EAAE;AACxCG,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE,KAAA;AACX,KAAC,CAAC,CAAA;AACFxG,IAAAA,GAAG,CAACsG,gBAAgB,CAAC,SAAS,EAAEH,QAAQ,EAAE;AACxCI,MAAAA,OAAO,EAAE,IAAI;AACbC,MAAAA,OAAO,EAAE,KAAA;AACX,KAAC,CAAC,CAAA;AAEF,IAAA,OAAOxJ,IAAI,CAAA;GACZ,CAAA;AAED,EAAA,IAAMyJ,eAAe,GAAG,SAAlBA,eAAeA,GAAe;AAClC,IAAA,IAAI,CAAClG,KAAK,CAACM,MAAM,EAAE;AACjB,MAAA,OAAA;AACF,KAAA;IAEAb,GAAG,CAAC0G,mBAAmB,CAAC,SAAS,EAAElB,YAAY,EAAE,IAAI,CAAC,CAAA;IACtDxF,GAAG,CAAC0G,mBAAmB,CAAC,WAAW,EAAExB,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAC5DlF,GAAG,CAAC0G,mBAAmB,CAAC,YAAY,EAAExB,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAC7DlF,GAAG,CAAC0G,mBAAmB,CAAC,OAAO,EAAEN,UAAU,EAAE,IAAI,CAAC,CAAA;IAClDpG,GAAG,CAAC0G,mBAAmB,CAAC,SAAS,EAAEP,QAAQ,EAAE,IAAI,CAAC,CAAA;AAElD,IAAA,OAAOnJ,IAAI,CAAA;GACZ,CAAA;;AAED;AACA;AACA;;AAEA,EAAA,IAAM2J,eAAe,GAAG,SAAlBA,eAAeA,CAAaC,SAAS,EAAE;IAC3C,IAAMC,oBAAoB,GAAGD,SAAS,CAACZ,IAAI,CAAC,UAAUc,QAAQ,EAAE;MAC9D,IAAMC,YAAY,GAAG3H,KAAK,CAAC4H,IAAI,CAACF,QAAQ,CAACC,YAAY,CAAC,CAAA;AACtD,MAAA,OAAOA,YAAY,CAACf,IAAI,CAAC,UAAUrI,IAAI,EAAE;AACvC,QAAA,OAAOA,IAAI,KAAK4C,KAAK,CAACK,uBAAuB,CAAA;AAC/C,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;;AAEF;AACA;AACA,IAAA,IAAIiG,oBAAoB,EAAE;AACxB7C,MAAAA,QAAQ,CAAC3B,mBAAmB,EAAE,CAAC,CAAA;AACjC,KAAA;GACD,CAAA;;AAED;AACA;AACA,EAAA,IAAM4E,gBAAgB,GACpB,OAAOC,MAAM,KAAK,WAAW,IAAI,kBAAkB,IAAIA,MAAM,GACzD,IAAIC,gBAAgB,CAACR,eAAe,CAAC,GACrC3F,SAAS,CAAA;AAEf,EAAA,IAAMoG,mBAAmB,GAAG,SAAtBA,mBAAmBA,GAAe;IACtC,IAAI,CAACH,gBAAgB,EAAE;AACrB,MAAA,OAAA;AACF,KAAA;IAEAA,gBAAgB,CAACI,UAAU,EAAE,CAAA;IAC7B,IAAI9G,KAAK,CAACM,MAAM,IAAI,CAACN,KAAK,CAACO,MAAM,EAAE;AACjCP,MAAAA,KAAK,CAACC,UAAU,CAACoC,GAAG,CAAC,UAAUnB,SAAS,EAAE;AACxCwF,QAAAA,gBAAgB,CAACK,OAAO,CAAC7F,SAAS,EAAE;AAClC8F,UAAAA,OAAO,EAAE,IAAI;AACbC,UAAAA,SAAS,EAAE,IAAA;AACb,SAAC,CAAC,CAAA;AACJ,OAAC,CAAC,CAAA;AACJ,KAAA;GACD,CAAA;;AAED;AACA;AACA;;AAEAxK,EAAAA,IAAI,GAAG;IACL,IAAI6D,MAAMA,GAAG;MACX,OAAON,KAAK,CAACM,MAAM,CAAA;KACpB;IAED,IAAIC,MAAMA,GAAG;MACX,OAAOP,KAAK,CAACO,MAAM,CAAA;KACpB;IAED2G,QAAQ,EAAA,SAAAA,QAACC,CAAAA,eAAe,EAAE;MACxB,IAAInH,KAAK,CAACM,MAAM,EAAE;AAChB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,IAAM8G,UAAU,GAAGzG,SAAS,CAACwG,eAAe,EAAE,YAAY,CAAC,CAAA;AAC3D,MAAA,IAAME,cAAc,GAAG1G,SAAS,CAACwG,eAAe,EAAE,gBAAgB,CAAC,CAAA;AACnE,MAAA,IAAMG,iBAAiB,GAAG3G,SAAS,CAACwG,eAAe,EAAE,mBAAmB,CAAC,CAAA;MAEzE,IAAI,CAACG,iBAAiB,EAAE;AACtBlF,QAAAA,mBAAmB,EAAE,CAAA;AACvB,OAAA;MAEApC,KAAK,CAACM,MAAM,GAAG,IAAI,CAAA;MACnBN,KAAK,CAACO,MAAM,GAAG,KAAK,CAAA;AACpBP,MAAAA,KAAK,CAACI,2BAA2B,GAAGX,GAAG,CAACwC,aAAa,CAAA;AAErDmF,MAAAA,UAAU,KAAVA,IAAAA,IAAAA,UAAU,KAAVA,KAAAA,CAAAA,IAAAA,UAAU,EAAI,CAAA;AAEd,MAAA,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAgBA,GAAS;AAC7B,QAAA,IAAID,iBAAiB,EAAE;AACrBlF,UAAAA,mBAAmB,EAAE,CAAA;AACvB,SAAA;AACA0D,QAAAA,YAAY,EAAE,CAAA;AACde,QAAAA,mBAAmB,EAAE,CAAA;AACrBQ,QAAAA,cAAc,KAAdA,IAAAA,IAAAA,cAAc,KAAdA,KAAAA,CAAAA,IAAAA,cAAc,EAAI,CAAA;OACnB,CAAA;AAED,MAAA,IAAIC,iBAAiB,EAAE;AACrBA,QAAAA,iBAAiB,CAACtH,KAAK,CAACC,UAAU,CAAC2B,MAAM,EAAE,CAAC,CAAC4F,IAAI,CAC/CD,gBAAgB,EAChBA,gBACF,CAAC,CAAA;AACD,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAEAA,MAAAA,gBAAgB,EAAE,CAAA;AAClB,MAAA,OAAO,IAAI,CAAA;KACZ;IAED1C,UAAU,EAAA,SAAAA,UAAC4C,CAAAA,iBAAiB,EAAE;AAC5B,MAAA,IAAI,CAACzH,KAAK,CAACM,MAAM,EAAE;AACjB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;MAEA,IAAMoH,OAAO,GAAA9H,cAAA,CAAA;QACX+H,YAAY,EAAEhI,MAAM,CAACgI,YAAY;QACjCC,gBAAgB,EAAEjI,MAAM,CAACiI,gBAAgB;QACzCC,mBAAmB,EAAElI,MAAM,CAACkI,mBAAAA;AAAmB,OAAA,EAC5CJ,iBAAiB,CACrB,CAAA;AAEDK,MAAAA,YAAY,CAAC9H,KAAK,CAACQ,sBAAsB,CAAC,CAAC;MAC3CR,KAAK,CAACQ,sBAAsB,GAAGC,SAAS,CAAA;AAExCyF,MAAAA,eAAe,EAAE,CAAA;MACjBlG,KAAK,CAACM,MAAM,GAAG,KAAK,CAAA;MACpBN,KAAK,CAACO,MAAM,GAAG,KAAK,CAAA;AACpBsG,MAAAA,mBAAmB,EAAE,CAAA;AAErBvK,MAAAA,gBAAgB,CAACW,cAAc,CAACT,SAAS,EAAEC,IAAI,CAAC,CAAA;AAEhD,MAAA,IAAMkL,YAAY,GAAGhH,SAAS,CAAC+G,OAAO,EAAE,cAAc,CAAC,CAAA;AACvD,MAAA,IAAME,gBAAgB,GAAGjH,SAAS,CAAC+G,OAAO,EAAE,kBAAkB,CAAC,CAAA;AAC/D,MAAA,IAAMG,mBAAmB,GAAGlH,SAAS,CAAC+G,OAAO,EAAE,qBAAqB,CAAC,CAAA;MACrE,IAAM5C,WAAW,GAAGnE,SAAS,CAC3B+G,OAAO,EACP,aAAa,EACb,yBACF,CAAC,CAAA;AAEDC,MAAAA,YAAY,KAAZA,IAAAA,IAAAA,YAAY,KAAZA,KAAAA,CAAAA,IAAAA,YAAY,EAAI,CAAA;AAEhB,MAAA,IAAMI,kBAAkB,GAAG,SAArBA,kBAAkBA,GAAS;AAC/B/J,QAAAA,KAAK,CAAC,YAAM;AACV,UAAA,IAAI8G,WAAW,EAAE;AACfrB,YAAAA,QAAQ,CAACG,kBAAkB,CAAC5D,KAAK,CAACI,2BAA2B,CAAC,CAAC,CAAA;AACjE,WAAA;AACAwH,UAAAA,gBAAgB,KAAhBA,IAAAA,IAAAA,gBAAgB,KAAhBA,KAAAA,CAAAA,IAAAA,gBAAgB,EAAI,CAAA;AACtB,SAAC,CAAC,CAAA;OACH,CAAA;MAED,IAAI9C,WAAW,IAAI+C,mBAAmB,EAAE;AACtCA,QAAAA,mBAAmB,CACjBjE,kBAAkB,CAAC5D,KAAK,CAACI,2BAA2B,CACtD,CAAC,CAACoH,IAAI,CAACO,kBAAkB,EAAEA,kBAAkB,CAAC,CAAA;AAC9C,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAEAA,MAAAA,kBAAkB,EAAE,CAAA;AACpB,MAAA,OAAO,IAAI,CAAA;KACZ;IAEDnL,KAAK,EAAA,SAAAA,KAACoL,CAAAA,YAAY,EAAE;MAClB,IAAIhI,KAAK,CAACO,MAAM,IAAI,CAACP,KAAK,CAACM,MAAM,EAAE;AACjC,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,IAAM2H,OAAO,GAAGtH,SAAS,CAACqH,YAAY,EAAE,SAAS,CAAC,CAAA;AAClD,MAAA,IAAME,WAAW,GAAGvH,SAAS,CAACqH,YAAY,EAAE,aAAa,CAAC,CAAA;MAE1DhI,KAAK,CAACO,MAAM,GAAG,IAAI,CAAA;AACnB0H,MAAAA,OAAO,KAAPA,IAAAA,IAAAA,OAAO,KAAPA,KAAAA,CAAAA,IAAAA,OAAO,EAAI,CAAA;AAEX/B,MAAAA,eAAe,EAAE,CAAA;AACjBW,MAAAA,mBAAmB,EAAE,CAAA;AAErBqB,MAAAA,WAAW,KAAXA,IAAAA,IAAAA,WAAW,KAAXA,KAAAA,CAAAA,IAAAA,WAAW,EAAI,CAAA;AACf,MAAA,OAAO,IAAI,CAAA;KACZ;IAEDhL,OAAO,EAAA,SAAAA,OAACiL,CAAAA,cAAc,EAAE;MACtB,IAAI,CAACnI,KAAK,CAACO,MAAM,IAAI,CAACP,KAAK,CAACM,MAAM,EAAE;AAClC,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,IAAM8H,SAAS,GAAGzH,SAAS,CAACwH,cAAc,EAAE,WAAW,CAAC,CAAA;AACxD,MAAA,IAAME,aAAa,GAAG1H,SAAS,CAACwH,cAAc,EAAE,eAAe,CAAC,CAAA;MAEhEnI,KAAK,CAACO,MAAM,GAAG,KAAK,CAAA;AACpB6H,MAAAA,SAAS,KAATA,IAAAA,IAAAA,SAAS,KAATA,KAAAA,CAAAA,IAAAA,SAAS,EAAI,CAAA;AAEbhG,MAAAA,mBAAmB,EAAE,CAAA;AACrB0D,MAAAA,YAAY,EAAE,CAAA;AACde,MAAAA,mBAAmB,EAAE,CAAA;AAErBwB,MAAAA,aAAa,KAAbA,IAAAA,IAAAA,aAAa,KAAbA,KAAAA,CAAAA,IAAAA,aAAa,EAAI,CAAA;AACjB,MAAA,OAAO,IAAI,CAAA;KACZ;IAEDC,uBAAuB,EAAA,SAAAA,uBAACC,CAAAA,iBAAiB,EAAE;AACzC,MAAA,IAAMC,eAAe,GAAG,EAAE,CAAC5G,MAAM,CAAC2G,iBAAiB,CAAC,CAAClF,MAAM,CAACoF,OAAO,CAAC,CAAA;MAEpEzI,KAAK,CAACC,UAAU,GAAGuI,eAAe,CAACnG,GAAG,CAAC,UAACrB,OAAO,EAAA;AAAA,QAAA,OAC7C,OAAOA,OAAO,KAAK,QAAQ,GAAGvB,GAAG,CAACoC,aAAa,CAACb,OAAO,CAAC,GAAGA,OAAO,CAAA;AAAA,OACpE,CAAC,CAAA;MAED,IAAIhB,KAAK,CAACM,MAAM,EAAE;AAChB8B,QAAAA,mBAAmB,EAAE,CAAA;AACvB,OAAA;AAEAyE,MAAAA,mBAAmB,EAAE,CAAA;AAErB,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;GACD,CAAA;;AAED;AACApK,EAAAA,IAAI,CAAC6L,uBAAuB,CAAC/I,QAAQ,CAAC,CAAA;AAEtC,EAAA,OAAO9C,IAAI,CAAA;AACb;;;;"}