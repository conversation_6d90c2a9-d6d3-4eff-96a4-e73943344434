{"name": "focus-trap", "version": "7.5.4", "description": "Trap focus within a DOM node.", "main": "dist/focus-trap.js", "module": "dist/focus-trap.esm.js", "types": "index.d.ts", "sideEffects": false, "files": ["package.json", "README.md", "CHANGELOG.md", "SECURITY.md", "LICENSE", "index.js", "index.d.ts", "dist"], "scripts": {"demo-bundle": "npm run compile:demo", "format": "prettier --write \"{*,src/**/*,test/**/*,docs/js/**/*,.github/workflows/*,cypress/**/*}.+(js|yml)\"", "format:check": "prettier --check \"{*,src/**/*,test/**/*,docs/js/**/*,.github/workflows/*,cypress/**/*}.+(js|yml)\"", "format:watch": "onchange \"{*,src/**/*,test/**/*,docs/js/**/*,.github/workflows/*,cypress/**/*}.+(js|yml)\" -- prettier --write {{changed}}", "lint": "eslint \"*.js\" \"docs/js/**/*.js\" \"cypress/**/*.js\"", "clean": "rm -rf ./dist", "compile:esm": "cross-env BUILD_ENV=esm BABEL_ENV=esm rollup -c", "compile:cjs": "cross-env BUILD_ENV=cjs BABEL_ENV=es5 rollup -c", "compile:umd": "cross-env BUILD_ENV=umd BABEL_ENV=es5 rollup -c", "compile:demo": "cross-env BUILD_ENV=demo BABEL_ENV=es5 rollup -c", "compile": "npm run compile:esm && npm run compile:cjs && npm run compile:umd", "build": "npm run clean && npm run compile", "start": "npm run compile:demo -- --watch --environment SERVE,RELOAD", "start:cypress": "npm run compile:demo -- --environment SERVE,IS_CYPRESS_ENV:\"$CYPRESS_BROWSER\"", "test:types": "tsc index.d.ts", "test:unit": "echo \"No unit tests to run!\"", "test:e2e": "ELECTRON_ENABLE_LOGGING=1 start-server-and-test start:cypress 9966 'cypress run --browser $CYPRESS_BROWSER --headless'", "test:e2e:chrome": "CYPRESS_BROWSER=chrome npm run test:e2e", "test:e2e:dev": "ELECTRON_ENABLE_LOGGING=1 start-server-and-test start:cypress 9966 'cypress open'", "test": "npm run format:check && npm run lint && npm run test:unit && npm run test:types && npm run test:e2e:chrome", "prepare": "npm run build", "prepublishOnly": "npm run test && npm run build", "release": "npm run build && changeset publish", "all-contributors": "all-contributors"}, "repository": {"type": "git", "url": "git+https://github.com/focus-trap/focus-trap.git"}, "keywords": ["focus", "accessibility", "trap", "capture", "keyboard", "modal"], "author": {"name": "<PERSON>", "url": "http://davidtheclark.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/focus-trap/focus-trap/issues"}, "homepage": "https://github.com/focus-trap/focus-trap#readme", "dependencies": {"tabbable": "^6.2.0"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.0", "@babel/eslint-parser": "^7.22.15", "@babel/preset-env": "^7.22.20", "@changesets/cli": "^2.26.2", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.5", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@testing-library/cypress": "^10.0.1", "@types/jquery": "^3.5.22", "all-contributors-cli": "^6.26.1", "babel-loader": "^9.1.3", "cross-env": "^7.0.3", "cypress": "^13.3.0", "cypress-plugin-tab": "^1.0.5", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-jest": "^27.4.2", "onchange": "^7.1.0", "prettier": "^3.0.3", "rollup": "^2.79.1", "rollup-plugin-inject-process-env": "^1.3.1", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-serve": "^2.0.2", "rollup-plugin-sourcemaps": "^0.6.3", "start-server-and-test": "^2.0.1", "typescript": "^5.2.2"}}