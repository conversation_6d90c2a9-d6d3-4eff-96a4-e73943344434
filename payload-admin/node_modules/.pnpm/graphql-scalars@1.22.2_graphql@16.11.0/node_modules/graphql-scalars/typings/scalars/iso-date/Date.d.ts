/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON>
 * All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { GraphQLScalarType } from 'graphql';
import type { GraphQLScalarTypeConfig } from 'graphql';
export declare const GraphQLDateConfig: GraphQLScalarTypeConfig<Date, string>;
/**
 * An RFC 3339 compliant date scalar.
 *
 * Input:
 *    This scalar takes an RFC 3339 date string as input and
 *    parses it to a javascript Date.
 *
 * Output:
 *    This scalar serializes javascript Dates and
 *    RFC 3339 date strings to RFC 3339 date strings.
 */
export declare const GraphQLDate: GraphQLScalarType<Date, string>;
