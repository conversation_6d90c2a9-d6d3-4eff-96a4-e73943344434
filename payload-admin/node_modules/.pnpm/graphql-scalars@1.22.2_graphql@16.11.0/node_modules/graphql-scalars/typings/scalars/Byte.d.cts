/// <reference types="node" />
import { GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
type BufferJson = {
    type: 'Buffer';
    data: number[];
};
export declare const GraphQLByteConfig: GraphQLScalarTypeConfig<Buffer | string | <PERSON><PERSON><PERSON><PERSON><PERSON>, Buffer>;
export declare const GraphQLByte: GraphQLScalarType<string | Buffer<PERSON>son | <PERSON><PERSON><PERSON>, Buffer>;
export {};
