/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON>
 * All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { GraphQLScalarType } from 'graphql';
import type { GraphQLScalarTypeConfig } from 'graphql';
export declare const GraphQLDateTimeConfig: GraphQLScalarTypeConfig<Date, Date>;
/**
 * An RFC 3339 compliant date-time scalar.
 *
 * Input:
 *    This scalar takes an RFC 3339 date-time string as input and
 *    parses it to a javascript Date.
 *
 * Output:
 *    This scalar serializes javascript Dates,
 *    RFC 3339 date-time strings and unix timestamps
 *    to RFC 3339 UTC date-time strings.
 */
export declare const GraphQLDateTime: GraphQLScalarType<Date, Date>;
