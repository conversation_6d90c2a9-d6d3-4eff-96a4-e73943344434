/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON>
 * All rights reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
export declare const parseTime: (time: string) => Date;
export declare const serializeTime: (date: Date) => string;
export declare const serializeTimeString: (time: string) => string;
export declare const parseDate: (date: string) => Date;
export declare const serializeDate: (date: Date) => string;
export declare const parseDateTime: (dateTime: string) => Date;
export declare const serializeDateTimeString: (dateTime: string) => Date;
