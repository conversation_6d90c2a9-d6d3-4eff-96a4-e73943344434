{"name": "graphql-scalars", "version": "1.22.2", "description": "A collection of scalar types not included in base GraphQL.", "sideEffects": false, "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}, "dependencies": {"tslib": "^2.5.0"}, "repository": {"type": "git", "url": "https://github.com/Urigo/graphql-scalars.git"}, "license": "MIT", "engines": {"node": ">=10"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}