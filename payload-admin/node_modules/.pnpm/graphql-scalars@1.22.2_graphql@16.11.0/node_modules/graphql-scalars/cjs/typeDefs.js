"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Currency = exports.USCurrency = exports.URL = exports.SafeInt = exports.RGBA = exports.RGB = exports.PostalCode = exports.PositiveInt = exports.PositiveFloat = exports.Port = exports.PhoneNumber = exports.NonPositiveInt = exports.NonPositiveFloat = exports.NonNegativeInt = exports.NonNegativeFloat = exports.NonEmptyString = exports.NegativeInt = exports.NegativeFloat = exports.MAC = exports.JSONObject = exports.JSON = exports.Longitude = exports.Latitude = exports.JWT = exports.ISBN = exports.IPv6 = exports.IPv4 = exports.IP = exports.IBAN = exports.HSLA = exports.HSL = exports.HexColorCode = exports.Hexadecimal = exports.UUID = exports.EmailAddress = exports.LocalEndTime = exports.LocalDateTime = exports.LocalTime = exports.LocalDate = exports.ISO8601Duration = exports.Duration = exports.UtcOffset = exports.DateTimeISO = exports.DateTime = exports.TimeZone = exports.Timestamp = exports.Time = exports.Date = exports.Byte = exports.BigInt = void 0;
exports.typeDefs = exports.IPCPatent = exports.LCCSubclass = exports.DeweyDecimal = exports.Locale = exports.CountryCode = exports.DID = exports.Void = exports.ObjectID = exports.Long = exports.GUID = exports.UnsignedInt = exports.UnsignedFloat = exports.SemVer = exports.Cuid = exports.AccountNumber = exports.RoutingNumber = void 0;
exports.BigInt = 'scalar BigInt';
exports.Byte = 'scalar Byte';
exports.Date = 'scalar Date';
exports.Time = 'scalar Time';
exports.Timestamp = 'scalar Timestamp';
exports.TimeZone = 'scalar TimeZone';
exports.DateTime = 'scalar DateTime';
exports.DateTimeISO = 'scalar DateTimeISO';
exports.UtcOffset = 'scalar UtcOffset';
exports.Duration = 'scalar Duration';
exports.ISO8601Duration = 'scalar ISO8601Duration';
exports.LocalDate = 'scalar LocalDate';
exports.LocalTime = 'scalar LocalTime';
exports.LocalDateTime = 'scalar LocalDateTime';
exports.LocalEndTime = 'scalar LocalEndTime';
exports.EmailAddress = 'scalar EmailAddress';
exports.UUID = `scalar UUID`;
exports.Hexadecimal = `scalar Hexadecimal`;
exports.HexColorCode = `scalar HexColorCode`;
exports.HSL = `scalar HSL`;
exports.HSLA = `scalar HSLA`;
exports.IBAN = `scalar IBAN`;
exports.IP = `scalar IP`;
exports.IPv4 = `scalar IPv4`;
exports.IPv6 = `scalar IPv6`;
exports.ISBN = `scalar ISBN`;
exports.JWT = `scalar JWT`;
exports.Latitude = `scalar Latitude`;
exports.Longitude = `scalar Longitude`;
exports.JSON = `scalar JSON`;
exports.JSONObject = `scalar JSONObject`;
exports.MAC = `scalar MAC`;
exports.NegativeFloat = 'scalar NegativeFloat';
exports.NegativeInt = 'scalar NegativeInt';
exports.NonEmptyString = 'scalar NonEmptyString';
exports.NonNegativeFloat = 'scalar NonNegativeFloat';
exports.NonNegativeInt = 'scalar NonNegativeInt';
exports.NonPositiveFloat = 'scalar NonPositiveFloat';
exports.NonPositiveInt = 'scalar NonPositiveInt';
exports.PhoneNumber = 'scalar PhoneNumber';
exports.Port = `scalar Port`;
exports.PositiveFloat = 'scalar PositiveFloat';
exports.PositiveInt = 'scalar PositiveInt';
exports.PostalCode = 'scalar PostalCode';
exports.RGB = `scalar RGB`;
exports.RGBA = `scalar RGBA`;
exports.SafeInt = `scalar SafeInt`;
exports.URL = 'scalar URL';
exports.USCurrency = `scalar USCurrency`;
exports.Currency = `scalar Currency`;
exports.RoutingNumber = 'scalar RoutingNumber';
exports.AccountNumber = 'scalar AccountNumber';
exports.Cuid = 'scalar Cuid';
exports.SemVer = 'scalar SemVer';
exports.UnsignedFloat = 'scalar UnsignedFloat';
exports.UnsignedInt = 'scalar UnsignedInt';
exports.GUID = `scalar GUID`;
exports.Long = 'scalar Long';
exports.ObjectID = 'scalar ObjectID';
exports.Void = 'scalar Void';
exports.DID = 'scalar DID';
exports.CountryCode = 'scalar CountryCode';
exports.Locale = 'scalar Locale';
exports.DeweyDecimal = 'scalar DeweyDecimal';
exports.LCCSubclass = 'scalar LCCSubclass';
exports.IPCPatent = 'scalar IPCPatent';
exports.typeDefs = [
    exports.Date,
    exports.Time,
    exports.DateTime,
    exports.DateTimeISO,
    exports.Timestamp,
    exports.TimeZone,
    exports.UtcOffset,
    exports.Duration,
    exports.ISO8601Duration,
    exports.LocalDate,
    exports.LocalTime,
    exports.LocalDateTime,
    exports.LocalEndTime,
    exports.EmailAddress,
    exports.NegativeFloat,
    exports.NegativeInt,
    exports.NonEmptyString,
    exports.NonNegativeFloat,
    exports.NonNegativeInt,
    exports.NonPositiveFloat,
    exports.NonPositiveInt,
    exports.PhoneNumber,
    exports.PositiveFloat,
    exports.PositiveInt,
    exports.PostalCode,
    exports.UnsignedFloat,
    exports.UnsignedInt,
    exports.URL,
    exports.BigInt,
    exports.Long,
    exports.Byte,
    exports.UUID,
    exports.GUID,
    exports.Hexadecimal,
    exports.HexColorCode,
    exports.HSL,
    exports.HSLA,
    exports.IP,
    exports.IPv4,
    exports.IPv6,
    exports.ISBN,
    exports.JWT,
    exports.Latitude,
    exports.Longitude,
    exports.MAC,
    exports.Port,
    exports.RGB,
    exports.RGBA,
    exports.SafeInt,
    exports.USCurrency,
    exports.Currency,
    exports.JSON,
    exports.JSONObject,
    exports.IBAN,
    exports.ObjectID,
    exports.Void,
    exports.DID,
    exports.CountryCode,
    exports.Locale,
    exports.RoutingNumber,
    exports.AccountNumber,
    exports.Cuid,
    exports.SemVer,
    exports.DeweyDecimal,
    exports.LCCSubclass,
    exports.IPCPatent,
];
