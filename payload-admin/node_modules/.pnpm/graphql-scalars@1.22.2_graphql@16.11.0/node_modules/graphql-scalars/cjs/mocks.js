"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Locale = exports.CountryCode = exports.DID = exports.SafeInt = exports.Void = exports.IBAN = exports.JSONObject = exports.JSON = exports.Currency = exports.USCurrency = exports.Longitude = exports.Latitude = exports.JWT = exports.ISBN = exports.RGBA = exports.RGB = exports.Port = exports.MAC = exports.IPv6 = exports.IPv4 = exports.IP = exports.HSLA = exports.HSL = exports.Hexadecimal = exports.HexColorCode = exports.UUID = exports.PostalCode = exports.PositiveInt = exports.PositiveFloat = exports.ObjectID = exports.PhoneNumber = exports.NonPositiveInt = exports.NonPositiveFloat = exports.NonNegativeInt = exports.NonNegativeFloat = exports.NonEmptyString = exports.NegativeInt = exports.NegativeFloat = exports.EmailAddress = exports.LocalEndTime = exports.LocalDateTime = exports.LocalTime = exports.LocalDate = exports.Duration = exports.UtcOffset = exports.TimeZone = exports.Timestamp = exports.DateTimeISO = exports.DateTime = exports.Time = void 0;
exports.ISO8601Duration = exports.Byte = exports.BigInt = exports.Long = exports.GUID = exports.UnsignedFloat = exports.UnsignedInt = exports.URL = exports.Date = exports.IPCPatent = exports.LCCSubclass = exports.DeweyDecimal = exports.SemVer = exports.Cuid = exports.AccountNumber = exports.RoutingNumber = void 0;
const BigIntMock = () => BigInt(Number.MAX_SAFE_INTEGER);
exports.Long = BigIntMock;
exports.BigInt = BigIntMock;
const ByteMock = () => new Uint8Array([1988, 1981, 1965, 1963, 1959, 1955]);
exports.Byte = ByteMock;
const DateMock = () => '2007-12-03';
exports.Date = DateMock;
const Time = () => '10:15:30Z';
exports.Time = Time;
const DateTime = () => '2007-12-03T10:15:30Z';
exports.DateTime = DateTime;
const DateTimeISO = () => '2007-12-03T10:15:30Z';
exports.DateTimeISO = DateTimeISO;
const Timestamp = () => **********;
exports.Timestamp = Timestamp;
const TimeZone = () => 'Etc/UTC';
exports.TimeZone = TimeZone;
const UtcOffset = () => '+03:00';
exports.UtcOffset = UtcOffset;
const Duration = () => 'P3Y6M4DT12H30M5S';
exports.Duration = Duration;
exports.ISO8601Duration = exports.Duration;
const LocalDate = () => '2020-07-19';
exports.LocalDate = LocalDate;
const LocalTime = () => '08:45:59';
exports.LocalTime = LocalTime;
const LocalDateTime = () => '2020-07-19T08:45:59';
exports.LocalDateTime = LocalDateTime;
const LocalEndTime = () => '24:00:00';
exports.LocalEndTime = LocalEndTime;
const EmailAddress = () => '<EMAIL>';
exports.EmailAddress = EmailAddress;
const NegativeFloat = () => -123.45;
exports.NegativeFloat = NegativeFloat;
const NegativeInt = () => -123;
exports.NegativeInt = NegativeInt;
const NonEmptyString = () => 'string';
exports.NonEmptyString = NonEmptyString;
const NonNegativeFloat = () => 123.45;
exports.NonNegativeFloat = NonNegativeFloat;
exports.UnsignedFloat = exports.NonNegativeFloat;
const NonNegativeInt = () => 123;
exports.NonNegativeInt = NonNegativeInt;
exports.UnsignedInt = exports.NonNegativeInt;
const NonPositiveFloat = () => -123.45;
exports.NonPositiveFloat = NonPositiveFloat;
const NonPositiveInt = () => -123;
exports.NonPositiveInt = NonPositiveInt;
const PhoneNumber = () => '+17895551234';
exports.PhoneNumber = PhoneNumber;
const ObjectID = () => '5e5677d71bdc2ae76344968c';
exports.ObjectID = ObjectID;
const PositiveFloat = () => 123.45;
exports.PositiveFloat = PositiveFloat;
const PositiveInt = () => 123;
exports.PositiveInt = PositiveInt;
const PostalCode = () => '60031';
exports.PostalCode = PostalCode;
const URLMock = () => new URL('http://www.test.com/');
exports.URL = URLMock;
// https://stackoverflow.com/questions/105034/create-guid-uuid-in-javascript
const UUID = () => {
    // Public Domain/MIT
    let d = new Date().getTime();
    if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
        d += performance.now(); // use high-precision timer if available
    }
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        // tslint:disable-next-line: no-bitwise
        const r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        // tslint:disable-next-line: no-bitwise
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
};
exports.UUID = UUID;
exports.GUID = exports.UUID;
const HexColorCode = () => '#' + Math.floor(Math.random() * 16777215).toString(16);
exports.HexColorCode = HexColorCode;
const Hexadecimal = () => Math.floor(Math.random() * 16777215).toString(16);
exports.Hexadecimal = Hexadecimal;
const randomVal = (min, max) => {
    return Math.floor(Math.random() * (max - min) + 1) + min;
};
// https://codepen.io/meowwwls/pen/jbEJRp
const HSL = () => `hsl(${randomVal(0, 360)}, ${randomVal(30, 95)}%, ${randomVal(30, 80)}%)`;
exports.HSL = HSL;
const HSLA = () => `hsla(${randomVal(0, 360)}, ${randomVal(30, 95)}%, ${randomVal(30, 80)}%, ${Math.random()})`;
exports.HSLA = HSLA;
const IP = () => '2001:0db8:85a3:0000:0000:8a2e:0370:7334';
exports.IP = IP;
// https://stackoverflow.com/questions/43464519/creating-fake-ip-address-using-javascript
const IPv4 = () => Math.floor(Math.random() * 255) +
    1 +
    '.' +
    (Math.floor(Math.random() * 255) + 0) +
    '.' +
    (Math.floor(Math.random() * 255) + 0) +
    '.' +
    (Math.floor(Math.random() * 255) + 0);
exports.IPv4 = IPv4;
const IPv6 = () => '2001:0db8:85a3:0000:0000:8a2e:0370:7334';
exports.IPv6 = IPv6;
// http://jsfiddle.net/guest271314/qhbC9/
const MAC = () => 'XX:XX:XX:XX:XX:XX'.replace(/X/g, () => '0123456789ABCDEF'.charAt(Math.floor(Math.random() * 16)));
exports.MAC = MAC;
const Port = () => randomVal(0, 65535);
exports.Port = Port;
const RGB = () => `rgb(${randomVal(0, 255)}, ${randomVal(0, 255)}, ${randomVal(0, 255)})`;
exports.RGB = RGB;
const RGBA = () => `rgba(${randomVal(0, 255)}, ${randomVal(0, 255)}, ${randomVal(0, 255)}, ${Math.random()})`;
exports.RGBA = RGBA;
const ISBN = () => `978-3-16-148410-0`;
exports.ISBN = ISBN;
const JWT = () => {
    // HEADER: {
    //   "alg": "HS256",
    //   "typ": "JWT"
    // }
    //
    // PAYLOAD: {
    //   "sub": "1234567890",
    //   "iat": 1516239022,
    //   "project": "graphql-scalars"
    // }
    //
    // SIGNATURE: {
    //   HMACSHA256(
    //     base64UrlEncode(header) + "." +
    //     base64UrlEncode(payload),
    //     password
    //   )
    // }
    return `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwiaWF0IjoxNTE2MjM5MDIyLCJwcm9qZWN0IjoiZ3JhcGhxbC1zY2FsYXJzIn0.nYdrSfE2nNRAgpiEU1uKgn2AYYKLo28Z0nhPXvsuIww`;
};
exports.JWT = JWT;
const Latitude = () => 41.902782;
exports.Latitude = Latitude;
const Longitude = () => 12.496366;
exports.Longitude = Longitude;
const USCurrency = () => 1000;
exports.USCurrency = USCurrency;
const Currency = () => 'USD';
exports.Currency = Currency;
const JSON = () => ({});
exports.JSON = JSON;
const JSONObject = () => ({});
exports.JSONObject = JSONObject;
const IBAN = () => '******************';
exports.IBAN = IBAN;
const Void = () => null;
exports.Void = Void;
const SafeInt = () => Number.MAX_SAFE_INTEGER;
exports.SafeInt = SafeInt;
const DID = () => 'did:example:123456789abcdefghi';
exports.DID = DID;
const CountryCode = () => 'US';
exports.CountryCode = CountryCode;
const Locale = () => 'zh-cmn-Hans-CN';
exports.Locale = Locale;
const RoutingNumber = () => '*********';
exports.RoutingNumber = RoutingNumber;
const AccountNumber = () => '************';
exports.AccountNumber = AccountNumber;
const Cuid = () => 'cjld2cyuq0000t3rmniod1foy';
exports.Cuid = Cuid;
const SemVer = () => '1.0.0-alpha.1';
exports.SemVer = SemVer;
const DeweyDecimal = () => '435.4357';
exports.DeweyDecimal = DeweyDecimal;
const LCCSubclass = () => 'KBM';
exports.LCCSubclass = LCCSubclass;
const IPCPatent = () => 'G06F 12/803';
exports.IPCPatent = IPCPatent;
