"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphQLHSL = exports.GraphQLHSLConfig = void 0;
const graphql_1 = require("graphql");
const error_js_1 = require("../error.js");
const HSL_REGEX = /^hsl\(\s*(-?\d+|-?\d*.\d+)\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)%\s*\)$/;
const validate = (value, ast) => {
    if (typeof value !== 'string') {
        throw (0, error_js_1.createGraphQLError)(`Value is not string: ${value}`, ast ? { nodes: ast } : undefined);
    }
    if (!HSL_REGEX.test(value)) {
        throw (0, error_js_1.createGraphQLError)(`Value is not a valid HSL color: ${value}`, ast ? { nodes: ast } : undefined);
    }
    return value;
};
const specifiedByURL = 'https://developer.mozilla.org/en-US/docs/Web/CSS/color_value#hsl()_and_hsla()';
exports.GraphQLHSLConfig = {
    name: `HSL`,
    description: `A field whose value is a CSS HSL color: https://developer.mozilla.org/en-US/docs/Web/CSS/color_value#hsl()_and_hsla().`,
    serialize(value) {
        return validate(value);
    },
    parseValue(value) {
        return validate(value);
    },
    parseLiteral(ast) {
        if (ast.kind !== graphql_1.Kind.STRING) {
            throw (0, error_js_1.createGraphQLError)(`Can only validate strings as HSL colors but got a: ${ast.kind}`, {
                nodes: ast,
            });
        }
        return validate(ast.value, ast);
    },
    specifiedByURL,
    specifiedByUrl: specifiedByURL,
    extensions: {
        codegenScalarType: 'string',
        jsonSchema: {
            title: 'HSL',
            type: 'string',
            pattern: HSL_REGEX.source,
        },
    },
};
exports.GraphQLHSL = new graphql_1.GraphQLScalarType(exports.GraphQLHSLConfig);
