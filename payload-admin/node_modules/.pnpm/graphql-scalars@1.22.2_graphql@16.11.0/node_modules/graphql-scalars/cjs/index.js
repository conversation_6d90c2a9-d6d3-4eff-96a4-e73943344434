"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSONDefinition = exports.CurrencyDefinition = exports.USCurrencyDefinition = exports.RGBADefinition = exports.RGBDefinition = exports.PortDefinition = exports.MACDefinition = exports.LongitudeDefinition = exports.LatitudeDefinition = exports.JWTDefinition = exports.ISBNDefinition = exports.IPv6Definition = exports.IPv4Definition = exports.IPDefinition = exports.HSLADefinition = exports.HSLDefinition = exports.HexColorCodeDefinition = exports.HexadecimalTypeDefinition = exports.GUIDDefinition = exports.UUIDDefinition = exports.SafeIntDefinition = exports.LongTypeDefinition = exports.ByteTypeDefinition = exports.BigIntTypeDefinition = exports.URLTypeDefinition = exports.UnsignedIntTypeDefinition = exports.UnsignedFloatTypeDefinition = exports.PostalCodeTypeDefinition = exports.PositiveIntTypeDefinition = exports.PositiveFloatTypeDefinition = exports.PhoneNumberTypeDefinition = exports.NonPositiveIntTypeDefinition = exports.NonPositiveFloatTypeDefinition = exports.NonNegativeIntTypeDefinition = exports.NonNegativeFloatTypeDefinition = exports.NonEmptyStringTypeDefinition = exports.NegativeIntTypeDefinition = exports.NegativeFloatTypeDefinition = exports.EmailAddressTypeDefinition = exports.LocalEndTimeTypeDefinition = exports.LocalDateTimeTypeDefinition = exports.LocalTimeTypeDefinition = exports.LocalDateTypeDefinition = exports.UtcOffsetTypeDefinition = exports.TimeZoneTypeDefinition = exports.TimestampTypeDefinition = exports.DateTimeISOTypeDefinition = exports.DateTimeTypeDefinition = exports.TimeTypeDefinition = exports.DateTypeDefinition = void 0;
exports.GUIDResolver = exports.UUIDResolver = exports.SafeIntResolver = exports.LongResolver = exports.ByteResolver = exports.BigIntResolver = exports.URLResolver = exports.UnsignedIntResolver = exports.UnsignedFloatResolver = exports.PostalCodeResolver = exports.PositiveIntResolver = exports.PositiveFloatResolver = exports.PhoneNumberResolver = exports.NonPositiveIntResolver = exports.NonPositiveFloatResolver = exports.NonNegativeIntResolver = exports.NonNegativeFloatResolver = exports.NonEmptyStringResolver = exports.NegativeIntResolver = exports.NegativeFloatResolver = exports.EmailAddressResolver = exports.LocalEndTimeResolver = exports.LocalDateTimeResolver = exports.LocalTimeResolver = exports.LocalDateResolver = exports.ISO8601DurationResolver = exports.DurationResolver = exports.UtcOffsetResolver = exports.TimeZoneResolver = exports.TimestampResolver = exports.DateTimeISOResolver = exports.DateTimeResolver = exports.TimeResolver = exports.DateResolver = exports.typeDefs = exports.IPCPatentDefinition = exports.LCCSubclassDefinition = exports.DeweyDecimalDefinition = exports.SemVerDefinition = exports.CuidDefinition = exports.AccountNumberDefinition = exports.RoutingNumberDefinition = exports.LocaleDefinition = exports.CountryCodeDefinition = exports.DurationTypeDefinition = exports.DIDDefinition = exports.VoidTypeDefinition = exports.ObjectIDTypeDefinition = exports.IBANTypeDefinition = exports.JSONObjectDefinition = void 0;
exports.NonNegativeFloatMock = exports.NonEmptyStringMock = exports.NegativeIntMock = exports.NegativeFloatMock = exports.EmailAddressMock = exports.LocalEndTimeMock = exports.LocalDateTimeMock = exports.LocalTimeMock = exports.LocalDateMock = exports.UtcOffsetMock = exports.TimeZoneMock = exports.TimestampMock = exports.ISO8601DurationMock = exports.DurationMock = exports.DateTimeISOMock = exports.DateTimeMock = exports.TimeMock = exports.DateMock = exports.resolvers = exports.GraphQLIPCPatentResolver = exports.GraphQLDeweyDecimalResolver = exports.SemVerResolver = exports.CuidResolver = exports.AccountNumberResolver = exports.RoutingNumberResolver = exports.LocaleResolver = exports.CountryCodeResolver = exports.DIDResolver = exports.VoidResolver = exports.ObjectIDResolver = exports.IBANResolver = exports.JSONObjectResolver = exports.JSONResolver = exports.CurrencyResolver = exports.USCurrencyResolver = exports.RGBAResolver = exports.RGBResolver = exports.PortResolver = exports.MACResolver = exports.LongitudeResolver = exports.LatitudeResolver = exports.JWTResolver = exports.ISBNResolver = exports.IPv6Resolver = exports.IPv4Resolver = exports.IPResolver = exports.HSLAResolver = exports.HSLResolver = exports.HexColorCodeResolver = exports.HexadecimalResolver = void 0;
exports.RegularExpression = exports.mocks = exports.IPCPatentMock = exports.LCCSubclassMock = exports.DeweyDecimalMock = exports.SemVerMock = exports.CuidMock = exports.AccountNumberMock = exports.RoutingNumberMock = exports.LocaleMock = exports.CountryCodeMock = exports.DIDMock = exports.VoidMock = exports.ObjectIDMock = exports.IBANMock = exports.JSONObjectMock = exports.JSONMock = exports.CurrencyMock = exports.USCurrencyMock = exports.RGBAMock = exports.RGBMock = exports.PortMock = exports.MACMock = exports.LongitudeMock = exports.LatitudeMock = exports.JWTMock = exports.ISBNMock = exports.IPv6Mock = exports.IPv4Mock = exports.IPMock = exports.HSLAMock = exports.HSLMock = exports.HexColorCodeMock = exports.HexadecimalMock = exports.GUIDMock = exports.UUIDMock = exports.SafeIntMock = exports.LongMock = exports.ByteMock = exports.BigIntMock = exports.URLMock = exports.UnsignedIntMock = exports.UnsignedFloatMock = exports.PostalCodeMock = exports.PositiveIntMock = exports.PositiveFloatMock = exports.PhoneNumberMock = exports.NonPositiveIntMock = exports.NonPositiveFloatMock = exports.NonNegativeIntMock = void 0;
exports.GraphQLUSCurrency = exports.GraphQLRGBA = exports.GraphQLRGB = exports.GraphQLPort = exports.GraphQLMAC = exports.GraphQLLongitude = exports.GraphQLLatitude = exports.GraphQLJWT = exports.GraphQLISBN = exports.GraphQLIPv6 = exports.GraphQLIPv4 = exports.GraphQLIP = exports.GraphQLHSLA = exports.GraphQLHSL = exports.GraphQLHexColorCode = exports.GraphQLHexadecimal = exports.GraphQLGUID = exports.GraphQLUUID = exports.GraphQLSafeInt = exports.GraphQLLong = exports.GraphQLByte = exports.GraphQLBigInt = exports.GraphQLURL = exports.GraphQLUnsignedInt = exports.GraphQLUnsignedFloat = exports.GraphQLPostalCode = exports.GraphQLPositiveInt = exports.GraphQLPositiveFloat = exports.GraphQLPhoneNumber = exports.GraphQLNonPositiveInt = exports.GraphQLNonPositiveFloat = exports.GraphQLNonNegativeInt = exports.GraphQLNonNegativeFloat = exports.GraphQLNonEmptyString = exports.GraphQLNegativeInt = exports.GraphQLNegativeFloat = exports.GraphQLEmailAddress = exports.GraphQLLocalEndTime = exports.GraphQLLocalDateTime = exports.GraphQLLocalTime = exports.GraphQLLocalDate = exports.GraphQLISO8601Duration = exports.GraphQLDuration = exports.GraphQLUtcOffset = exports.GraphQLTimeZone = exports.GraphQLTimestamp = exports.GraphQLDateTimeISO = exports.GraphQLDateTime = exports.GraphQLTime = exports.GraphQLDate = void 0;
exports.GraphQLIPCPatent = exports.GraphQLLCCSubclass = exports.GraphQLDeweyDecimal = exports.GraphQLSemVer = exports.GraphQLCuid = exports.GraphQLAccountNumber = exports.GraphQLRoutingNumber = exports.GraphQLLocale = exports.GraphQLCountryCode = exports.GraphQLDID = exports.GraphQLVoid = exports.GraphQLObjectID = exports.GraphQLIBAN = exports.GraphQLJSONObject = exports.GraphQLJSON = exports.GraphQLCurrency = void 0;
const mocks = require("./mocks.js");
exports.mocks = mocks;
const index_js_1 = require("./scalars/index.js");
Object.defineProperty(exports, "AccountNumberResolver", { enumerable: true, get: function () { return index_js_1.GraphQLAccountNumber; } });
Object.defineProperty(exports, "GraphQLAccountNumber", { enumerable: true, get: function () { return index_js_1.GraphQLAccountNumber; } });
Object.defineProperty(exports, "BigIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLBigInt; } });
Object.defineProperty(exports, "GraphQLBigInt", { enumerable: true, get: function () { return index_js_1.GraphQLBigInt; } });
Object.defineProperty(exports, "ByteResolver", { enumerable: true, get: function () { return index_js_1.GraphQLByte; } });
Object.defineProperty(exports, "GraphQLByte", { enumerable: true, get: function () { return index_js_1.GraphQLByte; } });
Object.defineProperty(exports, "CountryCodeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLCountryCode; } });
Object.defineProperty(exports, "GraphQLCountryCode", { enumerable: true, get: function () { return index_js_1.GraphQLCountryCode; } });
Object.defineProperty(exports, "CuidResolver", { enumerable: true, get: function () { return index_js_1.GraphQLCuid; } });
Object.defineProperty(exports, "GraphQLCuid", { enumerable: true, get: function () { return index_js_1.GraphQLCuid; } });
Object.defineProperty(exports, "CurrencyResolver", { enumerable: true, get: function () { return index_js_1.GraphQLCurrency; } });
Object.defineProperty(exports, "GraphQLCurrency", { enumerable: true, get: function () { return index_js_1.GraphQLCurrency; } });
Object.defineProperty(exports, "DateResolver", { enumerable: true, get: function () { return index_js_1.GraphQLDate; } });
Object.defineProperty(exports, "GraphQLDate", { enumerable: true, get: function () { return index_js_1.GraphQLDate; } });
Object.defineProperty(exports, "DateTimeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLDateTime; } });
Object.defineProperty(exports, "GraphQLDateTime", { enumerable: true, get: function () { return index_js_1.GraphQLDateTime; } });
Object.defineProperty(exports, "DateTimeISOResolver", { enumerable: true, get: function () { return index_js_1.GraphQLDateTimeISO; } });
Object.defineProperty(exports, "GraphQLDateTimeISO", { enumerable: true, get: function () { return index_js_1.GraphQLDateTimeISO; } });
Object.defineProperty(exports, "GraphQLDeweyDecimalResolver", { enumerable: true, get: function () { return index_js_1.GraphQLDeweyDecimal; } });
Object.defineProperty(exports, "GraphQLDeweyDecimal", { enumerable: true, get: function () { return index_js_1.GraphQLDeweyDecimal; } });
Object.defineProperty(exports, "DIDResolver", { enumerable: true, get: function () { return index_js_1.GraphQLDID; } });
Object.defineProperty(exports, "GraphQLDID", { enumerable: true, get: function () { return index_js_1.GraphQLDID; } });
Object.defineProperty(exports, "DurationResolver", { enumerable: true, get: function () { return index_js_1.GraphQLDuration; } });
Object.defineProperty(exports, "GraphQLDuration", { enumerable: true, get: function () { return index_js_1.GraphQLDuration; } });
Object.defineProperty(exports, "EmailAddressResolver", { enumerable: true, get: function () { return index_js_1.GraphQLEmailAddress; } });
Object.defineProperty(exports, "GraphQLEmailAddress", { enumerable: true, get: function () { return index_js_1.GraphQLEmailAddress; } });
Object.defineProperty(exports, "GUIDResolver", { enumerable: true, get: function () { return index_js_1.GraphQLGUID; } });
Object.defineProperty(exports, "GraphQLGUID", { enumerable: true, get: function () { return index_js_1.GraphQLGUID; } });
Object.defineProperty(exports, "HexadecimalResolver", { enumerable: true, get: function () { return index_js_1.GraphQLHexadecimal; } });
Object.defineProperty(exports, "GraphQLHexadecimal", { enumerable: true, get: function () { return index_js_1.GraphQLHexadecimal; } });
Object.defineProperty(exports, "HexColorCodeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLHexColorCode; } });
Object.defineProperty(exports, "GraphQLHexColorCode", { enumerable: true, get: function () { return index_js_1.GraphQLHexColorCode; } });
Object.defineProperty(exports, "HSLResolver", { enumerable: true, get: function () { return index_js_1.GraphQLHSL; } });
Object.defineProperty(exports, "GraphQLHSL", { enumerable: true, get: function () { return index_js_1.GraphQLHSL; } });
Object.defineProperty(exports, "HSLAResolver", { enumerable: true, get: function () { return index_js_1.GraphQLHSLA; } });
Object.defineProperty(exports, "GraphQLHSLA", { enumerable: true, get: function () { return index_js_1.GraphQLHSLA; } });
Object.defineProperty(exports, "IBANResolver", { enumerable: true, get: function () { return index_js_1.GraphQLIBAN; } });
Object.defineProperty(exports, "GraphQLIBAN", { enumerable: true, get: function () { return index_js_1.GraphQLIBAN; } });
Object.defineProperty(exports, "IPResolver", { enumerable: true, get: function () { return index_js_1.GraphQLIP; } });
Object.defineProperty(exports, "GraphQLIP", { enumerable: true, get: function () { return index_js_1.GraphQLIP; } });
Object.defineProperty(exports, "GraphQLIPCPatentResolver", { enumerable: true, get: function () { return index_js_1.GraphQLIPCPatent; } });
Object.defineProperty(exports, "GraphQLIPCPatent", { enumerable: true, get: function () { return index_js_1.GraphQLIPCPatent; } });
Object.defineProperty(exports, "IPv4Resolver", { enumerable: true, get: function () { return index_js_1.GraphQLIPv4; } });
Object.defineProperty(exports, "GraphQLIPv4", { enumerable: true, get: function () { return index_js_1.GraphQLIPv4; } });
Object.defineProperty(exports, "IPv6Resolver", { enumerable: true, get: function () { return index_js_1.GraphQLIPv6; } });
Object.defineProperty(exports, "GraphQLIPv6", { enumerable: true, get: function () { return index_js_1.GraphQLIPv6; } });
Object.defineProperty(exports, "ISBNResolver", { enumerable: true, get: function () { return index_js_1.GraphQLISBN; } });
Object.defineProperty(exports, "GraphQLISBN", { enumerable: true, get: function () { return index_js_1.GraphQLISBN; } });
Object.defineProperty(exports, "ISO8601DurationResolver", { enumerable: true, get: function () { return index_js_1.GraphQLISO8601Duration; } });
Object.defineProperty(exports, "GraphQLISO8601Duration", { enumerable: true, get: function () { return index_js_1.GraphQLISO8601Duration; } });
Object.defineProperty(exports, "JSONResolver", { enumerable: true, get: function () { return index_js_1.GraphQLJSON; } });
Object.defineProperty(exports, "GraphQLJSON", { enumerable: true, get: function () { return index_js_1.GraphQLJSON; } });
Object.defineProperty(exports, "JSONObjectResolver", { enumerable: true, get: function () { return index_js_1.GraphQLJSONObject; } });
Object.defineProperty(exports, "GraphQLJSONObject", { enumerable: true, get: function () { return index_js_1.GraphQLJSONObject; } });
Object.defineProperty(exports, "JWTResolver", { enumerable: true, get: function () { return index_js_1.GraphQLJWT; } });
Object.defineProperty(exports, "GraphQLJWT", { enumerable: true, get: function () { return index_js_1.GraphQLJWT; } });
Object.defineProperty(exports, "LatitudeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLatitude; } });
Object.defineProperty(exports, "GraphQLLatitude", { enumerable: true, get: function () { return index_js_1.GraphQLLatitude; } });
Object.defineProperty(exports, "GraphQLLCCSubclass", { enumerable: true, get: function () { return index_js_1.GraphQLLCCSubclass; } });
Object.defineProperty(exports, "LocalDateResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLocalDate; } });
Object.defineProperty(exports, "GraphQLLocalDate", { enumerable: true, get: function () { return index_js_1.GraphQLLocalDate; } });
Object.defineProperty(exports, "LocalDateTimeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLocalDateTime; } });
Object.defineProperty(exports, "GraphQLLocalDateTime", { enumerable: true, get: function () { return index_js_1.GraphQLLocalDateTime; } });
Object.defineProperty(exports, "LocaleResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLocale; } });
Object.defineProperty(exports, "GraphQLLocale", { enumerable: true, get: function () { return index_js_1.GraphQLLocale; } });
Object.defineProperty(exports, "LocalEndTimeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLocalEndTime; } });
Object.defineProperty(exports, "GraphQLLocalEndTime", { enumerable: true, get: function () { return index_js_1.GraphQLLocalEndTime; } });
Object.defineProperty(exports, "LocalTimeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLocalTime; } });
Object.defineProperty(exports, "GraphQLLocalTime", { enumerable: true, get: function () { return index_js_1.GraphQLLocalTime; } });
Object.defineProperty(exports, "LongResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLong; } });
Object.defineProperty(exports, "GraphQLLong", { enumerable: true, get: function () { return index_js_1.GraphQLLong; } });
Object.defineProperty(exports, "LongitudeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLLongitude; } });
Object.defineProperty(exports, "GraphQLLongitude", { enumerable: true, get: function () { return index_js_1.GraphQLLongitude; } });
Object.defineProperty(exports, "MACResolver", { enumerable: true, get: function () { return index_js_1.GraphQLMAC; } });
Object.defineProperty(exports, "GraphQLMAC", { enumerable: true, get: function () { return index_js_1.GraphQLMAC; } });
Object.defineProperty(exports, "NegativeFloatResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNegativeFloat; } });
Object.defineProperty(exports, "GraphQLNegativeFloat", { enumerable: true, get: function () { return index_js_1.GraphQLNegativeFloat; } });
Object.defineProperty(exports, "NegativeIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNegativeInt; } });
Object.defineProperty(exports, "GraphQLNegativeInt", { enumerable: true, get: function () { return index_js_1.GraphQLNegativeInt; } });
Object.defineProperty(exports, "NonEmptyStringResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNonEmptyString; } });
Object.defineProperty(exports, "GraphQLNonEmptyString", { enumerable: true, get: function () { return index_js_1.GraphQLNonEmptyString; } });
Object.defineProperty(exports, "NonNegativeFloatResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNonNegativeFloat; } });
Object.defineProperty(exports, "GraphQLNonNegativeFloat", { enumerable: true, get: function () { return index_js_1.GraphQLNonNegativeFloat; } });
Object.defineProperty(exports, "NonNegativeIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNonNegativeInt; } });
Object.defineProperty(exports, "GraphQLNonNegativeInt", { enumerable: true, get: function () { return index_js_1.GraphQLNonNegativeInt; } });
Object.defineProperty(exports, "NonPositiveFloatResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNonPositiveFloat; } });
Object.defineProperty(exports, "GraphQLNonPositiveFloat", { enumerable: true, get: function () { return index_js_1.GraphQLNonPositiveFloat; } });
Object.defineProperty(exports, "NonPositiveIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLNonPositiveInt; } });
Object.defineProperty(exports, "GraphQLNonPositiveInt", { enumerable: true, get: function () { return index_js_1.GraphQLNonPositiveInt; } });
Object.defineProperty(exports, "ObjectIDResolver", { enumerable: true, get: function () { return index_js_1.GraphQLObjectID; } });
Object.defineProperty(exports, "GraphQLObjectID", { enumerable: true, get: function () { return index_js_1.GraphQLObjectID; } });
Object.defineProperty(exports, "PhoneNumberResolver", { enumerable: true, get: function () { return index_js_1.GraphQLPhoneNumber; } });
Object.defineProperty(exports, "GraphQLPhoneNumber", { enumerable: true, get: function () { return index_js_1.GraphQLPhoneNumber; } });
Object.defineProperty(exports, "PortResolver", { enumerable: true, get: function () { return index_js_1.GraphQLPort; } });
Object.defineProperty(exports, "GraphQLPort", { enumerable: true, get: function () { return index_js_1.GraphQLPort; } });
Object.defineProperty(exports, "PositiveFloatResolver", { enumerable: true, get: function () { return index_js_1.GraphQLPositiveFloat; } });
Object.defineProperty(exports, "GraphQLPositiveFloat", { enumerable: true, get: function () { return index_js_1.GraphQLPositiveFloat; } });
Object.defineProperty(exports, "PositiveIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLPositiveInt; } });
Object.defineProperty(exports, "GraphQLPositiveInt", { enumerable: true, get: function () { return index_js_1.GraphQLPositiveInt; } });
Object.defineProperty(exports, "PostalCodeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLPostalCode; } });
Object.defineProperty(exports, "GraphQLPostalCode", { enumerable: true, get: function () { return index_js_1.GraphQLPostalCode; } });
Object.defineProperty(exports, "RGBResolver", { enumerable: true, get: function () { return index_js_1.GraphQLRGB; } });
Object.defineProperty(exports, "GraphQLRGB", { enumerable: true, get: function () { return index_js_1.GraphQLRGB; } });
Object.defineProperty(exports, "RGBAResolver", { enumerable: true, get: function () { return index_js_1.GraphQLRGBA; } });
Object.defineProperty(exports, "GraphQLRGBA", { enumerable: true, get: function () { return index_js_1.GraphQLRGBA; } });
Object.defineProperty(exports, "RoutingNumberResolver", { enumerable: true, get: function () { return index_js_1.GraphQLRoutingNumber; } });
Object.defineProperty(exports, "GraphQLRoutingNumber", { enumerable: true, get: function () { return index_js_1.GraphQLRoutingNumber; } });
Object.defineProperty(exports, "SafeIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLSafeInt; } });
Object.defineProperty(exports, "GraphQLSafeInt", { enumerable: true, get: function () { return index_js_1.GraphQLSafeInt; } });
Object.defineProperty(exports, "SemVerResolver", { enumerable: true, get: function () { return index_js_1.GraphQLSemVer; } });
Object.defineProperty(exports, "GraphQLSemVer", { enumerable: true, get: function () { return index_js_1.GraphQLSemVer; } });
Object.defineProperty(exports, "TimeResolver", { enumerable: true, get: function () { return index_js_1.GraphQLTime; } });
Object.defineProperty(exports, "GraphQLTime", { enumerable: true, get: function () { return index_js_1.GraphQLTime; } });
Object.defineProperty(exports, "TimestampResolver", { enumerable: true, get: function () { return index_js_1.GraphQLTimestamp; } });
Object.defineProperty(exports, "GraphQLTimestamp", { enumerable: true, get: function () { return index_js_1.GraphQLTimestamp; } });
Object.defineProperty(exports, "TimeZoneResolver", { enumerable: true, get: function () { return index_js_1.GraphQLTimeZone; } });
Object.defineProperty(exports, "GraphQLTimeZone", { enumerable: true, get: function () { return index_js_1.GraphQLTimeZone; } });
Object.defineProperty(exports, "UnsignedFloatResolver", { enumerable: true, get: function () { return index_js_1.GraphQLUnsignedFloat; } });
Object.defineProperty(exports, "GraphQLUnsignedFloat", { enumerable: true, get: function () { return index_js_1.GraphQLUnsignedFloat; } });
Object.defineProperty(exports, "UnsignedIntResolver", { enumerable: true, get: function () { return index_js_1.GraphQLUnsignedInt; } });
Object.defineProperty(exports, "GraphQLUnsignedInt", { enumerable: true, get: function () { return index_js_1.GraphQLUnsignedInt; } });
Object.defineProperty(exports, "URLResolver", { enumerable: true, get: function () { return index_js_1.GraphQLURL; } });
Object.defineProperty(exports, "GraphQLURL", { enumerable: true, get: function () { return index_js_1.GraphQLURL; } });
Object.defineProperty(exports, "USCurrencyResolver", { enumerable: true, get: function () { return index_js_1.GraphQLUSCurrency; } });
Object.defineProperty(exports, "GraphQLUSCurrency", { enumerable: true, get: function () { return index_js_1.GraphQLUSCurrency; } });
Object.defineProperty(exports, "UtcOffsetResolver", { enumerable: true, get: function () { return index_js_1.GraphQLUtcOffset; } });
Object.defineProperty(exports, "GraphQLUtcOffset", { enumerable: true, get: function () { return index_js_1.GraphQLUtcOffset; } });
Object.defineProperty(exports, "UUIDResolver", { enumerable: true, get: function () { return index_js_1.GraphQLUUID; } });
Object.defineProperty(exports, "GraphQLUUID", { enumerable: true, get: function () { return index_js_1.GraphQLUUID; } });
Object.defineProperty(exports, "VoidResolver", { enumerable: true, get: function () { return index_js_1.GraphQLVoid; } });
Object.defineProperty(exports, "GraphQLVoid", { enumerable: true, get: function () { return index_js_1.GraphQLVoid; } });
var typeDefs_js_1 = require("./typeDefs.js");
Object.defineProperty(exports, "DateTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Date; } });
Object.defineProperty(exports, "TimeTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Time; } });
Object.defineProperty(exports, "DateTimeTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.DateTime; } });
Object.defineProperty(exports, "DateTimeISOTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.DateTimeISO; } });
Object.defineProperty(exports, "TimestampTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Timestamp; } });
Object.defineProperty(exports, "TimeZoneTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.TimeZone; } });
Object.defineProperty(exports, "UtcOffsetTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.UtcOffset; } });
Object.defineProperty(exports, "LocalDateTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.LocalDate; } });
Object.defineProperty(exports, "LocalTimeTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.LocalTime; } });
Object.defineProperty(exports, "LocalDateTimeTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.LocalDateTime; } });
Object.defineProperty(exports, "LocalEndTimeTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.LocalEndTime; } });
Object.defineProperty(exports, "EmailAddressTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.EmailAddress; } });
Object.defineProperty(exports, "NegativeFloatTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NegativeFloat; } });
Object.defineProperty(exports, "NegativeIntTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NegativeInt; } });
Object.defineProperty(exports, "NonEmptyStringTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NonEmptyString; } });
Object.defineProperty(exports, "NonNegativeFloatTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NonNegativeFloat; } });
Object.defineProperty(exports, "NonNegativeIntTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NonNegativeInt; } });
Object.defineProperty(exports, "NonPositiveFloatTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NonPositiveFloat; } });
Object.defineProperty(exports, "NonPositiveIntTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.NonPositiveInt; } });
Object.defineProperty(exports, "PhoneNumberTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.PhoneNumber; } });
Object.defineProperty(exports, "PositiveFloatTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.PositiveFloat; } });
Object.defineProperty(exports, "PositiveIntTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.PositiveInt; } });
Object.defineProperty(exports, "PostalCodeTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.PostalCode; } });
Object.defineProperty(exports, "UnsignedFloatTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.UnsignedFloat; } });
Object.defineProperty(exports, "UnsignedIntTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.UnsignedInt; } });
Object.defineProperty(exports, "URLTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.URL; } });
Object.defineProperty(exports, "BigIntTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.BigInt; } });
Object.defineProperty(exports, "ByteTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Byte; } });
Object.defineProperty(exports, "LongTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Long; } });
Object.defineProperty(exports, "SafeIntDefinition", { enumerable: true, get: function () { return typeDefs_js_1.SafeInt; } });
Object.defineProperty(exports, "UUIDDefinition", { enumerable: true, get: function () { return typeDefs_js_1.UUID; } });
Object.defineProperty(exports, "GUIDDefinition", { enumerable: true, get: function () { return typeDefs_js_1.GUID; } });
Object.defineProperty(exports, "HexadecimalTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Hexadecimal; } });
Object.defineProperty(exports, "HexColorCodeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.HexColorCode; } });
Object.defineProperty(exports, "HSLDefinition", { enumerable: true, get: function () { return typeDefs_js_1.HSL; } });
Object.defineProperty(exports, "HSLADefinition", { enumerable: true, get: function () { return typeDefs_js_1.HSLA; } });
Object.defineProperty(exports, "IPDefinition", { enumerable: true, get: function () { return typeDefs_js_1.IP; } });
Object.defineProperty(exports, "IPv4Definition", { enumerable: true, get: function () { return typeDefs_js_1.IPv4; } });
Object.defineProperty(exports, "IPv6Definition", { enumerable: true, get: function () { return typeDefs_js_1.IPv6; } });
Object.defineProperty(exports, "ISBNDefinition", { enumerable: true, get: function () { return typeDefs_js_1.ISBN; } });
Object.defineProperty(exports, "JWTDefinition", { enumerable: true, get: function () { return typeDefs_js_1.JWT; } });
Object.defineProperty(exports, "LatitudeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Latitude; } });
Object.defineProperty(exports, "LongitudeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Longitude; } });
Object.defineProperty(exports, "MACDefinition", { enumerable: true, get: function () { return typeDefs_js_1.MAC; } });
Object.defineProperty(exports, "PortDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Port; } });
Object.defineProperty(exports, "RGBDefinition", { enumerable: true, get: function () { return typeDefs_js_1.RGB; } });
Object.defineProperty(exports, "RGBADefinition", { enumerable: true, get: function () { return typeDefs_js_1.RGBA; } });
Object.defineProperty(exports, "USCurrencyDefinition", { enumerable: true, get: function () { return typeDefs_js_1.USCurrency; } });
Object.defineProperty(exports, "CurrencyDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Currency; } });
Object.defineProperty(exports, "JSONDefinition", { enumerable: true, get: function () { return typeDefs_js_1.JSON; } });
Object.defineProperty(exports, "JSONObjectDefinition", { enumerable: true, get: function () { return typeDefs_js_1.JSONObject; } });
Object.defineProperty(exports, "IBANTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.IBAN; } });
Object.defineProperty(exports, "ObjectIDTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.ObjectID; } });
Object.defineProperty(exports, "VoidTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Void; } });
Object.defineProperty(exports, "DIDDefinition", { enumerable: true, get: function () { return typeDefs_js_1.DID; } });
Object.defineProperty(exports, "DurationTypeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Duration; } });
Object.defineProperty(exports, "CountryCodeDefinition", { enumerable: true, get: function () { return typeDefs_js_1.CountryCode; } });
Object.defineProperty(exports, "LocaleDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Locale; } });
Object.defineProperty(exports, "RoutingNumberDefinition", { enumerable: true, get: function () { return typeDefs_js_1.RoutingNumber; } });
Object.defineProperty(exports, "AccountNumberDefinition", { enumerable: true, get: function () { return typeDefs_js_1.AccountNumber; } });
Object.defineProperty(exports, "CuidDefinition", { enumerable: true, get: function () { return typeDefs_js_1.Cuid; } });
Object.defineProperty(exports, "SemVerDefinition", { enumerable: true, get: function () { return typeDefs_js_1.SemVer; } });
Object.defineProperty(exports, "DeweyDecimalDefinition", { enumerable: true, get: function () { return typeDefs_js_1.DeweyDecimal; } });
Object.defineProperty(exports, "LCCSubclassDefinition", { enumerable: true, get: function () { return typeDefs_js_1.LCCSubclass; } });
Object.defineProperty(exports, "IPCPatentDefinition", { enumerable: true, get: function () { return typeDefs_js_1.IPCPatent; } });
var typeDefs_js_2 = require("./typeDefs.js");
Object.defineProperty(exports, "typeDefs", { enumerable: true, get: function () { return typeDefs_js_2.typeDefs; } });
exports.resolvers = {
    Date: index_js_1.GraphQLDate,
    Time: index_js_1.GraphQLTime,
    DateTime: index_js_1.GraphQLDateTime,
    DateTimeISO: index_js_1.GraphQLDateTimeISO,
    Timestamp: index_js_1.GraphQLTimestamp,
    TimeZone: index_js_1.GraphQLTimeZone,
    UtcOffset: index_js_1.GraphQLUtcOffset,
    Duration: index_js_1.GraphQLDuration,
    ISO8601Duration: index_js_1.GraphQLISO8601Duration,
    LocalDate: index_js_1.GraphQLLocalDate,
    LocalTime: index_js_1.GraphQLLocalTime,
    LocalDateTime: index_js_1.GraphQLLocalDateTime,
    LocalEndTime: index_js_1.GraphQLLocalEndTime,
    EmailAddress: index_js_1.GraphQLEmailAddress,
    NegativeFloat: index_js_1.GraphQLNegativeFloat,
    NegativeInt: index_js_1.GraphQLNegativeInt,
    NonEmptyString: index_js_1.GraphQLNonEmptyString,
    NonNegativeFloat: index_js_1.GraphQLNonNegativeFloat,
    NonNegativeInt: index_js_1.GraphQLNonNegativeInt,
    NonPositiveFloat: index_js_1.GraphQLNonPositiveFloat,
    NonPositiveInt: index_js_1.GraphQLNonPositiveInt,
    PhoneNumber: index_js_1.GraphQLPhoneNumber,
    PositiveFloat: index_js_1.GraphQLPositiveFloat,
    PositiveInt: index_js_1.GraphQLPositiveInt,
    PostalCode: index_js_1.GraphQLPostalCode,
    UnsignedFloat: index_js_1.GraphQLUnsignedFloat,
    UnsignedInt: index_js_1.GraphQLUnsignedInt,
    URL: index_js_1.GraphQLURL,
    BigInt: index_js_1.GraphQLBigInt,
    Byte: index_js_1.GraphQLByte,
    Long: index_js_1.GraphQLLong,
    SafeInt: index_js_1.GraphQLSafeInt,
    UUID: index_js_1.GraphQLUUID,
    GUID: index_js_1.GraphQLGUID,
    Hexadecimal: index_js_1.GraphQLHexadecimal,
    HexColorCode: index_js_1.GraphQLHexColorCode,
    HSL: index_js_1.GraphQLHSL,
    HSLA: index_js_1.GraphQLHSLA,
    IP: index_js_1.GraphQLIP,
    IPv4: index_js_1.GraphQLIPv4,
    IPv6: index_js_1.GraphQLIPv6,
    ISBN: index_js_1.GraphQLISBN,
    JWT: index_js_1.GraphQLJWT,
    Latitude: index_js_1.GraphQLLatitude,
    Longitude: index_js_1.GraphQLLongitude,
    MAC: index_js_1.GraphQLMAC,
    Port: index_js_1.GraphQLPort,
    RGB: index_js_1.GraphQLRGB,
    RGBA: index_js_1.GraphQLRGBA,
    USCurrency: index_js_1.GraphQLUSCurrency,
    Currency: index_js_1.GraphQLCurrency,
    JSON: index_js_1.GraphQLJSON,
    JSONObject: index_js_1.GraphQLJSONObject,
    IBAN: index_js_1.GraphQLIBAN,
    ObjectID: index_js_1.GraphQLObjectID,
    Void: index_js_1.GraphQLVoid,
    DID: index_js_1.GraphQLDID,
    CountryCode: index_js_1.GraphQLCountryCode,
    Locale: index_js_1.GraphQLLocale,
    RoutingNumber: index_js_1.GraphQLRoutingNumber,
    AccountNumber: index_js_1.GraphQLAccountNumber,
    Cuid: index_js_1.GraphQLCuid,
    SemVer: index_js_1.GraphQLSemVer,
    DeweyDecimal: index_js_1.GraphQLDeweyDecimal,
    LCCSubclass: index_js_1.GraphQLLCCSubclass,
    IPCPatent: index_js_1.GraphQLIPCPatent,
};
var mocks_js_1 = require("./mocks.js");
Object.defineProperty(exports, "DateMock", { enumerable: true, get: function () { return mocks_js_1.Date; } });
Object.defineProperty(exports, "TimeMock", { enumerable: true, get: function () { return mocks_js_1.Time; } });
Object.defineProperty(exports, "DateTimeMock", { enumerable: true, get: function () { return mocks_js_1.DateTime; } });
Object.defineProperty(exports, "DateTimeISOMock", { enumerable: true, get: function () { return mocks_js_1.DateTimeISO; } });
Object.defineProperty(exports, "DurationMock", { enumerable: true, get: function () { return mocks_js_1.Duration; } });
Object.defineProperty(exports, "ISO8601DurationMock", { enumerable: true, get: function () { return mocks_js_1.ISO8601Duration; } });
Object.defineProperty(exports, "TimestampMock", { enumerable: true, get: function () { return mocks_js_1.Timestamp; } });
Object.defineProperty(exports, "TimeZoneMock", { enumerable: true, get: function () { return mocks_js_1.TimeZone; } });
Object.defineProperty(exports, "UtcOffsetMock", { enumerable: true, get: function () { return mocks_js_1.UtcOffset; } });
Object.defineProperty(exports, "LocalDateMock", { enumerable: true, get: function () { return mocks_js_1.LocalDate; } });
Object.defineProperty(exports, "LocalTimeMock", { enumerable: true, get: function () { return mocks_js_1.LocalTime; } });
Object.defineProperty(exports, "LocalDateTimeMock", { enumerable: true, get: function () { return mocks_js_1.LocalDateTime; } });
Object.defineProperty(exports, "LocalEndTimeMock", { enumerable: true, get: function () { return mocks_js_1.LocalEndTime; } });
Object.defineProperty(exports, "EmailAddressMock", { enumerable: true, get: function () { return mocks_js_1.EmailAddress; } });
Object.defineProperty(exports, "NegativeFloatMock", { enumerable: true, get: function () { return mocks_js_1.NegativeFloat; } });
Object.defineProperty(exports, "NegativeIntMock", { enumerable: true, get: function () { return mocks_js_1.NegativeInt; } });
Object.defineProperty(exports, "NonEmptyStringMock", { enumerable: true, get: function () { return mocks_js_1.NonEmptyString; } });
Object.defineProperty(exports, "NonNegativeFloatMock", { enumerable: true, get: function () { return mocks_js_1.NonNegativeFloat; } });
Object.defineProperty(exports, "NonNegativeIntMock", { enumerable: true, get: function () { return mocks_js_1.NonNegativeInt; } });
Object.defineProperty(exports, "NonPositiveFloatMock", { enumerable: true, get: function () { return mocks_js_1.NonPositiveFloat; } });
Object.defineProperty(exports, "NonPositiveIntMock", { enumerable: true, get: function () { return mocks_js_1.NonPositiveInt; } });
Object.defineProperty(exports, "PhoneNumberMock", { enumerable: true, get: function () { return mocks_js_1.PhoneNumber; } });
Object.defineProperty(exports, "PositiveFloatMock", { enumerable: true, get: function () { return mocks_js_1.PositiveFloat; } });
Object.defineProperty(exports, "PositiveIntMock", { enumerable: true, get: function () { return mocks_js_1.PositiveInt; } });
Object.defineProperty(exports, "PostalCodeMock", { enumerable: true, get: function () { return mocks_js_1.PostalCode; } });
Object.defineProperty(exports, "UnsignedFloatMock", { enumerable: true, get: function () { return mocks_js_1.UnsignedFloat; } });
Object.defineProperty(exports, "UnsignedIntMock", { enumerable: true, get: function () { return mocks_js_1.UnsignedInt; } });
Object.defineProperty(exports, "URLMock", { enumerable: true, get: function () { return mocks_js_1.URL; } });
Object.defineProperty(exports, "BigIntMock", { enumerable: true, get: function () { return mocks_js_1.BigInt; } });
Object.defineProperty(exports, "ByteMock", { enumerable: true, get: function () { return mocks_js_1.Byte; } });
Object.defineProperty(exports, "LongMock", { enumerable: true, get: function () { return mocks_js_1.Long; } });
Object.defineProperty(exports, "SafeIntMock", { enumerable: true, get: function () { return mocks_js_1.SafeInt; } });
Object.defineProperty(exports, "UUIDMock", { enumerable: true, get: function () { return mocks_js_1.UUID; } });
Object.defineProperty(exports, "GUIDMock", { enumerable: true, get: function () { return mocks_js_1.GUID; } });
Object.defineProperty(exports, "HexadecimalMock", { enumerable: true, get: function () { return mocks_js_1.Hexadecimal; } });
Object.defineProperty(exports, "HexColorCodeMock", { enumerable: true, get: function () { return mocks_js_1.HexColorCode; } });
Object.defineProperty(exports, "HSLMock", { enumerable: true, get: function () { return mocks_js_1.HSL; } });
Object.defineProperty(exports, "HSLAMock", { enumerable: true, get: function () { return mocks_js_1.HSLA; } });
Object.defineProperty(exports, "IPMock", { enumerable: true, get: function () { return mocks_js_1.IP; } });
Object.defineProperty(exports, "IPv4Mock", { enumerable: true, get: function () { return mocks_js_1.IPv4; } });
Object.defineProperty(exports, "IPv6Mock", { enumerable: true, get: function () { return mocks_js_1.IPv6; } });
Object.defineProperty(exports, "ISBNMock", { enumerable: true, get: function () { return mocks_js_1.ISBN; } });
Object.defineProperty(exports, "JWTMock", { enumerable: true, get: function () { return mocks_js_1.JWT; } });
Object.defineProperty(exports, "LatitudeMock", { enumerable: true, get: function () { return mocks_js_1.Latitude; } });
Object.defineProperty(exports, "LongitudeMock", { enumerable: true, get: function () { return mocks_js_1.Longitude; } });
Object.defineProperty(exports, "MACMock", { enumerable: true, get: function () { return mocks_js_1.MAC; } });
Object.defineProperty(exports, "PortMock", { enumerable: true, get: function () { return mocks_js_1.Port; } });
Object.defineProperty(exports, "RGBMock", { enumerable: true, get: function () { return mocks_js_1.RGB; } });
Object.defineProperty(exports, "RGBAMock", { enumerable: true, get: function () { return mocks_js_1.RGBA; } });
Object.defineProperty(exports, "USCurrencyMock", { enumerable: true, get: function () { return mocks_js_1.USCurrency; } });
Object.defineProperty(exports, "CurrencyMock", { enumerable: true, get: function () { return mocks_js_1.Currency; } });
Object.defineProperty(exports, "JSONMock", { enumerable: true, get: function () { return mocks_js_1.JSON; } });
Object.defineProperty(exports, "JSONObjectMock", { enumerable: true, get: function () { return mocks_js_1.JSONObject; } });
Object.defineProperty(exports, "IBANMock", { enumerable: true, get: function () { return mocks_js_1.IBAN; } });
Object.defineProperty(exports, "ObjectIDMock", { enumerable: true, get: function () { return mocks_js_1.ObjectID; } });
Object.defineProperty(exports, "VoidMock", { enumerable: true, get: function () { return mocks_js_1.Void; } });
Object.defineProperty(exports, "DIDMock", { enumerable: true, get: function () { return mocks_js_1.DID; } });
Object.defineProperty(exports, "CountryCodeMock", { enumerable: true, get: function () { return mocks_js_1.CountryCode; } });
Object.defineProperty(exports, "LocaleMock", { enumerable: true, get: function () { return mocks_js_1.Locale; } });
Object.defineProperty(exports, "RoutingNumberMock", { enumerable: true, get: function () { return mocks_js_1.RoutingNumber; } });
Object.defineProperty(exports, "AccountNumberMock", { enumerable: true, get: function () { return mocks_js_1.AccountNumber; } });
Object.defineProperty(exports, "CuidMock", { enumerable: true, get: function () { return mocks_js_1.Cuid; } });
Object.defineProperty(exports, "SemVerMock", { enumerable: true, get: function () { return mocks_js_1.SemVer; } });
Object.defineProperty(exports, "DeweyDecimalMock", { enumerable: true, get: function () { return mocks_js_1.DeweyDecimal; } });
Object.defineProperty(exports, "LCCSubclassMock", { enumerable: true, get: function () { return mocks_js_1.LCCSubclass; } });
Object.defineProperty(exports, "IPCPatentMock", { enumerable: true, get: function () { return mocks_js_1.IPCPatent; } });
var RegularExpression_js_1 = require("./RegularExpression.js");
Object.defineProperty(exports, "RegularExpression", { enumerable: true, get: function () { return RegularExpression_js_1.RegularExpression; } });
