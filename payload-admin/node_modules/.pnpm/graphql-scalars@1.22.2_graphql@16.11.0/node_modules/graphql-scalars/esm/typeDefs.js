export const BigInt = 'scalar BigInt';
export const Byte = 'scalar Byte';
export const Date = 'scalar Date';
export const Time = 'scalar Time';
export const Timestamp = 'scalar Timestamp';
export const TimeZone = 'scalar TimeZone';
export const DateTime = 'scalar DateTime';
export const DateTimeISO = 'scalar DateTimeISO';
export const UtcOffset = 'scalar UtcOffset';
export const Duration = 'scalar Duration';
export const ISO8601Duration = 'scalar ISO8601Duration';
export const LocalDate = 'scalar LocalDate';
export const LocalTime = 'scalar LocalTime';
export const LocalDateTime = 'scalar LocalDateTime';
export const LocalEndTime = 'scalar LocalEndTime';
export const EmailAddress = 'scalar EmailAddress';
export const UUID = `scalar UUID`;
export const Hexadecimal = `scalar Hexadecimal`;
export const HexColorCode = `scalar HexColorCode`;
export const HSL = `scalar HSL`;
export const HSLA = `scalar HSLA`;
export const IBAN = `scalar IBAN`;
export const IP = `scalar IP`;
export const IPv4 = `scalar IPv4`;
export const IPv6 = `scalar IPv6`;
export const ISBN = `scalar ISBN`;
export const JWT = `scalar JWT`;
export const Latitude = `scalar Latitude`;
export const Longitude = `scalar Longitude`;
export const JSON = `scalar JSON`;
export const JSONObject = `scalar JSONObject`;
export const MAC = `scalar MAC`;
export const NegativeFloat = 'scalar NegativeFloat';
export const NegativeInt = 'scalar NegativeInt';
export const NonEmptyString = 'scalar NonEmptyString';
export const NonNegativeFloat = 'scalar NonNegativeFloat';
export const NonNegativeInt = 'scalar NonNegativeInt';
export const NonPositiveFloat = 'scalar NonPositiveFloat';
export const NonPositiveInt = 'scalar NonPositiveInt';
export const PhoneNumber = 'scalar PhoneNumber';
export const Port = `scalar Port`;
export const PositiveFloat = 'scalar PositiveFloat';
export const PositiveInt = 'scalar PositiveInt';
export const PostalCode = 'scalar PostalCode';
export const RGB = `scalar RGB`;
export const RGBA = `scalar RGBA`;
export const SafeInt = `scalar SafeInt`;
export const URL = 'scalar URL';
export const USCurrency = `scalar USCurrency`;
export const Currency = `scalar Currency`;
export const RoutingNumber = 'scalar RoutingNumber';
export const AccountNumber = 'scalar AccountNumber';
export const Cuid = 'scalar Cuid';
export const SemVer = 'scalar SemVer';
export const UnsignedFloat = 'scalar UnsignedFloat';
export const UnsignedInt = 'scalar UnsignedInt';
export const GUID = `scalar GUID`;
export const Long = 'scalar Long';
export const ObjectID = 'scalar ObjectID';
export const Void = 'scalar Void';
export const DID = 'scalar DID';
export const CountryCode = 'scalar CountryCode';
export const Locale = 'scalar Locale';
export const DeweyDecimal = 'scalar DeweyDecimal';
export const LCCSubclass = 'scalar LCCSubclass';
export const IPCPatent = 'scalar IPCPatent';
export const typeDefs = [
    Date,
    Time,
    DateTime,
    DateTimeISO,
    Timestamp,
    TimeZone,
    UtcOffset,
    Duration,
    ISO8601Duration,
    LocalDate,
    LocalTime,
    LocalDateTime,
    LocalEndTime,
    EmailAddress,
    NegativeFloat,
    NegativeInt,
    NonEmptyString,
    NonNegativeFloat,
    NonNegativeInt,
    NonPositiveFloat,
    NonPositiveInt,
    PhoneNumber,
    PositiveFloat,
    PositiveInt,
    PostalCode,
    UnsignedFloat,
    UnsignedInt,
    URL,
    BigInt,
    Long,
    Byte,
    UUID,
    GUID,
    Hexadecimal,
    HexColorCode,
    HSL,
    HSLA,
    IP,
    IPv4,
    IPv6,
    ISBN,
    JWT,
    Latitude,
    Longitude,
    MAC,
    Port,
    RGB,
    RGBA,
    SafeInt,
    USCurrency,
    Currency,
    JSON,
    JSONObject,
    IBAN,
    ObjectID,
    Void,
    DID,
    CountryCode,
    Locale,
    RoutingNumber,
    AccountNumber,
    Cuid,
    SemVer,
    DeweyDecimal,
    LCCSubclass,
    IPCPatent,
];
