{"name": "bson-objectid", "version": "2.0.4", "description": "Construct ObjectIDs without the mongodb driver or bson module", "main": "objectid.js", "types": "./objectid.d.ts", "directories": {"test": "test"}, "scripts": {"typescript-test": "tsc --project typing-tests", "test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/williamkapke/bson-objectid.git"}, "keywords": ["ObjectID", "mongo", "mongodb", "bson", "createFromHexString", "hex"], "author": "<PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/williamkapke/bson-objectid/issues"}, "homepage": "https://github.com/williamkapke/bson-objectid", "devDependencies": {"@types/node": "^8.0.31", "mocha": "^5.2.0", "should": "^4.1.0", "typescript": "^4.6.2"}}