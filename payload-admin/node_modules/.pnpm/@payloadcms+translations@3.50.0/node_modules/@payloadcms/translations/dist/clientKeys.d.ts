export declare const clientTranslationKeys: ("authentication:account" | "authentication:accountOfCurrentUser" | "authentication:accountVerified" | "authentication:alreadyActivated" | "authentication:alreadyLoggedIn" | "authentication:apiKey" | "authentication:authenticated" | "authentication:backToLogin" | "authentication:beginCreateFirstUser" | "authentication:changePassword" | "authentication:checkYourEmailForPasswordReset" | "authentication:confirmGeneration" | "authentication:confirmPassword" | "authentication:createFirstUser" | "authentication:emailNotValid" | "authentication:emailOrUsername" | "authentication:emailSent" | "authentication:emailVerified" | "authentication:enableAPIKey" | "authentication:failedToUnlock" | "authentication:forceUnlock" | "authentication:forgotPassword" | "authentication:forgotPasswordEmailInstructions" | "authentication:forgotPasswordUsernameInstructions" | "authentication:usernameNotValid" | "authentication:forgotPasswordQuestion" | "authentication:generate" | "authentication:generateNewAPIKey" | "authentication:generatingNewAPIKeyWillInvalidate" | "authentication:logBackIn" | "authentication:loggedIn" | "authentication:loggedInChangePassword" | "authentication:loggedOutInactivity" | "authentication:loggedOutSuccessfully" | "authentication:loggingOut" | "authentication:login" | "authentication:logOut" | "authentication:logout" | "authentication:logoutSuccessful" | "authentication:logoutUser" | "authentication:newAPIKeyGenerated" | "authentication:newPassword" | "authentication:passed" | "authentication:passwordResetSuccessfully" | "authentication:resetPassword" | "authentication:stayLoggedIn" | "authentication:successfullyRegisteredFirstUser" | "authentication:successfullyUnlocked" | "authentication:tokenRefreshSuccessful" | "authentication:unableToVerify" | "authentication:username" | "authentication:verified" | "authentication:verifiedSuccessfully" | "authentication:verify" | "authentication:verifyUser" | "authentication:youAreInactive" | "error:autosaving" | "error:correctInvalidFields" | "error:deletingTitle" | "error:documentNotFound" | "error:emailOrPasswordIncorrect" | "error:insufficientClipboardPermissions" | "error:invalidClipboardData" | "error:invalidFileType" | "error:invalidRequestArgs" | "error:loadingDocument" | "error:logoutFailed" | "error:noMatchedField" | "error:notAllowedToAccessPage" | "error:previewing" | "error:problemUploadingFile" | "error:restoringTitle" | "error:tokenNotProvided" | "error:unableToCopy" | "error:unableToDeleteCount" | "error:unableToReindexCollection" | "error:unableToUpdateCount" | "error:unauthorized" | "error:unauthorizedAdmin" | "error:unknown" | "error:unPublishingDocument" | "error:unspecific" | "error:unverifiedEmail" | "error:userEmailAlreadyRegistered" | "error:usernameAlreadyRegistered" | "error:usernameOrPasswordIncorrect" | "fields:block" | "fields:blocks" | "fields:addLabel" | "fields:addLink" | "fields:addNew" | "fields:addNewLabel" | "fields:addRelationship" | "fields:addUpload" | "fields:blockType" | "fields:chooseBetweenCustomTextOrDocument" | "fields:chooseDocumentToLink" | "fields:chooseFromExisting" | "fields:collapseAll" | "fields:customURL" | "fields:editLink" | "fields:editRelationship" | "fields:enterURL" | "fields:internalLink" | "fields:itemsAndMore" | "fields:labelRelationship" | "fields:latitude" | "fields:linkedTo" | "fields:linkType" | "fields:longitude" | "fields:openInNewTab" | "fields:passwordsDoNotMatch" | "fields:removeRelationship" | "fields:removeUpload" | "fields:saveChanges" | "fields:searchForBlock" | "fields:selectFieldsToEdit" | "fields:showAll" | "fields:swapRelationship" | "fields:swapUpload" | "fields:textToDisplay" | "fields:toggleBlock" | "fields:uploadNewLabel" | "folder:browseByFolder" | "folder:byFolder" | "folder:deleteFolder" | "folder:folderName" | "folder:folders" | "folder:folderTypeDescription" | "folder:itemHasBeenMoved" | "folder:itemHasBeenMovedToRoot" | "folder:itemsMovedToFolder" | "folder:itemsMovedToRoot" | "folder:moveFolder" | "folder:moveItemsToFolderConfirmation" | "folder:moveItemsToRootConfirmation" | "folder:moveItemToFolderConfirmation" | "folder:moveItemToRootConfirmation" | "folder:movingFromFolder" | "folder:newFolder" | "folder:noFolder" | "folder:renameFolder" | "folder:searchByNameInFolder" | "folder:selectFolderForItem" | "general:item" | "general:items" | "general:of" | "general:language" | "general:error" | "general:username" | "general:notFound" | "general:unauthorized" | "general:name" | "general:aboutToDelete" | "general:aboutToPermanentlyDelete" | "general:aboutToPermanentlyDeleteTrash" | "general:aboutToRestore" | "general:aboutToRestoreAsDraft" | "general:aboutToRestoreAsDraftCount" | "general:aboutToRestoreCount" | "general:aboutToTrash" | "general:aboutToTrashCount" | "general:addBelow" | "general:addFilter" | "general:adminTheme" | "general:all" | "general:allCollections" | "general:allLocales" | "general:and" | "general:anotherUser" | "general:anotherUserTakenOver" | "general:applyChanges" | "general:ascending" | "general:automatic" | "general:backToDashboard" | "general:cancel" | "general:changesNotSaved" | "general:clear" | "general:clearAll" | "general:close" | "general:collapse" | "general:collections" | "general:columns" | "general:columnToSort" | "general:confirm" | "general:confirmCopy" | "general:confirmDeletion" | "general:confirmDuplication" | "general:confirmMove" | "general:confirmReindex" | "general:confirmReindexAll" | "general:confirmReindexDescription" | "general:confirmReindexDescriptionAll" | "general:confirmRestoration" | "general:copied" | "general:copy" | "general:copyField" | "general:copying" | "general:copyRow" | "general:copyWarning" | "general:create" | "general:created" | "general:createdAt" | "general:createNew" | "general:createNewLabel" | "general:creating" | "general:creatingNewLabel" | "general:currentlyEditing" | "general:custom" | "general:dark" | "general:dashboard" | "general:delete" | "general:deleted" | "general:deletedAt" | "general:deletedCountSuccessfully" | "general:deletedSuccessfully" | "general:deletePermanently" | "general:deleting" | "general:depth" | "general:descending" | "general:deselectAllRows" | "general:document" | "general:documentIsTrashed" | "general:documentLocked" | "general:documents" | "general:duplicate" | "general:duplicateWithoutSaving" | "general:edit" | "general:editAll" | "general:editedSince" | "general:editing" | "general:editingTakenOver" | "general:editLabel" | "general:email" | "general:emailAddress" | "general:emptyTrash" | "general:emptyTrashLabel" | "general:enterAValue" | "general:errors" | "general:exitLivePreview" | "general:export" | "general:fallbackToDefaultLocale" | "general:false" | "general:filters" | "general:filterWhere" | "general:globals" | "general:goBack" | "general:groupByLabel" | "general:import" | "general:isEditing" | "general:lastModified" | "general:leaveAnyway" | "general:leaveWithoutSaving" | "general:light" | "general:livePreview" | "general:loading" | "general:locale" | "general:locales" | "general:menu" | "general:moreOptions" | "general:move" | "general:moveConfirm" | "general:moveCount" | "general:moveDown" | "general:moveUp" | "general:moving" | "general:movingCount" | "general:next" | "general:no" | "general:noDateSelected" | "general:noFiltersSet" | "general:noLabel" | "general:none" | "general:noOptions" | "general:noResults" | "general:nothingFound" | "general:noTrashResults" | "general:noUpcomingEventsScheduled" | "general:noValue" | "general:only" | "general:open" | "general:or" | "general:order" | "general:overwriteExistingData" | "general:pageNotFound" | "general:password" | "general:pasteField" | "general:pasteRow" | "general:payloadSettings" | "general:permanentlyDelete" | "general:permanentlyDeletedCountSuccessfully" | "general:perPage" | "general:previous" | "general:reindex" | "general:reindexingAll" | "general:remove" | "general:rename" | "general:reset" | "general:resetPreferences" | "general:resetPreferencesDescription" | "general:resettingPreferences" | "general:restore" | "general:restoreAsPublished" | "general:restoredCountSuccessfully" | "general:restoring" | "general:row" | "general:rows" | "general:save" | "general:saving" | "general:schedulePublishFor" | "general:searchBy" | "general:select" | "general:selectAll" | "general:selectAllRows" | "general:selectedCount" | "general:selectLabel" | "general:selectValue" | "general:showAllLabel" | "general:sorryNotFound" | "general:sort" | "general:sortByLabelDirection" | "general:stayOnThisPage" | "general:submissionSuccessful" | "general:submit" | "general:submitting" | "general:success" | "general:successfullyCreated" | "general:successfullyDuplicated" | "general:successfullyReindexed" | "general:takeOver" | "general:thisLanguage" | "general:time" | "general:timezone" | "general:titleDeleted" | "general:titleRestored" | "general:titleTrashed" | "general:trash" | "general:trashedCountSuccessfully" | "general:true" | "general:unsavedChanges" | "general:unsavedChangesDuplicate" | "general:untitled" | "general:upcomingEvents" | "general:updatedAt" | "general:updatedCountSuccessfully" | "general:updatedLabelSuccessfully" | "general:updatedSuccessfully" | "general:updateForEveryone" | "general:updating" | "general:uploading" | "general:uploadingBulk" | "general:user" | "general:users" | "general:value" | "general:viewing" | "general:viewReadOnly" | "general:welcome" | "general:yes" | "localization:cannotCopySameLocale" | "localization:copyFrom" | "localization:copyFromTo" | "localization:copyTo" | "localization:copyToLocale" | "localization:localeToPublish" | "localization:selectLocaleToCopy" | "operators:contains" | "operators:equals" | "operators:exists" | "operators:intersects" | "operators:near" | "operators:within" | "operators:isGreaterThan" | "operators:isGreaterThanOrEqualTo" | "operators:isIn" | "operators:isLessThan" | "operators:isLessThanOrEqualTo" | "operators:isLike" | "operators:isNotEqualTo" | "operators:isNotIn" | "operators:isNotLike" | "upload:addFile" | "upload:addFiles" | "upload:bulkUpload" | "upload:crop" | "upload:cropToolDescription" | "upload:download" | "upload:dragAndDrop" | "upload:editImage" | "upload:fileName" | "upload:fileSize" | "upload:filesToUpload" | "upload:fileToUpload" | "upload:focalPoint" | "upload:focalPointDescription" | "upload:height" | "upload:noFile" | "upload:pasteURL" | "upload:previewSizes" | "upload:selectCollectionToBrowse" | "upload:selectFile" | "upload:setCropArea" | "upload:setFocalPoint" | "upload:sizes" | "upload:sizesFor" | "upload:width" | "validation:username" | "validation:emailAddress" | "validation:enterNumber" | "validation:fieldHasNo" | "validation:greaterThanMax" | "validation:invalidInput" | "validation:invalidSelection" | "validation:invalidSelections" | "validation:lessThanMin" | "validation:limitReached" | "validation:longerThanMin" | "validation:notValidDate" | "validation:required" | "validation:requiresAtLeast" | "validation:requiresNoMoreThan" | "validation:requiresTwoNumbers" | "validation:shorterThanMax" | "validation:timezoneRequired" | "validation:trueOrFalse" | "validation:validUploadID" | "version:version" | "version:aboutToRestore" | "version:restoring" | "version:type" | "version:aboutToPublishSelection" | "version:aboutToRestoreGlobal" | "version:aboutToRevertToPublished" | "version:aboutToUnpublish" | "version:aboutToUnpublishSelection" | "version:autosave" | "version:autosavedSuccessfully" | "version:autosavedVersion" | "version:changed" | "version:compareVersions" | "version:comparingAgainst" | "version:confirmPublish" | "version:confirmRevertToSaved" | "version:confirmUnpublish" | "version:confirmVersionRestoration" | "version:currentDraft" | "version:currentlyPublished" | "version:currentlyViewing" | "version:currentPublishedVersion" | "version:draft" | "version:draftSavedSuccessfully" | "version:lastSavedAgo" | "version:modifiedOnly" | "version:moreVersions" | "version:noFurtherVersionsFound" | "version:noRowsFound" | "version:noRowsSelected" | "version:preview" | "version:previouslyDraft" | "version:previouslyPublished" | "version:previousVersion" | "version:problemRestoringVersion" | "version:publish" | "version:publishAllLocales" | "version:publishChanges" | "version:published" | "version:publishIn" | "version:publishing" | "version:restoreAsDraft" | "version:restoredSuccessfully" | "version:restoreThisVersion" | "version:reverting" | "version:revertToPublished" | "version:saveDraft" | "version:scheduledSuccessfully" | "version:schedulePublish" | "version:selectLocales" | "version:selectVersionToCompare" | "version:showLocales" | "version:specificVersion" | "version:status" | "version:unpublish" | "version:unpublishing" | "version:versionAgo" | "version:versionCreatedOn" | "version:versionID" | "version:versions" | "version:viewingVersion" | "version:viewingVersionGlobal" | "version:viewingVersions" | "version:viewingVersionsGlobal" | "general:aboutToDeleteCount" | "general:editingLabel" | "version:changedFieldsCount")[];
//# sourceMappingURL=clientKeys.d.ts.map