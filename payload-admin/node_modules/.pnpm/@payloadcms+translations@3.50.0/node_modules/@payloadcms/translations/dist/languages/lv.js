export const lvTranslations = {
    authentication: {
        account: 'Konts',
        accountOfCurrentUser: 'Pašreizējā lietotāja konts',
        accountVerified: 'Konts veiksmīgi verificēts.',
        alreadyActivated: 'Jau aktivizēts',
        alreadyLoggedIn: 'Jau pieslēdzies',
        apiKey: 'API atslēga',
        authenticated: 'Autentificēts',
        backToLogin: 'Atpakaļ uz pieslēgšanos',
        beginCreateFirstUser: 'Lai sāktu, izveidojiet savu pirmo lietotāju.',
        changePassword: 'Mainīt paroli',
        checkYourEmailForPasswordReset: 'Ja e-pasta adrese ir saistīta ar kontu, drīz saņemsiet norādījumus paroles atiestatīšanai. Lūdzu, pārbaudiet arī surogātpasta mapi, ja e-pasts nav iesūtnē.',
        confirmGeneration: 'Apstiprināt ģenerēšanu',
        confirmPassword: 'Apstiprin<PERSON>t paroli',
        createFirstUser: 'Izveidot pirmo lietotāju',
        emailNotValid: 'Norādītais e-pasts nav derīgs',
        emailOrUsername: 'E-pasts vai lietotājvārds',
        emailSent: 'E-pasts nosūtīts',
        emailVerified: 'E-pasts veiksmīgi verificēts.',
        enableAPIKey: 'Ieslēgt API atslēgu',
        failedToUnlock: 'Neizdevās atbloķēt',
        forceUnlock: 'Piespiedu atbloķēšana',
        forgotPassword: 'Aizmirsi paroli?',
        forgotPasswordEmailInstructions: 'Lūdzu, ievadiet savu e-pastu zemāk. Saņemsiet ziņojumu ar norādījumiem paroles atiestatīšanai.',
        forgotPasswordQuestion: 'Aizmirsi paroli?',
        forgotPasswordUsernameInstructions: 'Lūdzu, ievadiet savu lietotājvārdu zemāk. Norādījumi paroles atiestatīšanai tiks nosūtīti uz e-pastu, kas saistīts ar jūsu lietotājvārdu.',
        generate: 'Ģenerēt',
        generateNewAPIKey: 'Ģenerēt jaunu API atslēgu',
        generatingNewAPIKeyWillInvalidate: 'Ģenerējot jaunu API atslēgu, <1>iepriekšējā atslēga kļūs nederīga</1>. Vai tiešām vēlaties turpināt?',
        lockUntil: 'Bloķēts līdz',
        logBackIn: 'Pieslēgties atkārtoti',
        loggedIn: 'Lai pieslēgtos ar citu lietotāju, vispirms <0>atslēdzieties</0>.',
        loggedInChangePassword: 'Lai mainītu paroli, dodieties uz savu <0>kontu</0> un rediģējiet paroli tur.',
        loggedOutInactivity: 'Jūs esat atslēgts neaktivitātes dēļ.',
        loggedOutSuccessfully: 'Jūs veiksmīgi atslēdzāties.',
        loggingOut: 'Notiek atslēgšanās...',
        login: 'Pieslēgties',
        loginAttempts: 'Pieslēgšanās mēģinājumi',
        loginUser: 'Pieslēgt lietotāju',
        loginWithAnotherUser: 'Lai pieslēgtos ar citu lietotāju, vispirms <0>atslēdzieties</0>.',
        logOut: 'Atslēgties',
        logout: 'Atslēgties',
        logoutSuccessful: 'Atslēgšanās veiksmīga.',
        logoutUser: 'Atslēgt lietotāju',
        newAccountCreated: 'Jums tikko ir izveidots jauns konts piekļuvei <a href="{{serverURL}}">{{serverURL}}</a>. Lūdzu, noklikšķiniet uz šīs saites vai iekopējiet URL pārlūkprogrammā, lai verificētu savu e-pastu: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Pēc e-pasta verificēšanas varēsiet veiksmīgi pieslēgties.',
        newAPIKeyGenerated: 'Jauna API atslēga ģenerēta.',
        newPassword: 'Jauna parole',
        passed: 'Autentifikācija veiksmīga',
        passwordResetSuccessfully: 'Parole veiksmīgi atiestatīta.',
        resetPassword: 'Atiestatīt paroli',
        resetPasswordExpiration: 'Paroles atiestatīšanas termiņš',
        resetPasswordToken: 'Paroles atiestatīšanas tokens',
        resetYourPassword: 'Atiestatīt savu paroli',
        stayLoggedIn: 'Palikt pieslēgtam',
        successfullyRegisteredFirstUser: 'Pirmais lietotājs veiksmīgi reģistrēts.',
        successfullyUnlocked: 'Veiksmīgi atbloķēts',
        tokenRefreshSuccessful: 'Tokens veiksmīgi atjaunots.',
        unableToVerify: 'Neizdevās verificēt',
        username: 'Lietotājvārds',
        usernameNotValid: 'Norādītais lietotājvārds nav derīgs',
        verified: 'Verificēts',
        verifiedSuccessfully: 'Veiksmīgi verificēts',
        verify: 'Verificēt',
        verifyUser: 'Verificēt lietotāju',
        verifyYourEmail: 'Verificējiet savu e-pastu',
        youAreInactive: 'Jūs kādu laiku neesat bijis aktīvs, un drošības nolūkos drīz automātiski tiksiet atslēgts. Vai vēlaties palikt pieslēgts?',
        youAreReceivingResetPassword: 'Jūs saņemat šo ziņojumu, jo (vai kāds cits) esat pieprasījis paroles atiestatīšanu savam kontam. Lūdzu, noklikšķiniet uz šīs saites vai iekopējiet to pārlūkprogrammā, lai pabeigtu procesu:',
        youDidNotRequestPassword: 'Ja neesat pieprasījis paroles atiestatīšanu, lūdzu, ignorējiet šo e-pastu, un parole paliks nemainīta.'
    },
    error: {
        accountAlreadyActivated: 'Šis konts jau ir aktivizēts.',
        autosaving: 'Radās problēma, automātiski saglabājot šo dokumentu.',
        correctInvalidFields: 'Lūdzu, izlabojiet nederīgos laukus.',
        deletingFile: 'Radās kļūda, dzēšot failu.',
        deletingTitle: 'Radās kļūda, dzēšot {{title}}. Lūdzu, pārbaudiet savienojumu un mēģiniet vēlreiz.',
        documentNotFound: 'Dokuments ar ID {{id}} netika atrasts. Iespējams, tas ir izdzēsts vai nekad nav eksistējis, vai arī jums nav pieejas tam.',
        emailOrPasswordIncorrect: 'Norādītais e-pasts vai parole nav pareiza.',
        followingFieldsInvalid_one: 'Šis lauks nav derīgs:',
        followingFieldsInvalid_other: 'Šie lauki nav derīgi:',
        incorrectCollection: 'Nepareiza kolekcija',
        insufficientClipboardPermissions: 'Piekļuve starpliktuvei liegta. Lūdzu, pārbaudiet savas starpliktuves atļaujas.',
        invalidClipboardData: 'Nederīgi starpliktuves dati.',
        invalidFileType: 'Nederīgs faila tips',
        invalidFileTypeValue: 'Nederīgs faila tips: {{value}}',
        invalidRequestArgs: 'Pieprasījumā nodoti nederīgi argumenti: {{args}}',
        loadingDocument: 'Radās problēma, ielādējot dokumentu ar ID {{id}}.',
        localesNotSaved_one: 'Šo lokalizāciju nevarēja saglabāt:',
        localesNotSaved_other: 'Šīs lokalizācijas nevarēja saglabāt:',
        logoutFailed: 'Neizdevās atslēgties.',
        missingEmail: 'Trūkst e-pasta.',
        missingIDOfDocument: 'Trūkst dokumenta ID, ko atjaunināt.',
        missingIDOfVersion: 'Trūkst versijas ID.',
        missingRequiredData: 'Trūkst nepieciešamo datu.',
        noFilesUploaded: 'Nav augšupielādēti faili.',
        noMatchedField: 'Nav atrasts atbilstošs lauks "{{label}}"',
        notAllowedToAccessPage: 'Jums nav atļauts piekļūt šai lapai.',
        notAllowedToPerformAction: 'Jums nav atļauts veikt šo darbību.',
        notFound: 'Pieprasītais resurss nav atrasts.',
        noUser: 'Nav lietotāja',
        previewing: 'Radās problēma, priekšskatot šo dokumentu.',
        problemUploadingFile: 'Radās problēma, augšupielādējot failu.',
        restoringTitle: 'Notika kļūda, atjaunojot {{title}}. Lūdzu, pārbaudiet savu savienojumu un mēģiniet vēlreiz.',
        tokenInvalidOrExpired: 'Tokens ir nederīgs vai beidzies.',
        tokenNotProvided: 'Tokens nav norādīts.',
        unableToCopy: 'Neizdevās kopēt.',
        unableToDeleteCount: 'Neizdevās izdzēst {{count}} no {{total}} {{label}}.',
        unableToReindexCollection: 'Radās kļūda, pārindeksējot kolekciju {{collection}}. Operācija pārtraukta.',
        unableToUpdateCount: 'Neizdevās atjaunināt {{count}} no {{total}} {{label}}.',
        unauthorized: 'Neautorizēts, jums jāpieslēdzas, lai veiktu šo pieprasījumu.',
        unauthorizedAdmin: 'Neautorizēts, šim lietotājam nav piekļuves administrācijas panelim.',
        unknown: 'Radās nezināma kļūda.',
        unPublishingDocument: 'Radās problēma, atceļot dokumenta publicēšanu.',
        unspecific: 'Radās kļūda.',
        unverifiedEmail: 'Lūdzu, verificējiet savu e-pastu pirms pieslēgšanās.',
        userEmailAlreadyRegistered: 'Lietotājs ar šo e-pastu jau ir reģistrēts.',
        userLocked: 'Šis lietotājs ir bloķēts pārāk daudzu neveiksmīgu pieslēgšanās mēģinājumu dēļ.',
        usernameAlreadyRegistered: 'Lietotājs ar šo lietotājvārdu jau ir reģistrēts.',
        usernameOrPasswordIncorrect: 'Norādītais lietotājvārds vai parole nav pareiza.',
        valueMustBeUnique: 'Vērtībai jābūt unikālai',
        verificationTokenInvalid: 'Verifikācijas tokens nav derīgs.'
    },
    fields: {
        addLabel: 'Pievienot {{label}}',
        addLink: 'Pievienot saiti',
        addNew: 'Pievienot jaunu',
        addNewLabel: 'Pievienot jaunu {{label}}',
        addRelationship: 'Pievienot saistību',
        addUpload: 'Pievienot augšupielādi',
        block: 'bloks',
        blocks: 'bloki',
        blockType: 'Bloka tips',
        chooseBetweenCustomTextOrDocument: 'Izvēlieties starp pielāgotu teksta URL vai saiti uz citu dokumentu.',
        chooseDocumentToLink: 'Izvēlieties dokumentu, uz kuru saistīt',
        chooseFromExisting: 'Izvēlieties no esošajiem',
        chooseLabel: 'Izvēlieties {{label}}',
        collapseAll: 'Sakļaut visus',
        customURL: 'Pielāgots URL',
        editLabelData: 'Rediģēt {{label}} datus',
        editLink: 'Rediģēt saiti',
        editRelationship: 'Rediģēt saistību',
        enterURL: 'Ievadiet URL',
        internalLink: 'Iekšēja saite',
        itemsAndMore: '{{items}} un vēl {{count}}',
        labelRelationship: '{{label}} saistība',
        latitude: 'Platums',
        linkedTo: 'Saistīts ar <0>{{label}}</0>',
        linkType: 'Saites tips',
        longitude: 'Garums',
        newLabel: 'Jauns {{label}}',
        openInNewTab: 'Atvērt jaunā cilnē',
        passwordsDoNotMatch: 'Paroles nesakrīt.',
        relatedDocument: 'Saistītais dokuments',
        relationTo: 'Saistība ar',
        removeRelationship: 'Noņemt saistību',
        removeUpload: 'Noņemt augšupielādi',
        saveChanges: 'Saglabāt izmaiņas',
        searchForBlock: 'Meklēt bloku',
        selectExistingLabel: 'Izvēlēties esošo {{label}}',
        selectFieldsToEdit: 'Izvēlēties laukus rediģēšanai',
        showAll: 'Rādīt visus',
        swapRelationship: 'Mainīt saistību',
        swapUpload: 'Mainīt augšupielādi',
        textToDisplay: 'Rādāmais teksts',
        toggleBlock: 'Pārslēgt bloku',
        uploadNewLabel: 'Augšupielādēt jaunu {{label}}'
    },
    folder: {
        browseByFolder: 'Pārlūkot pēc mapes',
        byFolder: 'Pēc mapi',
        deleteFolder: 'Dzēst mapi',
        folderName: 'Mapes nosaukums',
        folders: 'Mapes',
        folderTypeDescription: 'Izvēlieties, kāda veida kolekcijas dokumentiem jābūt atļautiem šajā mapē.',
        itemHasBeenMoved: '{{title}} ir pārvietots uz {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} ir pārvietots uz saknes mapi',
        itemsMovedToFolder: '{{title}} pārvietots uz {{folderName}}',
        itemsMovedToRoot: '{{title}} pārvietots uz saknes mapi',
        moveFolder: 'Pārvietot mapi',
        moveItemsToFolderConfirmation: 'Jūs esat gatavs pārvietot <1>{{count}} {{label}}</1> uz <2>{{toFolder}}</2>. Vai esat pārliecināts?',
        moveItemsToRootConfirmation: 'Jūs gatavojaties pārvietot <1>{{count}} {{label}}</1> uz saknes mapi. Vai esat pārliecināts?',
        moveItemToFolderConfirmation: 'Jūs gatavojaties pārvietot <1>{{title}}</1> uz <2>{{toFolder}}</2>. Vai esat pārliecināts?',
        moveItemToRootConfirmation: 'Jūs gatavojaties pārvietot <1>{{title}}</1> uz saknes mapi. Vai esat pārliecināts?',
        movingFromFolder: 'Pārvietojot {{title}} no {{fromFolder}}',
        newFolder: 'Jauna Mape',
        noFolder: 'Nav mapes',
        renameFolder: 'Pārdēvēt mapi',
        searchByNameInFolder: 'Meklēšana pēc vārda mapē {{folderName}}',
        selectFolderForItem: 'Izvēlieties mapi priekš {{title}}'
    },
    general: {
        name: 'Vārds',
        aboutToDelete: 'Jūs grasāties dzēst {{label}} <1>{{title}}</1>. Vai esat pārliecināts?',
        aboutToDeleteCount_many: 'Jūs grasāties dzēst {{count}} {{label}}',
        aboutToDeleteCount_one: 'Jūs grasāties dzēst {{count}} {{label}}',
        aboutToDeleteCount_other: 'Jūs grasāties dzēst {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Jūs esat gatavs neatgriezeniski dzēst {{label}} <1>{{title}}</1>. Vai esat pārliecināts?',
        aboutToPermanentlyDeleteTrash: 'Jūs gatavojaties neatgriezeniski dzēst <0>{{count}}</0> <1>{{label}}</1> no miskastes. Vai esat pārliecināts?',
        aboutToRestore: 'Jūs esat gatavs atjaunot {{label}} <1>{{title}}</1>. Vai esat pārliecināts?',
        aboutToRestoreAsDraft: 'Jūs gatavojaties atjaunot {{label}} <1>{{title}}</1> kā melnrakstu. Vai esat pārliecināts?',
        aboutToRestoreAsDraftCount: 'Jūs gatavojaties atjaunot {{count}} {{label}} kā melnrakstu',
        aboutToRestoreCount: 'Jūs gatavojaties atjaunot {{count}} {{label}}',
        aboutToTrash: 'Jūs gatavojaties pārvietot {{label}} <1>{{title}}</1> uz miskasti. Vai esat pārliecināts?',
        aboutToTrashCount: 'Jūs gatavojaties pārvietot {{count}} {{label}} uz miskasti',
        addBelow: 'Pievienot zemāk',
        addFilter: 'Pievienot filtru',
        adminTheme: 'Administratora tēma',
        all: 'Visi',
        allCollections: 'Visas kolekcijas',
        allLocales: 'Visi lokalizācijas variants',
        and: 'Un',
        anotherUser: 'Cits lietotājs',
        anotherUserTakenOver: 'Cits lietotājs ir pārņēmis šī dokumenta rediģēšanu.',
        applyChanges: 'Pielietot izmaiņas',
        ascending: 'Augošā secībā',
        automatic: 'Automātiski',
        backToDashboard: 'Atpakaļ uz paneli',
        cancel: 'Atcelt',
        changesNotSaved: 'Jūsu izmaiņas nav saglabātas. Ja tagad pametīsiet, izmaiņas tiks zaudētas.',
        clear: 'Izpratiet oriģinālteksta nozīmi Payload kontekstā. Šeit ir saraksts ar Payload terminiem, kas ir ļoti specifiskas nozīmes:\n    - Kolekcija: Kolekcija ir dokumentu grupa, kuriem ir kopīga struktūra un mērķis. Kolekcijas tiek izmantotas saturu organizēšanai un pārvaldīšanai Payload.\n    - Lauks: Lauks ir konkrēts datu fragments dokumentā iekš kolekcijas. Lauki definē struktūru un dat',
        clearAll: 'Notīrīt visu',
        close: 'Aizvērt',
        collapse: 'Sakļaut',
        collections: 'Kolekcijas',
        columns: 'Kolonnas',
        columnToSort: 'Kolonna kārtošanai',
        confirm: 'Apstiprināt',
        confirmCopy: 'Apstiprināt kopēšanu',
        confirmDeletion: 'Apstiprināt dzēšanu',
        confirmDuplication: 'Apstiprināt dublēšanu',
        confirmMove: 'Apstiprināt pārvietošanu',
        confirmReindex: 'Pārindeksēt visus {{collections}}?',
        confirmReindexAll: 'Pārindeksēt visas kolekcijas?',
        confirmReindexDescription: 'Tas noņems esošos indeksus un pārindeksēs dokumentus kolekcijās {{collections}}.',
        confirmReindexDescriptionAll: 'Tas noņems esošos indeksus un pārindeksēs dokumentus visās kolekcijās.',
        confirmRestoration: 'Apstipriniet atjaunošanu',
        copied: 'Nokopēts',
        copy: 'Kopēt',
        copyField: 'Kopēt lauku',
        copying: 'Kopē...',
        copyRow: 'Kopēt rindu',
        copyWarning: 'Jūs grasāties pārrakstīt {{to}} ar {{from}} priekš {{label}} {{title}}. Vai esat pārliecināts?',
        create: 'Izveidot',
        created: 'Izveidots',
        createdAt: 'Izveidots',
        createNew: 'Izveidot jaunu',
        createNewLabel: 'Izveidot jaunu {{label}}',
        creating: 'Izveido...',
        creatingNewLabel: 'Izveido jaunu {{label}}',
        currentlyEditing: 'pašlaik rediģē šo dokumentu. Ja pārņemsiet, viņi tiks bloķēti no turpmākas rediģēšanas un var zaudēt nesaglabātās izmaiņas.',
        custom: 'Pielāgots',
        dark: 'Tumšs',
        dashboard: 'Panelis',
        delete: 'Dzēst',
        deleted: 'Dzēsts',
        deletedAt: 'Dzēsts datumā',
        deletedCountSuccessfully: 'Veiksmīgi izdzēsti {{count}} {{label}}.',
        deletedSuccessfully: 'Veiksmīgi izdzēsts.',
        deletePermanently: 'Izlaidiet miskasti un dzēsiet neatgriezeniski',
        deleting: 'Dzēš...',
        depth: 'Dziļums',
        descending: 'Dilstošā secībā',
        deselectAllRows: 'Atdzēlēt visas rindas',
        document: 'Dokuments',
        documentIsTrashed: 'Šis {{label}} ir miskastē un ir tikai lasāms.',
        documentLocked: 'Dokuments bloķēts',
        documents: 'Dokumenti',
        duplicate: 'Dublēt',
        duplicateWithoutSaving: 'Dublēt bez izmaiņu saglabāšanas',
        edit: 'Rediģēt',
        editAll: 'Rediģēt visus',
        editedSince: 'Rediģēts kopš',
        editing: 'Rediģē',
        editingLabel_many: 'Rediģē {{count}} {{label}}',
        editingLabel_one: 'Rediģē {{count}} {{label}}',
        editingLabel_other: 'Rediģē {{count}} {{label}}',
        editingTakenOver: 'Rediģēšana pārņemta',
        editLabel: 'Rediģēt {{label}}',
        email: 'E-pasts',
        emailAddress: 'E-pasta adrese',
        emptyTrash: 'Iztukšot miskasti',
        emptyTrashLabel: 'Izrakstīt {{label}} atkritumu',
        enterAValue: 'Ievadiet vērtību',
        error: 'Kļūda',
        errors: 'Kļūdas',
        exitLivePreview: 'Iziet no tiešā priekšskatījuma',
        export: 'Eksports',
        fallbackToDefaultLocale: 'Izmantot noklusēto lokalizāciju',
        false: 'Nepatiesi',
        filter: 'Filtrs',
        filters: 'Filtri',
        filterWhere: 'Filtrēt {{label}} kur',
        globals: 'Globālie',
        goBack: 'Doties atpakaļ',
        groupByLabel: 'Grupēt pēc {{label}}',
        import: 'Imports',
        isEditing: 'redzē',
        item: 'vienība',
        items: 'vienības',
        language: 'Valoda',
        lastModified: 'Pēdējoreiz mainīts',
        leaveAnyway: 'Pamest tāpat',
        leaveWithoutSaving: 'Pamest nesaglabājot',
        light: 'Gaišs',
        livePreview: 'Tiešais priekšskatījums',
        loading: 'Ielādē...',
        locale: 'Lokalizācija',
        locales: 'Lokalizācijas',
        menu: 'Izvēlne',
        moreOptions: 'Vairāk opciju',
        move: 'Pārvietoties',
        moveConfirm: 'Jūs gatavojaties pārvietot {{count}} {{label}} uz <1>{{destination}}</1>. Vai esat pārliecināts?',
        moveCount: 'Pārvietot {{count}} {{label}}',
        moveDown: 'Pārvietot uz leju',
        moveUp: 'Pārvietot uz augšu',
        moving: 'Pārvietojas',
        movingCount: 'Pārvietojot {{count}} {{label}}',
        newPassword: 'Jauna parole',
        next: 'Nākamais',
        no: 'Nē',
        noDateSelected: 'Datums nav izvēlēts',
        noFiltersSet: 'Nav uzstādīti filtri',
        noLabel: '<Nav {{label}}>',
        none: 'Nav',
        noOptions: 'Nav opciju',
        noResults: 'Nav atrasts neviens {{label}}. Vai nu vēl nav izveidots, vai neviens neatbilst augstāk norādītajiem filtriem.',
        notFound: 'Nav atrasts',
        nothingFound: 'Nekas nav atrasts',
        noTrashResults: 'Nav {{label}} miskastē.',
        noUpcomingEventsScheduled: 'Nav ieplānotu notikumu.',
        noValue: 'Nav vērtības',
        of: 'no',
        only: 'Tikai',
        open: 'Atvērt',
        or: 'Vai',
        order: 'Kārtība',
        overwriteExistingData: 'Pārrakstīt esošos datus',
        pageNotFound: 'Lapa nav atrasta',
        password: 'Parole',
        pasteField: 'Ielīmēt lauku',
        pasteRow: 'Ielīmēt rindu',
        payloadSettings: 'Payload iestatījumi',
        permanentlyDelete: 'Pastāvīgi Dzēst',
        permanentlyDeletedCountSuccessfully: 'Veiksmīgi neatgriezeniski izdzēsts {{count}} {{label}}.',
        perPage: 'Lapas ieraksti: {{limit}}',
        previous: 'Iepriekšējais',
        reindex: 'Pārindeksēt',
        reindexingAll: 'Pārindeksē visus {{collections}}.',
        remove: 'Noņemt',
        rename: 'Pārdēvēt',
        reset: 'Atiestatīt',
        resetPreferences: 'Atiestatīt iestatījumus',
        resetPreferencesDescription: 'Tas atjaunos visus jūsu iestatījumus uz noklusētajiem.',
        resettingPreferences: 'Atiestata iestatījumus...',
        restore: 'Atjaunot',
        restoreAsPublished: 'Atjaunot kā publicēto versiju',
        restoredCountSuccessfully: 'Veiksmīgi atjaunots {{count}} {{label}}.',
        restoring: 'Atjaunojot...',
        row: 'Rinda',
        rows: 'Rindas',
        save: 'Saglabāt',
        saving: 'Saglabā...',
        schedulePublishFor: 'Ieplānot publicēšanu priekš {{title}}',
        searchBy: 'Meklēt pēc {{label}}',
        select: 'Izvēlieties',
        selectAll: 'Atlasīt visus {{count}} {{label}}',
        selectAllRows: 'Atlasīt visas rindas',
        selectedCount: 'Atlasīti {{count}} {{label}}',
        selectLabel: 'Atlasīt {{label}}',
        selectValue: 'Atlasīt vērtību',
        showAllLabel: 'Rādīt visus {{label}}',
        sorryNotFound: 'Atvainojiet — jūsu pieprasījumam neatbilst nekas.',
        sort: 'Kārtot',
        sortByLabelDirection: 'Kārtot pēc {{label}} {{direction}}',
        stayOnThisPage: 'Palikt šajā lapā',
        submissionSuccessful: 'Iesniegšana veiksmīga.',
        submit: 'Iesniegt',
        submitting: 'Iesniedz...',
        success: 'Veiksmīgi',
        successfullyCreated: '{{label}} veiksmīgi izveidots.',
        successfullyDuplicated: '{{label}} veiksmīgi dublēts.',
        successfullyReindexed: 'Veiksmīgi pārindeksēti {{count}} no {{total}} dokumentiem no {{collections}}',
        takeOver: 'Pārņemt',
        thisLanguage: 'Latviešu',
        time: 'Laiks',
        timezone: 'Laika zona',
        titleDeleted: '{{label}} "{{title}}" veiksmīgi izdzēsts.',
        titleRestored: '{{label}} "{{title}}" veiksmīgi atjaunots.',
        titleTrashed: '{{label}} "{{title}}" pārvietots uz miskasti.',
        trash: 'Atkritumi',
        trashedCountSuccessfully: '{{count}} {{label}} pārvietoti uz miskasti.',
        true: 'Patiesi',
        unauthorized: 'Neautorizēts',
        unsavedChanges: 'Jums ir nesaglabātas izmaiņas. Saglabājiet vai atceliet pirms turpināšanas.',
        unsavedChangesDuplicate: 'Jums ir nesaglabātas izmaiņas. Vai vēlaties turpināt dublēšanu?',
        untitled: 'Bez nosaukuma',
        upcomingEvents: 'Gaidāmie notikumi',
        updatedAt: 'Atjaunināts',
        updatedCountSuccessfully: 'Veiksmīgi atjaunināti {{count}} {{label}}.',
        updatedLabelSuccessfully: '{{label}} veiksmīgi atjaunināts.',
        updatedSuccessfully: 'Veiksmīgi atjaunināts.',
        updateForEveryone: 'Atjaunināt visiem',
        updating: 'Atjaunina',
        uploading: 'Augšupielādē...',
        uploadingBulk: 'Augšupielādē {{current}} no {{total}}',
        user: 'Lietotājs',
        username: 'Lietotājvārds',
        users: 'Lietotāji',
        value: 'Vērtība',
        viewing: 'Skatīšanās',
        viewReadOnly: 'Skatīt tikai lasāmu',
        welcome: 'Laipni lūdzam',
        yes: 'Jā'
    },
    localization: {
        cannotCopySameLocale: 'Nevar kopēt uz to pašu lokalizāciju',
        copyFrom: 'Kopēt no',
        copyFromTo: 'Kopē no {{from}} uz {{to}}',
        copyTo: 'Kopēt uz',
        copyToLocale: 'Kopēt uz lokalizāciju',
        localeToPublish: 'Lokalizācija publicēšanai',
        selectLocaleToCopy: 'Izvēlieties lokalizāciju, no kuras kopēt'
    },
    operators: {
        contains: 'satur',
        equals: 'ir vienāds ar',
        exists: 'eksistē',
        intersects: 'krustojas',
        isGreaterThan: 'ir lielāks par',
        isGreaterThanOrEqualTo: 'ir lielāks vai vienāds ar',
        isIn: 'ir iekšā',
        isLessThan: 'ir mazāks par',
        isLessThanOrEqualTo: 'ir mazāks vai vienāds ar',
        isLike: 'ir līdzīgs',
        isNotEqualTo: 'nav vienāds ar',
        isNotIn: 'nav iekšā',
        isNotLike: 'nav līdzīgs',
        near: 'tuvu',
        within: 'iekšā'
    },
    upload: {
        addFile: 'Pievienot failu',
        addFiles: 'Pievienot failus',
        bulkUpload: 'Masveida augšupielāde',
        crop: 'Apgriezt',
        cropToolDescription: 'Velciet atlasītā apgabala stūrus, uzzīmējiet jaunu apgabalu vai pielāgojiet vērtības zemāk.',
        download: 'Lejupielādēt',
        dragAndDrop: 'Ievelciet un nometiet failu',
        dragAndDropHere: 'vai ievelciet un nometiet failu šeit',
        editImage: 'Rediģēt attēlu',
        fileName: 'Faila nosaukums',
        fileSize: 'Faila izmērs',
        filesToUpload: 'Faili augšupielādei',
        fileToUpload: 'Fails augšupielādei',
        focalPoint: 'Fokusa punkts',
        focalPointDescription: 'Velciet fokusa punktu tieši priekšskatījumā vai pielāgojiet vērtības zemāk.',
        height: 'Augstums',
        lessInfo: 'Mazāk informācijas',
        moreInfo: 'Vairāk informācijas',
        noFile: 'Nav faila',
        pasteURL: 'Ielīmēt URL',
        previewSizes: 'Priekšskatījuma izmēri',
        selectCollectionToBrowse: 'Izvēlieties kolekciju, ko pārlūkot',
        selectFile: 'Izvēlieties failu',
        setCropArea: 'Iestatīt apgriešanas apgabalu',
        setFocalPoint: 'Iestatīt fokusa punktu',
        sizes: 'Izmēri',
        sizesFor: 'Izmēri priekš {{label}}',
        width: 'Platums'
    },
    validation: {
        emailAddress: 'Lūdzu, ievadiet derīgu e-pasta adresi.',
        enterNumber: 'Lūdzu, ievadiet derīgu numuru.',
        fieldHasNo: 'Šim laukam nav {{label}}',
        greaterThanMax: '{{value}} ir lielāks par maksimāli atļauto {{label}}: {{max}}.',
        invalidInput: 'Šim laukam ir nederīga ievade.',
        invalidSelection: 'Šim laukam ir nederīga izvēle.',
        invalidSelections: 'Šim laukam ir šādas nederīgas izvēles:',
        lessThanMin: '{{value}} ir mazāks par minimāli atļauto {{label}}: {{min}}.',
        limitReached: 'Sasniegts limits, var pievienot tikai {{max}} vienumus.',
        longerThanMin: 'Šai vērtībai jābūt garākai par minimālo garumu: {{minLength}} rakstzīmes.',
        notValidDate: '"{{value}}" nav derīgs datums.',
        required: 'Šis lauks ir obligāts.',
        requiresAtLeast: 'Šim laukam nepieciešami vismaz {{count}} {{label}}.',
        requiresNoMoreThan: 'Šim laukam nepieciešams ne vairāk kā {{count}} {{label}}.',
        requiresTwoNumbers: 'Šim laukam nepieciešami divi skaitļi.',
        shorterThanMax: 'Šai vērtībai jābūt īsākai par maksimālo garumu: {{maxLength}} rakstzīmes.',
        timezoneRequired: 'Nepieciešama laika josla.',
        trueOrFalse: 'Šis lauks var būt tikai "true" vai "false".',
        username: 'Lūdzu, ievadiet derīgu lietotājvārdu. Drīkst saturēt burtus, ciparus, defises, punktus un pasvītras.',
        validUploadID: 'Šis lauks nav derīgs augšupielādes ID.'
    },
    version: {
        type: 'Tips',
        aboutToPublishSelection: 'Jūs grasāties publicēt visus {{label}} izvēlētajā sarakstā. Vai esat pārliecināts?',
        aboutToRestore: 'Jūs grasāties atjaunot šo {{label}} dokumentu uz stāvokli, kādā tas bija {{versionDate}}.',
        aboutToRestoreGlobal: 'Jūs grasāties atjaunot globālo {{label}} uz stāvokli, kādā tas bija {{versionDate}}.',
        aboutToRevertToPublished: 'Jūs grasāties atsaukt šī dokumenta izmaiņas uz publicēto versiju. Vai esat pārliecināts?',
        aboutToUnpublish: 'Jūs grasāties atcelt šī dokumenta publicēšanu. Vai esat pārliecināts?',
        aboutToUnpublishSelection: 'Jūs grasāties atcelt publicēšanu visiem {{label}} izvēlētajā sarakstā. Vai esat pārliecināts?',
        autosave: 'Automātiskā saglabāšana',
        autosavedSuccessfully: 'Veiksmīgi automātiski saglabāts.',
        autosavedVersion: 'Automātiski saglabāta versija',
        changed: 'Mainīts',
        changedFieldsCount_one: '{{count}} mainīts lauks',
        changedFieldsCount_other: '{{count}} mainīti lauki',
        compareVersion: 'Salīdzināt versiju ar:',
        compareVersions: 'Salīdzināt versijas',
        comparingAgainst: 'Salīdzinot ar',
        confirmPublish: 'Apstiprināt publicēšanu',
        confirmRevertToSaved: 'Apstiprināt atgriešanu uz saglabāto',
        confirmUnpublish: 'Apstiprināt publicēšanas atcelšanu',
        confirmVersionRestoration: 'Apstiprināt versijas atjaunošanu',
        currentDocumentStatus: 'Pašreizējais {{docStatus}} dokuments',
        currentDraft: 'Pašreizējais melnraksts',
        currentlyPublished: 'Pašlaik publicēts',
        currentlyViewing: 'Pašlaik skatās',
        currentPublishedVersion: 'Pašreizējā publicētā versija',
        draft: 'Melnraksts',
        draftSavedSuccessfully: 'Melnraksts veiksmīgi saglabāts.',
        lastSavedAgo: 'Pēdējo reizi saglabāts pirms {{distance}}',
        modifiedOnly: 'Tikai modificētie',
        moreVersions: 'Vairāk versijas...',
        noFurtherVersionsFound: 'Papildu versijas nav atrastas',
        noRowsFound: 'Nav atrasts neviens {{label}}',
        noRowsSelected: 'Nav atlasīts neviens {{label}}',
        preview: 'Priekšskatījums',
        previouslyDraft: 'Iepriekšējais melnraksts',
        previouslyPublished: 'Iepriekš publicēts',
        previousVersion: 'Iepriekšējā versija',
        problemRestoringVersion: 'Radās problēma, atjaunojot šo versiju',
        publish: 'Publicēt',
        publishAllLocales: 'Publicēt visas lokalizācijas',
        publishChanges: 'Publicēt izmaiņas',
        published: 'Publicēts',
        publishIn: 'Publicēt {{locale}}',
        publishing: 'Publicē...',
        restoreAsDraft: 'Atjaunot kā melnrakstu',
        restoredSuccessfully: 'Veiksmīgi atjaunots.',
        restoreThisVersion: 'Atjaunot šo versiju',
        restoring: 'Atjauno...',
        reverting: 'Atgriež...',
        revertToPublished: 'Atgriezt uz publicēto',
        saveDraft: 'Saglabāt melnrakstu',
        scheduledSuccessfully: 'Veiksmīgi ieplānots.',
        schedulePublish: 'Ieplānot publicēšanu',
        selectLocales: 'Izvēlēties lokalizācijas, ko rādīt',
        selectVersionToCompare: 'Izvēlēties versiju salīdzināšanai',
        showingVersionsFor: 'Rāda versijas priekš:',
        showLocales: 'Rādīt lokalizācijas:',
        specificVersion: 'Konkrētā versija',
        status: 'Statuss',
        unpublish: 'Atcelt publicēšanu',
        unpublishing: 'Atceļ publicēšanu...',
        version: 'Versija',
        versionAgo: '{{distance}} pirms',
        versionCount_many: 'Atrastas {{count}} versijas',
        versionCount_none: 'Nav atrastu versiju',
        versionCount_one: 'Atrasta {{count}} versija',
        versionCount_other: 'Atrastas {{count}} versijas',
        versionCreatedOn: '{{version}} izveidota:',
        versionID: 'Versijas ID',
        versions: 'Versijas',
        viewingVersion: 'Skatās versiju priekš {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Skatās versiju globālajam {{entityLabel}}',
        viewingVersions: 'Skatās versijas priekš {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Skatās versijas globālajam {{entityLabel}}'
    }
};
export const lv = {
    dateFNSKey: 'lv',
    translations: lvTranslations
};

//# sourceMappingURL=lv.js.map