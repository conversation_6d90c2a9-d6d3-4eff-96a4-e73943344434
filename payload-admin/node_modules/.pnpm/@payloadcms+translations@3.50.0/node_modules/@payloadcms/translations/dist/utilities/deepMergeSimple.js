/**
 * Very simple, but fast deepMerge implementation. Only deepMerges objects, not arrays and clones everything.
 * Do not use this if your object contains any complex objects like React Components, or if you would like to combine Arrays.
 * If you only have simple objects and need a fast deepMerge, this is the function for you.
 *
 * obj2 takes precedence over obj1 - thus if obj2 has a key that obj1 also has, obj2's value will be used.
 *
 * @param obj1 base object
 * @param obj2 object to merge "into" obj1
 */ export function deepMergeSimple(obj1, obj2) {
    const output = {
        ...obj1
    };
    for(const key in obj2){
        if (Object.prototype.hasOwnProperty.call(obj2, key)) {
            // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve
            if (typeof obj2[key] === 'object' && !Array.isArray(obj2[key]) && obj1[key]) {
                // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve
                output[key] = deepMergeSimple(obj1[key], obj2[key]);
            } else {
                // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve
                output[key] = obj2[key];
            }
        }
    }
    return output;
}

//# sourceMappingURL=deepMergeSimple.js.map