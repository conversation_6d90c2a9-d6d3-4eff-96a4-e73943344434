{"version": 3, "sources": ["../../src/languages/ru.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const ruTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Аккаунт',\n    accountOfCurrentUser: 'Аккаунт текущего пользователя',\n    accountVerified: 'Учетная запись успешно подтверждена.',\n    alreadyActivated: 'Уже активирован',\n    alreadyLoggedIn: 'Уже вошли в систему',\n    apiKey: 'API ключ',\n    authenticated: 'Аутентифицирован',\n    backToLogin: 'Вернуться к входу',\n    beginCreateFirstUser: 'Чтобы начать - создайте первого пользователя.',\n    changePassword: 'Сменить пароль',\n    checkYourEmailForPasswordReset:\n      'Если указанный адрес электронной почты связан с аккаунтом, вы скоро получите инструкции по сбросу пароля. Пожалуйста, проверьте папку со спамом или нежелательной почтой, если вы не видите письма во входящих.',\n    confirmGeneration: 'Подтвердить генерацию',\n    confirmPassword: 'Подтверждение пароля',\n    createFirstUser: 'Создание первого пользователя',\n    emailNotValid: 'Указанный адрес электронной почты неверен',\n    emailOrUsername: 'Электронная почта или Имя пользователя',\n    emailSent: 'Email отправлен',\n    emailVerified: 'Электронная почта успешно подтверждена.',\n    enableAPIKey: 'Активировать API ключ',\n    failedToUnlock: 'Не удалось разблокировать',\n    forceUnlock: 'Принудительная разблокировка',\n    forgotPassword: 'Забыли пароль',\n    forgotPasswordEmailInstructions:\n      'Пожалуйста, введите ваш email. Вы получите письмо с инструкцией по восстановлению пароля.',\n    forgotPasswordQuestion: 'Забыли пароль?',\n    forgotPasswordUsernameInstructions:\n      'Пожалуйста, введите ваше имя пользователя ниже. Инструкции по сбросу вашего пароля будут отправлены на адрес электронной почты, связанный с вашим именем пользователя.',\n    generate: 'Сгенерировать',\n    generateNewAPIKey: 'Сгенерировать новый API ключ',\n    generatingNewAPIKeyWillInvalidate:\n      'Генерация нового API ключа приведёт к <1>недействительности</1> предыдущего ключа. Вы уверены, что хотите продолжить?',\n    lockUntil: 'Заблокировать до',\n    logBackIn: 'Войти снова',\n    loggedIn: 'Чтобы войти в другую учетную запись, сначала <0>выйдите</0>.',\n    loggedInChangePassword:\n      'Чтобы изменить пароль, зайдите в свой <0>аккаунт</0> и измените пароль там.',\n    loggedOutInactivity: 'Вы вышли из системы из-за неактивности.',\n    loggedOutSuccessfully: 'Вы успешно вышли из системы.',\n    loggingOut: 'Выход из системы...',\n    login: 'Войти',\n    loginAttempts: 'Попытки входа',\n    loginUser: 'Вход пользователя в систему',\n    loginWithAnotherUser:\n      'Чтобы войти в систему под другим пользователем, необходимо сначала <0>выйти</0>.',\n    logOut: 'Выйти',\n    logout: 'Выйти',\n    logoutSuccessful: 'Выход выполнен успешно.',\n    logoutUser: 'Вход из системы',\n    newAccountCreated:\n      'Новый аккаунт был создан для доступа к <a href=\"{{serverURL}}\">{{serverURL}}</a> Пожалуйста, кликните по следующей ссылке или вставьте в адресную строку браузера чтобы подтвердить email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> После подтверждения вашего email, вы сможете успешно войти в систему.',\n    newAPIKeyGenerated: 'Новый API ключ сгенерирован.',\n    newPassword: 'Новый пароль',\n    passed: 'Аутентификация пройдена',\n    passwordResetSuccessfully: 'Сброс пароля успешно выполнен.',\n    resetPassword: 'Сброс пароля',\n    resetPasswordExpiration: 'Сброс пароля по истечении срока действия',\n    resetPasswordToken: 'Токен сброса пароля',\n    resetYourPassword: 'Сброс вашего пароля',\n    stayLoggedIn: 'Остаться в системе',\n    successfullyRegisteredFirstUser: 'Успешно зарегистрирован первый пользователь.',\n    successfullyUnlocked: 'Успешно разблокирован',\n    tokenRefreshSuccessful: 'Обновление токена прошло успешно.',\n    unableToVerify: 'Невозможно подтвердить',\n    username: 'Имя пользователя',\n    usernameNotValid: 'Предоставленное имя пользователя недействительно.',\n    verified: 'Подтверждено',\n    verifiedSuccessfully: 'Успешно подтверждено',\n    verify: 'Подтвердить',\n    verifyUser: 'Подтвердить пользователя',\n    verifyYourEmail: 'Подтвердить ваш email',\n    youAreInactive:\n      'Вы не были активны в течение некоторого времени и скоро автоматически выйдете из системы в целях вашей безопасности. Вы хотите остаться в системе?',\n    youAreReceivingResetPassword:\n      'Вы получили это сообщение, потому что вы (или кто-то другой) запросили сброс пароля для вашей учетной записи. Пожалуйста, нажмите на следующую ссылку или вставьте ее в браузер, чтобы завершить процесс:',\n    youDidNotRequestPassword:\n      'Если вы не запрашивали этого, пожалуйста, проигнорируйте это письмо, и ваш пароль останется неизменным.',\n  },\n  error: {\n    accountAlreadyActivated: 'Этот аккаунт уже был активирован.',\n    autosaving: 'При автосохранении этого документа возникла проблема.',\n    correctInvalidFields: 'Пожалуйста, исправьте неправильные поля.',\n    deletingFile: 'Произошла ошибка при удалении файла.',\n    deletingTitle:\n      'При удалении {{title}} произошла ошибка. Пожалуйста, проверьте соединение и повторите попытку.',\n    documentNotFound:\n      'Документ с ID {{id}} не удалось найти. Возможно, он был удален или никогда не существовал, или у вас нет доступа к нему.',\n    emailOrPasswordIncorrect: 'Указанный email или пароль неверен.',\n    followingFieldsInvalid_one: 'Следующее поле недействительно:',\n    followingFieldsInvalid_other: 'Следующие поля недействительны:',\n    incorrectCollection: 'Неправильная Коллекция',\n    insufficientClipboardPermissions:\n      'Доступ к буферу обмена отклонен. Проверьте разрешения буфера обмена.',\n    invalidClipboardData: 'Неверные данные в буфере обмена.',\n    invalidFileType: 'Недопустимый тип файла',\n    invalidFileTypeValue: 'Недопустимый тип файла: {{value}}',\n    invalidRequestArgs: 'В запрос переданы недопустимые аргументы: {{args}}',\n    loadingDocument: 'Возникла проблема при загрузке документа с ID {{id}}.',\n    localesNotSaved_one: 'Следующую локализацию не удалось сохранить:',\n    localesNotSaved_other: 'Следующие локализации не удалось сохранить:',\n    logoutFailed: 'Выход не удался.',\n    missingEmail: 'Отсутствует email.',\n    missingIDOfDocument: 'Отсутствующий ID документа для обновления.',\n    missingIDOfVersion: 'Отсутствует ID версии.',\n    missingRequiredData: 'Отсутствуют необходимые данные.',\n    noFilesUploaded: 'Не было загружено ни одного файла.',\n    noMatchedField: 'Не найдено подходящего поля для \"{{label}}\"',\n    notAllowedToAccessPage: 'Вы не имеете права доступа к этой странице.',\n    notAllowedToPerformAction: 'У вас нет права на выполнение этого действия.',\n    notFound: 'Запрашиваемый ресурс не найден.',\n    noUser: 'Нет Пользователя',\n    previewing: 'При предварительном просмотре этого документа возникла проблема.',\n    problemUploadingFile: 'Возникла проблема при загрузке файла.',\n    restoringTitle:\n      'Произошла ошибка при восстановлении {{title}}. Пожалуйста, проверьте свое соединение и попробуйте снова.',\n    tokenInvalidOrExpired: 'Токен либо недействителен, либо срок его действия истек.',\n    tokenNotProvided: 'Токен не предоставлен.',\n    unableToCopy: 'Не удалось скопировать.',\n    unableToDeleteCount: 'Не удалось удалить {{count}} из {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Ошибка при переиндексации коллекции {{collection}}. Операция прервана.',\n    unableToUpdateCount: 'Не удалось обновить {{count}} из {{total}} {{label}}.',\n    unauthorized: 'Нет доступа, вы должны войти, чтобы сделать этот запрос.',\n    unauthorizedAdmin: 'Нет доступа, этот пользователь не имеет доступа к панели администратора.',\n    unknown: 'Произошла неизвестная ошибка.',\n    unPublishingDocument: 'При отмене публикации этого документа возникла проблема.',\n    unspecific: 'Произошла ошибка.',\n    unverifiedEmail: 'Пожалуйста, подтвердите свою электронную почту перед входом.',\n    userEmailAlreadyRegistered: 'Пользователь с указанным email уже зарегистрирован.',\n    userLocked:\n      'Этот пользователь заблокирован из-за слишком большого количества неудачных попыток входа.',\n    usernameAlreadyRegistered: 'Пользователь с данным именем пользователя уже зарегистрирован.',\n    usernameOrPasswordIncorrect: 'Указанное имя пользователя или пароль неверны.',\n    valueMustBeUnique: 'Значение должно быть уникальным',\n    verificationTokenInvalid: 'Проверочный токен недействителен.',\n  },\n  fields: {\n    addLabel: 'Добавить {{label}}',\n    addLink: 'Добавить ссылку',\n    addNew: 'Добавить новый',\n    addNewLabel: 'Добавить {{label}}',\n    addRelationship: 'Добавить Отношения',\n    addUpload: 'Добавить загрузку',\n    block: 'Блок',\n    blocks: 'Блоки',\n    blockType: 'Тип Блока',\n    chooseBetweenCustomTextOrDocument:\n      'Выберите между вводом пользовательского текстового URL и ссылкой на другой документ.',\n    chooseDocumentToLink: 'Выберите документ для ссылки',\n    chooseFromExisting: 'Выбрать из существующих',\n    chooseLabel: 'Выбрать {{label}}',\n    collapseAll: 'Свернуть все',\n    customURL: 'Пользовательский URL',\n    editLabelData: 'Редактировать данные {{label}}',\n    editLink: 'Редактировать ссылку',\n    editRelationship: 'Редактировать Отношения',\n    enterURL: 'Введите URL',\n    internalLink: 'Внутренняя ссылка',\n    itemsAndMore: '{{items}} и ещё {{count}}',\n    labelRelationship: '{{label}} Отношения',\n    latitude: 'Широта',\n    linkedTo: 'Связано с <0>{{label}}</0>',\n    linkType: 'Тип ссылки',\n    longitude: 'Долгота',\n    newLabel: 'Новый {{label}}',\n    openInNewTab: 'Открывать в новой вкладке',\n    passwordsDoNotMatch: 'Пароли не совпадают.',\n    relatedDocument: 'Связанный документ',\n    relationTo: 'Отношение к',\n    removeRelationship: 'Удалить связь',\n    removeUpload: 'Удалить загруженное',\n    saveChanges: 'Сохранить изменения',\n    searchForBlock: 'Найти Блок',\n    selectExistingLabel: 'Выберите существующий {{label}}',\n    selectFieldsToEdit: 'Выберите поля для редактирования',\n    showAll: 'Показать все',\n    swapRelationship: 'Поменять отношения',\n    swapUpload: 'Заменить загруженное',\n    textToDisplay: 'Текст для отображения',\n    toggleBlock: 'Переключить Блок',\n    uploadNewLabel: 'Загрузить новый {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Просмотр по папкам',\n    byFolder: 'По папке',\n    deleteFolder: 'Удалить папку',\n    folderName: 'Название папки',\n    folders: 'Папки',\n    folderTypeDescription:\n      'Выберите, какие типы документов коллекции должны быть разрешены в этой папке.',\n    itemHasBeenMoved: '{{title}} был перемещен в {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} был перемещен в корневую папку',\n    itemsMovedToFolder: '{{title}} перемещен в {{folderName}}',\n    itemsMovedToRoot: '{{title}} перемещен в корневую папку',\n    moveFolder: 'Переместить папку',\n    moveItemsToFolderConfirmation:\n      'Вы собираетесь переместить <1>{{count}} {{label}}</1> в <2>{{toFolder}}</2>. Вы уверены?',\n    moveItemsToRootConfirmation:\n      'Вы собираетесь перенести <1>{{count}} {{label}}</1> в корневую папку. Вы уверены?',\n    moveItemToFolderConfirmation:\n      'Вы собираетесь переместить <1>{{title}}</1> в <2>{{toFolder}}</2>. Вы уверены?',\n    moveItemToRootConfirmation:\n      'Вы собираетесь переместить <1>{{title}}</1> в корневую папку. Вы уверены?',\n    movingFromFolder: 'Перемещение {{title}} из {{fromFolder}}',\n    newFolder: 'Новая папка',\n    noFolder: 'Нет папки',\n    renameFolder: 'Переименовать папку',\n    searchByNameInFolder: 'Поиск по имени в {{folderName}}',\n    selectFolderForItem: 'Выберите папку для {{title}}',\n  },\n  general: {\n    name: 'Имя',\n    aboutToDelete: 'Вы собираетесь удалить {{label}} <1>{{title}}</1>. Вы уверены?',\n    aboutToDeleteCount_many: 'Вы собираетесь удалить {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Вы собираетесь удалить {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Вы собираетесь удалить {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Вы собираетесь навсегда удалить {{label}} <1>{{title}}</1>. Вы уверены?',\n    aboutToPermanentlyDeleteTrash:\n      'Вы собираетесь навсегда удалить <0>{{count}}</0> <1>{{label}}</1> из корзины. Вы уверены?',\n    aboutToRestore: 'Вы собираетесь восстановить {{label}} <1>{{title}}</1>. Вы уверены?',\n    aboutToRestoreAsDraft:\n      'Вы собираетесь восстановить {{label}} <1>{{title}}</1> как черновик. Вы уверены?',\n    aboutToRestoreAsDraftCount: 'Вы собираетесь восстановить {{count}} {{label}} как черновик',\n    aboutToRestoreCount: 'Вы собираетесь восстановить {{count}} {{label}}',\n    aboutToTrash: 'Вы собираетесь переместить {{label}} <1>{{title}}</1> в корзину. Вы уверены?',\n    aboutToTrashCount: 'Вы собираетесь переместить {{count}} {{label}} в корзину',\n    addBelow: 'Добавить ниже',\n    addFilter: 'Добавить фильтр',\n    adminTheme: 'Тема Панели',\n    all: 'Все',\n    allCollections: 'Все Коллекции',\n    allLocales: 'Все локали',\n    and: 'А также',\n    anotherUser: 'Другой пользователь',\n    anotherUserTakenOver: 'Другой пользователь взял на себя редактирование этого документа.',\n    applyChanges: 'Применить изменения',\n    ascending: 'Восходящий',\n    automatic: 'Автоматически',\n    backToDashboard: 'Назад к Панели',\n    cancel: 'Отмена',\n    changesNotSaved:\n      'Ваши изменения не были сохранены. Если вы сейчас уйдете, то потеряете свои изменения.',\n    clear: 'Четкий',\n    clearAll: 'Очистить все',\n    close: 'Закрыть',\n    collapse: 'Свернуть',\n    collections: 'Коллекции',\n    columns: 'Колонки',\n    columnToSort: 'Колонка для сортировки',\n    confirm: 'Подтвердить',\n    confirmCopy: 'Подтвердить копирование',\n    confirmDeletion: 'Подтвердить удаление',\n    confirmDuplication: 'Подтвердить копирование',\n    confirmMove: 'Подтвердите перемещение',\n    confirmReindex: 'Переиндексировать все {{collections}}?',\n    confirmReindexAll: 'Переиндексировать все коллекции?',\n    confirmReindexDescription:\n      'Это удалит существующие индексы и переиндексирует документы в коллекциях {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Это удалит существующие индексы и переиндексирует документы во всех коллекциях.',\n    confirmRestoration: 'Подтвердите восстановление',\n    copied: 'Скопировано',\n    copy: 'Скопировать',\n    copyField: 'Копировать поле',\n    copying: 'Копирование',\n    copyRow: 'Копировать строку',\n    copyWarning:\n      'Вы собираетесь перезаписать {{to}} на {{from}} для {{label}} {{title}}. Вы уверены?',\n    create: 'Создать',\n    created: 'Создано',\n    createdAt: 'Дата создания',\n    createNew: 'Создать',\n    createNewLabel: 'Создать новый {{label}}',\n    creating: 'Создание',\n    creatingNewLabel: 'Создание нового {{label}}',\n    currentlyEditing:\n      'в настоящее время редактирует этот документ. Если вы возьмете на себя, они будут заблокированы от продолжения редактирования и могут потерять несохраненные изменения.',\n    custom: 'Обычай',\n    dark: 'Тёмная',\n    dashboard: 'Панель',\n    delete: 'Удалить',\n    deleted: 'Удалено',\n    deletedAt: 'Удалено В',\n    deletedCountSuccessfully: 'Удалено {{count}} {{label}} успешно.',\n    deletedSuccessfully: 'Удален успешно.',\n    deletePermanently: 'Пропустить корзину и удалить навсегда',\n    deleting: 'Удаление...',\n    depth: 'Глубина',\n    descending: 'Уменьшение',\n    deselectAllRows: 'Снять выделение со всех строк',\n    document: 'Документ',\n    documentIsTrashed: 'Этот {{label}} находится в корзине и доступен только для чтения.',\n    documentLocked: 'Документ заблокирован',\n    documents: 'Документы',\n    duplicate: 'Дублировать',\n    duplicateWithoutSaving: 'Дублирование без сохранения изменений',\n    edit: 'Редактировать',\n    editAll: 'Редактировать все',\n    editedSince: 'Отредактировано с',\n    editing: 'Редактирование',\n    editingLabel_many: 'Редактирование {{count}} {{label}}',\n    editingLabel_one: 'Редактирование {{count}} {{label}}',\n    editingLabel_other: 'Редактирование {{count}} {{label}}',\n    editingTakenOver: 'Редактирование взято под контроль',\n    editLabel: 'Редактировать {{label}}',\n    email: 'Email',\n    emailAddress: 'Email',\n    emptyTrash: 'Очистить корзину',\n    emptyTrashLabel: 'Очистить корзину для {{label}}',\n    enterAValue: 'Введите значение',\n    error: 'Ошибка',\n    errors: 'Ошибки',\n    exitLivePreview: 'Выйти из режима прямого просмотра',\n    export: 'Экспорт',\n    fallbackToDefaultLocale: 'Возврат к локали по умолчанию',\n    false: 'Ложь',\n    filter: 'Фильтр',\n    filters: 'Фильтры',\n    filterWhere: 'Где фильтровать',\n    globals: 'Глобальные',\n    goBack: 'Назад',\n    groupByLabel: 'Группировать по {{label}}',\n    import: 'Импорт',\n    isEditing: 'редактирует',\n    item: 'предмет',\n    items: 'предметы',\n    language: 'Язык',\n    lastModified: 'Последнее изменение',\n    leaveAnyway: 'Все равно уйти',\n    leaveWithoutSaving: 'Выход без сохранения',\n    light: 'Светлая',\n    livePreview: 'Предпросмотр',\n    loading: 'Загрузка',\n    locale: 'Локаль',\n    locales: 'Локали',\n    menu: 'Меню',\n    moreOptions: 'Больше вариантов',\n    move: 'Переместить',\n    moveConfirm:\n      'Вы собираетесь переместить {{count}} {{label}} в <1>{{destination}}</1>. Вы уверены?',\n    moveCount: 'Переместить {{count}} {{label}}',\n    moveDown: 'Сдвинуть вниз',\n    moveUp: 'Сдвинуть вверх',\n    moving: 'Переезд',\n    movingCount: 'Перемещение {{count}} {{label}}',\n    newPassword: 'Новый пароль',\n    next: 'Следующий',\n    no: 'Нет',\n    noDateSelected: 'Дата не выбрана',\n    noFiltersSet: 'Фильтры не установлены',\n    noLabel: 'Без метки',\n    none: 'Никто',\n    noOptions: 'Нет вариантов',\n    noResults:\n      'Ничего не найдено. Возможно, {{label}} еще не существует или не соответствует указанным фильтрам.',\n    notFound: 'Не найдено',\n    nothingFound: 'Ничего не найдено',\n    noTrashResults: 'Нет {{label}} в корзине.',\n    noUpcomingEventsScheduled: 'Нет запланированных предстоящих событий.',\n    noValue: 'Нет значения',\n    of: 'из',\n    only: 'Только',\n    open: 'Открыть',\n    or: 'Или же',\n    order: 'Порядок',\n    overwriteExistingData: 'Перезаписать существующие данные поля',\n    pageNotFound: 'Страница не найдена',\n    password: 'Пароль',\n    pasteField: 'Вставить поле',\n    pasteRow: 'Вставить строку',\n    payloadSettings: 'Настройки Payload',\n    permanentlyDelete: 'Удалить Навсегда',\n    permanentlyDeletedCountSuccessfully: 'Успешно удалено {{count}} {{label}} навсегда.',\n    perPage: 'На странице: {{limit}}',\n    previous: 'Предыдущий',\n    reindex: 'Переиндексировать',\n    reindexingAll: 'Переиндексирование всех {{collections}}.',\n    remove: 'Удалить',\n    rename: 'Переименовать',\n    reset: 'Сброс',\n    resetPreferences: 'Сбросить настройки',\n    resetPreferencesDescription: 'Это сбросит все ваши настройки до значений по умолчанию.',\n    resettingPreferences: 'Сброс настроек.',\n    restore: 'Восстановить',\n    restoreAsPublished: 'Восстановить как опубликованную версию',\n    restoredCountSuccessfully: 'Восстановлено успешно {{count}} {{label}}.',\n    restoring: 'Восстановление...',\n    row: 'Строка',\n    rows: 'Строки',\n    save: 'Сохранить',\n    saving: 'Сохранение...',\n    schedulePublishFor: 'Запланировать публикацию для {{title}}',\n    searchBy: 'Искать по',\n    select: 'Выбрать',\n    selectAll: 'Выбрать все {{count}} {{label}}',\n    selectAllRows: 'Выбрать все строки',\n    selectedCount: '{{count}} {{label}} выбрано',\n    selectLabel: 'Выберите {{label}}',\n    selectValue: 'Выбрать значение',\n    showAllLabel: 'Показать все {{label}}',\n    sorryNotFound: 'К сожалению, ничего подходящего под ваш запрос нет.',\n    sort: 'Сортировать',\n    sortByLabelDirection: 'Сортировать по {{label}} {{direction}}',\n    stayOnThisPage: 'Остаться на этой странице',\n    submissionSuccessful: 'Успешно отправлено.',\n    submit: 'Отправить',\n    submitting: 'Подача заявления...',\n    success: 'Успех',\n    successfullyCreated: '{{label}} успешно создан.',\n    successfullyDuplicated: '{{label}} успешно продублирован.',\n    successfullyReindexed:\n      'Успешно переиндексировано {{count}} из {{total}} документов из {{collections}} коллекций.',\n    takeOver: 'Взять на себя',\n    thisLanguage: 'Русский',\n    time: 'Время',\n    timezone: 'Часовой пояс',\n    titleDeleted: '{{label}} {{title}} успешно удалено.',\n    titleRestored: '{{label}} \"{{title}}\" успешно восстановлен.',\n    titleTrashed: '{{label}} \"{{title}}\" перемещен в корзину.',\n    trash: 'Мусор',\n    trashedCountSuccessfully: '{{count}} {{label}} перемещено в корзину.',\n    true: 'Правда',\n    unauthorized: 'Нет доступа',\n    unsavedChanges:\n      'У вас есть несохраненные изменения. Сохраните или отмените их перед продолжением.',\n    unsavedChangesDuplicate:\n      'У вас есть несохраненные изменения. Вы хотите продолжить дублирование?',\n    untitled: 'Без названия',\n    upcomingEvents: 'Предстоящие события',\n    updatedAt: 'Дата правки',\n    updatedCountSuccessfully: 'Обновлено {{count}} {{label}} успешно.',\n    updatedLabelSuccessfully: 'Успешно обновлено {{label}}.',\n    updatedSuccessfully: 'Успешно Обновлено.',\n    updateForEveryone: 'Обновление для всех',\n    updating: 'Обновление',\n    uploading: 'Загрузка',\n    uploadingBulk: 'Загрузка {{current}} из {{total}}',\n    user: 'пользователь',\n    username: 'Имя пользователя',\n    users: 'пользователи',\n    value: 'Значение',\n    viewing: 'Просмотр',\n    viewReadOnly: 'Просмотр только для чтения',\n    welcome: 'Добро пожаловать',\n    yes: 'Да',\n  },\n  localization: {\n    cannotCopySameLocale: 'Невозможно скопировать в ту же локаль',\n    copyFrom: 'Скопировать из',\n    copyFromTo: 'Копирование из {{from}} в {{to}}',\n    copyTo: 'Копировать в',\n    copyToLocale: 'Копировать в локаль',\n    localeToPublish: 'Локаль для публикации',\n    selectLocaleToCopy: 'Выберите локаль для копирования',\n  },\n  operators: {\n    contains: 'содержит',\n    equals: 'равно',\n    exists: 'существует',\n    intersects: 'пересекает',\n    isGreaterThan: 'больше чем',\n    isGreaterThanOrEqualTo: 'больше или равно',\n    isIn: 'находится',\n    isLessThan: 'меньше чем',\n    isLessThanOrEqualTo: 'меньше или равно',\n    isLike: 'похоже',\n    isNotEqualTo: 'не равно',\n    isNotIn: 'нет в',\n    isNotLike: 'не похож',\n    near: 'рядом',\n    within: 'в пределах',\n  },\n  upload: {\n    addFile: 'Добавить файл',\n    addFiles: 'Добавить файлы',\n    bulkUpload: 'Массовая загрузка',\n    crop: 'Обрезать',\n    cropToolDescription:\n      'Перетащите углы выбранной области, нарисуйте новую область или отрегулируйте значения ниже.',\n    download: 'Скачать',\n    dragAndDrop: 'Перетащите файл',\n    dragAndDropHere: 'или перетащите файл сюда',\n    editImage: 'Редактировать изображение',\n    fileName: 'Имя файла',\n    fileSize: 'Размер файла',\n    filesToUpload: 'Файлы для загрузки',\n    fileToUpload: 'Файл для загрузки',\n    focalPoint: 'Центральная точка',\n    focalPointDescription:\n      'Перетащите фокусное расстояние прямо на предварительный просмотр или отрегулируйте значения ниже.',\n    height: 'Высота',\n    lessInfo: 'Меньше информации',\n    moreInfo: 'Больше информации',\n    noFile: 'Нет файла',\n    pasteURL: 'Вставить URL',\n    previewSizes: 'Предварительный просмотр размеров',\n    selectCollectionToBrowse: 'Выберите Коллекцию для просмотра',\n    selectFile: 'Выберите файл',\n    setCropArea: 'Установите область обрезки',\n    setFocalPoint: 'Установить фокусное расстояние',\n    sizes: 'Размеры',\n    sizesFor: 'Размеры для {{label}}',\n    width: 'Ширина',\n  },\n  validation: {\n    emailAddress: 'Пожалуйста, введите корректный адрес email.',\n    enterNumber: 'Пожалуйста, введите корректный номер.',\n    fieldHasNo: 'У этого поля нет {{label}}',\n    greaterThanMax: '{{value}} больше максимально допустимого значения {{label}} {{max}}.',\n    invalidInput: 'Это поле имеет недопустимое значение.',\n    invalidSelection: 'В этом поле выбран недопустимый вариант.',\n    invalidSelections: \"'Это поле содержит следующие неправильные варианты:'\",\n    lessThanMin: '{{value}} меньше минимально допустимого значения {{label}} {{min}}.',\n    limitReached: 'Достигнут лимит, можно добавить только {{max}} элементов.',\n    longerThanMin: 'Это значение должно быть больше минимальной длины символов: {{minLength}}.',\n    notValidDate: '\"{{value}}\" это не действительная дата.',\n    required: 'Это обязательное поле.',\n    requiresAtLeast: 'Это поле требует не менее {{count}} {{label}}',\n    requiresNoMoreThan: 'Это поле требует не более {{count}} {{label}}',\n    requiresTwoNumbers: 'В этом поле требуется два числа.',\n    shorterThanMax: 'Это значение должно быть короче максимальной длины символов {{maxLength}}.',\n    timezoneRequired: 'Требуется указать часовой пояс.',\n    trueOrFalse: 'Это поле может быть равно только true или false.',\n    username:\n      'Пожалуйста, введите действительное имя пользователя. Может содержать буквы, цифры, дефисы, точки и подчёркивания.',\n    validUploadID: \"'Это поле не является действительным ID загрузки.'\",\n  },\n  version: {\n    type: 'Тип',\n    aboutToPublishSelection: 'Вы собираетесь опубликовать все {{label}} в выборе. Вы уверены?',\n    aboutToRestore:\n      'Вы собираетесь восстановить этот документ {{label}} в состояние, в котором он находился {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Вы собираетесь восстановить глобальную запись {{label}} в состояние, в котором она находилась {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Вы собираетесь вернуть изменения этого документа к его опубликованному состоянию. Вы уверены?',\n    aboutToUnpublish: 'Вы собираетесь отменить публикацию этого документа. Вы уверены?',\n    aboutToUnpublishSelection:\n      'Вы собираетесь отменить публикацию всех выбранных {{label}}. Вы уверены?',\n    autosave: 'Автосохранение',\n    autosavedSuccessfully: 'Автосохранение успешно.',\n    autosavedVersion: 'Автоматически сохраненная версия',\n    changed: 'Изменено',\n    changedFieldsCount_one: '{{count}} изменил поле',\n    changedFieldsCount_other: '{{count}} измененных полей',\n    compareVersion: 'Сравнить версию с:',\n    compareVersions: 'Сравнить версии',\n    comparingAgainst: 'Сравнивая с',\n    confirmPublish: 'Подтвердить публикацию',\n    confirmRevertToSaved: 'Подтвердить возврат к сохраненному',\n    confirmUnpublish: 'Подтвердить отмену публикации',\n    confirmVersionRestoration: 'Подтвердить восстановление версии',\n    currentDocumentStatus: 'Текущий статус {{docStatus}} документа',\n    currentDraft: 'Текущий проект',\n    currentlyPublished: 'В настоящее время опубликовано',\n    currentlyViewing: 'В настоящее время просматривается',\n    currentPublishedVersion: 'Текущая опубликованная версия',\n    draft: 'Черновик',\n    draftSavedSuccessfully: 'Черновик успешно сохранен.',\n    lastSavedAgo: 'Последний раз сохранено {{distance}} назад',\n    modifiedOnly: 'Модифицирован только',\n    moreVersions: 'Больше версий...',\n    noFurtherVersionsFound: 'Другие версии не найдены',\n    noRowsFound: 'Не найдено {{label}}',\n    noRowsSelected: 'Не выбран {{label}}',\n    preview: 'Предпросмотр',\n    previouslyDraft: 'Ранее был черновик',\n    previouslyPublished: 'Ранее опубликовано',\n    previousVersion: 'Предыдущая версия',\n    problemRestoringVersion: 'Возникла проблема с восстановлением этой версии',\n    publish: 'Публиковать',\n    publishAllLocales: 'Опубликовать все локали',\n    publishChanges: 'Опубликовать изменения',\n    published: 'Опубликовано',\n    publishIn: 'Опубликовать на {{locale}}',\n    publishing: 'Публикация',\n    restoreAsDraft: 'Восстановить как черновик',\n    restoredSuccessfully: 'Восстановлен успешно.',\n    restoreThisVersion: 'Восстановить эту версию',\n    restoring: 'Восстановление...',\n    reverting: 'Возврат...',\n    revertToPublished: 'Вернуться к опубликованному',\n    saveDraft: 'Сохранить черновик',\n    scheduledSuccessfully: 'Успешно запланировано.',\n    schedulePublish: 'Планирование публикации',\n    selectLocales: 'Выберите локали для отображения',\n    selectVersionToCompare: 'Выбрать версию для сравнения',\n    showingVersionsFor: 'Показаны версии для:',\n    showLocales: 'Показать локали:',\n    specificVersion: 'Конкретная версия',\n    status: 'Статус',\n    unpublish: 'Отменить публикацию',\n    unpublishing: 'Отмена публикации...',\n    version: 'Версия',\n    versionAgo: '{{distance}} назад',\n    versionCount_many: '{{count}} версий найдено',\n    versionCount_none: 'Версий не найдено',\n    versionCount_one: '{{count}} версия найдена',\n    versionCount_other: 'Найдено {{count}} версий',\n    versionCreatedOn: '{{version}} создана:',\n    versionID: 'ID версии',\n    versions: 'Версии',\n    viewingVersion: 'Просмотр версии для {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: '`Просмотр версии для глобальной Коллекции {{entityLabel}}',\n    viewingVersions: 'Просмотр версий для {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: '`Просмотр версии для глобальной Коллекции {{entityLabel}}',\n  },\n}\n\nexport const ru: Language = {\n  dateFNSKey: 'ru',\n  translations: ruTranslations,\n}\n"], "names": ["ruTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "ru", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YACE;QACFC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBACE;QACFC,yBACE;QACFC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}