{"version": 3, "sources": ["../../src/languages/az.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const azTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Hesa<PERSON>',\n    accountOfCurrentUser: '<PERSON>i istifadəçinin hesabı',\n    accountVerified: '<PERSON><PERSON><PERSON> uğurla doğrulandı.',\n    alreadyActivated: 'Artıq Aktivləşdirilib',\n    alreadyLoggedIn: 'Artıq daxil olunub',\n    apiKey: 'API Açarı',\n    authenticated: 'Doğrulandı',\n    backToLogin: 'Giriş<PERSON> qayıt',\n    beginCreateFirstUser: 'Başlamaq üçün ilk istifadəçinizi yaradın.',\n    changePassword: '<PERSON><PERSON><PERSON> dəyi<PERSON>dir',\n    checkYourEmailForPasswordReset:\n      'Əgər e-poçt ünvanı bir hesabla əlaqəli olsa, tezliklə şifrənizi yenidən qurmaq üçün təlimatlari alacaqsınız. E-poçtu giriş qutunuzda görmürsəniz, zəhmət olmasa spam və ya zibil poçt qovluğunu yoxlayın.',\n    confirmGeneration: 'Generasiyani təsdiqlə',\n    confirmPassword: 'Şifrəni təsdiq et',\n    createFirstUser: 'İlk istifadəçini yaradın',\n    emailNotValid: 'Təqdim olunan e-poçt etibarlı deyil',\n    emailOrUsername: 'E-poçt və ya İstifadəçi adı',\n    emailSent: 'E-poçt göndərildi',\n    emailVerified: 'Email uğurla təsdiqləndi.',\n    enableAPIKey: 'API açarını aktivləşdir',\n    failedToUnlock: 'Kilidi açmaq alınmadı',\n    forceUnlock: 'Kilidi zorla aç',\n    forgotPassword: 'Şifrəni unutmusan',\n    forgotPasswordEmailInstructions:\n      'Zəhmət olmasa, e-poçt ünvanınızı aşağıda daxil edin. Siz parolunuzu necə sıfırlamaq barədə təlimatları olan e-poçt mesajı alacaqsınız.',\n    forgotPasswordQuestion: 'Şifrəni unutmusan?',\n    forgotPasswordUsernameInstructions:\n      'Zəhmət olmasa, aşağıda istifadəçi adınızı daxil edin. İstifadəçi adınıza uyğun e-poçt ünvanınıza əlavə proqramın sıfırlanması ilə əlaqəli təlimatlar göndəriləcək.',\n    generate: 'Yarad',\n    generateNewAPIKey: 'Yeni API açarı yarad',\n    generatingNewAPIKeyWillInvalidate:\n      'Yeni API açarının yaradılması əvvəlki açarı etibarsız edəcək. Davam etmək istədiyinizə əminsiniz?',\n    lockUntil: 'Klidklə',\n    logBackIn: 'Yenidən daxil ol',\n    loggedIn: 'Başqa istifadəçi ilə daxil olmaq üçün əvvəlcə çıxış etməlisiniz.',\n    loggedInChangePassword:\n      'Parolu dəyişdirmək üçün hesabınıza get və orada şifrənizi redaktə edin.',\n    loggedOutInactivity: 'Hərəkətsizlik səbəbindən sistemdən çıxmısınız.',\n    loggedOutSuccessfully: 'Siz uğurla çıxış etdiniz.',\n    loggingOut: 'Çıxış edilir...',\n    login: 'Giriş',\n    loginAttempts: 'Giriş cəhdləri',\n    loginUser: 'Giriş istifadəçisi',\n    loginWithAnotherUser: 'Başqa istifadəçi ilə daxil olmaq üçün əvvəlcə çıxış etməlisiniz.',\n    logOut: 'Çıxış et',\n    logout: 'Çıxış',\n    logoutSuccessful: 'Uğurlu çıxış.',\n    logoutUser: 'Sistemdən çıxış',\n    newAccountCreated:\n      'Sizin üçün yeni hesab yaradıldı. Zəhmət olmasa, e-poçtunuzu doğrulamaq üçün aşağıdakı linke klikləyin: <a href=\"{{verificationURL}}\">{{verificationURL}}</a>. E-poçtunuzu doğruladıqdan sonra uğurla daxil ola bilərsiniz.',\n    newAPIKeyGenerated: 'Yeni API Açarı Yaradıldı.',\n    newPassword: 'Yeni şifrə',\n    passed: 'Doğrulama Keçildi',\n    passwordResetSuccessfully: 'Parol uğurla yenidən quruldu.',\n    resetPassword: 'Şifrəni sıfırla',\n    resetPasswordExpiration: 'Şifrənin müddətini sıfırla',\n    resetPasswordToken: 'Şifrə Tokenini Sıfırla',\n    resetYourPassword: 'Şifrənizi sıfırlayın',\n    stayLoggedIn: 'Sistemdə qal',\n    successfullyRegisteredFirstUser: 'İlk istifadəçiyi uğurla qeyd etdik.',\n    successfullyUnlocked: 'Uğurla kilidi açıldı',\n    tokenRefreshSuccessful: 'Tokenin yenilənməsi uğurlu oldu.',\n    unableToVerify: 'Doğrulamaq mümkün deyil',\n    username: 'İstifadəçi adı',\n    usernameNotValid: 'Təqdim edilən istifadəçi adı düzgün deyil',\n    verified: 'Doğrulanmış',\n    verifiedSuccessfully: 'Uğurla doğrulandı',\n    verify: 'Doğrula',\n    verifyUser: 'İstifadəçini doğrula',\n    verifyYourEmail: 'E-poçtunuzu doğrulayın',\n    youAreInactive:\n      'Siz bir müddətdir aktiv deyilsiniz və tezliklə öz təhlükəsizliyiniz üçün avtomatik olaraq sistemdən çıxacaqsınız. Daxil olmaq istərdinizmi?',\n    youAreReceivingResetPassword:\n      'Siz (və ya başqası) hesabınız üçün parolun sıfırlanmasını tələb etdiyiniz üçün bunu alırsınız. Prosesi tamamlamaq üçün zəhmət olmasa aşağıdakı linkə klikləyin:',\n    youDidNotRequestPassword:\n      'Əgər siz bunu tələb etməmisinizsə, lütfən, bu e-poçtu nəzərə almayın və şifrəniz dəyişilməz qalacaq.',\n  },\n  error: {\n    accountAlreadyActivated: 'Bu hesab artıq aktivləşdirilib.',\n    autosaving: 'Bu sənədin avto yadda saxlanılması zamanı problem yarandı.',\n    correctInvalidFields: 'Zəhmət olmasa, yanlış sahələri düzəlt.',\n    deletingFile: 'Faylın silinməsində xəta baş verdi.',\n    deletingTitle:\n      '{{title}} silinərkən xəta baş verdi. Zəhmət olmasa, bağlantınızı yoxlayın və yenidən cəhd edin.',\n    documentNotFound:\n      '{{id}} ID-li sənəd tapılmadı. Bu, onun silinmiş və ya heç vaxt mövcud olmamış ola bilər və ya sizin ona giriş hüququnuz olmayabilir.',\n    emailOrPasswordIncorrect: 'Təqdim olunan e-poçt və ya şifrə yanlışdır.',\n    followingFieldsInvalid_one: 'Aşağıdakı sahə yanlışdır:',\n    followingFieldsInvalid_other: 'Aşağıdaki sahələr yanlışdır:',\n    incorrectCollection: 'Yanlış Kolleksiya',\n    insufficientClipboardPermissions:\n      'Mübadilə buferinə giriş rədd edildi. Zəhmət olmasa, icazələri yoxlayın.',\n    invalidClipboardData: 'Yanlış mübadilə buferi məlumatı.',\n    invalidFileType: 'Yanlış fayl növü',\n    invalidFileTypeValue: 'Yanlış fayl növü: {{value}}',\n    invalidRequestArgs: 'Sorguda etibarsız arqumentlər təqdim edildi: {{args}}',\n    loadingDocument: '{{id}} ID-li sənədin yüklənməsində problem baş verdi.',\n    localesNotSaved_one: 'Aşağıdakı yerləşdirmə saxlanıla bilmədi:',\n    localesNotSaved_other: 'Aşağıdakı yerləşdirmələr saxlanıla bilmədi:',\n    logoutFailed: 'Çıxış uğursuz oldu.',\n    missingEmail: 'E-poçt adresi çatışmır.',\n    missingIDOfDocument: 'Yeniləmək üçün sənədin ID-si çatışmır.',\n    missingIDOfVersion: 'Versiyanın ID-si çatışmır.',\n    missingRequiredData: 'Tələb olunan məlumat çatışmır.',\n    noFilesUploaded: 'Heç bir fayl yüklənilməyib.',\n    noMatchedField: '\"{{label}}\" üçün uyğun sahə tapılmadı',\n    notAllowedToAccessPage: 'Bu səhifəyə girməyə icazəniz yoxdur.',\n    notAllowedToPerformAction: 'Bu əməliyyatı icra etməyə icazəniz yoxdur.',\n    notFound: 'Tələb olunan resurs tapılmadı.',\n    noUser: 'İstifadəçi Yoxdur',\n    previewing: 'Bu sənədin ön baxışı zamanı problem yarandı.',\n    problemUploadingFile: 'Faylın yüklənməsi zamanı problem yarandı.',\n    restoringTitle:\n      '{{title}} bərpa olunarkən xəta baş verdi. Zəhmət olmasa, internet bağlantınızı yoxlayın və yenidən cəhd edin.',\n    tokenInvalidOrExpired: 'Token ya yanlışdır və ya müddəti bitib.',\n    tokenNotProvided: 'Token təqdim edilməyib.',\n    unableToCopy: 'Kopyalama mümkün deyil.',\n    unableToDeleteCount: '{{count}} dən {{total}} {{label}} silinə bilmir.',\n    unableToReindexCollection:\n      '{{collection}} kolleksiyasının yenidən indekslənməsi zamanı səhv baş verdi. Əməliyyat dayandırıldı.',\n    unableToUpdateCount: '{{count}} dən {{total}} {{label}} yenilənə bilmir.',\n    unauthorized: 'İcazəniz yoxdur, bu tələbi yerinə yetirmək üçün daxil olmalısınız.',\n    unauthorizedAdmin: 'Bu əməliyyatı yerinə yetirmək üçün admin olmalısınız.',\n    unknown: 'Naməlum bir xəta baş verdi.',\n    unPublishingDocument: 'Bu sənədin nəşrini ləğv etmək zamanı problem baş verdi.',\n    unspecific: 'Xəta baş verdi.',\n    unverifiedEmail: 'Zəhmət olmasa, daxil olmadan əvvəl e-poçtunuzu təsdiqləyin.',\n    userEmailAlreadyRegistered: 'Verilən e-poçt ünvanı ilə artıq istifadəçi qeydiyyatdan keçib.',\n    userLocked: 'Bu istifadəçi çoxsaylı uğursuz giriş cəhdləri səbəbindən kilidlənib.',\n    usernameAlreadyRegistered: 'Verilən istifadəçi adı ilə artıq qeydiyyatdan keçmişdir.',\n    usernameOrPasswordIncorrect: 'Təqdim edilən istifadəçi adı və ya şifrə yanlışdır.',\n    valueMustBeUnique: 'Dəyər təkrar olmamalıdır',\n    verificationTokenInvalid: 'Doğrulama tokenı yanlışdır.',\n  },\n  fields: {\n    addLabel: '{{label}} əlavə et',\n    addLink: 'Keçid əlavə et',\n    addNew: 'Yenisini əlavə et',\n    addNewLabel: 'Yeni {{label}} əlavə et',\n    addRelationship: 'Relationship əlavə et',\n    addUpload: 'Yükləmə əlavə et',\n    block: 'blok',\n    blocks: 'bloklar',\n    blockType: 'Blok Növü',\n    chooseBetweenCustomTextOrDocument:\n      \"Xüsusi mətn URL'si daxil etmək və ya başqa bir sənədə keçid yaratmaq arasında seçim edin.\",\n    chooseDocumentToLink: 'Keçid yaratmaq üçün sənəd seçin',\n    chooseFromExisting: 'Mövcuddan seçin',\n    chooseLabel: '{{label}} seçin',\n    collapseAll: 'Hamısını Bağla',\n    customURL: 'Xüsusi URL',\n    editLabelData: '{{label}} məlumatını redaktə et',\n    editLink: 'Keçidi redaktə et',\n    editRelationship: 'Relationship redaktə et',\n    enterURL: 'URL daxil edin',\n    internalLink: 'Daxili Keçid',\n    itemsAndMore: '{{items}} və daha {{count}} nəfər',\n    labelRelationship: '{{label}} Relationship',\n    latitude: 'Enlik',\n    linkedTo: '<0>{{label}}</0> ilə əlaqəli',\n    linkType: 'Keçid Növü',\n    longitude: 'Uzunluq',\n    newLabel: 'Yeni {{label}}',\n    openInNewTab: 'Yeni sekmede aç',\n    passwordsDoNotMatch: 'Şifrələr uyğun gəlmir.',\n    relatedDocument: 'Əlaqəli Sənəd',\n    relationTo: 'Relationship',\n    removeRelationship: 'Relationship sil',\n    removeUpload: 'Yükləməni sil',\n    saveChanges: 'Dəyişiklikləri saxla',\n    searchForBlock: 'Blok üçün axtarış',\n    selectExistingLabel: 'Mövcud {{label}} seçin',\n    selectFieldsToEdit: 'Redaktə ediləcək sahələri seçin',\n    showAll: 'Hamısını Göstər',\n    swapRelationship: 'Relationship dəyiş',\n    swapUpload: 'Yükləməni dəyiş',\n    textToDisplay: 'Göstəriləcək mətn',\n    toggleBlock: 'Bloku keç',\n    uploadNewLabel: 'Yeni {{label}} yüklə',\n  },\n  folder: {\n    browseByFolder: 'Qovluqlara görə gözdən keçirin',\n    byFolder: 'Qovluğa görə',\n    deleteFolder: 'Qovluğu Sil',\n    folderName: 'Qovluq Adı',\n    folders: 'Qovluqlar',\n    folderTypeDescription: 'Bu qovluqda hangi tip kolleksiya sənədlərinə icazə verilməlidir seçin.',\n    itemHasBeenMoved: '{{title}} {{folderName}} qovluğuna köçürüldü.',\n    itemHasBeenMovedToRoot: '{{title}} kök qovluğa köçürüldü.',\n    itemsMovedToFolder: '{{title}} {{folderName}} qovluğuna köçürüldü',\n    itemsMovedToRoot: '{{title}} kök qovluğa köçürdü',\n    moveFolder: 'Qovluğu Köçür',\n    moveItemsToFolderConfirmation:\n      'Siz <1>{{count}} {{label}}</1> -i <2>{{toFolder}}</2> -ə köçürmək üzərəsiniz. Eminsiniz?',\n    moveItemsToRootConfirmation:\n      'Siz <1>{{count}} {{label}}</1> əsas qovluğa köçürmək üzərəsiniz. Eminsiniz?',\n    moveItemToFolderConfirmation:\n      'Siz <1>{{title}}</1>-i <2>{{toFolder}}</2>ə köçürmək barədəsiniz. Eminsinizmi?',\n    moveItemToRootConfirmation:\n      \"Siz <1>{{title}}</1>'i kök qovluğa köçürmək barədəsindəsiniz. Əminsiniz?\",\n    movingFromFolder: '{{title}}-i {{fromFolder}}-dən köçürmək',\n    newFolder: 'Yeni Qovluq',\n    noFolder: 'Qovluq Yoxdur',\n    renameFolder: 'Qovluğun adını dəyişdirin',\n    searchByNameInFolder: '{{folderName}} qovluğunda adla axtarış',\n    selectFolderForItem: '{{title}} üçün qovluğu seçin',\n  },\n  general: {\n    name: 'Ad',\n    aboutToDelete: 'Siz {{label}} <1>{{title}}</1> silməyə hazırsınız. Eminsiniz?',\n    aboutToDeleteCount_many: 'Siz {{count}} {{label}} silməyə hazırsınız.',\n    aboutToDeleteCount_one: 'Siz {{count}} {{label}} silməyə hazırsınız.',\n    aboutToDeleteCount_other: 'Siz {{count}} {{label}} silməyə hazırsınız.',\n    aboutToPermanentlyDelete:\n      'Siz əbədi olaraq {{label}} <1>{{title}}</1> silmək üzrəsiniz. Eminsiniz?',\n    aboutToPermanentlyDeleteTrash:\n      'Siz müllifdən daimi olaraq <0>{{count}}</0> <1>{{label}}</1> silinəcəkdir. Eminsiniz?',\n    aboutToRestore: '{{label}} <1>{{title}}</1> bərpa edilmək üzrədir. Eminsiniz?',\n    aboutToRestoreAsDraft:\n      'Siz {{label}} <1>{{title}}</1> draft kimi bərpa etmək əzəldəsiniz. Eminsinizmi?',\n    aboutToRestoreAsDraftCount:\n      'Siz {{count}} {{label}}-ni qaralamak üçün bərpa etməyə hazırlaşırsınız',\n    aboutToRestoreCount: 'Siz {{count}} {{label}} bərpa etməyə hazırlaşırsınız.',\n    aboutToTrash:\n      'Siz {{label}} <1>{{title}}</1> elementini zibilliyə köçürmək barədəsiniz. Eminsiniz?',\n    aboutToTrashCount: 'Siz {{count}} {{label}}-i zibilə köçürmək barədəsiz.',\n    addBelow: 'Aşağıya əlavə et',\n    addFilter: 'Filter əlavə et',\n    adminTheme: 'Admin Mövzusu',\n    all: 'Hamısı',\n    allCollections: 'Bütün kolleksiyalar',\n    allLocales: 'Bütün lokal məkanlar',\n    and: 'Və',\n    anotherUser: 'Başqa bir istifadəçi',\n    anotherUserTakenOver: 'Başqa bir istifadəçi bu sənədin redaktəsini ələ keçirdi.',\n    applyChanges: 'Dəyişiklikləri Tətbiq Edin',\n    ascending: 'Artan',\n    automatic: 'Avtomatik',\n    backToDashboard: 'Panelə qayıdın',\n    cancel: 'Ləğv et',\n    changesNotSaved:\n      'Dəyişiklikləriniz saxlanılmayıb. İndi çıxsanız, dəyişikliklərinizi itirəcəksiniz.',\n    clear:\n      'Payload kontekstində orijinal mətnin mənasını qoruya. İşte Payload terminləri siyahısıdır ki, onlar üzərində çox xüsusi mənalar gəlir:\\n    - Kolleksiya: Kolleksiya sənədlərin hamıya ortaq struktur və məqsəd sərbəst olan bir qrupdur. Kolleksiyalar Payload-da məzmunu təşkil etmək və idarə etmək üçün istifadə edilir.\\n    - Sahə: Sahə',\n    clearAll: 'Hamısını təmizlə',\n    close: 'Bağla',\n    collapse: 'Bağla',\n    collections: 'Kolleksiyalar',\n    columns: 'Sütunlar',\n    columnToSort: 'Sıralamağa sütun',\n    confirm: 'Təsdiqlə',\n    confirmCopy: 'Kopyanı təsdiqləyin',\n    confirmDeletion: 'Silməni təsdiqlə',\n    confirmDuplication: 'Dublikasiyanı təsdiqlə',\n    confirmMove: 'Hərəkəti təsdiqləyin',\n    confirmReindex: 'Bütün {{collections}} yenidən indekslənsin?',\n    confirmReindexAll: 'Bütün kolleksiyalar yenidən indekslənsin?',\n    confirmReindexDescription:\n      'Bu, mövcud indeksləri siləcək və {{collections}} kolleksiyalarında sənədləri yenidən indeksləyəcək.',\n    confirmReindexDescriptionAll:\n      'Bu, mövcud indeksləri siləcək və bütün kolleksiyalardakı sənədləri yenidən indeksləyəcək.',\n    confirmRestoration: 'Bərpa etməni təsdiqləyin',\n    copied: 'Kopyalandı',\n    copy: 'Kopyala',\n    copyField: 'Sahəni kopyala',\n    copying: 'Kopyalama',\n    copyRow: 'Sətiri kopyala',\n    copyWarning:\n      'Siz {{label}} {{title}} üçün {{from}} ilə {{to}} -nu üzərindən yazmaq ətrafındasınız. Eminsiniz?',\n    create: 'Yarat',\n    created: 'Yaradıldı',\n    createdAt: 'Yaradıldığı tarix',\n    createNew: 'Yeni yarat',\n    createNewLabel: 'Yeni {{label}} yarat',\n    creating: 'Yaradılır',\n    creatingNewLabel: 'Yeni {{label}} yaradılır',\n    currentlyEditing:\n      'hazırda bu sənədi redaktə edir. Siz öhdəliyi götürsəniz, redaktəni davam etdirməkdən bloklanacaqlar və qeydə alınmamış dəyişiklikləri itirə bilərlər.',\n    custom: 'Xüsusi',\n    dark: 'Tünd',\n    dashboard: 'Panel',\n    delete: 'Sil',\n    deleted: 'Silinmiş',\n    deletedAt: 'Silinib Tarixi',\n    deletedCountSuccessfully: '{{count}} {{label}} uğurla silindi.',\n    deletedSuccessfully: 'Uğurla silindi.',\n    deletePermanently: 'Çöplüyü atlayın və daimi olaraq silin',\n    deleting: 'Silinir...',\n    depth: 'Dərinlik',\n    descending: 'Azalan',\n    deselectAllRows: 'Bütün sıraları seçimi ləğv edin',\n    document: 'Sənəd',\n    documentIsTrashed: 'Bu {{label}} zibil qutusuna atılıb və yalnız oxuna bilər.',\n    documentLocked: 'Sənəd kilidləndi',\n    documents: 'Sənədlər',\n    duplicate: 'Dublikat',\n    duplicateWithoutSaving: 'Dəyişiklikləri saxlamadan dublikatla',\n    edit: 'Redaktə et',\n    editAll: 'Hamısını redaktə et',\n    editedSince: 'Redaktə edilib',\n    editing: 'Redaktə olunur',\n    editingLabel_many: '{{count}} {{label}} redaktə olunur',\n    editingLabel_one: '{{count}} {{label}} redaktə olunur',\n    editingLabel_other: '{{count}} {{label}} redaktə olunur',\n    editingTakenOver: 'Redaktə ələ keçirildi',\n    editLabel: '{{label}} redaktə et',\n    email: 'Elektron poçt',\n    emailAddress: 'Elektron poçt ünvanı',\n    emptyTrash: 'Zibil qutusunu boşaltın',\n    emptyTrashLabel: '{{label}} zibilini boşaltın',\n    enterAValue: 'Bir dəyər daxil edin',\n    error: 'Xəta',\n    errors: 'Xətalar',\n    exitLivePreview: 'Canlı Önizləmədən Çıxın',\n    export: 'İxrac',\n    fallbackToDefaultLocale: 'Standart lokalə keçid',\n    false: 'Yalan',\n    filter: 'Filter',\n    filters: 'Filtərlər',\n    filterWhere: '{{label}} filtrlə',\n    globals: 'Qloballar',\n    goBack: 'Geri qayıt',\n    groupByLabel: '{{label}} ilə qruplaşdırın',\n    import: 'İdxal',\n    isEditing: 'redaktə edir',\n    item: 'əşya',\n    items: 'maddələr',\n    language: 'Dil',\n    lastModified: 'Son dəyişdirildi',\n    leaveAnyway: 'Heç olmasa çıx',\n    leaveWithoutSaving: 'Saxlamadan çıx',\n    light: 'Açıq',\n    livePreview: 'Öncədən baxış',\n    loading: 'Yüklənir',\n    locale: 'Lokal',\n    locales: 'Dillər',\n    menu: 'Menyu',\n    moreOptions: 'Daha çox seçimlər',\n    move: 'Hərəkət et',\n    moveConfirm:\n      'Siz <1>{{destination}}</1> -ə {{count}} {{label}} köçürmək ətrafında. Eminsinizmi?',\n    moveCount: '{{count}} {{label}} hərəkət etdirin',\n    moveDown: 'Aşağı hərəkət et',\n    moveUp: 'Yuxarı hərəkət et',\n    moving: 'Hərəkət edir',\n    movingCount: '{{count}} {{label}} köçürülür',\n    newPassword: 'Yeni şifrə',\n    next: 'Növbəti',\n    no: 'Xeyr',\n    noDateSelected: 'Heç bir tarix seçilməyib',\n    noFiltersSet: 'Filter təyin edilməyib',\n    noLabel: '<Heç bir {{label}}>',\n    none: 'Heç bir',\n    noOptions: 'Heç bir seçim yoxdur',\n    noResults:\n      'Heç bir {{label}} tapılmadı. Ya hələ {{label}} yoxdur, ya da yuxarıda göstərdiyiniz filtrlərə uyğun gəlmir.',\n    notFound: 'Tapılmadı',\n    nothingFound: 'Heç nə tapılmadı',\n    noTrashResults: 'Çöplükdə heç bir {{label}} yoxdur.',\n    noUpcomingEventsScheduled: 'Heç bir gələcək tədbir cədvələ alınmayıb.',\n    noValue: 'Dəyər yoxdur',\n    of: 'dən',\n    only: 'Yalnız',\n    open: 'Aç',\n    or: 'Və ya',\n    order: 'Sıra',\n    overwriteExistingData: 'Mövcud sahə məlumatlarını yenidən yazın',\n    pageNotFound: 'Səhifə tapılmadı',\n    password: 'Şifrə',\n    pasteField: 'Sahəni yapışdır',\n    pasteRow: 'Sətiri yapışdır',\n    payloadSettings: 'Payload Parametrləri',\n    permanentlyDelete: 'Daimi Olaraq Sil',\n    permanentlyDeletedCountSuccessfully: '{{count}} {{label}} uğurla daimi olaraq silindi.',\n    perPage: 'Hər səhifədə: {{limit}}',\n    previous: 'Əvvəlki',\n    reindex: 'Yenidən indekslə',\n    reindexingAll: 'Bütün {{collections}} yenidən indekslənir.',\n    remove: 'Sil',\n    rename: 'Yenidən adlandırın',\n    reset: 'Yenidən başlat',\n    resetPreferences: 'Təhlükəsizlik parametrlərini sıfırlamaq',\n    resetPreferencesDescription: 'Bu, bütün parametrlərinizi standart vəziyyətlərinə sıfırlayacaq.',\n    resettingPreferences: 'Təhlükəsizlik parametrləri sıfırlanır.',\n    restore: 'Bərpa et',\n    restoreAsPublished: 'Nəşr edilmiş versiya kimi bərpa et',\n    restoredCountSuccessfully: '{{count}} {{label}} uğurla bərpa edildi.',\n    restoring:\n      'Orijinal mətnin mənasını Payload kontekstində qoruyun. Ən əhəmiyyətli Payload ifadələrinin siyahısı aşağıdakı kimi dir:\\n\\n    - Collection: \"Collection\" bir sıra sənədlərin əməkdaş olduğu, ortaq struktur və məqsədi olan bir qrupdur. \"Collections\", Payload-də məzmunu təşkil etmək və idarə etmək üçün istifadə edilir.\\n    - Field: \"Field\", kolle',\n    row: 'Sətir',\n    rows: 'Sətirlər',\n    save: 'Saxla',\n    saving: 'Saxlanılır...',\n    schedulePublishFor: '{{title}} üçün nəşr cədvəlini təyin edin',\n    searchBy: '{{label}} ilə axtar',\n    select: 'Seçin',\n    selectAll: 'Bütün {{count}} {{label}} seç',\n    selectAllRows: 'Bütün sıraları seçin',\n    selectedCount: '{{count}} {{label}} seçildi',\n    selectLabel: '{{label}} seçin',\n    selectValue: 'Dəyər seçin',\n    showAllLabel: 'Bütün {{label}}-ı göstər',\n    sorryNotFound: 'Üzr istəyirik - sizin tələbinizə uyğun heç nə yoxdur.',\n    sort: 'Sırala',\n    sortByLabelDirection: '{{label}} {{direction}} ilə sırala',\n    stayOnThisPage: 'Bu səhifədə qal',\n    submissionSuccessful: 'Təqdimat uğurlu oldu.',\n    submit: 'Təqdim et',\n    submitting: 'Təqdim olunur...',\n    success: 'Uğur',\n    successfullyCreated: '{{label}} uğurla yaradıldı.',\n    successfullyDuplicated: '{{label}} uğurla dublikatlandı.',\n    successfullyReindexed:\n      '{{collections}} kolleksiyalarından {{total}} sənəddən {{count}} sənəd uğurla yenidən indeksləndi.',\n    takeOver: 'Əvvəl',\n    thisLanguage: 'Azərbaycan dili',\n    time: 'Vaxt',\n    timezone: 'Saat qurşağı',\n    titleDeleted: '{{label}} \"{{title}}\" uğurla silindi.',\n    titleRestored: '\"{{title}}\" \"{{label}}\" uğurla bərpa edildi.',\n    titleTrashed: '{{label}} \"{{title}}\" zibilə köçürüldü.',\n    trash: 'Zibil',\n    trashedCountSuccessfully: '{{count}} {{label}} zibilə köçürüldü.',\n    true: 'Doğru',\n    unauthorized: 'İcazəsiz',\n    unsavedChanges:\n      'Sizin saxlanılmamış dəyişiklikləriniz var. Davam etmədən əvvəl saxlayın və ya atın.',\n    unsavedChangesDuplicate:\n      'Saxlanılmamış dəyişiklikləriniz var. Dublikatla davam etmək istəyirsiniz?',\n    untitled: 'Başlıqsız',\n    upcomingEvents: 'Gələcək Tədbirlər',\n    updatedAt: 'Yeniləndiyi tarix',\n    updatedCountSuccessfully: '{{count}} {{label}} uğurla yeniləndi.',\n    updatedLabelSuccessfully: '{{label}} uğurla yeniləndi.',\n    updatedSuccessfully: 'Uğurla yeniləndi.',\n    updateForEveryone: 'Hər kəs üçün yeniləmə',\n    updating: 'Yenilənir',\n    uploading: 'Yüklənir',\n    uploadingBulk: '{{total}}-dan {{current}}-un yüklənməsi',\n    user: 'İstifadəçi',\n    username: 'İstifadəçi adı',\n    users: 'İstifadəçilər',\n    value: 'Dəyər',\n    viewing: 'Baxış',\n    viewReadOnly: 'Yalnız oxu rejimində bax',\n    welcome: 'Xoş gəldiniz',\n    yes: 'Bəli',\n  },\n  localization: {\n    cannotCopySameLocale: 'Eyni dildə köçürmək mümkün deyil',\n    copyFrom: 'Kopyalayın',\n    copyFromTo: '{{from}}-dan {{to}}-ya kopyalama',\n    copyTo: 'Köçür',\n    copyToLocale: 'Yerliyə köçürün',\n    localeToPublish: 'Yayımlamaq üçün yerləşdirin',\n    selectLocaleToCopy: 'Köçürmək üçün yerli seçin',\n  },\n  operators: {\n    contains: 'daxilində',\n    equals: 'bərabərdir',\n    exists: 'mövcuddur',\n    intersects: 'kəsişir',\n    isGreaterThan: 'dən böyük',\n    isGreaterThanOrEqualTo: 'böyük və ya bərabər',\n    isIn: 'daxildir',\n    isLessThan: 'dən kiçik',\n    isLessThanOrEqualTo: 'kiçik və ya bərabər',\n    isLike: 'kimi',\n    isNotEqualTo: 'bərabər deyil',\n    isNotIn: 'daxil deyil',\n    isNotLike: 'deyil kimi',\n    near: 'yaxın',\n    within: 'daxilinde',\n  },\n  upload: {\n    addFile: 'Fayl əlavə et',\n    addFiles: 'Faylları Əlavə Edin',\n    bulkUpload: 'Kütləvi Yükləmə',\n    crop: 'Məhsul',\n    cropToolDescription:\n      'Seçilmiş sahənin köşələrini sürükləyin, yeni bir sahə çəkin və ya aşağıdakı dəyərləri düzəltin.',\n    download: 'Yükləyin',\n    dragAndDrop: 'Faylı buraya sürükləyin və buraxın',\n    dragAndDropHere: 'və ya faylı buraya sürükləyin və buraxın',\n    editImage: 'Şəkili Redaktə Et',\n    fileName: 'Faylın Adı',\n    fileSize: 'Faylım Ölçüsü',\n    filesToUpload: 'Yükləmək üçün fayllar',\n    fileToUpload: 'Yükləmək üçün Fayl',\n    focalPoint: 'Mərkəzi Nöqtə',\n    focalPointDescription:\n      'Fokus nöqtəsini birbaşa önizləməyə sürükləyin və ya aşağıdakı dəyərləri düzəltin.',\n    height: 'Hündürlük',\n    lessInfo: 'Daha az məlumat',\n    moreInfo: 'Daha çox məlumat',\n    noFile: 'Heç bir fayl',\n    pasteURL: 'URL yapışdır',\n    previewSizes: 'Öncədən baxış ölçüləri',\n    selectCollectionToBrowse: 'Gözdən keçirmək üçün bir Kolleksiya seçin',\n    selectFile: 'Fayl seçin',\n    setCropArea: 'Məhsul sahəsini təyin et',\n    setFocalPoint: 'Fokus nöqtəsi təyin et',\n    sizes: 'Ölçülər',\n    sizesFor: '{{label}} üçün ölçülər',\n    width: 'En',\n  },\n  validation: {\n    emailAddress: 'Xahiş edirik doğru elektron poçt ünvanını daxil edin.',\n    enterNumber: 'Xahiş edirik doğru nömrəni daxil edin.',\n    fieldHasNo: 'Bu sahədə heç bir {{label}} yoxdur',\n    greaterThanMax: '{{value}} icazə verilən maksimal {{label}} olan {{max}}-dən böyükdür.',\n    invalidInput: 'Bu sahə yanlış daxil edilmişdir.',\n    invalidSelection: 'Bu sahədə yanlış seçim edilmişdir.',\n    invalidSelections: 'Bu sahədə aşağıdakı yanlış seçimlər edilmişdir:',\n    lessThanMin: '{{value}} icazə verilən minimal {{label}} olan {{min}}-dən kiçikdir.',\n    limitReached: 'Limitə çatdınız, yalnız {{max}} element əlavə edilə bilər.',\n    longerThanMin: 'Bu dəyər {{minLength}} simvoldan uzun olmalıdır.',\n    notValidDate: '\"{{value}}\" doğru tarix deyil.',\n    required: 'Bu sahə mütləq doldurulmalıdır.',\n    requiresAtLeast: 'Bu sahə ən azı {{count}} {{label}} tələb edir.',\n    requiresNoMoreThan: 'Bu sahə {{count}} {{label}}-dan çox olmamalıdır.',\n    requiresTwoNumbers: 'Bu sahə iki nömrə tələb edir.',\n    shorterThanMax: 'Bu dəyər {{maxLength}} simvoldan qısa olmalıdır.',\n    timezoneRequired: 'Vaxt zonası tələb olunur.',\n    trueOrFalse: 'Bu sahə yalnız doğru və ya yanlış ola bilər.',\n    username:\n      'Zəhmət olmasa, etibarlı bir istifadəçi adı daxil edin. Hərflər, rəqəmlər, tire, nöqtə və alt xəttlər ola bilər.',\n    validUploadID: 'Bu sahə doğru yükləmə ID-si deyil.',\n  },\n  version: {\n    type: 'Növ',\n    aboutToPublishSelection: 'Seçimdə olan bütün {{label}}-i dərc etməyə hazırsınız. Əminsiniz?',\n    aboutToRestore:\n      'Bu {{label}} sənədini {{versionDate}} tarixindəki vəziyyətinə bərpa etmək üzrəsiniz.',\n    aboutToRestoreGlobal:\n      'Qlobal {{label}}-i {{versionDate}} tarixindəki vəziyyətinə bərpa etmək üzrəsiniz.',\n    aboutToRevertToPublished:\n      'Bu sənədin dəyişikliklərini dərc edilmiş vəziyyətinə qaytarmağa hazırsınız. Əminsiniz?',\n    aboutToUnpublish: 'Bu sənədi dərcdən çıxartmağa hazırsınız. Əminsiniz?',\n    aboutToUnpublishSelection:\n      'Seçimdə olan bütün {{label}}-i dərcdən çıxartmağa hazırsınız. Əminsiniz?',\n    autosave: 'Avtomatik yadda saxlama',\n    autosavedSuccessfully: 'Uğurla avtomatik olaraq yadda saxlandı.',\n    autosavedVersion: 'Avtomatik yadda saxlanmış versiya',\n    changed: 'Dəyişdirildi',\n    changedFieldsCount_one: '{{count}} sahə dəyişdi',\n    changedFieldsCount_other: '{{count}} dəyişdirilmiş sahələr',\n    compareVersion: 'Versiyanı müqayisə et:',\n    compareVersions: 'Versiyaları Müqayisə Edin',\n    comparingAgainst: 'Müqayisə etmək',\n    confirmPublish: 'Dərci təsdiq edin',\n    confirmRevertToSaved: 'Yadda saxlanana qayıtmağı təsdiq edin',\n    confirmUnpublish: 'Dərcdən çıxartmağı təsdiq edin',\n    confirmVersionRestoration: 'Versiyanın bərpasını təsdiq edin',\n    currentDocumentStatus: 'Cari {{docStatus}} sənədi',\n    currentDraft: 'Hazırki Layihə',\n    currentlyPublished: 'Hazırda Nəşr Olunmuş',\n    currentlyViewing: 'Hazırda baxılır',\n    currentPublishedVersion: 'Hazırki Nəşr Versiyası',\n    draft: 'Qaralama',\n    draftSavedSuccessfully: 'Qaralama uğurla yadda saxlandı.',\n    lastSavedAgo: '{{distance}} əvvəl son yadda saxlanıldı',\n    modifiedOnly: 'Yalnızca dəyişdirilmişdir',\n    moreVersions: 'Daha çox versiyalar...',\n    noFurtherVersionsFound: 'Başqa versiyalar tapılmadı',\n    noRowsFound: 'Heç bir {{label}} tapılmadı',\n    noRowsSelected: 'Heç bir {{label}} seçilməyib',\n    preview: 'Öncədən baxış',\n    previouslyDraft: 'Daha öncə bir Qaralama',\n    previouslyPublished: 'Daha əvvəl nəşr olunmuş',\n    previousVersion: 'Əvvəlki Versiya',\n    problemRestoringVersion: 'Bu versiyanın bərpasında problem yaşandı',\n    publish: 'Dərc et',\n    publishAllLocales: 'Bütün lokalizasiyaları dərc edin',\n    publishChanges: 'Dəyişiklikləri dərc et',\n    published: 'Dərc edilmiş',\n    publishIn: '{{locale}} dili ilə nəşr edin',\n    publishing: 'Nəşr',\n    restoreAsDraft: 'Qaralamalar kimi bərpa et',\n    restoredSuccessfully: 'Uğurla bərpa edildi.',\n    restoreThisVersion: 'Bu versiyanı bərpa et',\n    restoring: 'Bərpa olunur...',\n    reverting: 'Qayıdılır...',\n    revertToPublished: 'Dərc edilmişə qayıt',\n    saveDraft: 'Qaralamayı yadda saxla',\n    scheduledSuccessfully: 'Uğurla cədvələ qoyuldu.',\n    schedulePublish: 'Nəşr Cədvəli',\n    selectLocales: 'Göstərmək üçün lokalları seçin',\n    selectVersionToCompare: 'Müqayisə üçün bir versiya seçin',\n    showingVersionsFor: 'Göstərilən versiyalar üçün:',\n    showLocales: 'Lokalları göstər:',\n    specificVersion: 'Xüsusi Versiya',\n    status: 'Status',\n    unpublish: 'Dərcdən çıxart',\n    unpublishing: 'Dərcdən çıxarılır...',\n    version: 'Versiya',\n    versionAgo: '{{distance}} əvvəl',\n    versionCount_many: '{{count}} versiya tapıldı',\n    versionCount_none: 'Versiya tapılmadı',\n    versionCount_one: '{{count}} versiya tapıldı',\n    versionCount_other: '{{count}} versiya tapıldı',\n    versionCreatedOn: '{{version}} tarixində yaradıldı:',\n    versionID: 'Versiyanın ID-si',\n    versions: 'Versiyalar',\n    viewingVersion: '{{entityLabel}} {{documentTitle}} üçün versiyanı göstərir',\n    viewingVersionGlobal: 'Qlobal {{entityLabel}} üçün versiyanı göstərir',\n    viewingVersions: '{{entityLabel}} {{documentTitle}} üçün versiyaları göstərir',\n    viewingVersionsGlobal: 'Qlobal {{entityLabel}} üçün versiyaları göstərir',\n  },\n}\n\nexport const az: Language = {\n  dateFNSKey: 'az',\n  translations: azTranslations,\n}\n"], "names": ["azTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "az", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BACE;QACFC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OACE;QACFC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBACE;QACFC,yBACE;QACFC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}