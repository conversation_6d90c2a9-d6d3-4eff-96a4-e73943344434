{"version": 3, "sources": ["../../src/languages/tr.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const trTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Hesa<PERSON>',\n    accountOfCurrentUser: '<PERSON>u anki kullanıcının hesabı',\n    accountVerified: 'Hesap başarıyla doğrulandı.',\n    alreadyActivated: 'Hesap zaten etkinleştirildi',\n    alreadyLoggedIn: 'Hesaba zaten giriş yapıldı',\n    apiKey: 'API Anahtarı',\n    authenticated: 'Doğrulandı',\n    backToLogin: 'Giriş ekranına geri dön',\n    beginCreateFirstUser: 'Başlamak için ilk kullanıcı hesabını oluşturun.',\n    changePassword: 'Parolayı Değiştir',\n    checkYourEmailForPasswordReset:\n      'E-posta adresi bir hesapla ilişkili<PERSON>e, şifrenizi sıfırlama talimatlarınızı kısa süre içerisinde alacaksınız. E-postayı gelen kutunuzda görmüyorsanız, lütfen spam veya gereksiz posta klasörünüzü kontrol edin.',\n    confirmGeneration: 'Oluştumayı Onayla',\n    confirmPassword: 'Parolayı Onayla',\n    createFirstUser: 'İlk kullanıcı oluştur',\n    emailNotValid: 'Girilen e-posta geçersiz',\n    emailOrUsername: 'E-posta veya Kullanıcı Adı',\n    emailSent: 'E-posta gönderildi',\n    emailVerified: 'E-posta başarıyla doğrulandı.',\n    enableAPIKey: 'Api anahtarını etkinleştir',\n    failedToUnlock: 'Hesabı aktifleştirme başarısız oldu',\n    forceUnlock: 'Hesabı Etkinleştir',\n    forgotPassword: 'Parolamı Unuttum',\n    forgotPasswordEmailInstructions:\n      'Lütfen e-posta adresinizi aşağıdaki alana girin. Parolanızı nasıl sıfırlayacağınızı gösteren bir e-posta adresi alacaksınız.',\n    forgotPasswordQuestion: 'Parolanızı mı unuttunuz?',\n    forgotPasswordUsernameInstructions:\n      'Lütfen kullanıcı adınızı aşağıya girin. Şifrenizi nasıl sıfırlayacağınıza dair talimatlar, kullanıcı adınızla ilişkilendirilmiş e-posta adresine gönderilecektir.',\n    generate: 'Oluştur',\n    generateNewAPIKey: 'Yeni bir API anahtarı oluştur',\n    generatingNewAPIKeyWillInvalidate:\n      'Yeni bir API anahtarı oluşturmak önceckini <1>geçersiz kılacaktır</1>. Devam etmek istiyor musunuz?',\n    lockUntil: 'Lock Until',\n    logBackIn: 'Tekrar giriş yapın',\n    loggedIn:\n      'Başka bir kullanıcı hesabıyla giriş yapabilmek için önce <0>çıkış yapmanız</0> gerekmektedir.',\n    loggedInChangePassword: 'Parolanızı değiştirmek için <0>hesabınıza</0> gidebilirsiniz.',\n    loggedOutInactivity: 'Uzun süre işlem yapmadığınız için oturumunuz kapatıldı.',\n    loggedOutSuccessfully: 'Başarıyla çıkış yaptınız.',\n    loggingOut: 'Çıkış yapılıyor...',\n    login: 'Giriş',\n    loginAttempts: 'Giriş Denemeleri',\n    loginUser: 'Kullanıcı girişi',\n    loginWithAnotherUser:\n      'Başka bir kullanıcı hesabıyla giriş yapmak için önce <0>çıkış</0> yapmalısınız.',\n    logOut: 'Çıkış',\n    logout: 'Çıkış',\n    logoutSuccessful: 'Çıkış başarılı.',\n    logoutUser: 'Kullanıcıyı çıkış yapmaya zorla',\n    newAccountCreated:\n      '<0>{{serverURL}}</0> sitesinde adınıza yeni bir hesap oluşturuldu. E-postanızı doğrulamak için bağlantıya tıklayabilirsiniz: <1>{{verificationURL}}</1><br> E-postanızı doğruladıktan sonra siteye hesap bilgilerinizle giriş yapabilirsiniz.',\n    newAPIKeyGenerated: 'Yeni API anahtarı oluşturuldu.',\n    newPassword: 'Yeni Parola',\n    passed: 'Doğrulama Başarılı',\n    passwordResetSuccessfully: 'Parola başarıyla sıfırlandı.',\n    resetPassword: 'Parolayı Sıfırla',\n    resetPasswordExpiration: 'Parola Geçerlik Süresini Sıfırla',\n    resetPasswordToken: 'Parola tokenini sıfırla',\n    resetYourPassword: 'Parolanızı Sıfırlayın',\n    stayLoggedIn: 'Oturumu açık tut',\n    successfullyRegisteredFirstUser: 'İlk kullanıcının kaydı başarıyla tamamlandı.',\n    successfullyUnlocked: 'Hesabın kilidi başarıyla açıldı',\n    tokenRefreshSuccessful: 'Token yenileme başarılı.',\n    unableToVerify: 'Doğrulama başarısız',\n    username: 'Kullanıcı Adı',\n    usernameNotValid: 'Sağlanan kullanıcı adı geçerli değil.',\n    verified: 'Doğrulandı',\n    verifiedSuccessfully: 'Hesap başarıyla doğrulandı',\n    verify: 'Doğrula',\n    verifyUser: 'Kullanıcıyı doğrula',\n    verifyYourEmail: 'E-postanızı doğrulayın',\n    youAreInactive:\n      'Bir süredir işlem yapmadığınız için yakında oturumunuz kapatılacak. Oturumunuzun açık kalmasını istiyor musunuz?',\n    youAreReceivingResetPassword:\n      'Siz veya bir başkası hesabınızın parolasını sıfırlama isteğinde bulunduğu için bu e-postayı alıyorsunuz. İşlemi tamamlamak için lütfen aşağıdaki bağlantıya tıklayın veya bağlantı adresini tarayıcınızın adres yazma bölümüne kopyalayın.',\n    youDidNotRequestPassword:\n      'Eğer bu işlemi siz gerçekleştirmediyseniz bu e-postayı görmezden gelebilirsiniz.',\n  },\n  error: {\n    accountAlreadyActivated: 'Hesap zaten etkinleştirildi.',\n    autosaving: 'Otomatik kaydetme başarısız oldu',\n    correctInvalidFields: 'Lütfen geçersiz alanları düzeltin.',\n    deletingFile: 'Dosya silinirken bir hatayla karşılaşıldı.',\n    deletingTitle:\n      '{{title}} silinirken bir sorun yaşandı. Lütfen internet bağlantınızı kontrol edip tekrar deneyin.',\n    documentNotFound:\n      \"ID'si {{id}} olan belge bulunamadı. Silinmiş olabilir, hiç var olmamış olabilir veya belgeye erişiminiz olmayabilir.\",\n    emailOrPasswordIncorrect: 'Girilen e-posta veya parola hatalı',\n    followingFieldsInvalid_one: 'Lütfen geçersiz alanı düzeltin:',\n    followingFieldsInvalid_other: 'Lütfen geçersiz alanları düzeltin:',\n    incorrectCollection: 'Hatalı koleksiyon',\n    insufficientClipboardPermissions:\n      'Pano erişim reddedildi. Lütfen pano izinlerinizi kontrol edin.',\n    invalidClipboardData: 'Geçersiz pano verisi.',\n    invalidFileType: 'Geçersiz dosya türü',\n    invalidFileTypeValue: 'Geçersiz dosya türü: {{value}}',\n    invalidRequestArgs: 'İstek içerisinde geçersiz argümanlar iletildi: {{args}}',\n    loadingDocument: \"{{id}} ID'ye sahip döküman yüklenirken bir sorun oluştu.\",\n    localesNotSaved_one: 'Aşağıdaki yerel ayar kaydedilemedi:',\n    localesNotSaved_other: 'Aşağıdaki yerel ayarlar kaydedilemedi:',\n    logoutFailed: 'Çıkış başarısız oldu.',\n    missingEmail: 'E-posta adresi girilmedi.',\n    missingIDOfDocument: \"Güncellenecek döküman ID'si eksik.\",\n    missingIDOfVersion: \"Versiyon ID'si geçersiz.\",\n    missingRequiredData: 'Gerekli veri eksik.',\n    noFilesUploaded: 'Yüklenen dosya yok',\n    noMatchedField: '\"{{label}}\" ile eşleşen alan bulunamadı.',\n    notAllowedToAccessPage: 'Bu sayfaya erişim izniniz yok.',\n    notAllowedToPerformAction: 'Bu işlemi gerçekleştirmek için izniniz yok.',\n    notFound: 'Sayfa bulunamadı.',\n    noUser: 'Kullanıcı yok',\n    previewing: 'Önizleme başarısız oldu',\n    problemUploadingFile: 'Dosya yüklenirken bir sorun oluştu.',\n    restoringTitle:\n      '{{title}} geri yüklenirken bir hata oluştu. Lütfen bağlantınızı kontrol edin ve tekrar deneyin.',\n    tokenInvalidOrExpired: 'Geçersiz veya süresi dolmuş token.',\n    tokenNotProvided: 'Jeton sağlanmadı.',\n    unableToCopy: 'Kopyalanamıyor.',\n    unableToDeleteCount: '{{total}} {{label}} içinden {{count}} silinemiyor.',\n    unableToReindexCollection:\n      '{{collection}} koleksiyonunun yeniden indekslenmesinde hata oluştu. İşlem durduruldu.',\n    unableToUpdateCount: '{{total}} {{label}} içinden {{count}} güncellenemiyor.',\n    unauthorized: 'Bu işlemi gerçekleştirmek için lütfen giriş yapın.',\n    unauthorizedAdmin: 'Bu kullanıcı yönetici paneline erişim iznine sahip değil.',\n    unknown: 'Bilinmeyen bir hata oluştu.',\n    unPublishingDocument: 'Geçerli döküman yayından kaldırılırken bir sorun oluştu.',\n    unspecific: 'Bir hata oluştu.',\n    unverifiedEmail: 'Giriş yapmadan önce e-posta adresinizi doğrulayın.',\n    userEmailAlreadyRegistered: 'Verilen e-posta ile zaten kayıtlı bir kullanıcı var.',\n    userLocked:\n      'Hesabınız hatalı giriş denemeleri yüzünden geçici olarak kilitlendi. Lütfen daha sonra tekrar deneyin.',\n    usernameAlreadyRegistered: 'Verilen kullanıcı adına sahip bir kullanıcı zaten kayıtlı.',\n    usernameOrPasswordIncorrect: 'Sağlanan kullanıcı adı veya şifre yanlış.',\n    valueMustBeUnique: 'Değer benzersiz olmalıdır',\n    verificationTokenInvalid: 'Doğrulama tokeni geçersiz.',\n  },\n  fields: {\n    addLabel: '{{label}} ekle',\n    addLink: 'Link Ekle',\n    addNew: 'Yeni',\n    addNewLabel: 'Yeni {{label}}',\n    addRelationship: 'İlişki Ekle',\n    addUpload: 'Yükleme Ekle',\n    block: 'blok',\n    blocks: 'blok',\n    blockType: 'Blok tipi',\n    chooseBetweenCustomTextOrDocument:\n      'Choose between entering a custom text URL or linking to another document.',\n    chooseDocumentToLink: 'Bağlantı verilecek bir döküman seçin.',\n    chooseFromExisting: 'Varolanlardan seç',\n    chooseLabel: '{{label}} seç',\n    collapseAll: 'Tümünü daralt',\n    customURL: 'Özel URL',\n    editLabelData: '{{label}} düzenle',\n    editLink: 'Bağlantıyı Düzenle',\n    editRelationship: 'İlişkiyi Ekle',\n    enterURL: 'Bir URL girin',\n    internalLink: 'İç bağlantı',\n    itemsAndMore: '{{items}} and {{count}} more',\n    labelRelationship: '{{label}} Relationship',\n    latitude: 'Enlem',\n    linkedTo: '<0>label</0> için bağlantı verildi',\n    linkType: 'Bağlantı türü',\n    longitude: 'Boylam',\n    newLabel: 'Yeni {{label}}',\n    openInNewTab: 'Yeni sekmede aç',\n    passwordsDoNotMatch: 'Parolalar eşleşmiyor.',\n    relatedDocument: 'İlişkili döküman',\n    relationTo: 'Relation To',\n    removeRelationship: 'İlişkiyi Kaldır',\n    removeUpload: 'Dosyayı Sil',\n    saveChanges: 'Değişiklikleri kaydet',\n    searchForBlock: 'Blok ara',\n    selectExistingLabel: 'Varolan {{label}} seç',\n    selectFieldsToEdit: 'Düzenlenecek alanları seçin',\n    showAll: 'Tümünü göster',\n    swapRelationship: 'Takas Ilişkisi',\n    swapUpload: 'Karşıya Yüklemeyi Değiştir',\n    textToDisplay: 'Görüntülenecek metin',\n    toggleBlock: 'Bloğu aç/kapat',\n    uploadNewLabel: 'Karşıya {{label}} yükle',\n  },\n  folder: {\n    browseByFolder: 'Klasöre Göre Gözat',\n    byFolder: 'Klasör Bazında',\n    deleteFolder: 'Klasörü Sil',\n    folderName: 'Klasör Adı',\n    folders: 'Klasörler',\n    folderTypeDescription:\n      'Bu klasörde hangi türden koleksiyon belgelerine izin verilmesi gerektiğini seçin.',\n    itemHasBeenMoved: '{{title}} {{folderName}} klasörüne taşındı.',\n    itemHasBeenMovedToRoot: '{{title}} kök klasöre taşındı.',\n    itemsMovedToFolder: \"{{title}} {{folderName}}'ye taşındı.\",\n    itemsMovedToRoot: '{{title}} kök klasörüne taşındı',\n    moveFolder: 'Klasörü Taşı',\n    moveItemsToFolderConfirmation:\n      \"<1>{{count}} {{label}}</1>'yi <2>{{toFolder}}</2>'ye taşımayı planlıyorsunuz. Emin misiniz?\",\n    moveItemsToRootConfirmation:\n      '<1>{{count}} {{label}}</1> kök klasöre taşımayı planlıyorsunuz. Emin misiniz?',\n    moveItemToFolderConfirmation:\n      '<1>{{title}}</1> ögesini <2>{{toFolder}}</2> konumuna taşımak üzeresiniz. Emin misiniz?',\n    moveItemToRootConfirmation:\n      '<1>{{title}}</1> öğesini ana klasöre taşımak üzeresiniz. Emin misiniz?',\n    movingFromFolder: '{{title}} öğesinin {{fromFolder}} klasöründen taşınması',\n    newFolder: 'Yeni Klasör',\n    noFolder: 'Klasör Yok',\n    renameFolder: 'Klasörü Yeniden Adlandır',\n    searchByNameInFolder: \"{{folderName}}'da İsme Göre Ara\",\n    selectFolderForItem: '{{title}} için klasör seçin',\n  },\n  general: {\n    name: 'İsim',\n    aboutToDelete:\n      '<1>{{title}}</1> {{label}} silinmek üzere. Silme işlemine devam etmek istiyor musunuz?',\n    aboutToDeleteCount_many: '{{count}} {{label}} silmek üzeresiniz',\n    aboutToDeleteCount_one: '{{count}} {{label}} silmek üzeresiniz',\n    aboutToDeleteCount_other: '{{count}} {{label}} silmek üzeresiniz',\n    aboutToPermanentlyDelete:\n      '{{label}} <1>{{title}}</1> kalıcı olarak silmek üzeresiniz. Emin misiniz?',\n    aboutToPermanentlyDeleteTrash:\n      'Çöpten <0>{{count}}</0> <1>{{label}}</1> kalıcı olarak silmek üzeresiniz. Emin misiniz?',\n    aboutToRestore: \"{{label}} <1>{{title}}</1>'yi geri yüklemek üzeresiniz. Emin misiniz?\",\n    aboutToRestoreAsDraft:\n      '{{label}} <1>{{title}}</1> taslağı olarak geri yüklemek üzeresiniz. Emin misiniz?',\n    aboutToRestoreAsDraftCount: 'Taslağı olarak geri yükleme üzeresiniz: {{count}} {{label}}',\n    aboutToRestoreCount: '{{count}} {{label}} geri yüklemek üzeresiniz.',\n    aboutToTrash: '{{label}} <1>{{title}}</1> çöp kutusuna taşımayı düşünüyorsunuz. Emin misiniz?',\n    aboutToTrashCount: '{{count}} {{label}} çöp kutusuna taşımayı düşünüyorsunuz.',\n    addBelow: 'Altına ekle',\n    addFilter: 'Filtre ekle',\n    adminTheme: 'Admin arayüzü',\n    all: 'Tüm',\n    allCollections: 'Tüm Koleksiyonlar',\n    allLocales: 'Tüm yerler',\n    and: 've',\n    anotherUser: 'Başka bir kullanıcı',\n    anotherUserTakenOver: 'Başka bir kullanıcı bu belgenin düzenlemesini devraldı.',\n    applyChanges: 'Değişiklikleri Uygula',\n    ascending: 'artan',\n    automatic: 'Otomatik',\n    backToDashboard: 'Anasayfaya geri dön',\n    cancel: 'İptal',\n    changesNotSaved:\n      'Değişiklikleriniz henüz kaydedilmedi. Eğer bu sayfayı terk ederseniz değişiklikleri kaybedeceksiniz.',\n    clear: 'Temiz',\n    clearAll: 'Hepsini Temizle',\n    close: 'Kapat',\n    collapse: 'Daralt',\n    collections: 'Koleksiyonlar',\n    columns: 'Sütunlar',\n    columnToSort: 'Sıralanacak Sütunlar',\n    confirm: 'Onayla',\n    confirmCopy: 'Kopyayı onayla',\n    confirmDeletion: 'Silmeyi onayla',\n    confirmDuplication: 'Çoğaltmayı onayla',\n    confirmMove: 'Hareketi onayla',\n    confirmReindex: 'Tüm {{collections}} yeniden dizine alınsın mı?',\n    confirmReindexAll: 'Tüm koleksiyonlar yeniden dizine alinsın mı?',\n    confirmReindexDescription:\n      'Bu işlem mevcut dizinleri kaldıracak ve {{collections}} koleksiyonlarındaki belgeleri yeniden dizine alacaktır.',\n    confirmReindexDescriptionAll:\n      'Bu işlem mevcut dizinleri kaldıracak ve tüm koleksiyonlardaki belgeleri yeniden dizine alacaktır.',\n    confirmRestoration: 'Onarımı onaylayın',\n    copied: 'Kopyalandı',\n    copy: 'Kopyala',\n    copyField: 'Alanı kopyala',\n    copying: 'Kopyalama',\n    copyRow: 'Satırı kopyala',\n    copyWarning:\n      \"{{to}}'yu {{from}} ile {{label}} {{title}} için üstüne yazmak üzeresiniz. Emin misiniz?\",\n    create: 'Oluştur',\n    created: 'Oluşturma tarihi',\n    createdAt: 'Oluşturma tarihi',\n    createNew: 'Yeni oluştur',\n    createNewLabel: 'Yeni bir {{label}} oluştur',\n    creating: 'Oluşturuluyor',\n    creatingNewLabel: 'Yeni bir {{label}} oluşturuluyor',\n    currentlyEditing:\n      'şu anda bu belgeyi düzenliyor. Devralırsanız, düzenlemeye devam etmeleri engellenecek ve kaydedilmemiş değişiklikleri de kaybedebilirler.',\n    custom: 'Özel',\n    dark: 'Karanlık',\n    dashboard: 'Anasayfa',\n    delete: 'Sil',\n    deleted: 'Silindi',\n    deletedAt: 'Silindiği Tarih',\n    deletedCountSuccessfully: '{{count}} {{label}} başarıyla silindi.',\n    deletedSuccessfully: 'Başarıyla silindi.',\n    deletePermanently: 'Çöpü atlayın ve kalıcı olarak silin',\n    deleting: 'Siliniyor...',\n    depth: 'Derinlik',\n    descending: 'Azalan',\n    deselectAllRows: 'Tüm satırların seçimini kaldır',\n    document: 'Belge',\n    documentIsTrashed: 'Bu {{label}} çöpe atıldı ve sadece okuma modunda.',\n    documentLocked: 'Belge kilitlendi',\n    documents: 'Belgeler',\n    duplicate: 'Çoğalt',\n    duplicateWithoutSaving: 'Ayarları kaydetmeden çoğalt',\n    edit: 'Düzenle',\n    editAll: 'Hepsini düzenle',\n    editedSince: 'O tarihten itibaren düzenlendi',\n    editing: 'Düzenleniyor',\n    editingLabel_many: '{{count}} {{label}} düzenleniyor',\n    editingLabel_one: '{{count}} {{label}} düzenleniyor',\n    editingLabel_other: '{{count}} {{label}} düzenleniyor',\n    editingTakenOver: 'Düzenleme devralındı',\n    editLabel: '{{label}} düzenle',\n    email: 'E-posta',\n    emailAddress: 'E-posta adresi',\n    emptyTrash: 'Çöpü Boşalt',\n    emptyTrashLabel: '{{label}} çöp kutusunu boşaltın',\n    enterAValue: 'Değer girin',\n    error: 'Hata',\n    errors: 'Hatalar',\n    exitLivePreview: 'Canlı Önizlemeyi Kapat',\n    export: 'İhracat',\n    fallbackToDefaultLocale: 'Varsayılan yerel ayara geri dönme',\n    false: 'Yanlış',\n    filter: 'Filtrele',\n    filters: 'Filtreler',\n    filterWhere: '{{label}} filtrele:',\n    globals: 'Globaller',\n    goBack: 'Geri dön',\n    groupByLabel: \"{{label}}'ye göre grupla\",\n    import: 'İthalat',\n    isEditing: 'düzenliyor',\n    item: 'öğe',\n    items: 'öğeler',\n    language: 'Dil',\n    lastModified: 'Son değiştirme',\n    leaveAnyway: 'Yine de ayrıl',\n    leaveWithoutSaving: 'Kaydetmeden ayrıl',\n    light: 'Aydınlık',\n    livePreview: 'Önizleme',\n    loading: 'Yükleniyor',\n    locale: 'Yerel ayar',\n    locales: 'Diller',\n    menu: 'Menü',\n    moreOptions: 'Daha fazla seçenek',\n    move: 'Hareket et',\n    moveConfirm:\n      '<1>{{destination}}</1> konumuna {{count}} {{label}} taşımayı planlıyorsunuz. Emin misiniz?',\n    moveCount: '{{count}} {{label}} taşı',\n    moveDown: 'Aşağı taşı',\n    moveUp: 'Yukarı taşı',\n    moving: 'Taşınma',\n    movingCount: '{{count}} {{label}} taşıma',\n    newPassword: 'Yeni parola',\n    next: 'Sonraki',\n    no: 'Hayır',\n    noDateSelected: 'Tarih seçilmedi',\n    noFiltersSet: 'Tanımlı filtre yok',\n    noLabel: '<{{label}} yok>',\n    none: 'Hiç',\n    noOptions: 'Seçenek yok',\n    noResults:\n      '{{label}} bulunamadı. Henüz bir {{label}} eklenmemiş olabilir veya seçtiğiniz filtrelerle eşleşen bir sonuç bulunamamış olabilir.',\n    notFound: 'Bulunamadı',\n    nothingFound: 'Hiçbir şey bulunamadı',\n    noTrashResults: 'Çöpte hiç {{label}} yok.',\n    noUpcomingEventsScheduled: 'Planlanan gelecek etkinlik yok.',\n    noValue: 'Değer yok',\n    of: 'of',\n    only: 'Sadece',\n    open: 'Aç',\n    or: 'Or',\n    order: 'Order',\n    overwriteExistingData: 'Mevcut alan verilerinin üzerine yazın',\n    pageNotFound: 'Sayfa bulunamadı',\n    password: 'Parola',\n    pasteField: 'Alanı yapıştır',\n    pasteRow: 'Satırı yapıştır',\n    payloadSettings: 'Ayarlar',\n    permanentlyDelete: 'Kalıcı Olarak Sil',\n    permanentlyDeletedCountSuccessfully: 'Kalıcı olarak {{count}} {{label}} başarıyla silindi.',\n    perPage: 'Sayfa başına: {{limit}}',\n    previous: 'Önceki',\n    reindex: 'Yeniden İndeksle',\n    reindexingAll: 'Tüm {{collections}} yeniden dizine alınıyor.',\n    remove: 'Kaldır',\n    rename: 'Yeniden adlandır',\n    reset: 'Sıfırla',\n    resetPreferences: 'Tercihleri sıfırla',\n    resetPreferencesDescription:\n      'Bu, tüm tercihlerinizin varsayılan ayarlara sıfırlanmasını sağlar.',\n    resettingPreferences: 'Tercihler sıfırlanıyor.',\n    restore: 'Geri Yükle',\n    restoreAsPublished: 'Yayınlanan sürüm olarak geri yükle',\n    restoredCountSuccessfully: '{{count}} {{label}} başarıyla geri yüklendi.',\n    restoring:\n      \"Özgün metnin anlamını Payload bağlamında saygıyla yeniden oluşturun. İşte çok belirli anlamlar taşıyan yaygın Payload terimlerinin bir listesi:\\n    - Koleksiyon: Bir koleksiyon, ortak bir yapı ve amaca sahip belgelerin grubudur. Koleksiyonlar içerik organizasyonu ve yönetiminde Payload'da kullanılır.\\n    - Alan: Bir alan, bir koleksiyon içindeki belgedeki belirli bir veri parçasıdır. Alanlar, bir belgede saklanabilen ver\",\n    row: 'Satır',\n    rows: 'Satır',\n    save: 'Kaydet',\n    saving: 'Kaydediliyor...',\n    schedulePublishFor: '{{title}} için yayınlama programı ayarlayın.',\n    searchBy: 'Şuna göre sırala: {{label}}',\n    select: 'Seçiniz',\n    selectAll: \"Tüm {{count}} {{label}}'ı seçin\",\n    selectAllRows: 'Tüm satırları seçin',\n    selectedCount: '{{count}} {{label}} seçildi',\n    selectLabel: '{{label}} seçin',\n    selectValue: 'Bir değer seçin',\n    showAllLabel: 'Tüm {{label}} göster',\n    sorryNotFound: 'Üzgünüz, isteğinizle eşleşen bir sonuç bulunamadı.',\n    sort: 'Sırala',\n    sortByLabelDirection: '{{label}} göre sırala {{direction}}',\n    stayOnThisPage: 'Bu sayfada kal',\n    submissionSuccessful: 'Gönderme başarılı',\n    submit: 'Gönder',\n    submitting: 'Gönderiliyor...',\n    success: 'Başarı',\n    successfullyCreated: '{{label}} başarıyla oluşturuldu.',\n    successfullyDuplicated: '{{label}} başarıyla kopyalandı.',\n    successfullyReindexed:\n      '{{collections}} koleksiyonlarından {{total}} belgenin {{count}} tanesi başarıyla yeniden indekslendi.',\n    takeOver: 'Devralmak',\n    thisLanguage: 'Türkçe',\n    time: 'Zaman',\n    timezone: 'Saat dilimi',\n    titleDeleted: '{{label}} {{title}} başarıyla silindi.',\n    titleRestored: '\"{{title}}\" başarıyla geri yüklendi.',\n    titleTrashed: '{{label}} \"{{title}}\" çöpe taşındı.',\n    trash: 'Çöp',\n    trashedCountSuccessfully: '{{count}} {{label}} çöp kutusuna taşındı.',\n    true: 'Doğru',\n    unauthorized: 'Yetkisiz',\n    unsavedChanges: 'Kaydedilmemiş değişiklikleriniz var. Devam etmeden önce kaydedin veya atın.',\n    unsavedChangesDuplicate:\n      'Kaydedilmemiş değişiklikler var. Çoğaltma işlemine devam etmek istiyor musunuz?',\n    untitled: 'Başlıksız',\n    upcomingEvents: 'Yaklaşan Etkinlikler',\n    updatedAt: 'Güncellenme tarihi',\n    updatedCountSuccessfully: '{{count}} {{label}} başarıyla güncellendi.',\n    updatedLabelSuccessfully: '{{label}} başarıyla güncellendi.',\n    updatedSuccessfully: 'Başarıyla güncellendi.',\n    updateForEveryone: 'Herkes için güncelleme',\n    updating: 'Güncelleniyor',\n    uploading: 'Yükleniyor',\n    uploadingBulk: \"{{total}}'den {{current}} yükleniyor\",\n    user: 'kullanıcı',\n    username: 'Kullanıcı Adı',\n    users: 'kullanıcı',\n    value: 'Değer',\n    viewing: 'Görüntüleme',\n    viewReadOnly: 'Salt okunur olarak görüntüle',\n    welcome: 'Hoşgeldiniz',\n    yes: 'Evet',\n  },\n  localization: {\n    cannotCopySameLocale: 'Aynı yerel ayara kopyalanamaz.',\n    copyFrom: 'Kopyala',\n    copyFromTo: \"{{from}} 'dan {{to}} 'ya kopyalama\",\n    copyTo: 'Kopyala',\n    copyToLocale: 'Yerel hafızaya kopyala',\n    localeToPublish: 'Yayınlanacak yerel',\n    selectLocaleToCopy: 'Kopyalamak için yerel seçimi yapın',\n  },\n  operators: {\n    contains: 'içerir',\n    equals: 'eşittir',\n    exists: 'var',\n    intersects: 'kesişir',\n    isGreaterThan: 'şundan büyüktür',\n    isGreaterThanOrEqualTo: 'büyüktür veya eşittir',\n    isIn: 'içinde',\n    isLessThan: 'küçüktür',\n    isLessThanOrEqualTo: 'küçüktür veya eşittir',\n    isLike: 'gibidir',\n    isNotEqualTo: 'eşit değildir',\n    isNotIn: 'içinde değil',\n    isNotLike: 'gibi değil',\n    near: 'yakın',\n    within: 'içinde',\n  },\n  upload: {\n    addFile: 'Dosya ekle',\n    addFiles: 'Dosya Ekle',\n    bulkUpload: 'Toplu Yükleme',\n    crop: 'Mahsulat',\n    cropToolDescription:\n      'Seçilen alanın köşelerini sürükleyin, yeni bir alan çizin ya da aşağıdaki değerleri ayarlayın.',\n    download: 'İndir',\n    dragAndDrop: 'Bir dosya sürükleyip bırakabilirsiniz',\n    dragAndDropHere: 'veya buraya bir dosya sürükleyip bırakabilirsiniz',\n    editImage: 'Görüntüyü Düzenle',\n    fileName: 'Dosya adı',\n    fileSize: 'Dosya boyutu',\n    filesToUpload: 'Yüklemek için Dosyalar',\n    fileToUpload: 'Yüklenecek Dosya',\n    focalPoint: 'Odak Noktası',\n    focalPointDescription:\n      'Önizlemeye odak noktasını doğrudan sürükleyin veya aşağıdaki değerleri ayarlayın.',\n    height: 'Yükseklik',\n    lessInfo: 'Daha az bilgi',\n    moreInfo: 'Daha fazla bilgi',\n    noFile: 'Dosya yok',\n    pasteURL: 'URL yapıştır',\n    previewSizes: 'Önizleme Boyutları',\n    selectCollectionToBrowse: 'Görüntülenecek bir koleksiyon seçin',\n    selectFile: 'Dosya seç',\n    setCropArea: 'Mahsul alanını ayarla',\n    setFocalPoint: 'Odak noktasını ayarla',\n    sizes: 'Boyutlar',\n    sizesFor: '{{label}} için boyutlar',\n    width: 'Genişlik',\n  },\n  validation: {\n    emailAddress: 'Lütfen geçerli bir e-posta adresi girin.',\n    enterNumber: 'Lütfen geçerli bir sayı girin.',\n    fieldHasNo: 'Bu alanda {{label}} girili değil.',\n    greaterThanMax: '{{value}} izin verilen maksimum {{label}} değerinden daha büyük.',\n    invalidInput: 'Bu alanda geçersiz bir giriş mevcut.',\n    invalidSelection: 'Bu alanda geçersiz bir seçim mevcut.',\n    invalidSelections: \"'Bu alan şu geçersiz seçimlere sahip:'\",\n    lessThanMin: '{{value}} izin verilen minimum {{label}} değerinden daha küçük.',\n    limitReached: 'Sınır aşıldı, yalnızca {{max}} öğe eklenebilir.',\n    longerThanMin: 'Bu değer minimum {{minLength}} karakterden uzun olmalıdır.',\n    notValidDate: '\"{{value}}\" geçerli bir tarih değil.',\n    required: 'Bu alan gereklidir.',\n    requiresAtLeast: 'Bu alan en az {{count}} adet {{label}} gerektirmektedir.',\n    requiresNoMoreThan: 'Bu alana {{count}} adetten fazla {{label}} girilemez.',\n    requiresTwoNumbers: 'Bu alana en az iki rakam girilmesi zorunludur.',\n    shorterThanMax: 'Bu alan {{maxLength}} karakterden daha kısa olmalıdır.',\n    timezoneRequired: 'Bir zaman dilimi gereklidir.',\n    trueOrFalse: 'Bu alan yalnızca doğru ve yanlış olabilir.',\n    username:\n      'Lütfen geçerli bir kullanıcı adı girin. Harfler, numaralar, kısa çizgiler, noktalar ve alt çizgiler içerebilir.',\n    validUploadID: \"'Bu alan geçerli bir karşıya yükleme ID'sine sahip değil.'\",\n  },\n  version: {\n    type: 'Tür',\n    aboutToPublishSelection: \"Seçimdeki tüm {{label}}'i yayınlamak üzeresiniz. Emin misin?\",\n    aboutToRestore: 'Döküman {{label}}, {{versionDate}} tarihindeki sürümüne geri döndürülecek.',\n    aboutToRestoreGlobal:\n      'Global {{label}}, {{versionDate}} tarihindeki sürümüne geri döndürülecek.',\n    aboutToRevertToPublished:\n      'Bu dökümanın değişikliklerini yayınladığı haline geri getirmek üzeresiniz. Devam etmek istiyor musunuz?',\n    aboutToUnpublish: 'Bu dökümanı yayından kaldırmak üzeresiniz. Devam etmek istiyor musunuz?',\n    aboutToUnpublishSelection: 'Seçimdeki tüm {{label}} yayınını kaldırmak üzeresiniz. Emin misin?',\n    autosave: 'Otomatik kaydet',\n    autosavedSuccessfully: 'Otomatik kaydetme başarılı',\n    autosavedVersion: 'Otomatik kayıtlı sürüm',\n    changed: 'Değişiklik yapıldı',\n    changedFieldsCount_one: '{{count}} alanı değişti',\n    changedFieldsCount_other: '{{count}} değişen alan',\n    compareVersion: 'Sürümü şununla karşılaştır:',\n    compareVersions: 'Sürümleri Karşılaştır',\n    comparingAgainst: 'Karşılaştırma',\n    confirmPublish: 'Yayınlamayı onayla',\n    confirmRevertToSaved: 'Confirm revert to saved',\n    confirmUnpublish: 'Yayından kaldırmayı onayla',\n    confirmVersionRestoration: 'Sürümü Geri Getirmeyi Onayla',\n    currentDocumentStatus: 'Şu an {{docStatus}} döküman',\n    currentDraft: 'Mevcut Taslak',\n    currentlyPublished: 'Şu Anda Yayınlanmaktadır',\n    currentlyViewing: 'Şu anda görüntüleniyor',\n    currentPublishedVersion: 'Mevcut Yayınlanan Sürüm',\n    draft: 'Taslak',\n    draftSavedSuccessfully: 'Taslak başarıyla kaydedildi.',\n    lastSavedAgo: 'Son kaydedildi {{distance}} önce',\n    modifiedOnly: 'Yalnızca değiştirilmiş',\n    moreVersions: 'Daha fazla versiyon...',\n    noFurtherVersionsFound: 'Başka sürüm bulunamadı.',\n    noRowsFound: '{{label}} bulunamadı',\n    noRowsSelected: 'Seçilen {{label}} yok',\n    preview: 'Önizleme',\n    previouslyDraft: 'Daha önce bir Taslak',\n    previouslyPublished: 'Daha Önce Yayınlanmış',\n    previousVersion: 'Önceki Sürüm',\n    problemRestoringVersion: 'Bu sürüme geri döndürürken bir hatayla karşılaşıldı.',\n    publish: 'Yayınla',\n    publishAllLocales: 'Tüm yerel ayarları yayınla',\n    publishChanges: 'Değişiklikleri yayınla',\n    published: 'Yayınlandı',\n    publishIn: '{{locale}} dilinde yayınlayın.',\n    publishing: 'Yayınlama',\n    restoreAsDraft: 'Taslak olarak geri yükle',\n    restoredSuccessfully: 'Geri getirme başarılı.',\n    restoreThisVersion: 'Bu sürüme geri döndür',\n    restoring: 'Geri döndürülüyor...',\n    reverting: 'Değişiklikler geri alınıyor...',\n    revertToPublished: 'Yayınlanana geri döndür',\n    saveDraft: 'Taslağı kaydet',\n    scheduledSuccessfully: 'Başarıyla planlandı.',\n    schedulePublish: 'Yayını Planla',\n    selectLocales: 'Görüntülenecek yerel ayarları seçin',\n    selectVersionToCompare: 'Karşılaştırılacak bir sürüm seçin',\n    showingVersionsFor: 'Şunun için sürümler gösteriliyor:',\n    showLocales: 'Yerel ayarları göster:',\n    specificVersion: 'Belirli Sürüm',\n    status: 'Durum',\n    unpublish: 'Yayından Kaldır',\n    unpublishing: 'Yayından kaldırılıyor...',\n    version: 'Sürüm',\n    versionAgo: '{{distance}} önce',\n    versionCount_many: '{{count}} sürüm bulundu',\n    versionCount_none: 'Sürüm bulunamadı',\n    versionCount_one: '{{count}} sürüm bulundu',\n    versionCount_other: '{{count}} sürüm bulundu',\n    versionCreatedOn: '{{version}} oluşturma tarihi:',\n    versionID: 'Sürüm ID',\n    versions: 'Sürümler',\n    viewingVersion: '{{entityLabel}} {{documentTitle}} için sürümler gösteriliyor',\n    viewingVersionGlobal: '`Global {{entityLabel}} için sürümler gösteriliyor',\n    viewingVersions: '{{entityLabel}} {{documentTitle}} için sürümler gösteriliyor',\n    viewingVersionsGlobal: '`Global {{entityLabel}} için sürümler gösteriliyor',\n  },\n}\n\nexport const tr: Language = {\n  dateFNSKey: 'tr',\n  translations: trTranslations,\n}\n"], "names": ["trTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "tr", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UACE;QACFC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YACE;QACFC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eACE;QACFC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBACE;QACFC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}