{"version": 3, "sources": ["../../src/languages/ja.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const jaTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'アカウント',\n    accountOfCurrentUser: '現在のユーザーアカウント',\n    accountVerified: 'アカウントが正常に確認されました。',\n    alreadyActivated: 'すでに有効です',\n    alreadyLoggedIn: 'すでにログインしています',\n    apiKey: 'API Key',\n    authenticated: '認証済み',\n    backToLogin: 'ログイン画面へ戻る',\n    beginCreateFirstUser: 'まずは、最初のユーザーを作成します。',\n    changePassword: 'パスワードを変更',\n    checkYourEmailForPasswordReset:\n      'そのメールアドレスがアカウントに関連付けられている場合、すぐにパスワードをリセットするための指示が送信されます。メールが受信トレイにない場合は、迷惑メールフォルダまたはジャンクメールフォルダを確認してください。',\n    confirmGeneration: '生成の確認',\n    confirmPassword: 'パスワードの確認',\n    createFirstUser: '最初のユーザーを作成',\n    emailNotValid: '入力されたメールアドレスは無効です。',\n    emailOrUsername: 'メールまたはユーザー名',\n    emailSent: 'Emailが送信されました。',\n    emailVerified: 'メールが正常に確認されました。',\n    enableAPIKey: 'API Keyを許可',\n    failedToUnlock: 'ロックの解除に失敗しました。',\n    forceUnlock: '強制的にロックを解除',\n    forgotPassword: 'パスワード再設定',\n    forgotPasswordEmailInstructions:\n      'アカウントのメールアドレスを以下に入力してください。パスワードの再設定方法が記載されたメールが届きます。',\n    forgotPasswordQuestion: 'パスワードをお忘れですか？',\n    forgotPasswordUsernameInstructions:\n      '以下にユーザー名を入力してください。パスワードのリセット方法については、ユーザー名に関連付けられたメールアドレスに送信されます。',\n    generate: '生成',\n    generateNewAPIKey: '新しいAPI Keyを生成',\n    generatingNewAPIKeyWillInvalidate:\n      '新しいAPI Keyを生成すると、以前のAPI Keyは<1>無効</1>になります。よろしいですか？',\n    lockUntil: 'ロック期限',\n    logBackIn: '改めてログイン',\n    loggedIn: '他のユーザーでログインするには、まず<0>ログアウト</0>する必要があります。',\n    loggedInChangePassword:\n      'パスワードを変更するには、<0>アカウント</0>にアクセスしてパスワードを編集してください。',\n    loggedOutInactivity: 'しばらく操作を行わなかったため、管理画面からログアウトしました。',\n    loggedOutSuccessfully: '管理画面からログアウトしました。',\n    loggingOut: 'ログアウト中...',\n    login: 'ログイン',\n    loginAttempts: 'ログイン試行回数',\n    loginUser: 'ログインユーザー',\n    loginWithAnotherUser:\n      '他のユーザーでログインするには、まず<0>ログアウト</0>する必要があります。',\n    logOut: 'ログアウト',\n    logout: 'ログアウト',\n    logoutSuccessful: 'ログアウト成功。',\n    logoutUser: 'ログアウトユーザー',\n    newAccountCreated:\n      '<a href=\"{{serverURL}}\">{{serverURL}}</a>にアクセスするための新しいアカウントが作成されました。以下のリンクをクリックするか、ブラウザに以下のURLを貼り付けて、メールアドレスの確認を行ってください。<a href=\"{{verificationURL}}\">{{verificationURL}}</a><br>メールアドレスの確認後に、正常にログインできるようになります。',\n    newAPIKeyGenerated: '新しいAPI Keyを生成しました。',\n    newPassword: '新しいパスワード',\n    passed: '認証が通りました',\n    passwordResetSuccessfully: 'パスワードが正常にリセットされました。',\n    resetPassword: 'パスワード再発行',\n    resetPasswordExpiration: 'パスワードの有効期限をリセット',\n    resetPasswordToken: 'パスワードのトークンをリセット',\n    resetYourPassword: 'パスワードの再設定',\n    stayLoggedIn: 'ログイン状態を維持',\n    successfullyRegisteredFirstUser: '最初のユーザーの登録が成功しました。',\n    successfullyUnlocked: 'ロックの解除に成功しました。',\n    tokenRefreshSuccessful: 'トークンの更新が成功しました。',\n    unableToVerify: '検証ができません',\n    username: 'ユーザー名',\n    usernameNotValid: '提供されたユーザーネームは無効です',\n    verified: '検証済み',\n    verifiedSuccessfully: '検証が成功しました',\n    verify: '検証',\n    verifyUser: 'ユーザーの確認',\n    verifyYourEmail: 'メールアドレスの確認',\n    youAreInactive:\n      'しばらく操作を行わなかったため、セキュリティのために自動的にログアウトします。ログイン状態を維持しますか？',\n    youAreReceivingResetPassword:\n      'アカウントのパスワードリセットがリクエストされました。次のリンクをクリックする、または、ブラウザにリンクを貼り付けて、手続きを行ってください:',\n    youDidNotRequestPassword:\n      'もし望まない場合は、このメールを無視してください。パスワードは変更されません。',\n  },\n  error: {\n    accountAlreadyActivated: 'このアカウントはすでに有効です。',\n    autosaving: 'このデータを自動保存する際に問題が発生しました。',\n    correctInvalidFields: '無効なフィールドを修正してください。',\n    deletingFile: 'ファイルの削除中にエラーが発生しました。',\n    deletingTitle:\n      '{{title}} を削除する際にエラーが発生しました。接続を確認してからもう一度お試しください。',\n    documentNotFound:\n      'ID {{id}}のドキュメントが見つかりませんでした。削除されたか、存在しなかったか、またはアクセス権限がない可能性があります。',\n    emailOrPasswordIncorrect: 'メールアドレス、または、パスワードが正しくありません。',\n    followingFieldsInvalid_one: '次のフィールドは無効です:',\n    followingFieldsInvalid_other: '次のフィールドは無効です:',\n    incorrectCollection: '不正なコレクション',\n    insufficientClipboardPermissions:\n      'クリップボードへのアクセスが拒否されました。クリップボードの権限を確認してください。',\n    invalidClipboardData: '無効なクリップボードデータ。',\n    invalidFileType: '無効なファイル形式',\n    invalidFileTypeValue: '無効なファイル形式: {{value}}',\n    invalidRequestArgs: 'リクエストに無効な引数が渡されました: {{args}}',\n    loadingDocument: 'IDが {{id}} のデータを読み込む際に問題が発生しました。',\n    localesNotSaved_one: '次のロケールを保存できませんでした：',\n    localesNotSaved_other: '次のロケールを保存できませんでした：',\n    logoutFailed: 'ログアウトに失敗しました。',\n    missingEmail: 'メールアドレスが不足しています。',\n    missingIDOfDocument: '更新するデータのIDが不足しています。',\n    missingIDOfVersion: 'バージョンIDが不足しています。',\n    missingRequiredData: '必須データが不足しています。',\n    noFilesUploaded: 'ファイルがアップロードされていません。',\n    noMatchedField: '\"{{label}}\" に該当するフィールドがありません。',\n    notAllowedToAccessPage: 'この画面へのアクセスは許可されていません。',\n    notAllowedToPerformAction: 'このアクションは許可されていません。',\n    notFound: 'リクエストされたリソースは見つかりませんでした。',\n    noUser: 'ユーザーなし',\n    previewing: 'このデータをプレビューする際に問題が発生しました。',\n    problemUploadingFile: 'ファイルのアップロード中に問題が発生しました。',\n    restoringTitle:\n      '{{title}}の復元中にエラーが発生しました。接続を確認して、もう一度お試しください。',\n    tokenInvalidOrExpired: 'トークンが無効、または、有効期限が切れています。',\n    tokenNotProvided: 'トークンが提供されていません。',\n    unableToCopy: 'コピーできません。',\n    unableToDeleteCount: '{{total}} {{label}} から {{count}} を削除できません。',\n    unableToReindexCollection:\n      'コレクション {{collection}} の再インデックス中にエラーが発生しました。操作は中止されました。',\n    unableToUpdateCount: '{{total}} {{label}} のうち {{count}} 個を更新できません。',\n    unauthorized: '認証されていません。このリクエストを行うにはログインが必要です。',\n    unauthorizedAdmin: '管理画面へのアクセス権がないため、認証されていません。',\n    unknown: '不明なエラーが発生しました。',\n    unPublishingDocument: 'このデータを非公開する際に問題が発生しました。',\n    unspecific: 'エラーが発生しました。',\n    unverifiedEmail: 'ログインする前にメールを確認してください。',\n    userEmailAlreadyRegistered: '指定されたメールのユーザーはすでに登録されています。',\n    userLocked: 'このユーザーは、ログイン試行回数が多すぎるため、ロックされています。',\n    usernameAlreadyRegistered: '指定されたユーザーネームのユーザーはすでに登録されています。',\n    usernameOrPasswordIncorrect: '提供されたユーザー名またはパスワードが間違っています。',\n    valueMustBeUnique: 'ユニークな値である必要があります。',\n    verificationTokenInvalid: '認証トークンが無効です。',\n  },\n  fields: {\n    addLabel: '{{label}} を追加',\n    addLink: 'リンクを追加',\n    addNew: '新規追加',\n    addNewLabel: '{{label}} を新規追加',\n    addRelationship: 'リレーションシップを追加',\n    addUpload: 'アップロードを追加',\n    block: 'ブロック',\n    blocks: 'ブロック',\n    blockType: 'ブロックタイプ',\n    chooseBetweenCustomTextOrDocument:\n      'カスタムテキストのURLを入力するか、他のドキュメントにリンクするかを選択してください。',\n    chooseDocumentToLink: 'リンクするドキュメントを選択してください。',\n    chooseFromExisting: '既存から選択',\n    chooseLabel: '{{label}} を選択',\n    collapseAll: 'すべて閉じる',\n    customURL: 'カスタムURL',\n    editLabelData: '{{label}} データを編集',\n    editLink: 'リンクを編集',\n    editRelationship: 'リレーションシップを編集',\n    enterURL: 'URL を入力してください',\n    internalLink: '内部リンク',\n    itemsAndMore: '{{items}} 他{{count}}件',\n    labelRelationship: '{{label}} リレーションシップ',\n    latitude: '緯度',\n    linkedTo: '<0>{{label}}</0> にリンク',\n    linkType: 'リンクタイプ',\n    longitude: '経度',\n    newLabel: '新規 {{label}}',\n    openInNewTab: '新しいタブで開く',\n    passwordsDoNotMatch: 'パスワードが一致しません',\n    relatedDocument: 'リレーションデータ',\n    relationTo: 'リレーション',\n    removeRelationship: '関係を削除',\n    removeUpload: '削除',\n    saveChanges: '変更を保存',\n    searchForBlock: 'ブロックを検索',\n    selectExistingLabel: '既存 {{label}} を選択',\n    selectFieldsToEdit: '編集するフィールドを選択',\n    showAll: 'すべて開く',\n    swapRelationship: 'スワップ関係',\n    swapUpload: '差し替え',\n    textToDisplay: '表示するテキスト',\n    toggleBlock: 'ブロックを切り替え',\n    uploadNewLabel: '新規 {{label}} アップロード',\n  },\n  folder: {\n    browseByFolder: 'フォルダーで閲覧する',\n    byFolder: 'フォルダー別に',\n    deleteFolder: 'フォルダを削除する',\n    folderName: 'フォルダ名',\n    folders: 'フォルダー',\n    folderTypeDescription:\n      'このフォルダーに許可されるコレクションドキュメントのタイプを選択してください。',\n    itemHasBeenMoved: '{{title}}は{{folderName}}に移動されました',\n    itemHasBeenMovedToRoot: '{{title}}はルートフォルダに移動されました',\n    itemsMovedToFolder: '{{title}}は{{folderName}}に移動されました',\n    itemsMovedToRoot: '{{title}}はルートフォルダに移動しました。',\n    moveFolder: 'フォルダを移動する',\n    moveItemsToFolderConfirmation:\n      'あなたは<1>{{count}} {{label}}</1>を<2>{{toFolder}}</2>に移動しようとしています。よろしいですか？',\n    moveItemsToRootConfirmation:\n      'あなたはまもなく<1>{{count}} {{label}}</1>をルートフォルダーに移動しようとしています。よろしいですか？',\n    moveItemToFolderConfirmation:\n      '<1>{{title}}</1>を<2>{{toFolder}}</2>に移動しようとしています。よろしいですか？',\n    moveItemToRootConfirmation:\n      'あなたは<1>{{title}}</1>をルートフォルダに移動しようとしています。よろしいですか？',\n    movingFromFolder: '{{title}}を{{fromFolder}}から移動します',\n    newFolder: '新しいフォルダ',\n    noFolder: 'フォルダーなし',\n    renameFolder: 'フォルダの名前を変更する',\n    searchByNameInFolder: '{{folderName}}で名前を検索する',\n    selectFolderForItem: '{{title}}のためのフォルダを選択してください。',\n  },\n  general: {\n    name: '名前',\n    aboutToDelete: '{{label}} <1>{{title}}</1> を削除します。よろしいですか？',\n    aboutToDeleteCount_many: '{{label}}を{{count}}つ削除しようとしています',\n    aboutToDeleteCount_one: '{{label}}を{{count}}つ削除しようとしています',\n    aboutToDeleteCount_other: '{{label}}を{{count}}つ削除しようとしています',\n    aboutToPermanentlyDelete:\n      'あなたは永久に{{label}} <1>{{title}}</1>を削除しようとしています。よろしいですか？',\n    aboutToPermanentlyDeleteTrash:\n      'あなたはゴミ箱から<0>{{count}}</0> <1>{{label}}</1>を永久に削除しようとしています。よろしいですか？',\n    aboutToRestore: 'あなたは{{label}} <1>{{title}}</1>を復元しようとしています。よろしいですか？',\n    aboutToRestoreAsDraft:\n      'あなたは {{label}} <1>{{title}}</1> を下書きとして復元しようとしています。よろしいですか？',\n    aboutToRestoreAsDraftCount:\n      'あなたはまもなく、{{count}} {{label}}を下書きとして復元しようとしています。',\n    aboutToRestoreCount: 'あなたはまもなく{{count}} {{label}}を復元しようとしています。',\n    aboutToTrash:\n      'あなたは{{label}} <1>{{title}}</1>をゴミ箱に移動しようとしています。よろしいですか？',\n    aboutToTrashCount: 'あなたはまもなく{{count}} {{label}}をゴミ箱に移動しようとしています。',\n    addBelow: '下に追加',\n    addFilter: '絞り込みを追加',\n    adminTheme: '管理画面のテーマ',\n    all: 'すべて',\n    allCollections: 'すべてのコレクション',\n    allLocales: 'すべてのロケール',\n    and: 'かつ',\n    anotherUser: '別のユーザー',\n    anotherUserTakenOver: '別のユーザーがこのドキュメントの編集を引き継ぎました。',\n    applyChanges: '変更を適用する',\n    ascending: '昇順',\n    automatic: '自動設定',\n    backToDashboard: 'ダッシュボードに戻る',\n    cancel: 'キャンセル',\n    changesNotSaved: '未保存の変更があります。このまま画面を離れると内容が失われます。',\n    clear: 'クリア',\n    clearAll: 'すべてクリア',\n    close: '閉じる',\n    collapse: '閉じる',\n    collections: 'コレクション',\n    columns: '行の表示',\n    columnToSort: '並び替え対象の行',\n    confirm: '実行',\n    confirmCopy: 'コピーを確認します',\n    confirmDeletion: '削除の確認',\n    confirmDuplication: '複製の確認',\n    confirmMove: '移動を確認してください',\n    confirmReindex: 'すべての{{collections}}を再インデックスしますか？',\n    confirmReindexAll: 'すべてのコレクションを再インデックスしますか？',\n    confirmReindexDescription:\n      'これにより既存のインデックスが削除され、{{collections}}コレクション内のドキュメントが再インデックスされます。',\n    confirmReindexDescriptionAll:\n      'これにより既存のインデックスが削除され、すべてのコレクション内のドキュメントが再インデックスされます。',\n    confirmRestoration: '復元を確認してください',\n    copied: 'コピーしました',\n    copy: 'コピー',\n    copyField: 'フィールドをコピー',\n    copying: 'コピーする',\n    copyRow: '行をコピー',\n    copyWarning:\n      'あなたは{{label}} {{title}}の{{to}}を{{from}}で上書きしようとしています。よろしいですか？',\n    create: '作成',\n    created: '作成',\n    createdAt: '作成日',\n    createNew: '新規作成',\n    createNewLabel: '{{label}} を新規作成',\n    creating: '作成中',\n    creatingNewLabel: '{{label}} を新規作成しています',\n    currentlyEditing:\n      'このドキュメントを編集中です。あなたが引き継ぐと、編集を続けることができなくなり、未保存の変更が失われる可能性があります。',\n    custom: 'カスタム',\n    dark: 'ダークモード',\n    dashboard: 'ダッシュボード',\n    delete: '削除',\n    deleted: '削除されました',\n    deletedAt: '削除された時間',\n    deletedCountSuccessfully: '{{count}}つの{{label}}を正常に削除しました。',\n    deletedSuccessfully: '正常に削除されました。',\n    deletePermanently: 'ゴミ箱をスキップして完全に削除します',\n    deleting: '削除しています...',\n    depth: '深さ',\n    descending: '降順',\n    deselectAllRows: 'すべての行の選択を解除します',\n    document: 'ドキュメント',\n    documentIsTrashed: 'この{{label}}は廃棄され、読み取り専用です。',\n    documentLocked: 'ドキュメントがロックされました',\n    documents: 'ドキュメント',\n    duplicate: '複製',\n    duplicateWithoutSaving: '変更を保存せずに複製',\n    edit: '編集',\n    editAll: 'すべてを編集',\n    editedSince: 'から編集',\n    editing: '編集',\n    editingLabel_many: '{{count}}つの{{label}}を編集しています',\n    editingLabel_one: '{{count}}つの{{label}}を編集しています',\n    editingLabel_other: '{{count}}つの{{label}}を編集しています',\n    editingTakenOver: '編集が引き継がれました',\n    editLabel: '{{label}} を編集',\n    email: 'メールアドレス',\n    emailAddress: 'メールアドレス',\n    emptyTrash: 'ゴミ箱を空にする',\n    emptyTrashLabel: '{{label}}のゴミ箱を空にする',\n    enterAValue: '値を入力',\n    error: 'エラー',\n    errors: 'エラー',\n    exitLivePreview: 'ライブプレビューを終了する',\n    export: '輸出',\n    fallbackToDefaultLocale: 'デフォルトロケールへのフォールバック',\n    false: '偽',\n    filter: '絞り込み',\n    filters: '絞り込み',\n    filterWhere: '{{label}} の絞り込み',\n    globals: 'グローバル',\n    goBack: '戻る',\n    groupByLabel: '{{label}}でグループ化する',\n    import: '輸入',\n    isEditing: '編集中',\n    item: 'アイテム',\n    items: 'アイテム',\n    language: '言語',\n    lastModified: '最終更新',\n    leaveAnyway: 'すぐに画面を離れる',\n    leaveWithoutSaving: '内容が保存されていません',\n    light: 'ライトモード',\n    livePreview: 'プレビュー',\n    loading: 'ローディング中',\n    locale: 'ロケール',\n    locales: 'ロケール',\n    menu: 'メニュー',\n    moreOptions: 'より多くのオプション',\n    move: '移動します',\n    moveConfirm:\n      'あなたは{{count}} {{label}}を<1>{{destination}}</1>に移動しようとしています。よろしいですか？',\n    moveCount: '{{count}} {{label}}を移動してください',\n    moveDown: '下へ移動',\n    moveUp: '上へ移動',\n    moving: '移動中',\n    movingCount: '{{count}} {{label}}を移動します',\n    newPassword: '新しいパスワード',\n    next: '次',\n    no: 'いいえ',\n    noDateSelected: '日付が選択されていません',\n    noFiltersSet: '絞り込みが未設定です。',\n    noLabel: '<No {{label}}>',\n    none: 'なし',\n    noOptions: '選択肢なし',\n    noResults:\n      '{{label}} データが見つかりませんでした。データが存在しない、または、絞り込みに一致するものがありません。',\n    notFound: 'Not Found',\n    nothingFound: 'Nothing found',\n    noTrashResults: 'ゴミ箱に{{label}}はありません。',\n    noUpcomingEventsScheduled: '予定されているイベントはありません。',\n    noValue: '未設定',\n    of: '/',\n    only: 'のみ',\n    open: '開く',\n    or: 'または',\n    order: '表示順',\n    overwriteExistingData: '既存のフィールドデータを上書きする',\n    pageNotFound: 'ページが見つかりません',\n    password: 'パスワード',\n    pasteField: 'フィールドを貼り付け',\n    pasteRow: '行を貼り付け',\n    payloadSettings: 'Payload 設定',\n    permanentlyDelete: '永久に削除する',\n    permanentlyDeletedCountSuccessfully: '{{count}} {{label}}を正常に完全に削除しました。',\n    perPage: '表示件数: {{limit}}',\n    previous: '前の',\n    reindex: '再インデックス',\n    reindexingAll: 'すべての{{collections}}を再インデックスしています。',\n    remove: '削除',\n    rename: '名前を変更する',\n    reset: 'リセット',\n    resetPreferences: '設定をリセット',\n    resetPreferencesDescription: 'これにより、すべての設定がデフォルト設定にリセットされます。',\n    resettingPreferences: '設定をリセットしています。',\n    restore: '復元',\n    restoreAsPublished: '公開バージョンとして復元する',\n    restoredCountSuccessfully: '{{count}} {{label}} の復元に成功しました。',\n    restoring:\n      '以下はPayloadの文脈での原文の意味を尊重してください。以下に、特定の意味を持つ一般的なPayload用語のリストを示します。\\n    - コレクション: コレクションは、共通の構造と目的を共有する文書のグループです。コレクションは、Payload内のコンテンツを整理および管理するために使用されます。\\n    - フィールド: フィールドは、コレクション内の文',\n    row: '列',\n    rows: '列',\n    save: '保存',\n    saving: '保存しています...',\n    schedulePublishFor: '{{title}}の公開を予定する',\n    searchBy: '{{label}} で検索',\n    select: '選択してください。',\n    selectAll: 'すべての{{count}}つの{{label}}を選択',\n    selectAllRows: 'すべての行を選択します',\n    selectedCount: '{{count}}つの{{label}}を選択中',\n    selectLabel: '{{label}}を選択してください',\n    selectValue: '値を選択',\n    showAllLabel: 'すべての{{label}}を表示する',\n    sorryNotFound: '申し訳ありません。リクエストに対応する内容が見つかりませんでした。',\n    sort: '並び替え',\n    sortByLabelDirection: '{{label}}により並べ替え {{direction}}',\n    stayOnThisPage: 'この画面にとどまる',\n    submissionSuccessful: '送信が成功しました。',\n    submit: '送信',\n    submitting: '提出中...',\n    success: '成功',\n    successfullyCreated: '{{label}} が作成されました。',\n    successfullyDuplicated: '{{label}} が複製されました。',\n    successfullyReindexed:\n      '{{collections}} コレクションから {{total}} 件中 {{count}} 件のドキュメントが正常に再インデックスされました。',\n    takeOver: '引き継ぐ',\n    thisLanguage: 'Japanese',\n    time: '時間',\n    timezone: 'タイムゾーン',\n    titleDeleted: '{{label}} \"{{title}}\" が削除されました。',\n    titleRestored: '「{{label}}」\"{{title}}\" が正常に復元されました。',\n    titleTrashed: '{{label}} \"{{title}}\"がゴミ箱へ移動されました。',\n    trash: 'ゴミ',\n    trashedCountSuccessfully: '{{count}} {{label}}がゴミ箱に移動しました。',\n    true: '真実',\n    unauthorized: '未認証',\n    unsavedChanges: '保存されていない変更があります。続行する前に保存または破棄してください。',\n    unsavedChangesDuplicate: '未保存の変更があります。複製を続けますか？',\n    untitled: 'Untitled',\n    upcomingEvents: '今後のイベント',\n    updatedAt: '更新日',\n    updatedCountSuccessfully: '{{count}}つの{{label}}を正常に更新しました。',\n    updatedLabelSuccessfully: '{{label}}の更新に成功しました。',\n    updatedSuccessfully: '更新成功。',\n    updateForEveryone: '皆様への更新情報',\n    updating: '更新中',\n    uploading: 'アップロード中',\n    uploadingBulk: '{{current}} / {{total}} をアップロード中',\n    user: 'ユーザー',\n    username: 'ユーザーネーム',\n    users: 'ユーザー',\n    value: '値',\n    viewing: '閲覧',\n    viewReadOnly: '読み取り専用で表示',\n    welcome: 'ようこそ',\n    yes: 'はい',\n  },\n  localization: {\n    cannotCopySameLocale: '同じロケールにはコピーできません',\n    copyFrom: 'からコピーする',\n    copyFromTo: '{{from}}から{{to}}へのコピー',\n    copyTo: 'コピー先',\n    copyToLocale: 'ロケールにコピー',\n    localeToPublish: '公開する場所',\n    selectLocaleToCopy: 'コピーするロケールを選択してください',\n  },\n  operators: {\n    contains: '含む',\n    equals: '等しい',\n    exists: '存在す',\n    intersects: '交差する',\n    isGreaterThan: 'より大きい',\n    isGreaterThanOrEqualTo: '以上',\n    isIn: 'あります',\n    isLessThan: 'より小さい',\n    isLessThanOrEqualTo: '以下',\n    isLike: 'のような',\n    isNotEqualTo: '等しくない',\n    isNotIn: '入っていません',\n    isNotLike: 'ではない',\n    near: '近く',\n    within: '内で',\n  },\n  upload: {\n    addFile: 'ファイルを追加',\n    addFiles: 'ファイルを追加する',\n    bulkUpload: '一括アップロード',\n    crop: 'クロップ',\n    cropToolDescription:\n      '選択したエリアのコーナーをドラッグしたり、新たなエリアを描画したり、下記の値を調整してください。',\n    download: 'ダウンロード',\n    dragAndDrop: 'ファイルをドラッグ アンド ドロップする',\n    dragAndDropHere: 'または、このエリアにファイルをドラッグ & ドロップ',\n    editImage: '画像を編集する',\n    fileName: 'ファイル名',\n    fileSize: 'ファイル容量',\n    filesToUpload: 'アップロードするファイル',\n    fileToUpload: 'アップロードするファイル',\n    focalPoint: '焦点',\n    focalPointDescription: 'プレビュー上で焦点を直接ドラッグするか、下の値を調整してください。',\n    height: '高さ',\n    lessInfo: '詳細を隠す',\n    moreInfo: '詳細を表示',\n    noFile: 'ファイルなし',\n    pasteURL: 'URLを貼り付け',\n    previewSizes: 'プレビューサイズ',\n    selectCollectionToBrowse: '閲覧するコレクションを選択',\n    selectFile: 'ファイルを選択',\n    setCropArea: 'クロップエリアを設定する',\n    setFocalPoint: '焦点を設定する',\n    sizes: '容量',\n    sizesFor: '{{label}}のサイズ',\n    width: '横幅',\n  },\n  validation: {\n    emailAddress: '有効なメールアドレスを入力してください。',\n    enterNumber: '有効な数値を入力してください。',\n    fieldHasNo: '{{label}} が必要です。',\n    greaterThanMax: '{{value}}は許容最大{{label}}の{{max}}を超えています。',\n    invalidInput: '無効な入力値です。',\n    invalidSelection: '無効な選択です。',\n    invalidSelections: '次の無効な選択があります: ',\n    lessThanMin: '{{value}}は許容最小{{label}}の{{min}}未満です。',\n    limitReached: '制限に達しました、{{max}}個以上のアイテムを追加することはできません。',\n    longerThanMin: '{{minLength}} 文字以上にする必要があります。',\n    notValidDate: '\"{{value}}\" は有効な日付ではありません。',\n    required: '必須フィールドです。',\n    requiresAtLeast: '少なくとも {{count}} {{label}} 以上が必要です。',\n    requiresNoMoreThan: '最大で {{count}} {{label}} 以下にする必要があります。',\n    requiresTwoNumbers: '2つの数値が必要です。',\n    shorterThanMax: '{{maxLength}} 文字以下にする必要があります。',\n    timezoneRequired: 'タイムゾーンが必要です。',\n    trueOrFalse: '\"true\" または \"false\" の値にする必要があります。',\n    username:\n      '有効なユーザーネームを入力してください。文字、数字、ハイフン、ピリオド、アンダースコアを使用できます。',\n    validUploadID: '有効なアップロードIDではありません。',\n  },\n  version: {\n    type: 'タイプ',\n    aboutToPublishSelection: '選択中のすべての{{label}}を公開しようとしています。よろしいですか？',\n    aboutToRestore:\n      'この {{label}} データを {{versionDate}} 時点のバージョンに復元しようとしています。',\n    aboutToRestoreGlobal:\n      'グローバルな {{label}} データを {{versionDate}} 時点のバージョンに復元しようとしています。',\n    aboutToRevertToPublished:\n      'このデータの変更を公開時の状態に戻そうとしています。よろしいですか？',\n    aboutToUnpublish: 'このデータを非公開にしようとしています。よろしいですか？',\n    aboutToUnpublishSelection:\n      '選択したすべての{{label}}の公開を取り消そうとしています。よろしいですか？',\n    autosave: '自動保存',\n    autosavedSuccessfully: '自動保存に成功しました。',\n    autosavedVersion: '自動保存されたバージョン',\n    changed: '変更済み',\n    changedFieldsCount_one: '{{count}} 変更されたフィールド',\n    changedFieldsCount_other: '{{count}}つの変更されたフィールド',\n    compareVersion: 'バージョンを比較:',\n    compareVersions: 'バージョンを比較する',\n    comparingAgainst: '比較対象とする',\n    confirmPublish: '公開を確認する',\n    confirmRevertToSaved: '保存された状態に戻す確認',\n    confirmUnpublish: '非公開の確認',\n    confirmVersionRestoration: 'バージョン復元の確認',\n    currentDocumentStatus: '現在の {{docStatus}} データ',\n    currentDraft: '現行の草案',\n    currentlyPublished: '現在公開中',\n    currentlyViewing: '現在表示中',\n    currentPublishedVersion: '現在公開されているバージョン',\n    draft: 'ドラフト',\n    draftSavedSuccessfully: '下書きは正常に保存されました。',\n    lastSavedAgo: '{{distance}}前に最後に保存されました',\n    modifiedOnly: '変更済みのみ',\n    moreVersions: 'さらに多くのバージョン...',\n    noFurtherVersionsFound: 'その他のバージョンは見つかりませんでした。',\n    noRowsFound: '{{label}} は未設定です',\n    noRowsSelected: '選択された{{label}}はありません',\n    preview: 'プレビュー',\n    previouslyDraft: '以前はドラフトでした',\n    previouslyPublished: '以前に公開された',\n    previousVersion: '以前のバージョン',\n    problemRestoringVersion: 'このバージョンの復元に問題がありました。',\n    publish: '公開する',\n    publishAllLocales: 'すべてのロケールを公開する',\n    publishChanges: '変更内容を公開',\n    published: '公開済み',\n    publishIn: '{{locale}}で公開する',\n    publishing: '公開',\n    restoreAsDraft: '下書きとして復元',\n    restoredSuccessfully: '正常に復元されました。',\n    restoreThisVersion: 'このバージョンを復元',\n    restoring: '復元しています...',\n    reverting: '内容を戻しています...',\n    revertToPublished: '公開時の内容に戻す',\n    saveDraft: 'ドラフトを保存',\n    scheduledSuccessfully: '正常にスケジュールされました。',\n    schedulePublish: 'スケジュール公開',\n    selectLocales: '表示するロケールを選択',\n    selectVersionToCompare: '比較するバージョンを選択',\n    showingVersionsFor: '次のバージョンを表示します：',\n    showLocales: 'ロケールを表示:',\n    specificVersion: '特定のバージョン',\n    status: 'ステータス',\n    unpublish: '非公開',\n    unpublishing: '非公開中...',\n    version: 'バージョン',\n    versionAgo: '{{distance}}前',\n    versionCount_many: '{{count}} バージョンがあります',\n    versionCount_none: 'バージョンがありません',\n    versionCount_one: '{{count}} バージョンがあります',\n    versionCount_other: '{{count}}バージョンが見つかりました',\n    versionCreatedOn: '{{version}} 作成日時:',\n    versionID: 'バージョンID',\n    versions: 'バージョン',\n    viewingVersion: '表示バージョン: {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: '表示バージョン: グローバルな {{entityLabel}}',\n    viewingVersions: '表示バージョン: {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: '表示バージョン: グローバルな {{entityLabel}}',\n  },\n}\n\nexport const ja: Language = {\n  dateFNSKey: 'ja',\n  translations: jaTranslations,\n}\n"], "names": ["jaTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "ja", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BACE;QACFC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}