{"version": 3, "sources": ["../../src/languages/nb.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const nbTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Konto',\n    accountOfCurrentUser: 'Konto til nåværende bruker',\n    accountVerified: 'Konto bekreftet.',\n    alreadyActivated: 'Allerede aktivert',\n    alreadyLoggedIn: 'Allerede logget inn',\n    apiKey: 'API-nøkkel',\n    authenticated: 'Autentisert',\n    backToLogin: 'Tilbake til innlogging',\n    beginCreateFirstUser: 'Opprett din første bruker for å fortsette.',\n    changePassword: 'Endre passord',\n    checkYourEmailForPasswordReset:\n      'Hvis e-postadressen er knyttet til en konto, vil du motta instruksjoner for å tilbakestille passordet ditt snart. Vennligst sjekk spam- eller søppelpost-mappen din hvis du ikke ser e-posten i innboksen din.',\n    confirmGeneration: 'Bekreft generering',\n    confirmPassword: 'Bekreft passord',\n    createFirstUser: 'Opprett første bruker',\n    emailNotValid: 'E-posten er ikke gyldig',\n    emailOrUsername: 'E-post eller brukernavn',\n    emailSent: 'E-post sendt',\n    emailVerified: 'E-post bekreftet med hell.',\n    enableAPIKey: 'Aktiver API-nøkkel',\n    failedToUnlock: 'Kunne ikke låse opp',\n    forceUnlock: 'Tving opplåsing',\n    forgotPassword: 'Glemt passord',\n    forgotPasswordEmailInstructions:\n      'Skriv inn e-postadressen din nedenfor, og vi vil sende deg en e-post med instruksjoner om hvordan du tilbakestiller passordet ditt.',\n    forgotPasswordQuestion: 'Glemt passord?',\n    forgotPasswordUsernameInstructions:\n      'Vennligst skriv inn brukernavnet ditt nedenfor. Instruksjoner om hvordan du tilbakestiller passordet ditt vil bli sendt til e-postadressen som er knyttet til brukernavnet ditt.',\n    generate: 'Generer',\n    generateNewAPIKey: 'Generer ny API-nøkkel',\n    generatingNewAPIKeyWillInvalidate:\n      'Å generere en ny API-nøkkel vil <1>ugyldiggjøre</1> den forrige nøkkelen. Er du sikker på at du vil fortsette?',\n    lockUntil: 'Lås til',\n    logBackIn: 'Logg inn igjen',\n    loggedIn: 'For å logge inn med en annen bruker, må du <0>logge ut</0> først.',\n    loggedInChangePassword:\n      'For å endre passordet ditt, gå til <0>kontoen</0> din og endre passordet der.',\n    loggedOutInactivity: 'Du har blitt logget ut på grunn av inaktivitet.',\n    loggedOutSuccessfully: 'Du har blitt logget ut.',\n    loggingOut: 'Logger ut...',\n    login: 'Logg inn',\n    loginAttempts: 'Innloggingsforsøk',\n    loginUser: 'Logg inn bruker',\n    loginWithAnotherUser: 'For å logge inn med en annen bruker, må du <0>logge ut</0> først.',\n    logOut: 'Logg ut',\n    logout: 'Logg ut',\n    logoutSuccessful: 'Utlogging vellykket.',\n    logoutUser: 'Logg ut bruker',\n    newAccountCreated:\n      'En ny konto har blitt opprettet for deg på <a href=\"{{serverURL}}\">{{serverURL}}</a> Klikk på lenken nedenfor eller lim inn URLen i nettleseren din for å bekrefte e-postadressen din: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Etter at du har bekreftet e-postadressen din, kan du logge inn.',\n    newAPIKeyGenerated: 'Ny API-nøkkel generert.',\n    newPassword: 'Nytt passord',\n    passed: 'Autentisering bestått',\n    passwordResetSuccessfully: 'Passordet er tilbakestilt.',\n    resetPassword: 'Tilbakestill passord',\n    resetPasswordExpiration: 'Tilbakestill passordets utløpsdato',\n    resetPasswordToken: 'Tilbakestill passordet-token',\n    resetYourPassword: 'Tilbakestill passordet ditt',\n    stayLoggedIn: 'Forbli logget inn',\n    successfullyRegisteredFirstUser: 'Registrerte første bruker vellykket.',\n    successfullyUnlocked: 'Låst opp',\n    tokenRefreshSuccessful: 'Token-oppdatering vellykket.',\n    unableToVerify: 'Kunne ikke bekrefte',\n    username: 'Brukernavn',\n    usernameNotValid: 'Brukernavnet som er oppgitt er ikke gyldig.',\n    verified: 'Bekreftet',\n    verifiedSuccessfully: 'Bekreftet',\n    verify: 'Bekreft',\n    verifyUser: 'Bekreft bruker',\n    verifyYourEmail: 'Bekreft e-postadressen din',\n    youAreInactive:\n      'Du har ikke vært aktiv i en stund, og vil snart bli logget ut automatisk for din egen sikkerhet. Vil du forbli innlogget?',\n    youAreReceivingResetPassword:\n      'Du mottar denne e-posten fordi du (eller noen andre) har bedt om tilbakestilling av passordet til kontoen din. Klikk på lenken nedenfor, eller lim den inn i nettleseren din for å fullføre prosessen:',\n    youDidNotRequestPassword:\n      'Hvis du ikke har bedt om dette, kan du ignorere denne e-posten, og passordet ditt vil forbli uendret.',\n  },\n  error: {\n    accountAlreadyActivated: 'Denne kontoen er allerede aktivert.',\n    autosaving: 'Det oppstod et problem under automatisk lagring av dokumentet.',\n    correctInvalidFields: 'Korriger ugyldige felt.',\n    deletingFile: 'Det oppstod en feil under sletting av filen.',\n    deletingTitle:\n      'Det oppstod en feil under sletting av {{title}}. Sjekk tilkoblingen og prøv igjen.',\n    documentNotFound:\n      'Dokumentet med ID {{id}} kunne ikke bli funnet. Det kan ha blitt slettet eller aldri eksistert, eller du har kanskje ikke tilgang til det.',\n    emailOrPasswordIncorrect: 'E-postadressen eller passordet er feil.',\n    followingFieldsInvalid_one: 'Følgende felt er ugyldig:',\n    followingFieldsInvalid_other: 'Følgende felter er ugyldige:',\n    incorrectCollection: 'Ugyldig samling',\n    insufficientClipboardPermissions:\n      'Tilgang til utklippstavlen ble nektet. Sjekk utklippstavle-tillatelsene dine.',\n    invalidClipboardData: 'Ugyldige utklippstavldata.',\n    invalidFileType: 'Ugyldig filtype',\n    invalidFileTypeValue: 'Ugyldig filtype: {{value}}',\n    invalidRequestArgs: 'Ugyldige argumenter i forespørselen: {{args}}',\n    loadingDocument: 'Det oppstod et problem under lasting av dokumentet med ID {{id}}.',\n    localesNotSaved_one: 'Følgende språk kunne ikke lagres:',\n    localesNotSaved_other: 'Følgende språk kunne ikke lagres:',\n    logoutFailed: 'Utlogging mislyktes.',\n    missingEmail: 'Mangler e-postadresse.',\n    missingIDOfDocument: 'Mangler ID for dokumentet som skal oppdateres.',\n    missingIDOfVersion: 'Mangler ID for versjonen.',\n    missingRequiredData: 'Mangler påkrevd data.',\n    noFilesUploaded: 'Ingen filer ble lastet opp.',\n    noMatchedField: 'Ingen matchende felt funnet for \"{{label}}\"',\n    notAllowedToAccessPage: 'Du har ikke tilgang til denne siden.',\n    notAllowedToPerformAction: 'Du har ikke tillatelse til å utføre denne handlingen.',\n    notFound: 'Den forespurte ressursen ble ikke funnet.',\n    noUser: 'Ingen bruker',\n    previewing: 'Det oppstod et problem under forhåndsvisning av dokumentet.',\n    problemUploadingFile: 'Det oppstod et problem under opplasting av filen.',\n    restoringTitle:\n      'Det oppstod en feil under gjenoppretting av {{title}}. Vennligst sjekk din tilkobling og prøv igjen.',\n    tokenInvalidOrExpired: 'Token er enten ugyldig eller har utløpt.',\n    tokenNotProvided: 'Token ikke angitt.',\n    unableToCopy: 'Kan ikke kopiere.',\n    unableToDeleteCount: 'Kan ikke slette {{count}} av {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Feil ved reindeksering av samlingen {{collection}}. Operasjonen ble avbrutt.',\n    unableToUpdateCount: 'Kan ikke oppdatere {{count}} av {{total}} {{label}}.',\n    unauthorized: 'Uautorisert, du må være innlogget for å gjøre denne forespørselen.',\n    unauthorizedAdmin: 'Uautorisert, denne brukeren har ikke tilgang til kontrollpanelet.',\n    unknown: 'En ukjent feil har oppstått.',\n    unPublishingDocument: 'Det oppstod et problem under avpublisering av dokumentet.',\n    unspecific: 'En feil har oppstått.',\n    unverifiedEmail: 'Vennligst bekreft e-posten din før du logger inn.',\n    userEmailAlreadyRegistered: 'En bruker med den oppgitte e-posten er allerede registrert.',\n    userLocked: 'Denne brukeren er låst på grunn av for mange mislykkede innloggingsforsøk.',\n    usernameAlreadyRegistered: 'En bruker med det gitte brukernavnet er allerede registrert.',\n    usernameOrPasswordIncorrect: 'Brukernavnet eller passordet som ble oppgitt er feil.',\n    valueMustBeUnique: 'Verdien må være unik',\n    verificationTokenInvalid: 'Verifiseringskoden er ugyldig.',\n  },\n  fields: {\n    addLabel: 'Legg til {{label}}',\n    addLink: 'Legg til Lenke',\n    addNew: 'Legg til ny',\n    addNewLabel: 'Legg til ny {{label}}',\n    addRelationship: 'Legg til Relasjon',\n    addUpload: 'Legg til Opplasting',\n    block: 'blokk',\n    blocks: 'blokker',\n    blockType: 'Blokktype',\n    chooseBetweenCustomTextOrDocument:\n      'Velg mellom å skrive inn en egen tekst-URL eller å lenke til et annet dokument.',\n    chooseDocumentToLink: 'Velg et dokument å lenke til',\n    chooseFromExisting: 'Velg fra eksisterende',\n    chooseLabel: 'Velg {{label}}',\n    collapseAll: 'Skjul alle',\n    customURL: 'Egendefinert URL',\n    editLabelData: 'Rediger {{label}}-data',\n    editLink: 'Rediger lenke',\n    editRelationship: 'Rediger relasjon',\n    enterURL: 'Skriv inn en URL',\n    internalLink: 'Intern lenke',\n    itemsAndMore: '{{items}} og {{count}} flere',\n    labelRelationship: '{{label}}-relasjon',\n    latitude: 'Breddegrad',\n    linkedTo: 'Lenket til <0>{{label}}</0>',\n    linkType: 'Lenketype',\n    longitude: 'Lengdegrad',\n    newLabel: 'Ny {{label}}',\n    openInNewTab: 'Åpne i ny fane',\n    passwordsDoNotMatch: 'Passordene er ikke like.',\n    relatedDocument: 'Relatert dokument',\n    relationTo: 'Relasjon til',\n    removeRelationship: 'Fjern Forhold',\n    removeUpload: 'Fjern Opplasting',\n    saveChanges: 'Lagre endringer',\n    searchForBlock: 'Søk etter en blokk',\n    selectExistingLabel: 'Velg eksisterende {{label}}',\n    selectFieldsToEdit: 'Velg felt som skal redigeres',\n    showAll: 'Vis alle',\n    swapRelationship: 'Bytte Forhold',\n    swapUpload: 'Bytt Opplasting',\n    textToDisplay: 'Tekst som skal vises',\n    toggleBlock: 'Veksle blokk',\n    uploadNewLabel: 'Last opp ny {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Bla gjennom etter mappe',\n    byFolder: 'Etter mappe',\n    deleteFolder: 'Slett mappe',\n    folderName: 'Mappenavn',\n    folders: 'Mapper',\n    folderTypeDescription: 'Velg hvilken type samling dokumenter som skal tillates i denne mappen.',\n    itemHasBeenMoved: '{{title}} er flyttet til {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} er flyttet til rotmappen',\n    itemsMovedToFolder: '{{title}} flyttet til {{folderName}}',\n    itemsMovedToRoot: '{{title}} flyttet til rodmappen',\n    moveFolder: 'Flytt mappe',\n    moveItemsToFolderConfirmation:\n      'Du er i ferd med å flytte <1>{{count}} {{label}}</1> til <2>{{toFolder}}</2>. Er du sikker?',\n    moveItemsToRootConfirmation:\n      'Du er i ferd med å flytte <1>{{count}} {{label}}</1> til rotmappen. Er du sikker?',\n    moveItemToFolderConfirmation:\n      'Du er i ferd med å flytte <1>{{title}}</1> til <2>{{toFolder}}</2>. Er du sikker?',\n    moveItemToRootConfirmation:\n      'Du er i ferd med å flytte <1>{{title}}</1> til rodmappen. Er du sikker?',\n    movingFromFolder: 'Flytte {{title}} fra {{fromFolder}}',\n    newFolder: 'Ny mappe',\n    noFolder: 'Ingen mappe',\n    renameFolder: 'Endre mappenavn',\n    searchByNameInFolder: 'Søk etter navn i {{folderName}}',\n    selectFolderForItem: 'Velg mappe for {{title}}',\n  },\n  general: {\n    name: 'Navn',\n    aboutToDelete: 'Du er i ferd med å slette {{label}} <1>{{title}}</1>. Er du sikker?',\n    aboutToDeleteCount_many: 'Du er i ferd med å slette {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Du er i ferd med å slette {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Du er i ferd med å slette {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Du er i ferd med å permanent slette {{label}} <1>{{title}}</1>. Er du sikker?',\n    aboutToPermanentlyDeleteTrash:\n      'Du er i ferd med å permanent slette <0>{{count}}</0> <1>{{label}}</1> fra søppelkassen. Er du sikker?',\n    aboutToRestore: 'Du er i ferd med å gjenopprette {{label}} <1>{{title}}</1>. Er du sikker?',\n    aboutToRestoreAsDraft:\n      'Du er i ferd med å gjenopprette {{label}} <1>{{title}}</1> som en kladd. Er du sikker?',\n    aboutToRestoreAsDraftCount: 'Du er i ferd med å gjenopprette {{count}} {{label}} som utkast',\n    aboutToRestoreCount: 'Du er i ferd med å gjenopprette {{count}} {{label}}',\n    aboutToTrash: 'Du er i ferd med å flytte {{label}} <1>{{title}}</1> til søppel. Er du sikker?',\n    aboutToTrashCount: 'Du er i ferd med å flytte {{count}} {{label}} til søppelkurven',\n    addBelow: 'Legg til under',\n    addFilter: 'Legg til filter',\n    adminTheme: 'Admin-tema',\n    all: 'Alle',\n    allCollections: 'Alle samlinger',\n    allLocales: 'Alle språk',\n    and: 'Og',\n    anotherUser: 'En annen bruker',\n    anotherUserTakenOver: 'En annen bruker har tatt over redigeringen av dette dokumentet.',\n    applyChanges: 'Bruk endringer',\n    ascending: 'Stigende',\n    automatic: 'Automatisk',\n    backToDashboard: 'Tilbake til kontrollpanel',\n    cancel: 'Avbryt',\n    changesNotSaved:\n      'Endringene dine er ikke lagret. Hvis du forlater nå, vil du miste endringene dine.',\n    clear: 'Tydelig',\n    clearAll: 'Tøm alt',\n    close: 'Lukk',\n    collapse: 'Skjul',\n    collections: 'Samlinger',\n    columns: 'Kolonner',\n    columnToSort: 'Kolonne å sortere',\n    confirm: 'Bekreft',\n    confirmCopy: 'Bekreft kopi',\n    confirmDeletion: 'Bekreft sletting',\n    confirmDuplication: 'Bekreft duplisering',\n    confirmMove: 'Bekreft flytting',\n    confirmReindex: 'Reindekser alle {{collections}}?',\n    confirmReindexAll: 'Reindekser alle samlinger?',\n    confirmReindexDescription:\n      'Dette vil fjerne eksisterende indekser og reindeksere dokumentene i {{collections}}-samlingene.',\n    confirmReindexDescriptionAll:\n      'Dette vil fjerne eksisterende indekser og reindeksere dokumentene i alle samlinger.',\n    confirmRestoration: 'Bekreft gjenoppretting',\n    copied: 'Kopiert',\n    copy: 'Kopiér',\n    copyField: 'Kopier felt',\n    copying: 'Kopiering',\n    copyRow: 'Kopier rad',\n    copyWarning:\n      'Du er i ferd med å overskrive {{to}} med {{from}} for {{label}} {{title}}. Er du sikker?',\n    create: 'Opprett',\n    created: 'Opprettet',\n    createdAt: 'Opprettet',\n    createNew: 'Opprett ny',\n    createNewLabel: 'Opprett ny {{label}}',\n    creating: 'Oppretter',\n    creatingNewLabel: 'Oppretter ny {{label}}',\n    currentlyEditing:\n      'redigerer for øyeblikket dette dokumentet. Hvis du tar over, blir de blokkert fra å fortsette å redigere, og de kan også miste ulagrede endringer.',\n    custom: 'Tilpasset',\n    dark: 'Mørk',\n    dashboard: 'Kontrollpanel',\n    delete: 'Slett',\n    deleted: 'Slettet',\n    deletedAt: 'Slettet kl.',\n    deletedCountSuccessfully: 'Slettet {{count}} {{label}}.',\n    deletedSuccessfully: 'Slettet.',\n    deletePermanently: 'Hopp over søppel og slett permanent',\n    deleting: 'Sletter...',\n    depth: 'Dybde',\n    descending: 'Synkende',\n    deselectAllRows: 'Fjern markeringen fra alle rader',\n    document: 'Dokument',\n    documentIsTrashed: 'Denne {{label}} er søppel og er skrivebeskyttet.',\n    documentLocked: 'Låst dokument',\n    documents: 'Dokumenter',\n    duplicate: 'Dupliser',\n    duplicateWithoutSaving: 'Dupliser uten å lagre endringer',\n    edit: 'Redigere',\n    editAll: 'Rediger alle',\n    editedSince: 'Redigert siden',\n    editing: 'Redigerer',\n    editingLabel_many: 'Redigerer {{count}} {{label}}',\n    editingLabel_one: 'Redigerer {{count}} {{label}}',\n    editingLabel_other: 'Redigerer {{count}} {{label}}',\n    editingTakenOver: 'Redigering overtatt',\n    editLabel: 'Rediger {{label}}',\n    email: 'E-post',\n    emailAddress: 'E-postadresse',\n    emptyTrash: 'Tøm søppelkassen',\n    emptyTrashLabel: 'Tøm {{label}} søppel',\n    enterAValue: 'Skriv inn en verdi',\n    error: 'Feil',\n    errors: 'Feil',\n    exitLivePreview: 'Avslutt live forhåndsvisning',\n    export: 'Eksport',\n    fallbackToDefaultLocale: 'Tilbakestilling til standard språk',\n    false: 'Falsk',\n    filter: 'Filtrer',\n    filters: 'Filter',\n    filterWhere: 'Filtrer {{label}} der',\n    globals: 'Globale variabler',\n    goBack: 'Gå tilbake',\n    groupByLabel: 'Grupper etter {{label}}',\n    import: 'Import',\n    isEditing: 'redigerer',\n    item: 'vare',\n    items: 'elementer',\n    language: 'Språk',\n    lastModified: 'Sist endret',\n    leaveAnyway: 'Forlat likevel',\n    leaveWithoutSaving: 'Forlat uten å lagre',\n    light: 'Lys',\n    livePreview: 'Forhåndsvisning',\n    loading: 'Laster',\n    locale: 'Språk',\n    locales: 'Språk',\n    menu: 'Meny',\n    moreOptions: 'Flere alternativer',\n    move: 'Flytt',\n    moveConfirm:\n      'Du er i ferd med å flytte {{count}} {{label}} til <1>{{destination}}</1>. Er du sikker?',\n    moveCount: 'Flytt {{count}} {{label}}',\n    moveDown: 'Flytt ned',\n    moveUp: 'Flytt opp',\n    moving: 'Flytting',\n    movingCount: 'Flytter {{count}} {{label}}',\n    newPassword: 'Nytt passord',\n    next: 'Neste',\n    no: 'Nei',\n    noDateSelected: 'Ingen dato valgt',\n    noFiltersSet: 'Ingen filtre satt',\n    noLabel: '<Ingen {{label}}>',\n    none: 'Ingen',\n    noOptions: 'Ingen alternativer',\n    noResults:\n      'Ingen {{label}} funnet. Enten finnes det ingen {{label}} enda eller ingen matcher filterne du har spesifisert ovenfor.',\n    notFound: 'Ikke funnet',\n    nothingFound: 'Ingenting funnet',\n    noTrashResults: 'Ingen {{label}} i søppelkassen.',\n    noUpcomingEventsScheduled: 'Ingen kommende hendelser planlagt.',\n    noValue: 'Ingen verdi',\n    of: 'av',\n    only: 'Bare',\n    open: 'Åpne',\n    or: 'Eller',\n    order: 'Rekkefølge',\n    overwriteExistingData: 'Overskriv eksisterende feltdata',\n    pageNotFound: 'Siden ble ikke funnet',\n    password: 'Passord',\n    pasteField: 'Lim inn felt',\n    pasteRow: 'Lim inn rad',\n    payloadSettings: 'Payload-innstillinger',\n    permanentlyDelete: 'Permanent slett',\n    permanentlyDeletedCountSuccessfully: 'Permanent slettet {{count}} {{label}} med suksess.',\n    perPage: 'Per side: {{limit}}',\n    previous: 'Forrige',\n    reindex: 'Reindekser',\n    reindexingAll: 'Reindekserer alle {{collections}}.',\n    remove: 'Fjern',\n    rename: 'Endre navn',\n    reset: 'Tilbakestill',\n    resetPreferences: 'Tilbakestill preferanser',\n    resetPreferencesDescription:\n      'Dette vil tilbakestille alle preferansene dine til standardinnstillingene.',\n    resettingPreferences: 'Tilbakestiller preferanser.',\n    restore: 'Gjenopprett',\n    restoreAsPublished: 'Gjenopprett som publisert versjon',\n    restoredCountSuccessfully: 'Gjenopprettet {{count}} {{label}} vellykket.',\n    restoring:\n      'Respekter betydningen av den opprinnelige teksten innenfor konteksten av Payload. Her er en liste over vanlige Payload-uttrykk som har veldig spesifikke betydninger:\\n    - Samling: En samling er en gruppe dokumenter som deler en felles struktur og formål. Samlinger brukes til å organisere og håndtere innhold i Payload.\\n    - Felt: Et felt er et bestemt stykke data innenfor et dokument i en samling. Felt definerer strukturen og typen data som kan lagres i et dokument.\\n    - Dokument: Et dokument er en individuell post innen',\n    row: 'Rad',\n    rows: 'Rader',\n    save: 'Lagre',\n    saving: 'Lagrer...',\n    schedulePublishFor: 'Planlegg publisering for {{title}}',\n    searchBy: 'Søk etter {{label}}',\n    select: 'Velg',\n    selectAll: 'Velg alle {{count}} {{label}}',\n    selectAllRows: 'Velg alle rader',\n    selectedCount: '{{count}} {{label}} valgt',\n    selectLabel: 'Velg {{label}}',\n    selectValue: 'Velg en verdi',\n    showAllLabel: 'Vis alle {{label}}',\n    sorryNotFound: 'Beklager, det er ingenting som samsvarer med forespørselen din.',\n    sort: 'Sortér',\n    sortByLabelDirection: 'Sorter etter {{label}} {{direction}}',\n    stayOnThisPage: 'Bli på denne siden',\n    submissionSuccessful: 'Innsending vellykket.',\n    submit: 'Send inn',\n    submitting: 'Innsending...',\n    success: 'Suksess',\n    successfullyCreated: '{{label}} ble opprettet.',\n    successfullyDuplicated: '{{label}} ble duplisert.',\n    successfullyReindexed:\n      'Vellykket reindeksering av {{count}} av {{total}} dokumenter fra {{collections}} samlinger.',\n    takeOver: 'Ta over',\n    thisLanguage: 'Norsk',\n    time: 'Tid',\n    timezone: 'Tidssone',\n    titleDeleted: '{{label}} \"{{title}}\" ble slettet.',\n    titleRestored: '{{label}} \"{{title}}\" ble gjenopprettet.',\n    titleTrashed: '{{label}} \"{{title}}\" flyttet til søppel.',\n    trash: 'Søppel',\n    trashedCountSuccessfully: '{{count}} {{label}} flyttet til søppel.',\n    true: 'Sann',\n    unauthorized: 'Ikke autorisert',\n    unsavedChanges: 'Du har ulagrede endringer. Lagre eller forkast før du fortsetter.',\n    unsavedChangesDuplicate: 'Du har ulagrede endringer. Vil du fortsette å duplisere?',\n    untitled: 'Uten tittel',\n    upcomingEvents: 'Kommende hendelser',\n    updatedAt: 'Oppdatert',\n    updatedCountSuccessfully: 'Oppdaterte {{count}} {{label}} vellykket.',\n    updatedLabelSuccessfully: 'Oppdatert {{label}} vellykket.',\n    updatedSuccessfully: 'Oppdatert.',\n    updateForEveryone: 'Oppdatering for alle',\n    updating: 'Oppdatering',\n    uploading: 'Opplasting',\n    uploadingBulk: 'Laster opp {{current}} av {{total}}',\n    user: 'Bruker',\n    username: 'Brukernavn',\n    users: 'Brukere',\n    value: 'Verdi',\n    viewing: 'Visning',\n    viewReadOnly: 'Vis skrivebeskyttet',\n    welcome: 'Velkommen',\n    yes: 'Ja',\n  },\n  localization: {\n    cannotCopySameLocale: 'Kan ikke kopiere til samme språk',\n    copyFrom: 'Kopier fra',\n    copyFromTo: 'Kopiering fra {{fra}} til {{til}}',\n    copyTo: 'Kopier til',\n    copyToLocale: 'Kopiere til språk',\n    localeToPublish: 'Språk å publisere',\n    selectLocaleToCopy: 'Velg språk for å kopiere',\n  },\n  operators: {\n    contains: 'inneholder',\n    equals: 'lik',\n    exists: 'eksisterer',\n    intersects: 'krysser',\n    isGreaterThan: 'er større enn',\n    isGreaterThanOrEqualTo: 'er større enn eller lik',\n    isIn: 'er i',\n    isLessThan: 'er mindre enn',\n    isLessThanOrEqualTo: 'er mindre enn eller lik',\n    isLike: 'er som',\n    isNotEqualTo: 'er ikke lik',\n    isNotIn: 'er ikke med',\n    isNotLike: 'er ikke lik',\n    near: 'nær',\n    within: 'innen',\n  },\n  upload: {\n    addFile: 'Legg til fil',\n    addFiles: 'Legg til filer',\n    bulkUpload: 'Bulk opplasting',\n    crop: 'Beskjær',\n    cropToolDescription:\n      'Dra hjørnene av det valgte området, tegn et nytt område eller juster verdiene nedenfor.',\n    download: 'Last ned',\n    dragAndDrop: 'Dra og slipp en fil',\n    dragAndDropHere: 'eller dra og slipp en fil her',\n    editImage: 'Rediger bilde',\n    fileName: 'Filnavn',\n    fileSize: 'Filstørrelse',\n    filesToUpload: 'Filer til opplasting',\n    fileToUpload: 'Fil til opplasting',\n    focalPoint: 'Fokuspunkt',\n    focalPointDescription:\n      'Dra fokuspunktet direkte på forhåndsvisningen eller juster verdiene nedenfor.',\n    height: 'Høyde',\n    lessInfo: 'Mindre info',\n    moreInfo: 'Mer info',\n    noFile: 'Ingen fil',\n    pasteURL: 'Lim inn URL',\n    previewSizes: 'Forhåndsvisningsstørrelser',\n    selectCollectionToBrowse: 'Velg en samling å bla i',\n    selectFile: 'Velg en fil',\n    setCropArea: 'Angi beskjæringsområde',\n    setFocalPoint: 'Angi fokuspunkt',\n    sizes: 'Størrelser',\n    sizesFor: 'Størrelser for {{label}}',\n    width: 'Bredde',\n  },\n  validation: {\n    emailAddress: 'Vennligst skriv inn en gyldig e-postadresse.',\n    enterNumber: 'Vennligst skriv inn et gyldig tall.',\n    fieldHasNo: 'Dette feltet har ingen {{label}}',\n    greaterThanMax: '{{value}} er større enn den tillatte maksimale {{label}} på {{max}}.',\n    invalidInput: 'Dette feltet har en ugyldig inndata.',\n    invalidSelection: 'Dette feltet har en ugyldig utvalg.',\n    invalidSelections: 'Dette feltet har følgende ugyldige utvalg:',\n    lessThanMin: '{{value}} er mindre enn den tillatte minimale {{label}} på {{min}}.',\n    limitReached: 'Begrensning nådd, bare {{max}} elementer kan legges til.',\n    longerThanMin: 'Denne verdien må være lengre enn minimumslengden på {{minLength}} tegn.',\n    notValidDate: '\"{{value}}\" er ikke en gyldig dato.',\n    required: 'Dette feltet er påkrevd.',\n    requiresAtLeast: 'Dette feltet krever minst {{count}} {{label}}.',\n    requiresNoMoreThan: 'Dette feltet krever maksimalt {{count}} {{label}}.',\n    requiresTwoNumbers: 'Dette feltet krever to tall.',\n    shorterThanMax: 'Denne verdien må være kortere enn maksimal lengde på {{maxLength}} tegn.',\n    timezoneRequired: 'En tidssone er nødvendig.',\n    trueOrFalse: 'Dette feltet kan bare være likt true eller false.',\n    username:\n      'Vennligst oppgi et gyldig brukernavn. Kan inneholde bokstaver, nummer, bindestreker, punktum og understrek.',\n    validUploadID: 'Dette feltet er ikke en gyldig opplastings-ID.',\n  },\n  version: {\n    type: 'Type',\n    aboutToPublishSelection:\n      'Du er i ferd med å publisere alle {{label}} i utvalget. Er du sikker?',\n    aboutToRestore:\n      'Du er i ferd med å gjenopprette denne {{label}} dokumentet til tilstanden det var i på {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Du er i ferd med å gjenopprette den globale variabelen {{label}} til tilstanden det var i på {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Du er i ferd med å tilbakestille endringene i dette dokumentet til den publiserte tilstanden. Er du sikker?',\n    aboutToUnpublish: 'Du er i ferd med å avpublisere dette dokumentet. Er du sikker?',\n    aboutToUnpublishSelection:\n      'Du er i ferd med å oppheve publiseringen av alle {{label}} i utvalget. Er du sikker?',\n    autosave: 'Lagre automatisk',\n    autosavedSuccessfully: 'Lagret automatisk.',\n    autosavedVersion: 'Automatisk lagret versjon',\n    changed: 'Endret',\n    changedFieldsCount_one: '{{count}} endret felt',\n    changedFieldsCount_other: '{{count}} endrede felt',\n    compareVersion: 'Sammenlign versjon mot:',\n    compareVersions: 'Sammenlign Versjoner',\n    comparingAgainst: 'Sammenligner mot',\n    confirmPublish: 'Bekreft publisering',\n    confirmRevertToSaved: 'Bekreft tilbakestilling til lagret',\n    confirmUnpublish: 'Bekreft avpublisering',\n    confirmVersionRestoration: 'Bekreft versjon-gjenoppretting',\n    currentDocumentStatus: 'Nåværende {{docStatus}} dokument',\n    currentDraft: 'Nåværende utkast',\n    currentlyPublished: 'For tiden publisert',\n    currentlyViewing: 'Ser på for øyeblikket',\n    currentPublishedVersion: 'Nåværende Publiserte Versjon',\n    draft: 'Utkast',\n    draftSavedSuccessfully: 'Utkast lagret.',\n    lastSavedAgo: 'Sist lagret {{distance}} siden',\n    modifiedOnly: 'Endret kun',\n    moreVersions: 'Flere versjoner...',\n    noFurtherVersionsFound: 'Ingen flere versjoner funnet',\n    noRowsFound: 'Ingen {{label}} funnet',\n    noRowsSelected: 'Ingen {{label}} valgt',\n    preview: 'Forhåndsvisning',\n    previouslyDraft: 'Tidligere et utkast',\n    previouslyPublished: 'Tidligere Publisert',\n    previousVersion: 'Tidligere versjon',\n    problemRestoringVersion: 'Det oppstod et problem med gjenoppretting av denne versjonen',\n    publish: 'Publisere',\n    publishAllLocales: 'Publiser alle språk',\n    publishChanges: 'Publiser endringer',\n    published: 'Publisert',\n    publishIn: 'Publiser på {{locale}}',\n    publishing: 'Publisering',\n    restoreAsDraft: 'Gjenopprett som utkast',\n    restoredSuccessfully: 'Gjenopprettet.',\n    restoreThisVersion: 'Gjenopprett denne versjonen',\n    restoring: 'Gjenoppretter...',\n    reverting: 'Tilbakestiller...',\n    revertToPublished: 'Tilbakestill til publisert',\n    saveDraft: 'Lagre utkast',\n    scheduledSuccessfully: 'Planlagt vellykket.',\n    schedulePublish: 'Planlegg Publisering',\n    selectLocales: 'Velg språk å vise',\n    selectVersionToCompare: 'Velg en versjon å sammenligne',\n    showingVersionsFor: 'Viser versjoner for:',\n    showLocales: 'Vis språk:',\n    specificVersion: 'Spesifikk versjon',\n    status: 'Status',\n    unpublish: 'Avpubliser',\n    unpublishing: 'Avpubliserer...',\n    version: 'Versjon',\n    versionAgo: '{{distance}} siden',\n    versionCount_many: '{{count}} versjoner funnet',\n    versionCount_none: 'Ingen versjoner funnet',\n    versionCount_one: '{{count}} versjon funnet',\n    versionCount_other: '{{count}} versjoner funnet',\n    versionCreatedOn: '{{version}} opprettet:',\n    versionID: 'Versjons-ID',\n    versions: 'Versjoner',\n    viewingVersion: 'Viser versjon for {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Viser versjon for den globale variabelen {{entityLabel}}',\n    viewingVersions: 'Viser versjoner for {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Viser versjoner for den globale variabelen {{entityLabel}}',\n  },\n}\n\nexport const nb: Language = {\n  dateFNSKey: 'nb',\n  translations: nbTranslations,\n}\n"], "names": ["nbTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "nb", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}