export const trTranslations = {
    authentication: {
        account: 'Hesa<PERSON>',
        accountOfCurrentUser: '<PERSON><PERSON> anki kullanıcının hesabı',
        accountVerified: 'Hesap başarıyla doğrulandı.',
        alreadyActivated: 'Hesap zaten etkinleştirildi',
        alreadyLoggedIn: 'Hesaba zaten giriş yapıldı',
        apiKey: 'API Anahtarı',
        authenticated: 'Doğrulandı',
        backToLogin: 'G<PERSON><PERSON> ekranına geri dön',
        beginCreateFirstUser: 'Başlamak için ilk kullanıcı hesabını oluşturun.',
        changePassword: 'Parolayı Değiştir',
        checkYourEmailForPasswordReset: 'E-posta adresi bir hesapla ilişkiliyse, şifrenizi sıfırlama talimatlarınızı kısa süre içerisinde alacaksınız. E-postayı gelen kutunuzda görmüyorsanız, lütfen spam veya gereksiz posta klasörünüzü kontrol edin.',
        confirmGeneration: 'Oluştumayı Onayla',
        confirmPassword: 'Parolayı Onayla',
        createFirstUser: 'İlk kullanıcı oluştur',
        emailNotValid: 'Girilen e-posta geçersiz',
        emailOrUsername: 'E-posta veya Kullanıcı Adı',
        emailSent: 'E-posta gönderildi',
        emailVerified: 'E-posta başarıyla doğrulandı.',
        enableAPIKey: 'Api anahtarını etkinleştir',
        failedToUnlock: 'Hesabı aktifleştirme başarısız oldu',
        forceUnlock: 'Hesabı Etkinleştir',
        forgotPassword: 'Parolamı Unuttum',
        forgotPasswordEmailInstructions: 'Lütfen e-posta adresinizi aşağıdaki alana girin. Parolanızı nasıl sıfırlayacağınızı gösteren bir e-posta adresi alacaksınız.',
        forgotPasswordQuestion: 'Parolanızı mı unuttunuz?',
        forgotPasswordUsernameInstructions: 'Lütfen kullanıcı adınızı aşağıya girin. Şifrenizi nasıl sıfırlayacağınıza dair talimatlar, kullanıcı adınızla ilişkilendirilmiş e-posta adresine gönderilecektir.',
        generate: 'Oluştur',
        generateNewAPIKey: 'Yeni bir API anahtarı oluştur',
        generatingNewAPIKeyWillInvalidate: 'Yeni bir API anahtarı oluşturmak önceckini <1>geçersiz kılacaktır</1>. Devam etmek istiyor musunuz?',
        lockUntil: 'Lock Until',
        logBackIn: 'Tekrar giriş yapın',
        loggedIn: 'Başka bir kullanıcı hesabıyla giriş yapabilmek için önce <0>çıkış yapmanız</0> gerekmektedir.',
        loggedInChangePassword: 'Parolanızı değiştirmek için <0>hesabınıza</0> gidebilirsiniz.',
        loggedOutInactivity: 'Uzun süre işlem yapmadığınız için oturumunuz kapatıldı.',
        loggedOutSuccessfully: 'Başarıyla çıkış yaptınız.',
        loggingOut: 'Çıkış yapılıyor...',
        login: 'Giriş',
        loginAttempts: 'Giriş Denemeleri',
        loginUser: 'Kullanıcı girişi',
        loginWithAnotherUser: 'Başka bir kullanıcı hesabıyla giriş yapmak için önce <0>çıkış</0> yapmalısınız.',
        logOut: 'Çıkış',
        logout: 'Çıkış',
        logoutSuccessful: 'Çıkış başarılı.',
        logoutUser: 'Kullanıcıyı çıkış yapmaya zorla',
        newAccountCreated: '<0>{{serverURL}}</0> sitesinde adınıza yeni bir hesap oluşturuldu. E-postanızı doğrulamak için bağlantıya tıklayabilirsiniz: <1>{{verificationURL}}</1><br> E-postanızı doğruladıktan sonra siteye hesap bilgilerinizle giriş yapabilirsiniz.',
        newAPIKeyGenerated: 'Yeni API anahtarı oluşturuldu.',
        newPassword: 'Yeni Parola',
        passed: 'Doğrulama Başarılı',
        passwordResetSuccessfully: 'Parola başarıyla sıfırlandı.',
        resetPassword: 'Parolayı Sıfırla',
        resetPasswordExpiration: 'Parola Geçerlik Süresini Sıfırla',
        resetPasswordToken: 'Parola tokenini sıfırla',
        resetYourPassword: 'Parolanızı Sıfırlayın',
        stayLoggedIn: 'Oturumu açık tut',
        successfullyRegisteredFirstUser: 'İlk kullanıcının kaydı başarıyla tamamlandı.',
        successfullyUnlocked: 'Hesabın kilidi başarıyla açıldı',
        tokenRefreshSuccessful: 'Token yenileme başarılı.',
        unableToVerify: 'Doğrulama başarısız',
        username: 'Kullanıcı Adı',
        usernameNotValid: 'Sağlanan kullanıcı adı geçerli değil.',
        verified: 'Doğrulandı',
        verifiedSuccessfully: 'Hesap başarıyla doğrulandı',
        verify: 'Doğrula',
        verifyUser: 'Kullanıcıyı doğrula',
        verifyYourEmail: 'E-postanızı doğrulayın',
        youAreInactive: 'Bir süredir işlem yapmadığınız için yakında oturumunuz kapatılacak. Oturumunuzun açık kalmasını istiyor musunuz?',
        youAreReceivingResetPassword: 'Siz veya bir başkası hesabınızın parolasını sıfırlama isteğinde bulunduğu için bu e-postayı alıyorsunuz. İşlemi tamamlamak için lütfen aşağıdaki bağlantıya tıklayın veya bağlantı adresini tarayıcınızın adres yazma bölümüne kopyalayın.',
        youDidNotRequestPassword: 'Eğer bu işlemi siz gerçekleştirmediyseniz bu e-postayı görmezden gelebilirsiniz.'
    },
    error: {
        accountAlreadyActivated: 'Hesap zaten etkinleştirildi.',
        autosaving: 'Otomatik kaydetme başarısız oldu',
        correctInvalidFields: 'Lütfen geçersiz alanları düzeltin.',
        deletingFile: 'Dosya silinirken bir hatayla karşılaşıldı.',
        deletingTitle: '{{title}} silinirken bir sorun yaşandı. Lütfen internet bağlantınızı kontrol edip tekrar deneyin.',
        documentNotFound: "ID'si {{id}} olan belge bulunamadı. Silinmiş olabilir, hiç var olmamış olabilir veya belgeye erişiminiz olmayabilir.",
        emailOrPasswordIncorrect: 'Girilen e-posta veya parola hatalı',
        followingFieldsInvalid_one: 'Lütfen geçersiz alanı düzeltin:',
        followingFieldsInvalid_other: 'Lütfen geçersiz alanları düzeltin:',
        incorrectCollection: 'Hatalı koleksiyon',
        insufficientClipboardPermissions: 'Pano erişim reddedildi. Lütfen pano izinlerinizi kontrol edin.',
        invalidClipboardData: 'Geçersiz pano verisi.',
        invalidFileType: 'Geçersiz dosya türü',
        invalidFileTypeValue: 'Geçersiz dosya türü: {{value}}',
        invalidRequestArgs: 'İstek içerisinde geçersiz argümanlar iletildi: {{args}}',
        loadingDocument: "{{id}} ID'ye sahip döküman yüklenirken bir sorun oluştu.",
        localesNotSaved_one: 'Aşağıdaki yerel ayar kaydedilemedi:',
        localesNotSaved_other: 'Aşağıdaki yerel ayarlar kaydedilemedi:',
        logoutFailed: 'Çıkış başarısız oldu.',
        missingEmail: 'E-posta adresi girilmedi.',
        missingIDOfDocument: "Güncellenecek döküman ID'si eksik.",
        missingIDOfVersion: "Versiyon ID'si geçersiz.",
        missingRequiredData: 'Gerekli veri eksik.',
        noFilesUploaded: 'Yüklenen dosya yok',
        noMatchedField: '"{{label}}" ile eşleşen alan bulunamadı.',
        notAllowedToAccessPage: 'Bu sayfaya erişim izniniz yok.',
        notAllowedToPerformAction: 'Bu işlemi gerçekleştirmek için izniniz yok.',
        notFound: 'Sayfa bulunamadı.',
        noUser: 'Kullanıcı yok',
        previewing: 'Önizleme başarısız oldu',
        problemUploadingFile: 'Dosya yüklenirken bir sorun oluştu.',
        restoringTitle: '{{title}} geri yüklenirken bir hata oluştu. Lütfen bağlantınızı kontrol edin ve tekrar deneyin.',
        tokenInvalidOrExpired: 'Geçersiz veya süresi dolmuş token.',
        tokenNotProvided: 'Jeton sağlanmadı.',
        unableToCopy: 'Kopyalanamıyor.',
        unableToDeleteCount: '{{total}} {{label}} içinden {{count}} silinemiyor.',
        unableToReindexCollection: '{{collection}} koleksiyonunun yeniden indekslenmesinde hata oluştu. İşlem durduruldu.',
        unableToUpdateCount: '{{total}} {{label}} içinden {{count}} güncellenemiyor.',
        unauthorized: 'Bu işlemi gerçekleştirmek için lütfen giriş yapın.',
        unauthorizedAdmin: 'Bu kullanıcı yönetici paneline erişim iznine sahip değil.',
        unknown: 'Bilinmeyen bir hata oluştu.',
        unPublishingDocument: 'Geçerli döküman yayından kaldırılırken bir sorun oluştu.',
        unspecific: 'Bir hata oluştu.',
        unverifiedEmail: 'Giriş yapmadan önce e-posta adresinizi doğrulayın.',
        userEmailAlreadyRegistered: 'Verilen e-posta ile zaten kayıtlı bir kullanıcı var.',
        userLocked: 'Hesabınız hatalı giriş denemeleri yüzünden geçici olarak kilitlendi. Lütfen daha sonra tekrar deneyin.',
        usernameAlreadyRegistered: 'Verilen kullanıcı adına sahip bir kullanıcı zaten kayıtlı.',
        usernameOrPasswordIncorrect: 'Sağlanan kullanıcı adı veya şifre yanlış.',
        valueMustBeUnique: 'Değer benzersiz olmalıdır',
        verificationTokenInvalid: 'Doğrulama tokeni geçersiz.'
    },
    fields: {
        addLabel: '{{label}} ekle',
        addLink: 'Link Ekle',
        addNew: 'Yeni',
        addNewLabel: 'Yeni {{label}}',
        addRelationship: 'İlişki Ekle',
        addUpload: 'Yükleme Ekle',
        block: 'blok',
        blocks: 'blok',
        blockType: 'Blok tipi',
        chooseBetweenCustomTextOrDocument: 'Choose between entering a custom text URL or linking to another document.',
        chooseDocumentToLink: 'Bağlantı verilecek bir döküman seçin.',
        chooseFromExisting: 'Varolanlardan seç',
        chooseLabel: '{{label}} seç',
        collapseAll: 'Tümünü daralt',
        customURL: 'Özel URL',
        editLabelData: '{{label}} düzenle',
        editLink: 'Bağlantıyı Düzenle',
        editRelationship: 'İlişkiyi Ekle',
        enterURL: 'Bir URL girin',
        internalLink: 'İç bağlantı',
        itemsAndMore: '{{items}} and {{count}} more',
        labelRelationship: '{{label}} Relationship',
        latitude: 'Enlem',
        linkedTo: '<0>label</0> için bağlantı verildi',
        linkType: 'Bağlantı türü',
        longitude: 'Boylam',
        newLabel: 'Yeni {{label}}',
        openInNewTab: 'Yeni sekmede aç',
        passwordsDoNotMatch: 'Parolalar eşleşmiyor.',
        relatedDocument: 'İlişkili döküman',
        relationTo: 'Relation To',
        removeRelationship: 'İlişkiyi Kaldır',
        removeUpload: 'Dosyayı Sil',
        saveChanges: 'Değişiklikleri kaydet',
        searchForBlock: 'Blok ara',
        selectExistingLabel: 'Varolan {{label}} seç',
        selectFieldsToEdit: 'Düzenlenecek alanları seçin',
        showAll: 'Tümünü göster',
        swapRelationship: 'Takas Ilişkisi',
        swapUpload: 'Karşıya Yüklemeyi Değiştir',
        textToDisplay: 'Görüntülenecek metin',
        toggleBlock: 'Bloğu aç/kapat',
        uploadNewLabel: 'Karşıya {{label}} yükle'
    },
    folder: {
        browseByFolder: 'Klasöre Göre Gözat',
        byFolder: 'Klasör Bazında',
        deleteFolder: 'Klasörü Sil',
        folderName: 'Klasör Adı',
        folders: 'Klasörler',
        folderTypeDescription: 'Bu klasörde hangi türden koleksiyon belgelerine izin verilmesi gerektiğini seçin.',
        itemHasBeenMoved: '{{title}} {{folderName}} klasörüne taşındı.',
        itemHasBeenMovedToRoot: '{{title}} kök klasöre taşındı.',
        itemsMovedToFolder: "{{title}} {{folderName}}'ye taşındı.",
        itemsMovedToRoot: '{{title}} kök klasörüne taşındı',
        moveFolder: 'Klasörü Taşı',
        moveItemsToFolderConfirmation: "<1>{{count}} {{label}}</1>'yi <2>{{toFolder}}</2>'ye taşımayı planlıyorsunuz. Emin misiniz?",
        moveItemsToRootConfirmation: '<1>{{count}} {{label}}</1> kök klasöre taşımayı planlıyorsunuz. Emin misiniz?',
        moveItemToFolderConfirmation: '<1>{{title}}</1> ögesini <2>{{toFolder}}</2> konumuna taşımak üzeresiniz. Emin misiniz?',
        moveItemToRootConfirmation: '<1>{{title}}</1> öğesini ana klasöre taşımak üzeresiniz. Emin misiniz?',
        movingFromFolder: '{{title}} öğesinin {{fromFolder}} klasöründen taşınması',
        newFolder: 'Yeni Klasör',
        noFolder: 'Klasör Yok',
        renameFolder: 'Klasörü Yeniden Adlandır',
        searchByNameInFolder: "{{folderName}}'da İsme Göre Ara",
        selectFolderForItem: '{{title}} için klasör seçin'
    },
    general: {
        name: 'İsim',
        aboutToDelete: '<1>{{title}}</1> {{label}} silinmek üzere. Silme işlemine devam etmek istiyor musunuz?',
        aboutToDeleteCount_many: '{{count}} {{label}} silmek üzeresiniz',
        aboutToDeleteCount_one: '{{count}} {{label}} silmek üzeresiniz',
        aboutToDeleteCount_other: '{{count}} {{label}} silmek üzeresiniz',
        aboutToPermanentlyDelete: '{{label}} <1>{{title}}</1> kalıcı olarak silmek üzeresiniz. Emin misiniz?',
        aboutToPermanentlyDeleteTrash: 'Çöpten <0>{{count}}</0> <1>{{label}}</1> kalıcı olarak silmek üzeresiniz. Emin misiniz?',
        aboutToRestore: "{{label}} <1>{{title}}</1>'yi geri yüklemek üzeresiniz. Emin misiniz?",
        aboutToRestoreAsDraft: '{{label}} <1>{{title}}</1> taslağı olarak geri yüklemek üzeresiniz. Emin misiniz?',
        aboutToRestoreAsDraftCount: 'Taslağı olarak geri yükleme üzeresiniz: {{count}} {{label}}',
        aboutToRestoreCount: '{{count}} {{label}} geri yüklemek üzeresiniz.',
        aboutToTrash: '{{label}} <1>{{title}}</1> çöp kutusuna taşımayı düşünüyorsunuz. Emin misiniz?',
        aboutToTrashCount: '{{count}} {{label}} çöp kutusuna taşımayı düşünüyorsunuz.',
        addBelow: 'Altına ekle',
        addFilter: 'Filtre ekle',
        adminTheme: 'Admin arayüzü',
        all: 'Tüm',
        allCollections: 'Tüm Koleksiyonlar',
        allLocales: 'Tüm yerler',
        and: 've',
        anotherUser: 'Başka bir kullanıcı',
        anotherUserTakenOver: 'Başka bir kullanıcı bu belgenin düzenlemesini devraldı.',
        applyChanges: 'Değişiklikleri Uygula',
        ascending: 'artan',
        automatic: 'Otomatik',
        backToDashboard: 'Anasayfaya geri dön',
        cancel: 'İptal',
        changesNotSaved: 'Değişiklikleriniz henüz kaydedilmedi. Eğer bu sayfayı terk ederseniz değişiklikleri kaybedeceksiniz.',
        clear: 'Temiz',
        clearAll: 'Hepsini Temizle',
        close: 'Kapat',
        collapse: 'Daralt',
        collections: 'Koleksiyonlar',
        columns: 'Sütunlar',
        columnToSort: 'Sıralanacak Sütunlar',
        confirm: 'Onayla',
        confirmCopy: 'Kopyayı onayla',
        confirmDeletion: 'Silmeyi onayla',
        confirmDuplication: 'Çoğaltmayı onayla',
        confirmMove: 'Hareketi onayla',
        confirmReindex: 'Tüm {{collections}} yeniden dizine alınsın mı?',
        confirmReindexAll: 'Tüm koleksiyonlar yeniden dizine alinsın mı?',
        confirmReindexDescription: 'Bu işlem mevcut dizinleri kaldıracak ve {{collections}} koleksiyonlarındaki belgeleri yeniden dizine alacaktır.',
        confirmReindexDescriptionAll: 'Bu işlem mevcut dizinleri kaldıracak ve tüm koleksiyonlardaki belgeleri yeniden dizine alacaktır.',
        confirmRestoration: 'Onarımı onaylayın',
        copied: 'Kopyalandı',
        copy: 'Kopyala',
        copyField: 'Alanı kopyala',
        copying: 'Kopyalama',
        copyRow: 'Satırı kopyala',
        copyWarning: "{{to}}'yu {{from}} ile {{label}} {{title}} için üstüne yazmak üzeresiniz. Emin misiniz?",
        create: 'Oluştur',
        created: 'Oluşturma tarihi',
        createdAt: 'Oluşturma tarihi',
        createNew: 'Yeni oluştur',
        createNewLabel: 'Yeni bir {{label}} oluştur',
        creating: 'Oluşturuluyor',
        creatingNewLabel: 'Yeni bir {{label}} oluşturuluyor',
        currentlyEditing: 'şu anda bu belgeyi düzenliyor. Devralırsanız, düzenlemeye devam etmeleri engellenecek ve kaydedilmemiş değişiklikleri de kaybedebilirler.',
        custom: 'Özel',
        dark: 'Karanlık',
        dashboard: 'Anasayfa',
        delete: 'Sil',
        deleted: 'Silindi',
        deletedAt: 'Silindiği Tarih',
        deletedCountSuccessfully: '{{count}} {{label}} başarıyla silindi.',
        deletedSuccessfully: 'Başarıyla silindi.',
        deletePermanently: 'Çöpü atlayın ve kalıcı olarak silin',
        deleting: 'Siliniyor...',
        depth: 'Derinlik',
        descending: 'Azalan',
        deselectAllRows: 'Tüm satırların seçimini kaldır',
        document: 'Belge',
        documentIsTrashed: 'Bu {{label}} çöpe atıldı ve sadece okuma modunda.',
        documentLocked: 'Belge kilitlendi',
        documents: 'Belgeler',
        duplicate: 'Çoğalt',
        duplicateWithoutSaving: 'Ayarları kaydetmeden çoğalt',
        edit: 'Düzenle',
        editAll: 'Hepsini düzenle',
        editedSince: 'O tarihten itibaren düzenlendi',
        editing: 'Düzenleniyor',
        editingLabel_many: '{{count}} {{label}} düzenleniyor',
        editingLabel_one: '{{count}} {{label}} düzenleniyor',
        editingLabel_other: '{{count}} {{label}} düzenleniyor',
        editingTakenOver: 'Düzenleme devralındı',
        editLabel: '{{label}} düzenle',
        email: 'E-posta',
        emailAddress: 'E-posta adresi',
        emptyTrash: 'Çöpü Boşalt',
        emptyTrashLabel: '{{label}} çöp kutusunu boşaltın',
        enterAValue: 'Değer girin',
        error: 'Hata',
        errors: 'Hatalar',
        exitLivePreview: 'Canlı Önizlemeyi Kapat',
        export: 'İhracat',
        fallbackToDefaultLocale: 'Varsayılan yerel ayara geri dönme',
        false: 'Yanlış',
        filter: 'Filtrele',
        filters: 'Filtreler',
        filterWhere: '{{label}} filtrele:',
        globals: 'Globaller',
        goBack: 'Geri dön',
        groupByLabel: "{{label}}'ye göre grupla",
        import: 'İthalat',
        isEditing: 'düzenliyor',
        item: 'öğe',
        items: 'öğeler',
        language: 'Dil',
        lastModified: 'Son değiştirme',
        leaveAnyway: 'Yine de ayrıl',
        leaveWithoutSaving: 'Kaydetmeden ayrıl',
        light: 'Aydınlık',
        livePreview: 'Önizleme',
        loading: 'Yükleniyor',
        locale: 'Yerel ayar',
        locales: 'Diller',
        menu: 'Menü',
        moreOptions: 'Daha fazla seçenek',
        move: 'Hareket et',
        moveConfirm: '<1>{{destination}}</1> konumuna {{count}} {{label}} taşımayı planlıyorsunuz. Emin misiniz?',
        moveCount: '{{count}} {{label}} taşı',
        moveDown: 'Aşağı taşı',
        moveUp: 'Yukarı taşı',
        moving: 'Taşınma',
        movingCount: '{{count}} {{label}} taşıma',
        newPassword: 'Yeni parola',
        next: 'Sonraki',
        no: 'Hayır',
        noDateSelected: 'Tarih seçilmedi',
        noFiltersSet: 'Tanımlı filtre yok',
        noLabel: '<{{label}} yok>',
        none: 'Hiç',
        noOptions: 'Seçenek yok',
        noResults: '{{label}} bulunamadı. Henüz bir {{label}} eklenmemiş olabilir veya seçtiğiniz filtrelerle eşleşen bir sonuç bulunamamış olabilir.',
        notFound: 'Bulunamadı',
        nothingFound: 'Hiçbir şey bulunamadı',
        noTrashResults: 'Çöpte hiç {{label}} yok.',
        noUpcomingEventsScheduled: 'Planlanan gelecek etkinlik yok.',
        noValue: 'Değer yok',
        of: 'of',
        only: 'Sadece',
        open: 'Aç',
        or: 'Or',
        order: 'Order',
        overwriteExistingData: 'Mevcut alan verilerinin üzerine yazın',
        pageNotFound: 'Sayfa bulunamadı',
        password: 'Parola',
        pasteField: 'Alanı yapıştır',
        pasteRow: 'Satırı yapıştır',
        payloadSettings: 'Ayarlar',
        permanentlyDelete: 'Kalıcı Olarak Sil',
        permanentlyDeletedCountSuccessfully: 'Kalıcı olarak {{count}} {{label}} başarıyla silindi.',
        perPage: 'Sayfa başına: {{limit}}',
        previous: 'Önceki',
        reindex: 'Yeniden İndeksle',
        reindexingAll: 'Tüm {{collections}} yeniden dizine alınıyor.',
        remove: 'Kaldır',
        rename: 'Yeniden adlandır',
        reset: 'Sıfırla',
        resetPreferences: 'Tercihleri sıfırla',
        resetPreferencesDescription: 'Bu, tüm tercihlerinizin varsayılan ayarlara sıfırlanmasını sağlar.',
        resettingPreferences: 'Tercihler sıfırlanıyor.',
        restore: 'Geri Yükle',
        restoreAsPublished: 'Yayınlanan sürüm olarak geri yükle',
        restoredCountSuccessfully: '{{count}} {{label}} başarıyla geri yüklendi.',
        restoring: "Özgün metnin anlamını Payload bağlamında saygıyla yeniden oluşturun. İşte çok belirli anlamlar taşıyan yaygın Payload terimlerinin bir listesi:\n    - Koleksiyon: Bir koleksiyon, ortak bir yapı ve amaca sahip belgelerin grubudur. Koleksiyonlar içerik organizasyonu ve yönetiminde Payload'da kullanılır.\n    - Alan: Bir alan, bir koleksiyon içindeki belgedeki belirli bir veri parçasıdır. Alanlar, bir belgede saklanabilen ver",
        row: 'Satır',
        rows: 'Satır',
        save: 'Kaydet',
        saving: 'Kaydediliyor...',
        schedulePublishFor: '{{title}} için yayınlama programı ayarlayın.',
        searchBy: 'Şuna göre sırala: {{label}}',
        select: 'Seçiniz',
        selectAll: "Tüm {{count}} {{label}}'ı seçin",
        selectAllRows: 'Tüm satırları seçin',
        selectedCount: '{{count}} {{label}} seçildi',
        selectLabel: '{{label}} seçin',
        selectValue: 'Bir değer seçin',
        showAllLabel: 'Tüm {{label}} göster',
        sorryNotFound: 'Üzgünüz, isteğinizle eşleşen bir sonuç bulunamadı.',
        sort: 'Sırala',
        sortByLabelDirection: '{{label}} göre sırala {{direction}}',
        stayOnThisPage: 'Bu sayfada kal',
        submissionSuccessful: 'Gönderme başarılı',
        submit: 'Gönder',
        submitting: 'Gönderiliyor...',
        success: 'Başarı',
        successfullyCreated: '{{label}} başarıyla oluşturuldu.',
        successfullyDuplicated: '{{label}} başarıyla kopyalandı.',
        successfullyReindexed: '{{collections}} koleksiyonlarından {{total}} belgenin {{count}} tanesi başarıyla yeniden indekslendi.',
        takeOver: 'Devralmak',
        thisLanguage: 'Türkçe',
        time: 'Zaman',
        timezone: 'Saat dilimi',
        titleDeleted: '{{label}} {{title}} başarıyla silindi.',
        titleRestored: '"{{title}}" başarıyla geri yüklendi.',
        titleTrashed: '{{label}} "{{title}}" çöpe taşındı.',
        trash: 'Çöp',
        trashedCountSuccessfully: '{{count}} {{label}} çöp kutusuna taşındı.',
        true: 'Doğru',
        unauthorized: 'Yetkisiz',
        unsavedChanges: 'Kaydedilmemiş değişiklikleriniz var. Devam etmeden önce kaydedin veya atın.',
        unsavedChangesDuplicate: 'Kaydedilmemiş değişiklikler var. Çoğaltma işlemine devam etmek istiyor musunuz?',
        untitled: 'Başlıksız',
        upcomingEvents: 'Yaklaşan Etkinlikler',
        updatedAt: 'Güncellenme tarihi',
        updatedCountSuccessfully: '{{count}} {{label}} başarıyla güncellendi.',
        updatedLabelSuccessfully: '{{label}} başarıyla güncellendi.',
        updatedSuccessfully: 'Başarıyla güncellendi.',
        updateForEveryone: 'Herkes için güncelleme',
        updating: 'Güncelleniyor',
        uploading: 'Yükleniyor',
        uploadingBulk: "{{total}}'den {{current}} yükleniyor",
        user: 'kullanıcı',
        username: 'Kullanıcı Adı',
        users: 'kullanıcı',
        value: 'Değer',
        viewing: 'Görüntüleme',
        viewReadOnly: 'Salt okunur olarak görüntüle',
        welcome: 'Hoşgeldiniz',
        yes: 'Evet'
    },
    localization: {
        cannotCopySameLocale: 'Aynı yerel ayara kopyalanamaz.',
        copyFrom: 'Kopyala',
        copyFromTo: "{{from}} 'dan {{to}} 'ya kopyalama",
        copyTo: 'Kopyala',
        copyToLocale: 'Yerel hafızaya kopyala',
        localeToPublish: 'Yayınlanacak yerel',
        selectLocaleToCopy: 'Kopyalamak için yerel seçimi yapın'
    },
    operators: {
        contains: 'içerir',
        equals: 'eşittir',
        exists: 'var',
        intersects: 'kesişir',
        isGreaterThan: 'şundan büyüktür',
        isGreaterThanOrEqualTo: 'büyüktür veya eşittir',
        isIn: 'içinde',
        isLessThan: 'küçüktür',
        isLessThanOrEqualTo: 'küçüktür veya eşittir',
        isLike: 'gibidir',
        isNotEqualTo: 'eşit değildir',
        isNotIn: 'içinde değil',
        isNotLike: 'gibi değil',
        near: 'yakın',
        within: 'içinde'
    },
    upload: {
        addFile: 'Dosya ekle',
        addFiles: 'Dosya Ekle',
        bulkUpload: 'Toplu Yükleme',
        crop: 'Mahsulat',
        cropToolDescription: 'Seçilen alanın köşelerini sürükleyin, yeni bir alan çizin ya da aşağıdaki değerleri ayarlayın.',
        download: 'İndir',
        dragAndDrop: 'Bir dosya sürükleyip bırakabilirsiniz',
        dragAndDropHere: 'veya buraya bir dosya sürükleyip bırakabilirsiniz',
        editImage: 'Görüntüyü Düzenle',
        fileName: 'Dosya adı',
        fileSize: 'Dosya boyutu',
        filesToUpload: 'Yüklemek için Dosyalar',
        fileToUpload: 'Yüklenecek Dosya',
        focalPoint: 'Odak Noktası',
        focalPointDescription: 'Önizlemeye odak noktasını doğrudan sürükleyin veya aşağıdaki değerleri ayarlayın.',
        height: 'Yükseklik',
        lessInfo: 'Daha az bilgi',
        moreInfo: 'Daha fazla bilgi',
        noFile: 'Dosya yok',
        pasteURL: 'URL yapıştır',
        previewSizes: 'Önizleme Boyutları',
        selectCollectionToBrowse: 'Görüntülenecek bir koleksiyon seçin',
        selectFile: 'Dosya seç',
        setCropArea: 'Mahsul alanını ayarla',
        setFocalPoint: 'Odak noktasını ayarla',
        sizes: 'Boyutlar',
        sizesFor: '{{label}} için boyutlar',
        width: 'Genişlik'
    },
    validation: {
        emailAddress: 'Lütfen geçerli bir e-posta adresi girin.',
        enterNumber: 'Lütfen geçerli bir sayı girin.',
        fieldHasNo: 'Bu alanda {{label}} girili değil.',
        greaterThanMax: '{{value}} izin verilen maksimum {{label}} değerinden daha büyük.',
        invalidInput: 'Bu alanda geçersiz bir giriş mevcut.',
        invalidSelection: 'Bu alanda geçersiz bir seçim mevcut.',
        invalidSelections: "'Bu alan şu geçersiz seçimlere sahip:'",
        lessThanMin: '{{value}} izin verilen minimum {{label}} değerinden daha küçük.',
        limitReached: 'Sınır aşıldı, yalnızca {{max}} öğe eklenebilir.',
        longerThanMin: 'Bu değer minimum {{minLength}} karakterden uzun olmalıdır.',
        notValidDate: '"{{value}}" geçerli bir tarih değil.',
        required: 'Bu alan gereklidir.',
        requiresAtLeast: 'Bu alan en az {{count}} adet {{label}} gerektirmektedir.',
        requiresNoMoreThan: 'Bu alana {{count}} adetten fazla {{label}} girilemez.',
        requiresTwoNumbers: 'Bu alana en az iki rakam girilmesi zorunludur.',
        shorterThanMax: 'Bu alan {{maxLength}} karakterden daha kısa olmalıdır.',
        timezoneRequired: 'Bir zaman dilimi gereklidir.',
        trueOrFalse: 'Bu alan yalnızca doğru ve yanlış olabilir.',
        username: 'Lütfen geçerli bir kullanıcı adı girin. Harfler, numaralar, kısa çizgiler, noktalar ve alt çizgiler içerebilir.',
        validUploadID: "'Bu alan geçerli bir karşıya yükleme ID'sine sahip değil.'"
    },
    version: {
        type: 'Tür',
        aboutToPublishSelection: "Seçimdeki tüm {{label}}'i yayınlamak üzeresiniz. Emin misin?",
        aboutToRestore: 'Döküman {{label}}, {{versionDate}} tarihindeki sürümüne geri döndürülecek.',
        aboutToRestoreGlobal: 'Global {{label}}, {{versionDate}} tarihindeki sürümüne geri döndürülecek.',
        aboutToRevertToPublished: 'Bu dökümanın değişikliklerini yayınladığı haline geri getirmek üzeresiniz. Devam etmek istiyor musunuz?',
        aboutToUnpublish: 'Bu dökümanı yayından kaldırmak üzeresiniz. Devam etmek istiyor musunuz?',
        aboutToUnpublishSelection: 'Seçimdeki tüm {{label}} yayınını kaldırmak üzeresiniz. Emin misin?',
        autosave: 'Otomatik kaydet',
        autosavedSuccessfully: 'Otomatik kaydetme başarılı',
        autosavedVersion: 'Otomatik kayıtlı sürüm',
        changed: 'Değişiklik yapıldı',
        changedFieldsCount_one: '{{count}} alanı değişti',
        changedFieldsCount_other: '{{count}} değişen alan',
        compareVersion: 'Sürümü şununla karşılaştır:',
        compareVersions: 'Sürümleri Karşılaştır',
        comparingAgainst: 'Karşılaştırma',
        confirmPublish: 'Yayınlamayı onayla',
        confirmRevertToSaved: 'Confirm revert to saved',
        confirmUnpublish: 'Yayından kaldırmayı onayla',
        confirmVersionRestoration: 'Sürümü Geri Getirmeyi Onayla',
        currentDocumentStatus: 'Şu an {{docStatus}} döküman',
        currentDraft: 'Mevcut Taslak',
        currentlyPublished: 'Şu Anda Yayınlanmaktadır',
        currentlyViewing: 'Şu anda görüntüleniyor',
        currentPublishedVersion: 'Mevcut Yayınlanan Sürüm',
        draft: 'Taslak',
        draftSavedSuccessfully: 'Taslak başarıyla kaydedildi.',
        lastSavedAgo: 'Son kaydedildi {{distance}} önce',
        modifiedOnly: 'Yalnızca değiştirilmiş',
        moreVersions: 'Daha fazla versiyon...',
        noFurtherVersionsFound: 'Başka sürüm bulunamadı.',
        noRowsFound: '{{label}} bulunamadı',
        noRowsSelected: 'Seçilen {{label}} yok',
        preview: 'Önizleme',
        previouslyDraft: 'Daha önce bir Taslak',
        previouslyPublished: 'Daha Önce Yayınlanmış',
        previousVersion: 'Önceki Sürüm',
        problemRestoringVersion: 'Bu sürüme geri döndürürken bir hatayla karşılaşıldı.',
        publish: 'Yayınla',
        publishAllLocales: 'Tüm yerel ayarları yayınla',
        publishChanges: 'Değişiklikleri yayınla',
        published: 'Yayınlandı',
        publishIn: '{{locale}} dilinde yayınlayın.',
        publishing: 'Yayınlama',
        restoreAsDraft: 'Taslak olarak geri yükle',
        restoredSuccessfully: 'Geri getirme başarılı.',
        restoreThisVersion: 'Bu sürüme geri döndür',
        restoring: 'Geri döndürülüyor...',
        reverting: 'Değişiklikler geri alınıyor...',
        revertToPublished: 'Yayınlanana geri döndür',
        saveDraft: 'Taslağı kaydet',
        scheduledSuccessfully: 'Başarıyla planlandı.',
        schedulePublish: 'Yayını Planla',
        selectLocales: 'Görüntülenecek yerel ayarları seçin',
        selectVersionToCompare: 'Karşılaştırılacak bir sürüm seçin',
        showingVersionsFor: 'Şunun için sürümler gösteriliyor:',
        showLocales: 'Yerel ayarları göster:',
        specificVersion: 'Belirli Sürüm',
        status: 'Durum',
        unpublish: 'Yayından Kaldır',
        unpublishing: 'Yayından kaldırılıyor...',
        version: 'Sürüm',
        versionAgo: '{{distance}} önce',
        versionCount_many: '{{count}} sürüm bulundu',
        versionCount_none: 'Sürüm bulunamadı',
        versionCount_one: '{{count}} sürüm bulundu',
        versionCount_other: '{{count}} sürüm bulundu',
        versionCreatedOn: '{{version}} oluşturma tarihi:',
        versionID: 'Sürüm ID',
        versions: 'Sürümler',
        viewingVersion: '{{entityLabel}} {{documentTitle}} için sürümler gösteriliyor',
        viewingVersionGlobal: '`Global {{entityLabel}} için sürümler gösteriliyor',
        viewingVersions: '{{entityLabel}} {{documentTitle}} için sürümler gösteriliyor',
        viewingVersionsGlobal: '`Global {{entityLabel}} için sürümler gösteriliyor'
    }
};
export const tr = {
    dateFNSKey: 'tr',
    translations: trTranslations
};

//# sourceMappingURL=tr.js.map