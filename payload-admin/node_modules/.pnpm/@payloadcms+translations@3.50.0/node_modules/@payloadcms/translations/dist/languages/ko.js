export const koTranslations = {
    authentication: {
        account: '계정',
        accountOfCurrentUser: '현재 사용자의 계정',
        accountVerified: '계정이 성공적으로 인증되었습니다.',
        alreadyActivated: '이미 활성화됨',
        alreadyLoggedIn: '이미 로그인됨',
        apiKey: 'API 키',
        authenticated: '인증됨',
        backToLogin: '로그인 화면으로 돌아가기',
        beginCreateFirstUser: '시작하려면 첫 번째 사용자를 생성하세요.',
        changePassword: '비밀번호 변경',
        checkYourEmailForPasswordReset: '이메일 주소가 계정과 연결되어 있다면, 곧 비밀번호를 재설정하는 방법에 대한 지시를 받게 될 것입니다. 인박스에서 이메일을 찾을 수 없다면 스팸 또는 정크 메일 폴더를 확인해 주시기 바랍니다.',
        confirmGeneration: '생성 확인',
        confirmPassword: '비밀번호 확인',
        createFirstUser: '첫 번째 사용자 생성',
        emailNotValid: '입력한 이메일은 유효하지 않습니다.',
        emailOrUsername: '이메일 또는 사용자 이름',
        emailSent: '이메일 전송됨',
        emailVerified: '이메일이 성공적으로 인증되었습니다.',
        enableAPIKey: 'API 키 활성화',
        failedToUnlock: '잠금 해제 실패',
        forceUnlock: '강제 잠금 해제',
        forgotPassword: '비밀번호를 잊으셨나요?',
        forgotPasswordEmailInstructions: '아래에 이메일을 입력하세요. 비밀번호를 재설정하는 방법에 대한 안내가 포함된 이메일 메시지를 받게 될 것입니다.',
        forgotPasswordQuestion: '비밀번호를 잊으셨나요?',
        forgotPasswordUsernameInstructions: '아래에 사용자 이름을 입력해 주세요. 암호를 재설정하는 방법에 대한 지침은 사용자 이름과 관련된 이메일 주소로 발송됩니다.',
        generate: '생성',
        generateNewAPIKey: '새로운 API 키 생성',
        generatingNewAPIKeyWillInvalidate: '새로운 API 키를 생성하면 이전 키가 무효화됩니다. 계속하시겠습니까?',
        lockUntil: '잠금 시간',
        logBackIn: '다시 로그인',
        loggedIn: '다른 사용자로 로그인하려면 먼저 <0>로그아웃</0>해야 합니다.',
        loggedInChangePassword: '비밀번호를 변경하려면 <0>계정 화면</0>으로 이동하여 비밀번호를 편집하세요.',
        loggedOutInactivity: '보안을 위해 일정 시간 동안 활동하지 않아 로그아웃되었습니다.',
        loggedOutSuccessfully: '로그아웃되었습니다.',
        loggingOut: '로그아웃 중...',
        login: '로그인',
        loginAttempts: '로그인 시도',
        loginUser: '현재 사용자 로그인',
        loginWithAnotherUser: '다른 사용자로 로그인하려면 먼저 <0>로그아웃</0>해야 합니다.',
        logOut: '로그아웃',
        logout: '로그아웃',
        logoutSuccessful: '로그아웃 성공.',
        logoutUser: '현재 사용자 로그아웃',
        newAccountCreated: '<a href="{{serverURL}}">{{serverURL}}</a>에 접근할 수 있는 새로운 계정이 생성되었습니다. 다음 링크를 클릭하거나 브라우저에 URL을 붙여넣으세요: <a href="{{verificationURL}}">{{verificationURL}}</a><br> 이메일을 확인한 후에 로그인할 수 있습니다.',
        newAPIKeyGenerated: '새로운 API 키가 생성되었습니다.',
        newPassword: '새로운 비밀번호',
        passed: '인증 통과',
        passwordResetSuccessfully: '비밀번호가 성공적으로 재설정되었습니다.',
        resetPassword: '비밀번호 재설정',
        resetPasswordExpiration: '비밀번호 재설정 만료',
        resetPasswordToken: '비밀번호 재설정 토큰',
        resetYourPassword: '비밀번호 재설정',
        stayLoggedIn: '로그인 상태 유지',
        successfullyRegisteredFirstUser: '첫 번째 사용자를 성공적으로 등록했습니다.',
        successfullyUnlocked: '잠금 해제 성공',
        tokenRefreshSuccessful: '토큰 새로 고침이 성공했습니다.',
        unableToVerify: '확인할 수 없음',
        username: '사용자 이름',
        usernameNotValid: '제공된 사용자 이름이 유효하지 않습니다.',
        verified: '확인됨',
        verifiedSuccessfully: '성공적으로 확인됨',
        verify: '확인',
        verifyUser: '현재 사용자 확인',
        verifyYourEmail: '이메일을 확인해주세요',
        youAreInactive: '잠시 활동하지 않았으며 보안을 위해 곧 자동 로그아웃됩니다. 로그인 상태를 유지하시겠습니까?',
        youAreReceivingResetPassword: '당신(혹은 다른 사람)이 계정의 비밀번호 초기화를 요청했기 때문에 이 이메일을 받았습니다. 다음 링크를 클릭하거나 브라우저에 붙여넣어 비밀번호를 초기화하세요:',
        youDidNotRequestPassword: '비밀번호 초기화를 요청하지 않았다면 이 이메일을 무시하시고 비밀번호를 변경하지 마세요.'
    },
    error: {
        accountAlreadyActivated: '이 계정은 이미 활성화되었습니다.',
        autosaving: '이 문서를 자동 저장하는 중에 문제가 발생했습니다.',
        correctInvalidFields: '입력하신 내용을 확인해주세요.',
        deletingFile: '파일을 삭제하는 중에 오류가 발생했습니다.',
        deletingTitle: '{{title}} 삭제하는 중에 오류가 발생했습니다. 인터넷 연결을 확인하고 다시 시도하세요.',
        documentNotFound: 'ID가 {{id}}인 문서를 찾을 수 없습니다. 이 문서는 삭제되었거나 존재하지 않았거나, 당신이 접근 권한이 없을 수 있습니다.',
        emailOrPasswordIncorrect: '입력한 이메일 또는 비밀번호가 올바르지 않습니다.',
        followingFieldsInvalid_one: '다음 입력란이 유효하지 않습니다:',
        followingFieldsInvalid_other: '다음 입력란이 유효하지 않습니다:',
        incorrectCollection: '잘못된 컬렉션',
        insufficientClipboardPermissions: '클립보드 접근이 거부되었습니다. 클립보드 권한을 확인하십시오.',
        invalidClipboardData: '유효하지 않은 클립보드 데이터입니다.',
        invalidFileType: '잘못된 파일 형식',
        invalidFileTypeValue: '잘못된 파일 형식: {{value}}',
        invalidRequestArgs: '요청에 잘못된 인수가 전달되었습니다: {{args}}',
        loadingDocument: 'ID가 {{id}}인 문서를 불러오는 중에 문제가 발생했습니다.',
        localesNotSaved_one: '다음 로케일을 저장할 수 없습니다:',
        localesNotSaved_other: '다음 로케일들을 저장할 수 없습니다:',
        logoutFailed: '로그아웃 실패했습니다.',
        missingEmail: '이메일이 누락되었습니다.',
        missingIDOfDocument: '업데이트할 문서의 ID가 누락되었습니다.',
        missingIDOfVersion: '버전의 ID가 누락되었습니다.',
        missingRequiredData: '필수 데이터가 누락되었습니다.',
        noFilesUploaded: '파일이 업로드되지 않았습니다.',
        noMatchedField: '"{{label}}"에 대한 일치하는 입력란이 없습니다.',
        notAllowedToAccessPage: '이 페이지에 접근할 권한이 없습니다.',
        notAllowedToPerformAction: '이 작업을 수행할 권한이 없습니다.',
        notFound: '요청한 리소스를 찾을 수 없습니다.',
        noUser: '사용자가 없습니다.',
        previewing: '이 문서를 미리보는 중에 문제가 발생했습니다.',
        problemUploadingFile: '파일 업로드 중에 문제가 발생했습니다.',
        restoringTitle: '{{title}} 복원 중 오류가 발생했습니다. 연결 상태를 확인하고 다시 시도해 주세요.',
        tokenInvalidOrExpired: '토큰이 유효하지 않거나 만료되었습니다.',
        tokenNotProvided: '토큰이 제공되지 않았습니다.',
        unableToCopy: '복사할 수 없습니다.',
        unableToDeleteCount: '총 {{total}}개 중 {{count}}개의 {{label}}을(를) 삭제할 수 없습니다.',
        unableToReindexCollection: '{{collection}} 컬렉션의 재인덱싱 중 오류가 발생했습니다. 작업이 중단되었습니다.',
        unableToUpdateCount: '총 {{total}}개 중 {{count}}개의 {{label}}을(를) 업데이트할 수 없습니다.',
        unauthorized: '권한 없음, 이 요청을 수행하려면 로그인해야 합니다.',
        unauthorizedAdmin: '관리자 패널에 액세스할 수 없습니다.',
        unknown: '알 수 없는 오류가 발생했습니다.',
        unPublishingDocument: '이 문서의 게시 취소 중에 문제가 발생했습니다.',
        unspecific: '오류가 발생했습니다.',
        unverifiedEmail: '로그인하기 전에 이메일을 확인하세요.',
        userEmailAlreadyRegistered: '주어진 이메일로 이미 등록된 사용자가 있습니다.',
        userLocked: '이 사용자는 로그인 실패 횟수가 너무 많아 잠겼습니다.',
        usernameAlreadyRegistered: '주어진 사용자 이름을 가진 사용자가 이미 등록되어 있습니다.',
        usernameOrPasswordIncorrect: '제공된 사용자 이름 또는 비밀번호가 잘못되었습니다.',
        valueMustBeUnique: '값은 고유해야 합니다.',
        verificationTokenInvalid: '확인 토큰이 유효하지 않습니다.'
    },
    fields: {
        addLabel: '{{label}} 추가',
        addLink: '링크 추가',
        addNew: '새로 추가',
        addNewLabel: '새로운 {{label}} 추가',
        addRelationship: '관계 추가',
        addUpload: '업로드 추가',
        block: '블록',
        blocks: '블록',
        blockType: '블록 유형',
        chooseBetweenCustomTextOrDocument: '사용자 지정 텍스트 URL 또는 다른 문서에 링크 중 선택하세요.',
        chooseDocumentToLink: '연결할 문서 선택',
        chooseFromExisting: '기존 항목 중 선택',
        chooseLabel: '{{label}} 선택',
        collapseAll: '모두 접기',
        customURL: '사용자 지정 URL',
        editLabelData: '{{label}} 데이터 수정',
        editLink: '링크 수정',
        editRelationship: '관계 수정',
        enterURL: 'URL 입력',
        internalLink: '내부 링크',
        itemsAndMore: '{{items}} 및 {{count}}개 더',
        labelRelationship: '{{label}} 관계',
        latitude: '위도',
        linkedTo: '<0>{{label}}</0>에 연결됨',
        linkType: '링크 유형',
        longitude: '경도',
        newLabel: '새로운 {{label}}',
        openInNewTab: '새 탭에서 열기',
        passwordsDoNotMatch: '비밀번호가 일치하지 않습니다.',
        relatedDocument: '관련 문서',
        relationTo: '관계',
        removeRelationship: '관계 제거',
        removeUpload: '제거',
        saveChanges: '변경 사항 저장',
        searchForBlock: '블록 검색',
        selectExistingLabel: '기존 {{label}} 선택',
        selectFieldsToEdit: '수정할 입력란 선택',
        showAll: '모두 표시',
        swapRelationship: '관계 교체',
        swapUpload: '업로드 교체',
        textToDisplay: '표시할 텍스트',
        toggleBlock: '블록 토글',
        uploadNewLabel: '새로운 {{label}} 업로드'
    },
    folder: {
        browseByFolder: '폴더별 브라우징',
        byFolder: '폴더별로',
        deleteFolder: '폴더 삭제',
        folderName: '폴더 이름',
        folders: '폴더들',
        folderTypeDescription: '이 폴더에서 어떤 유형의 컬렉션 문서가 허용되어야 하는지 선택하세요.',
        itemHasBeenMoved: '{{title}}는 {{folderName}}로 이동되었습니다.',
        itemHasBeenMovedToRoot: '{{title}}이(가) 루트 폴더로 이동되었습니다.',
        itemsMovedToFolder: '{{title}}이(가) {{folderName}}로 이동되었습니다.',
        itemsMovedToRoot: '{{title}}이(가) 루트 폴더로 이동되었습니다.',
        moveFolder: '폴더 이동',
        moveItemsToFolderConfirmation: '<1>{{count}} {{label}}</1>을(를) <2>{{toFolder}}</2>(으)로 이동하려 합니다. 확실합니까?',
        moveItemsToRootConfirmation: '당신은 <1>{{count}} {{label}}</1>을 최상위 폴더로 이동하려고 합니다. 확실합니까?',
        moveItemToFolderConfirmation: '<1>{{title}}</1>을(를) <2>{{toFolder}}</2>(으)로 이동하려고 합니다. 확실하신가요?',
        moveItemToRootConfirmation: '<1>{{title}}</1>을 루트 폴더로 이동하려고 합니다. 확실합니까?',
        movingFromFolder: '{{title}}를 {{fromFolder}}에서 이동합니다',
        newFolder: '새 폴더',
        noFolder: '폴더 없음',
        renameFolder: '폴더 이름 변경',
        searchByNameInFolder: '{{folderName}}에서 이름으로 검색하세요.',
        selectFolderForItem: '{{title}}에 대한 폴더 선택'
    },
    general: {
        name: '이름',
        aboutToDelete: '{{label}} <1>{{title}}</1>를 삭제하려고 합니다. 계속하시겠습니까?',
        aboutToDeleteCount_many: '{{label}}를 {{count}}개 삭제하려고 합니다.',
        aboutToDeleteCount_one: '{{label}}를 {{count}}개 삭제하려고 합니다.',
        aboutToDeleteCount_other: '{{label}}를 {{count}}개 삭제하려고 합니다.',
        aboutToPermanentlyDelete: '당신은 {{label}} <1>{{title}}</1>을 영구적으로 삭제하려고 합니다. 확실합니까?',
        aboutToPermanentlyDeleteTrash: '휴지통에서 <0>{{count}}</0> <1>{{label}}</1>을(를) 영구적으로 삭제하려고 합니다. 확실합니까?',
        aboutToRestore: '당신은 {{label}} <1>{{title}}</1>을 복원하려고 합니다. 확실합니까?',
        aboutToRestoreAsDraft: '당신은 {{label}} <1>{{title}}</1>을 초안으로 복원하려고 합니다. 확실합니까?',
        aboutToRestoreAsDraftCount: '당신은 {{count}}개의 {{label}}을 초안으로 복원하려고 합니다.',
        aboutToRestoreCount: '당신은 {{count}} {{label}}을 복원하려고 합니다.',
        aboutToTrash: '{{label}} <1>{{title}}</1>을 휴지통으로 이동하려고 합니다. 확실합니까?',
        aboutToTrashCount: '당신은 곧 {{count}} {{label}}을(를) 휴지통으로 이동하려고 합니다.',
        addBelow: '아래에 추가',
        addFilter: '필터 추가',
        adminTheme: '관리자 테마',
        all: '모두',
        allCollections: '모든 컬렉션',
        allLocales: '모든 지역 설정',
        and: '및',
        anotherUser: '다른 사용자',
        anotherUserTakenOver: '다른 사용자가 이 문서의 편집을 인수했습니다.',
        applyChanges: '변경 사항 적용',
        ascending: '오름차순',
        automatic: '자동 설정',
        backToDashboard: '대시보드로 돌아가기',
        cancel: '취소',
        changesNotSaved: '변경 사항이 저장되지 않았습니다. 지금 떠나면 변경 사항을 잃게 됩니다.',
        clear: '페이로드의 맥락 내에서 원문의 의미를 존중하십시오. 다음은 페이로드에서 사용되는 특정 의미를 내포하는 일반적인 페이로드 용어 목록입니다: \n- Collection: 컬렉션은 공통의 구조와 목적을 공유하는 문서의 그룹입니다. 컬렉션은 페이로드에서 콘텐츠를 정리하고 관리하는 데 사용됩니다.\n- Field: 필드는 컬렉',
        clearAll: '모두 지우기',
        close: '닫기',
        collapse: '접기',
        collections: '컬렉션',
        columns: '열',
        columnToSort: '정렬할 열',
        confirm: '확인',
        confirmCopy: '복사 확인',
        confirmDeletion: '삭제하시겠습니까?',
        confirmDuplication: '복제하시겠습니까?',
        confirmMove: '이동 확인',
        confirmReindex: '모든 {{collections}}를 다시 인덱싱하시겠습니까?',
        confirmReindexAll: '모든 컬렉션을 다시 인덱싱하시겠습니까?',
        confirmReindexDescription: '이 작업은 기존 인덱스를 삭제하고 {{collections}} 컬렉션 내의 문서를 다시 인덱싱합니다.',
        confirmReindexDescriptionAll: '이 작업은 기존 인덱스를 삭제하고 모든 컬렉션 내의 문서를 다시 인덱싱합니다.',
        confirmRestoration: '복구를 확인하십시오',
        copied: '복사됨',
        copy: '복사',
        copyField: '필드 복사',
        copying: '복사하기',
        copyRow: '행 복사',
        copyWarning: '{{label}} {{title}}에 대해 {{from}}으로 {{to}}를 덮어쓰려고 합니다. 확실합니까?',
        create: '생성',
        created: '생성됨',
        createdAt: '생성 일시',
        createNew: '새로 생성',
        createNewLabel: '새로운 {{label}} 생성',
        creating: '생성 중',
        creatingNewLabel: '{{label}} 생성 중',
        currentlyEditing: '현재 이 문서를 편집 중입니다. 당신이 인수하면, 편집을 계속할 수 없게 되고, 저장되지 않은 변경 사항이 손실될 수 있습니다.',
        custom: '사용자 정의',
        dark: '다크',
        dashboard: '대시보드',
        delete: '삭제',
        deleted: '삭제됨',
        deletedAt: '삭제된 시간',
        deletedCountSuccessfully: '{{count}}개의 {{label}}를 삭제했습니다.',
        deletedSuccessfully: '삭제되었습니다.',
        deletePermanently: '휴지통 건너뛰고 영구적으로 삭제하세요',
        deleting: '삭제 중...',
        depth: '깊이',
        descending: '내림차순',
        deselectAllRows: '모든 행 선택 해제',
        document: '문서',
        documentIsTrashed: '이 {{label}}은 휴지통에 있으며 읽기 전용입니다.',
        documentLocked: '문서가 잠겼습니다',
        documents: '문서들',
        duplicate: '복제',
        duplicateWithoutSaving: '변경 사항 저장 없이 복제',
        edit: '수정',
        editAll: '모두 수정',
        editedSince: '편집됨',
        editing: '수정 중',
        editingLabel_many: '{{count}}개의 {{label}} 수정 중',
        editingLabel_one: '{{count}}개의 {{label}} 수정 중',
        editingLabel_other: '{{count}}개의 {{label}} 수정 중',
        editingTakenOver: '편집이 인수되었습니다',
        editLabel: '{{label}} 수정',
        email: '이메일',
        emailAddress: '이메일 주소',
        emptyTrash: '휴지통 비우기',
        emptyTrashLabel: '{{label}} 휴지통 비우기',
        enterAValue: '값을 입력하세요',
        error: '오류',
        errors: '오류',
        exitLivePreview: '실시간 미리보기 종료',
        export: '수출',
        fallbackToDefaultLocale: '기본 locale로 대체',
        false: '거짓',
        filter: '필터',
        filters: '필터',
        filterWhere: '{{label}} 필터링 조건',
        globals: '글로벌',
        goBack: '돌아가기',
        groupByLabel: '{{label}}로 그룹화',
        import: '수입',
        isEditing: '편집 중',
        item: '항목',
        items: '항목들',
        language: '언어',
        lastModified: '마지막 수정 일시',
        leaveAnyway: '그래도 나가시겠습니까?',
        leaveWithoutSaving: '저장하지 않고 나가기',
        light: '라이트',
        livePreview: '실시간 미리보기',
        loading: '불러오는 중',
        locale: 'locale',
        locales: 'locale',
        menu: '메뉴',
        moreOptions: '더 많은 옵션',
        move: '움직이세요',
        moveConfirm: '당신은 <1>{{destination}}</1>로 {{count}}개의 {{label}}을(를) 이동하려고 합니다. 확실합니까?',
        moveCount: '{{count}} {{label}} 이동',
        moveDown: '아래로 이동',
        moveUp: '위로 이동',
        moving: '이동하는',
        movingCount: '{{count}} {{label}}을(를) 이동시킵니다.',
        newPassword: '새 비밀번호',
        next: '다음',
        no: '아니요',
        noDateSelected: '선택된 날짜가 없습니다.',
        noFiltersSet: '설정된 필터 없음',
        noLabel: '<{{label}} 없음>',
        none: '없음',
        noOptions: '옵션 없음',
        noResults: '{{label}}를 찾을 수 없습니다. 아직 {{label}}이 없거나 설정한 필터와 일치하는 것이 없습니다.',
        notFound: '찾을 수 없음',
        nothingFound: '찾을 수 없습니다',
        noTrashResults: '휴지통에 {{label}}이 없습니다.',
        noUpcomingEventsScheduled: '예정된 행사가 없습니다.',
        noValue: '값 없음',
        of: '의',
        only: '오직',
        open: '열기',
        or: '또는',
        order: '순서',
        overwriteExistingData: '기존 필드 데이터 덮어쓰기',
        pageNotFound: '페이지를 찾을 수 없음',
        password: '비밀번호',
        pasteField: '필드 붙여넣기',
        pasteRow: '행 붙여넣기',
        payloadSettings: 'Payload 설정',
        permanentlyDelete: '영구적으로 삭제',
        permanentlyDeletedCountSuccessfully: '영구적으로 {{count}} {{label}}가 성공적으로 삭제되었습니다.',
        perPage: '페이지당 개수: {{limit}}',
        previous: '이전',
        reindex: '재인덱싱',
        reindexingAll: '모든 {{collections}}를 다시 인덱싱하는 중입니다.',
        remove: '제거',
        rename: '이름 변경',
        reset: '초기화',
        resetPreferences: '기본 설정으로 재설정',
        resetPreferencesDescription: '이렇게 하면 모든 기본 설정이 기본값으로 재설정됩니다.',
        resettingPreferences: '기본 설정을 재설정하는 중.',
        restore: '복원',
        restoreAsPublished: '게시된 버전으로 복원하다',
        restoredCountSuccessfully: '성공적으로 {{count}} {{label}}를 복원했습니다.',
        restoring: '원래 텍스트의 의미를 Payload 문맥 내에서 존중하십시오. 여기에는 매우 특정한 의미를 가진 일반 Payload 용어 목록이 있습니다:\n    - Collection: 컬렉션은 공통 구조와 목적을 공유하는 문서의 그룹입니다. 컬렉션은 Payload에서 컨텐츠를 구성하고 관리하는 데 사용됩니다.\n    - Field: 필드는 컬렉션 내의 문서에 있는 특정 데이터 조각입니다.',
        row: '행',
        rows: '행',
        save: '저장',
        saving: '저장 중...',
        schedulePublishFor: '{{title}}에 대한 게시 일정 설정',
        searchBy: '{{label}}로 검색',
        select: '선택하십시오',
        selectAll: '{{count}}개 {{label}} 모두 선택',
        selectAllRows: '모든 행 선택',
        selectedCount: '{{count}}개의 {{label}} 선택됨',
        selectLabel: '{{label}}을 선택하십시오.',
        selectValue: '값 선택',
        showAllLabel: '{{label}} 모두 표시',
        sorryNotFound: '죄송합니다. 요청과 일치하는 항목이 없습니다.',
        sort: '정렬',
        sortByLabelDirection: '{{label}} {{direction}}으로 정렬',
        stayOnThisPage: '이 페이지에 머무르기',
        submissionSuccessful: '제출이 완료되었습니다.',
        submit: '제출',
        submitting: '제출 중...',
        success: '성공',
        successfullyCreated: '{{label}}이(가) 생성되었습니다.',
        successfullyDuplicated: '{{label}}이(가) 복제되었습니다.',
        successfullyReindexed: '{{collections}} 컬렉션에서 {{total}} 문서 중 {{count}} 문서가 성공적으로 재인덱싱되었습니다.',
        takeOver: '인수하기',
        thisLanguage: '한국어',
        time: '시간',
        timezone: '시간대',
        titleDeleted: '{{label}} "{{title}}"을(를) 삭제했습니다.',
        titleRestored: '"{{label}}" "{{title}}"이(가) 성공적으로 복원되었습니다.',
        titleTrashed: '"{{label}}" "{{title}}"이(가) 휴지통으로 이동되었습니다.',
        trash: '휴지통',
        trashedCountSuccessfully: '{{count}} {{label}}가 휴지통으로 이동했습니다.',
        true: '참',
        unauthorized: '권한 없음',
        unsavedChanges: '저장되지 않은 변경 사항이 있습니다. 계속하기 전에 저장하거나 무시하십시오.',
        unsavedChangesDuplicate: '저장되지 않은 변경 사항이 있습니다. 복제를 계속하시겠습니까?',
        untitled: '제목 없음',
        upcomingEvents: '다가오는 이벤트',
        updatedAt: '업데이트 일시',
        updatedCountSuccessfully: '{{count}}개의 {{label}}을(를) 업데이트했습니다.',
        updatedLabelSuccessfully: '{{label}}이(가) 성공적으로 업데이트되었습니다.',
        updatedSuccessfully: '성공적으로 업데이트되었습니다.',
        updateForEveryone: '모두를 위한 업데이트',
        updating: '업데이트 중',
        uploading: '업로드 중',
        uploadingBulk: '{{current}} / {{total}} 업로드 중',
        user: '사용자',
        username: '사용자 이름',
        users: '사용자',
        value: '값',
        viewing: '열람',
        viewReadOnly: '읽기 전용으로 보기',
        welcome: '환영합니다',
        yes: '네'
    },
    localization: {
        cannotCopySameLocale: '동일한 로캘에 복사할 수 없습니다.',
        copyFrom: '에서 복사하십시오.',
        copyFromTo: '{{from}}에서 {{to}}로 복사하기',
        copyTo: '복사하기',
        copyToLocale: '로케일로 복사',
        localeToPublish: '발행할 장소',
        selectLocaleToCopy: '복사할 지역을 선택하십시오.'
    },
    operators: {
        contains: '포함',
        equals: '같음',
        exists: '존재',
        intersects: '교차합니다',
        isGreaterThan: '보다 큼',
        isGreaterThanOrEqualTo: '보다 크거나 같음',
        isIn: '포함됨',
        isLessThan: '보다 작음',
        isLessThanOrEqualTo: '보다 작거나 같음',
        isLike: '유사',
        isNotEqualTo: '같지 않음',
        isNotIn: '포함되지 않음',
        isNotLike: '같지 않다',
        near: '근처',
        within: '내에서'
    },
    upload: {
        addFile: '파일 추가',
        addFiles: '파일 추가',
        bulkUpload: '일괄 업로드',
        crop: '자르기',
        cropToolDescription: '선택한 영역의 모퉁이를 드래그하거나 새로운 영역을 그리거나 아래의 값을 조정하세요.',
        download: '다운로드',
        dragAndDrop: '파일을 끌어다 놓으세요',
        dragAndDropHere: '또는 여기로 파일을 끌어다 놓으세요',
        editImage: '이미지 수정',
        fileName: '파일 이름',
        fileSize: '파일 크기',
        filesToUpload: '업로드할 파일들',
        fileToUpload: '업로드할 파일',
        focalPoint: '초점',
        focalPointDescription: '미리보기에서 초점을 직접 드래그하거나 아래의 값을 조정하세요.',
        height: '높이',
        lessInfo: '정보 숨기기',
        moreInfo: '정보 더보기',
        noFile: '파일 없음',
        pasteURL: 'URL 붙여넣기',
        previewSizes: '미리보기 크기',
        selectCollectionToBrowse: '찾을 컬렉션 선택',
        selectFile: '파일 선택',
        setCropArea: '자르기 영역 설정',
        setFocalPoint: '초점 설정',
        sizes: '크기',
        sizesFor: '{{label}} 크기',
        width: '너비'
    },
    validation: {
        emailAddress: '유효한 이메일 주소를 입력하세요.',
        enterNumber: '유효한 숫자를 입력하세요.',
        fieldHasNo: '이 입력란에는 {{label}}이(가) 없습니다.',
        greaterThanMax: '{{value}}은(는) 최대 허용된 {{label}}인 {{max}}개보다 큽니다.',
        invalidInput: '이 입력란에는 유효하지 않은 입력이 있습니다.',
        invalidSelection: '이 입력란에는 유효하지 않은 선택이 있습니다.',
        invalidSelections: '이 입력란에는 다음과 같은 유효하지 않은 선택 사항이 있습니다:',
        lessThanMin: '{{value}}은(는) 최소 허용된 {{label}}인 {{min}}개보다 작습니다.',
        limitReached: '제한에 도달했습니다. {{max}}개 항목만 추가할 수 있습니다.',
        longerThanMin: '이 값은 최소 길이인 {{minLength}}자보다 길어야 합니다.',
        notValidDate: '"{{value}}"은(는) 유효한 날짜가 아닙니다.',
        required: '이 입력란은 필수입니다.',
        requiresAtLeast: '이 입력란운 최소한 {{count}} {{label}}이 필요합니다.',
        requiresNoMoreThan: '이 입력란은 최대 {{count}} {{label}} 이하이어야 합니다.',
        requiresTwoNumbers: '이 입력란은 두 개의 숫자가 필요합니다.',
        shorterThanMax: '이 값은 최대 길이인 {{maxLength}}자보다 짧아야 합니다.',
        timezoneRequired: '시간대가 필요합니다.',
        trueOrFalse: '이 입력란은 true 또는 false만 가능합니다.',
        username: '유효한 사용자 이름을 입력해 주세요. 글자, 숫자, 하이픈, 마침표, 및 밑줄을 사용할 수 있습니다.',
        validUploadID: '이 입력란은 유효한 업로드 ID가 아닙니다.'
    },
    version: {
        type: '유형',
        aboutToPublishSelection: '선택한 {{label}}을(를) 게시하려고 합니다. 계속하시겠습니까?',
        aboutToRestore: '이 {{label}} 문서를 {{versionDate}}기준 버전으로 복원하려고 합니다.',
        aboutToRestoreGlobal: '글로벌 {{label}}을(를) {{versionDate}}기준 버전으로 복원하려고 합니다.',
        aboutToRevertToPublished: '이 문서의 변경 사항을 게시된 상태로 되돌리려고 합니다. 계속하시겠습니까?',
        aboutToUnpublish: '이 문서를 게시 해제하려고 합니다. 계속하시겠습니까?',
        aboutToUnpublishSelection: '선택한 {{label}}을(를) 게시 해제하려고 합니다. 계속하시겠습니까?',
        autosave: '자동 저장',
        autosavedSuccessfully: '자동 저장이 완료되었습니다.',
        autosavedVersion: '자동 저장된 버전',
        changed: '변경됨',
        changedFieldsCount_one: '{{count}} 변경된 필드',
        changedFieldsCount_other: '{{count}}개의 변경된 필드',
        compareVersion: '비교할 버전 선택:',
        compareVersions: '버전 비교',
        comparingAgainst: '비교 대상으로',
        confirmPublish: '게시하기',
        confirmRevertToSaved: '저장된 상태로 되돌리기',
        confirmUnpublish: '게시 해제하기',
        confirmVersionRestoration: '버전 복원하기',
        currentDocumentStatus: '현재 {{docStatus}} 문서',
        currentDraft: '현재 초안',
        currentlyPublished: '현재 게시됨',
        currentlyViewing: '현재 보고 있습니다',
        currentPublishedVersion: '현재 게시된 버전',
        draft: '초안',
        draftSavedSuccessfully: '초안이 저장되었습니다.',
        lastSavedAgo: '마지막으로 저장한지 {{distance}} 전',
        modifiedOnly: '수정된 것만',
        moreVersions: '더 많은 버전...',
        noFurtherVersionsFound: '더 이상의 버전을 찾을 수 없습니다.',
        noRowsFound: '{{label}}을(를) 찾을 수 없음',
        noRowsSelected: '선택된 {{label}} 없음',
        preview: '미리보기',
        previouslyDraft: '이전에는 초안',
        previouslyPublished: '이전에 발표된',
        previousVersion: '이전 버전',
        problemRestoringVersion: '이 버전을 복원하는 중 문제가 발생했습니다.',
        publish: '게시',
        publishAllLocales: '모든 로케일을 게시하십시오',
        publishChanges: '변경 사항 게시',
        published: '게시됨',
        publishIn: '{{locale}}에서 게시하십시오.',
        publishing: '게시',
        restoreAsDraft: '임시 저장으로 복원',
        restoredSuccessfully: '복원이 완료되었습니다.',
        restoreThisVersion: '이 버전 복원',
        restoring: '복원 중...',
        reverting: '되돌리는 중...',
        revertToPublished: '게시된 상태로 되돌리기',
        saveDraft: '초안 저장',
        scheduledSuccessfully: '성공적으로 예약되었습니다.',
        schedulePublish: '발행 일정',
        selectLocales: '표시할 locale 선택',
        selectVersionToCompare: '비교할 버전 선택',
        showingVersionsFor: '다음 버전 표시 중:',
        showLocales: 'locale 표시:',
        specificVersion: '특정 버전',
        status: '상태',
        unpublish: '게시 해제',
        unpublishing: '게시 해제 중...',
        version: '버전',
        versionAgo: '{{distance}} 전',
        versionCount_many: '{{count}}개의 버전을 찾았습니다',
        versionCount_none: '버전을 찾을 수 없습니다',
        versionCount_one: '{{count}}개의 버전을 찾았습니다',
        versionCount_other: '{{count}}개의 버전을 찾았습니다',
        versionCreatedOn: '{{version}} 생성 날짜:',
        versionID: '버전 ID',
        versions: '버전',
        viewingVersion: '{{entityLabel}} {{documentTitle}}의 버전 보기',
        viewingVersionGlobal: '글로벌 {{entityLabel}}의 버전 보기',
        viewingVersions: '{{entityLabel}} {{documentTitle}}에 대한 버전 보기',
        viewingVersionsGlobal: '글로벌 {{entityLabel}}에 대한 버전 보기'
    }
};
export const ko = {
    dateFNSKey: 'ko',
    translations: koTranslations
};

//# sourceMappingURL=ko.js.map