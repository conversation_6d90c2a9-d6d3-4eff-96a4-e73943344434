{"version": 3, "sources": ["../../src/languages/sl.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const slTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: '<PERSON><PERSON><PERSON>',\n    accountOfCurrentUser: 'Ra<PERSON><PERSON> trenutnega uporabnika',\n    accountVerified: '<PERSON><PERSON><PERSON> uspešno preverjen.',\n    alreadyActivated: 'Že aktivirano',\n    alreadyLoggedIn: 'Že prijavljeni',\n    apiKey: 'API ključ',\n    authenticated: 'Avtenticirano',\n    backToLogin: 'Nazaj na prijavo',\n    beginCreateFirstUser: 'Za začetek ustvarite prvega uporabnika.',\n    changePassword: 'Spremeni geslo',\n    checkYourEmailForPasswordReset:\n      'Če je e-poštni naslov povezan z računom, boste kmalu prejeli navodila za ponastavitev gesla. Prosimo, preverite mapo za neželeno pošto ali spam, če e-pošte ne vidite v vašem prejemu.',\n    confirmGeneration: 'Potrdi generiranje',\n    confirmPassword: 'Potrdi geslo',\n    createFirstUser: 'Ustvari prvega uporabnika',\n    emailNotValid: 'Vneseni e-poštni naslov ni veljaven',\n    emailOrUsername: 'E-pošta ali uporabniško ime',\n    emailSent: 'E-pošta poslana',\n    emailVerified: 'E-pošta uspešno preverjena.',\n    enableAPIKey: 'Omogoči API ključ',\n    failedToUnlock: 'Odklepanje ni uspelo',\n    forceUnlock: 'Prisili odklepanje',\n    forgotPassword: 'Pozabljeno geslo',\n    forgotPasswordEmailInstructions:\n      'Vnesite svoj e-poštni naslov. Prejeli boste e-pošto z navodili za ponastavitev gesla.',\n    forgotPasswordQuestion: 'Ste pozabili geslo?',\n    forgotPasswordUsernameInstructions:\n      'Vnesite svoje uporabniško ime. Navodila za ponastavitev gesla bodo poslana na e-poštni naslov, povezan z vašim uporabniškim imenom.',\n    generate: 'Generiraj',\n    generateNewAPIKey: 'Generiraj nov API ključ',\n    generatingNewAPIKeyWillInvalidate:\n      'Generiranje novega API ključa bo <1>razveljavilo</1> prejšnji ključ. Ste prepričani, da želite nadaljevati?',\n    lockUntil: 'Zakleni do',\n    logBackIn: 'Ponovno se prijavi',\n    loggedIn: 'Za prijavo z drugim uporabnikom se morate najprej <0>odjaviti</0>.',\n    loggedInChangePassword:\n      'Za spremembo gesla pojdite na svoj <0>račun</0> in tam uredite svoje geslo.',\n    loggedOutInactivity: 'Odjavljeni ste bili zaradi neaktivnosti.',\n    loggedOutSuccessfully: 'Uspešno ste se odjavili.',\n    loggingOut: 'Odjavljanje...',\n    login: 'Prijava',\n    loginAttempts: 'Poskusi prijave',\n    loginUser: 'Prijavi uporabnika',\n    loginWithAnotherUser: 'Za prijavo z drugim uporabnikom se morate najprej <0>odjaviti</0>.',\n    logOut: 'Odjava',\n    logout: 'Odjava',\n    logoutSuccessful: 'Odjava uspešna.',\n    logoutUser: 'Odjavi uporabnika',\n    newAccountCreated:\n      'Pravkar je bil ustvarjen nov račun za dostop do <a href=\"{{serverURL}}\">{{serverURL}}</a> Prosimo, kliknite na naslednjo povezavo ali jo prilepite v svoj brskalnik za potrditev e-pošte: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Po potrditvi e-pošte se boste lahko uspešno prijavili.',\n    newAPIKeyGenerated: 'Nov API ključ generiran.',\n    newPassword: 'Novo geslo',\n    passed: 'Avtentikacija uspešna',\n    passwordResetSuccessfully: 'Geslo uspešno ponastavljeno.',\n    resetPassword: 'Ponastavi geslo',\n    resetPasswordExpiration: 'Potek ponastavitve gesla',\n    resetPasswordToken: 'Žeton za ponastavitev gesla',\n    resetYourPassword: 'Ponastavite svoje geslo',\n    stayLoggedIn: 'Ostani prijavljen',\n    successfullyRegisteredFirstUser: 'Uspešno registriran prvi uporabnik.',\n    successfullyUnlocked: 'Uspešno odklenjeno',\n    tokenRefreshSuccessful: 'Osvežitev žetona uspešna.',\n    unableToVerify: 'Ni mogoče preveriti',\n    username: 'Uporabniško ime',\n    usernameNotValid: 'Vneseno uporabniško ime ni veljavno',\n    verified: 'Preverjeno',\n    verifiedSuccessfully: 'Uspešno preverjeno',\n    verify: 'Preveri',\n    verifyUser: 'Preveri uporabnika',\n    verifyYourEmail: 'Potrdite svojo e-pošto',\n    youAreInactive:\n      'Že nekaj časa niste bili aktivni in boste kmalu samodejno odjavljeni zaradi varnosti. Želite ostati prijavljeni?',\n    youAreReceivingResetPassword:\n      'To sporočilo ste prejeli, ker ste vi (ali nekdo drug) zahtevali ponastavitev gesla za vaš račun. Prosimo, kliknite na naslednjo povezavo ali jo prilepite v svoj brskalnik za dokončanje postopka:',\n    youDidNotRequestPassword:\n      'Če tega niste zahtevali, prezrite to e-pošto in vaše geslo bo ostalo nespremenjeno.',\n  },\n  error: {\n    accountAlreadyActivated: 'Ta račun je že aktiviran.',\n    autosaving: 'Pri samodejnem shranjevanju tega dokumenta je prišlo do težave.',\n    correctInvalidFields: 'Prosimo, popravite neveljavna polja.',\n    deletingFile: 'Pri brisanju datoteke je prišlo do napake.',\n    deletingTitle:\n      'Pri brisanju {{title}} je prišlo do napake. Prosimo, preverite povezavo in poskusite znova.',\n    documentNotFound:\n      'Dokumenta z ID {{id}} ni bilo mogoče najti. Morda je bil izbrisan ali nikoli ni obstajal, ali pa do njega nimate dostopa.',\n    emailOrPasswordIncorrect: 'Vnesena e-pošta ali geslo je napačno.',\n    followingFieldsInvalid_one: 'Naslednje polje je neveljavno:',\n    followingFieldsInvalid_other: 'Naslednja polja so neveljavna:',\n    incorrectCollection: 'Napačna zbirka',\n    insufficientClipboardPermissions:\n      'Dostop do odložišča je bil zavrnjen. Preverite dovoljenja za odložišče.',\n    invalidClipboardData: 'Neveljavni podatki v odložišču.',\n    invalidFileType: 'Neveljaven tip datoteke',\n    invalidFileTypeValue: 'Neveljaven tip datoteke: {{value}}',\n    invalidRequestArgs: 'V zahtevi so bili poslani neveljavni argumenti: {{args}}',\n    loadingDocument: 'Pri nalaganju dokumenta z ID-jem {{id}} je prišlo do težave.',\n    localesNotSaved_one: 'Naslednjega jezika ni bilo mogoče shraniti:',\n    localesNotSaved_other: 'Naslednjih jezikov ni bilo mogoče shraniti:',\n    logoutFailed: 'Odjava ni uspela.',\n    missingEmail: 'Manjka e-pošta.',\n    missingIDOfDocument: 'Manjka ID dokumenta za posodobitev.',\n    missingIDOfVersion: 'Manjka ID različice.',\n    missingRequiredData: 'Manjkajo zahtevani podatki.',\n    noFilesUploaded: 'Nobena datoteka ni bila naložena.',\n    noMatchedField: 'Za \"{{label}}\" ni bilo najdeno ujemajoče se polje',\n    notAllowedToAccessPage: 'Nimate dovoljenja za dostop do te strani.',\n    notAllowedToPerformAction: 'Nimate dovoljenja za izvedbo tega dejanja.',\n    notFound: 'Zahtevani vir ni bil najden.',\n    noUser: 'Ni uporabnika',\n    previewing: 'Pri predogledu tega dokumenta je prišlo do težave.',\n    problemUploadingFile: 'Pri nalaganju datoteke je prišlo do težave.',\n    restoringTitle:\n      'Pri obnavljanju {{title}} je prišlo do napake. Prosimo, preverite svojo povezavo in poskusite znova.',\n    tokenInvalidOrExpired: 'Žeton je neveljaven ali je potekel.',\n    tokenNotProvided: 'Žeton ni bil posredovan.',\n    unableToCopy: 'Kopiranje ni mogoče.',\n    unableToDeleteCount: 'Ni bilo mogoče izbrisati {{count}} od {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Napaka pri reindeksiranju zbirke {{collection}}. Operacija je bila prekinjena.',\n    unableToUpdateCount: 'Ni bilo mogoče posodobiti {{count}} od {{total}} {{label}}.',\n    unauthorized: 'Neavtorizirano, za to zahtevo morate biti prijavljeni.',\n    unauthorizedAdmin: 'Neavtorizirano, ta uporabnik nima dostopa do skrbniškega vmesnika.',\n    unknown: 'Prišlo je do neznane napake.',\n    unPublishingDocument: 'Pri umiku objave tega dokumenta je prišlo do težave.',\n    unspecific: 'Prišlo je do napake.',\n    unverifiedEmail: 'Pred prijavo preverite svoj e-poštni naslov.',\n    userEmailAlreadyRegistered: 'Uporabnik s tem e-poštnim naslovom je že registriran.',\n    userLocked: 'Ta uporabnik je zaklenjen zaradi prevelikega števila neuspešnih poskusov prijave.',\n    usernameAlreadyRegistered: 'Uporabnik s tem uporabniškim imenom je že registriran.',\n    usernameOrPasswordIncorrect: 'Vneseno uporabniško ime ali geslo je napačno.',\n    valueMustBeUnique: 'Vrednost mora biti unikatna',\n    verificationTokenInvalid: 'Žeton za preverjanje je neveljaven.',\n  },\n  fields: {\n    addLabel: 'Dodaj {{label}}',\n    addLink: 'Dodaj povezavo',\n    addNew: 'Dodaj novo',\n    addNewLabel: 'Dodaj nov {{label}}',\n    addRelationship: 'Dodaj povezavo',\n    addUpload: 'Dodaj nalaganje',\n    block: 'blok',\n    blocks: 'bloki',\n    blockType: 'Tip bloka',\n    chooseBetweenCustomTextOrDocument:\n      'Izberite med vnosom URL-ja po meri ali povezavo na drug dokument.',\n    chooseDocumentToLink: 'Izberite dokument za povezavo',\n    chooseFromExisting: 'Izberite iz obstoječih',\n    chooseLabel: 'Izberite {{label}}',\n    collapseAll: 'Strni vse',\n    customURL: 'URL po meri',\n    editLabelData: 'Uredi podatke {{label}}',\n    editLink: 'Uredi povezavo',\n    editRelationship: 'Uredi povezavo',\n    enterURL: 'Vnesite URL',\n    internalLink: 'Notranja povezava',\n    itemsAndMore: '{{items}} in še {{count}}',\n    labelRelationship: '{{label}} povezava',\n    latitude: 'Zemljepisna širina',\n    linkedTo: 'Povezano z <0>{{label}}</0>',\n    linkType: 'Tip povezave',\n    longitude: 'Zemljepisna dolžina',\n    newLabel: 'Nov {{label}}',\n    openInNewTab: 'Odpri v novem zavihku',\n    passwordsDoNotMatch: 'Gesli se ne ujemata.',\n    relatedDocument: 'Povezan dokument',\n    relationTo: 'Povezava z',\n    removeRelationship: 'Odstrani povezavo',\n    removeUpload: 'Odstrani nalaganje',\n    saveChanges: 'Shrani spremembe',\n    searchForBlock: 'Išči blok',\n    selectExistingLabel: 'Izberi obstoječ {{label}}',\n    selectFieldsToEdit: 'Izberi polja za urejanje',\n    showAll: 'Pokaži vse',\n    swapRelationship: 'Zamenjaj povezavo',\n    swapUpload: 'Zamenjaj nalaganje',\n    textToDisplay: 'Besedilo za prikaz',\n    toggleBlock: 'Preklopi blok',\n    uploadNewLabel: 'Naloži nov {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Brskaj po mapi',\n    byFolder: 'Po mapi',\n    deleteFolder: 'Izbriši mapo',\n    folderName: 'Ime mape',\n    folders: 'Mape',\n    folderTypeDescription:\n      'Izberite, katere vrste dokumentov zbirke naj bodo dovoljene v tej mapi.',\n    itemHasBeenMoved: '{{title}} je bil premaknjen v {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} je bil premaknjen v korensko mapo.',\n    itemsMovedToFolder: '{{title}} premaknjeno v {{folderName}}',\n    itemsMovedToRoot: '{{title}} premaknjeno v korensko mapo',\n    moveFolder: 'Premakni mapo',\n    moveItemsToFolderConfirmation:\n      'Ravno se pripravljate na premik <1>{{count}} {{label}}</1> v mapo <2>{{toFolder}}</2>. Ste prepričani?',\n    moveItemsToRootConfirmation:\n      'Ravno boste premaknili <1>{{count}} {{label}}</1> v korensko mapo. Ste prepričani?',\n    moveItemToFolderConfirmation:\n      'Pravkar boste premaknili <1>{{title}}</1> v <2>{{toFolder}}</2>. Ste prepričani?',\n    moveItemToRootConfirmation:\n      'Pravkar boste premaknili <1>{{title}}</1> v korensko mapo. Ali ste prepričani?',\n    movingFromFolder: 'Premik {{title}} iz {{fromFolder}}',\n    newFolder: 'Nova mapa',\n    noFolder: 'Brez mape',\n    renameFolder: 'Preimenuj Mapo',\n    searchByNameInFolder: 'Iskanje po imenu v {{folderName}}',\n    selectFolderForItem: 'Izberite mapo za {{title}}',\n  },\n  general: {\n    name: 'Ime',\n    aboutToDelete: 'Izbrisali boste {{label}} <1>{{title}}</1>. Ste prepričani?',\n    aboutToDeleteCount_many: 'Izbrisali boste {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Izbrisali boste {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Izbrisali boste {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Ravno boste trajno izbrisali {{label}} <1>{{title}}</1>. Ste prepričani?',\n    aboutToPermanentlyDeleteTrash:\n      'Pravkar boste trajno izbrisali <0>{{count}}</0> <1>{{label}}</1> iz smetnjaka. Ali ste prepričani?',\n    aboutToRestore: 'Ravno se odpravljate na obnovitev {{label}} <1>{{title}}</1>. Ste prepričani?',\n    aboutToRestoreAsDraft:\n      'Pravkar boste obnovili {{label}} <1>{{title}}</1> kot osnutek. Ali ste prepričani?',\n    aboutToRestoreAsDraftCount: 'Pravkar boste obnovili {{count}} {{label}} kot osnutek.',\n    aboutToRestoreCount: 'Pravkar boste obnovili {{count}} {{label}}',\n    aboutToTrash: 'Pravkar boste premaknili {{label}} <1>{{title}}</1> v smeti. Ste prepričani?',\n    aboutToTrashCount: 'Pravkar boste premaknili {{count}} {{label}} v smeti.',\n    addBelow: 'Dodaj spodaj',\n    addFilter: 'Dodaj filter',\n    adminTheme: 'Tema skrbnika',\n    all: 'Vse',\n    allCollections: 'Vse Zbirke',\n    allLocales: 'Vse lokacije',\n    and: 'In',\n    anotherUser: 'Drug uporabnik',\n    anotherUserTakenOver: 'Drug uporabnik je prevzel urejanje tega dokumenta.',\n    applyChanges: 'Uporabi spremembe',\n    ascending: 'Naraščajoče',\n    automatic: 'Samodejno',\n    backToDashboard: 'Nazaj na nadzorno ploščo',\n    cancel: 'Prekliči',\n    changesNotSaved:\n      'Vaše spremembe niso shranjene. Če zapustite zdaj, boste izgubili svoje spremembe.',\n    clear: 'Čisto',\n    clearAll: 'Počisti vse',\n    close: 'Zapri',\n    collapse: 'Strni',\n    collections: 'Zbirke',\n    columns: 'Stolpci',\n    columnToSort: 'Stolpec za razvrščanje',\n    confirm: 'Potrdi',\n    confirmCopy: 'Potrdi kopiranje',\n    confirmDeletion: 'Potrdi brisanje',\n    confirmDuplication: 'Potrdi podvajanje',\n    confirmMove: 'Potrdi premik',\n    confirmReindex: 'Ponovno indeksirati vse {{collections}}?',\n    confirmReindexAll: 'Ponovno indeksirati vse zbirke?',\n    confirmReindexDescription:\n      'To bo odstranilo obstoječe indekse in ponovno indeksiralo dokumente v zbirkah {{collections}}.',\n    confirmReindexDescriptionAll:\n      'To bo odstranilo obstoječe indekse in ponovno indeksiralo dokumente v vseh zbirkah.',\n    confirmRestoration: 'Potrdite obnovitev',\n    copied: 'Kopirano',\n    copy: 'Kopiraj',\n    copyField: 'Kopiraj polje',\n    copying: 'Kopiranje',\n    copyRow: 'Kopiraj vrstico',\n    copyWarning: 'Prepisali boste {{to}} z {{from}} za {{label}} {{title}}. Ste prepričani?',\n    create: 'Ustvari',\n    created: 'Ustvarjeno',\n    createdAt: 'Ustvarjeno',\n    createNew: 'Ustvari novo',\n    createNewLabel: 'Ustvari nov {{label}}',\n    creating: 'Ustvarjanje',\n    creatingNewLabel: 'Ustvarjanje novega {{label}}',\n    currentlyEditing:\n      'trenutno ureja ta dokument. Če prevzamete, jim bo onemogočeno nadaljnje urejanje in lahko izgubijo neshranjene spremembe.',\n    custom: 'Po meri',\n    dark: 'Temno',\n    dashboard: 'Nadzorna plošča',\n    delete: 'Izbriši',\n    deleted: 'Izbrisano',\n    deletedAt: 'Izbrisano ob',\n    deletedCountSuccessfully: 'Uspešno izbrisano {{count}} {{label}}.',\n    deletedSuccessfully: 'Uspešno izbrisano.',\n    deletePermanently: 'Preskoči smetnjak in trajno izbriši',\n    deleting: 'Brisanje...',\n    depth: 'Globina',\n    descending: 'Padajoče',\n    deselectAllRows: 'Odznači vse vrstice',\n    document: 'Dokument',\n    documentIsTrashed: 'Ta {{label}} je v smetnjaku in je samo za branje.',\n    documentLocked: 'Dokument zaklenjen',\n    documents: 'Dokumenti',\n    duplicate: 'Podvoji',\n    duplicateWithoutSaving: 'Podvoji brez shranjevanja sprememb',\n    edit: 'Uredi',\n    editAll: 'Uredi vse',\n    editedSince: 'Urejeno od',\n    editing: 'Urejanje',\n    editingLabel_many: 'Urejanje {{count}} {{label}}',\n    editingLabel_one: 'Urejanje {{count}} {{label}}',\n    editingLabel_other: 'Urejanje {{count}} {{label}}',\n    editingTakenOver: 'Urejanje prevzeto',\n    editLabel: 'Uredi {{label}}',\n    email: 'E-pošta',\n    emailAddress: 'E-poštni naslov',\n    emptyTrash: 'Izprazni koš',\n    emptyTrashLabel: 'Izprazni {{label}} smeti',\n    enterAValue: 'Vnesite vrednost',\n    error: 'Napaka',\n    errors: 'Napake',\n    exitLivePreview: 'Izhodi iz živega predogleda',\n    export: 'Izvoz',\n    fallbackToDefaultLocale: 'Uporabi privzeti jezik',\n    false: 'Ne',\n    filter: 'Filter',\n    filters: 'Filtri',\n    filterWhere: 'Filtriraj {{label}} kjer',\n    globals: 'Globalne nastavitve',\n    goBack: 'Nazaj',\n    groupByLabel: 'Razvrsti po {{label}}',\n    import: 'Uvoz',\n    isEditing: 'ureja',\n    item: 'predmet',\n    items: 'predmeti',\n    language: 'Jezik',\n    lastModified: 'Zadnja sprememba',\n    leaveAnyway: 'Vseeno zapusti',\n    leaveWithoutSaving: 'Zapusti brez shranjevanja',\n    light: 'Svetlo',\n    livePreview: 'Predogled',\n    loading: 'Nalaganje',\n    locale: 'Jezik',\n    locales: 'Jeziki',\n    menu: 'Meni',\n    moreOptions: 'Več možnosti',\n    move: 'Premakni',\n    moveConfirm:\n      'Pravkar boste premaknili {{count}} {{label}} na <1>{{destination}}</1>. Ste prepričani?',\n    moveCount: 'Premakni {{count}} {{label}}',\n    moveDown: 'Premakni dol',\n    moveUp: 'Premakni gor',\n    moving: 'Premikanje',\n    movingCount: 'Premikanje {{count}} {{label}}',\n    newPassword: 'Novo geslo',\n    next: 'Naprej',\n    no: 'Ne',\n    noDateSelected: 'Izbran ni noben datum',\n    noFiltersSet: 'Ni nastavljenih filtrov',\n    noLabel: '<Brez {{label}}>',\n    none: 'Brez',\n    noOptions: 'Ni možnosti',\n    noResults:\n      'Ni najdenih {{label}}. Ali {{label}} še ne obstajajo ali pa ne ustrezajo filtrom, ki ste jih določili zgoraj.',\n    notFound: 'Ni najdeno',\n    nothingFound: 'Nič ni najdeno',\n    noTrashResults: 'Ni {{label}} v smetnjaku.',\n    noUpcomingEventsScheduled: 'Ni načrtovanih prihajajočih dogodkov.',\n    noValue: 'Ni vrednosti',\n    of: 'od',\n    only: 'Samo',\n    open: 'Odpri',\n    or: 'Ali',\n    order: 'Vrstni red',\n    overwriteExistingData: 'Prepišite obstoječe podatke polja',\n    pageNotFound: 'Stran ni najdena',\n    password: 'Geslo',\n    pasteField: 'Prilepi polje',\n    pasteRow: 'Prilepi vrstico',\n    payloadSettings: 'Nastavitve Payloada',\n    permanentlyDelete: 'Trajno Izbrisano',\n    permanentlyDeletedCountSuccessfully: 'Uspešno trajno izbrisano {{count}} {{label}}.',\n    perPage: 'Na stran: {{limit}}',\n    previous: 'Prejšnji',\n    reindex: 'Reindeksiraj',\n    reindexingAll: 'Ponovno indeksiranje vseh {{collections}}.',\n    remove: 'Odstrani',\n    rename: 'Preimenuj',\n    reset: 'Ponastavi',\n    resetPreferences: 'Ponastavi nastavitve',\n    resetPreferencesDescription: 'To bo ponastavilo vse vaše nastavitve na privzete vrednosti.',\n    resettingPreferences: 'Ponastavitev nastavitve.',\n    restore: 'Obnovi',\n    restoreAsPublished: 'Obnovi kot objavljeno različico',\n    restoredCountSuccessfully: 'Uspešno obnovljeno {{count}} {{label}}.',\n    restoring:\n      'Spoštujte pomen izvirnega besedila znotraj konteksta Payload. Tu je seznam pogostih izrazov Payload, ki imajo zelo specifične pomene:\\n    - Zbirka: Zbirka je skupina dokumentov, ki delijo skupno strukturo in namen. Zbirke se uporabljajo za organizacijo in upravljanje vsebine v Payload.\\n    - Polje: Polje je določen del podatkov znotraj dokumenta v zbirki. Polja opredeljujejo strukturo in vrsto podatkov, ki jih je mogoče sh',\n    row: 'Vrstica',\n    rows: 'Vrstice',\n    save: 'Shrani',\n    saving: 'Shranjevanje...',\n    schedulePublishFor: 'Načrtujte objavo za {{naslov}}',\n    searchBy: 'Išči po {{label}}',\n    select: 'Izberi',\n    selectAll: 'Izberi vse {{count}} {{label}}',\n    selectAllRows: 'Izberi vse vrstice',\n    selectedCount: '{{count}} {{label}} izbranih',\n    selectLabel: 'Izberite {{label}}',\n    selectValue: 'Izberi vrednost',\n    showAllLabel: 'Pokaži vse {{label}}',\n    sorryNotFound: 'Oprostite - ničesar ni mogoče najti, kar bi ustrezalo vaši zahtevi.',\n    sort: 'Razvrsti',\n    sortByLabelDirection: 'Razvrsti po {{label}} {{direction}}',\n    stayOnThisPage: 'Ostani na tej strani',\n    submissionSuccessful: 'Oddaja uspešna.',\n    submit: 'Oddaj',\n    submitting: 'Oddajanje...',\n    success: 'Uspeh',\n    successfullyCreated: '{{label}} uspešno ustvarjen.',\n    successfullyDuplicated: '{{label}} uspešno podvojen.',\n    successfullyReindexed:\n      'Uspešno reindeksiranih {{count}} od {{total}} dokumentov iz zbirk {{collections}}.',\n    takeOver: 'Prevzemi',\n    thisLanguage: 'Slovenščina',\n    time: 'Čas',\n    timezone: 'Časovni pas',\n    titleDeleted: '{{label}} \"{{title}}\" uspešno izbrisan.',\n    titleRestored: 'Oznaka \"{{title}}\" je bila uspešno obnovljena.',\n    titleTrashed: '{{label}} \"{{title}}\" premaknjeno v smeti.',\n    trash: 'Smeti',\n    trashedCountSuccessfully: '{{count}} {{label}} premaknjeno v smeti.',\n    true: 'Da',\n    unauthorized: 'Nepooblaščeno',\n    unsavedChanges: 'Neshranjene spremembe',\n    unsavedChangesDuplicate: 'Imate neshranjene spremembe. Želite nadaljevati s podvajanjem?',\n    untitled: 'Brez naslova',\n    upcomingEvents: 'Prihajajoči dogodki',\n    updatedAt: 'Posodobljeno',\n    updatedCountSuccessfully: 'Uspešno posodobljeno {{count}} {{label}}.',\n    updatedLabelSuccessfully: '{{label}} uspešno posodobljen.',\n    updatedSuccessfully: 'Uspešno posodobljeno.',\n    updateForEveryone: 'Posodobitev za vse',\n    updating: 'Posodabljanje',\n    uploading: 'Nalaganje',\n    uploadingBulk: 'Nalaganje {{current}} od {{total}}',\n    user: 'Uporabnik',\n    username: 'Uporabniško ime',\n    users: 'Uporabniki',\n    value: 'Vrednost',\n    viewing: 'Ogled',\n    viewReadOnly: 'Ogled samo za branje',\n    welcome: 'Dobrodošli',\n    yes: 'Da',\n  },\n  localization: {\n    cannotCopySameLocale: 'Ni mogoče kopirati v isti jezik',\n    copyFrom: 'Kopiraj iz',\n    copyFromTo: 'Kopiranje iz {{from}} v {{to}}',\n    copyTo: 'Kopiraj v',\n    copyToLocale: 'Kopiraj v jezik',\n    localeToPublish: 'Lokalno za objavo',\n    selectLocaleToCopy: 'Izberite jezik za kopiranje',\n  },\n  operators: {\n    contains: 'vsebuje',\n    equals: 'je enako',\n    exists: 'obstaja',\n    intersects: 'se seka',\n    isGreaterThan: 'je večje od',\n    isGreaterThanOrEqualTo: 'je večje ali enako',\n    isIn: 'je v',\n    isLessThan: 'je manjše od',\n    isLessThanOrEqualTo: 'je manjše ali enako',\n    isLike: 'je podobno',\n    isNotEqualTo: 'ni enako',\n    isNotIn: 'ni v',\n    isNotLike: 'ni podobno',\n    near: 'blizu',\n    within: 'znotraj',\n  },\n  upload: {\n    addFile: 'Dodaj datoteko',\n    addFiles: 'Dodaj datoteke',\n    bulkUpload: 'Množično nalaganje',\n    crop: 'Obreži',\n    cropToolDescription:\n      'Povlecite kote izbranega območja, narišite novo območje ali prilagodite vrednosti spodaj.',\n    download: 'Prenos',\n    dragAndDrop: 'Povlecite in spustite datoteko',\n    dragAndDropHere: 'ali povlecite in spustite datoteko sem',\n    editImage: 'Uredi sliko',\n    fileName: 'Ime datoteke',\n    fileSize: 'Velikost datoteke',\n    filesToUpload: 'Datoteke za nalaganje',\n    fileToUpload: 'Datoteka za nalaganje',\n    focalPoint: 'Žarišče',\n    focalPointDescription:\n      'Povlecite žarišče neposredno na predogledu ali prilagodite vrednosti spodaj.',\n    height: 'Višina',\n    lessInfo: 'Manj informacij',\n    moreInfo: 'Več informacij',\n    noFile: 'Ni datoteke.',\n    pasteURL: 'Prilepi URL',\n    previewSizes: 'Velikosti predogleda',\n    selectCollectionToBrowse: 'Izberite zbirko za brskanje',\n    selectFile: 'Izberite datoteko',\n    setCropArea: 'Nastavi območje obrezovanja',\n    setFocalPoint: 'Nastavi žarišče',\n    sizes: 'Velikosti',\n    sizesFor: 'Velikosti za {{label}}',\n    width: 'Širina',\n  },\n  validation: {\n    emailAddress: 'Vnesite veljaven e-poštni naslov.',\n    enterNumber: 'Vnesite veljavno številko.',\n    fieldHasNo: 'To polje nima {{label}}',\n    greaterThanMax: '{{value}} je večje od največje dovoljene {{label}} {{max}}.',\n    invalidInput: 'To polje ima neveljaven vnos.',\n    invalidSelection: 'To polje ima neveljavno izbiro.',\n    invalidSelections: 'To polje ima naslednje neveljavne izbire:',\n    lessThanMin: '{{value}} je manjše od najmanjše dovoljene {{label}} {{min}}.',\n    limitReached: 'Dosežena omejitev, dodati je mogoče samo {{max}} elementov.',\n    longerThanMin: 'Ta vrednost mora biti daljša od najmanjše dolžine {{minLength}} znakov.',\n    notValidDate: '\"{{value}}\" ni veljaven datum.',\n    required: 'To polje je obvezno.',\n    requiresAtLeast: 'To polje zahteva vsaj {{count}} {{label}}.',\n    requiresNoMoreThan: 'To polje zahteva največ {{count}} {{label}}.',\n    requiresTwoNumbers: 'To polje zahteva dve številki.',\n    shorterThanMax: 'Ta vrednost mora biti krajša od največje dolžine {{maxLength}} znakov.',\n    timezoneRequired: 'Potrebna je časovna cona.',\n    trueOrFalse: 'To polje je lahko samo enako true ali false.',\n    username:\n      'Vnesite veljavno uporabniško ime. Lahko vsebuje črke, številke, vezaje, pike in podčrtaje.',\n    validUploadID: 'To polje ni veljaven ID nalaganja.',\n  },\n  version: {\n    type: 'Tip',\n    aboutToPublishSelection: 'Objavili boste vse {{label}} v izboru. Ste prepričani?',\n    aboutToRestore:\n      'Ta {{label}} dokument boste obnovili v stanje, v katerem je bil {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Globalni {{label}} boste obnovili v stanje, v katerem je bil {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Spremembe tega dokumenta boste povrnili v objavljeno stanje. Ste prepričani?',\n    aboutToUnpublish: 'Ta dokument boste umaknili iz objave. Ste prepričani?',\n    aboutToUnpublishSelection: 'Umaknili boste iz objave vse {{label}} v izboru. Ste prepričani?',\n    autosave: 'Samodejno shranjevanje',\n    autosavedSuccessfully: 'Samodejno shranjeno uspešno.',\n    autosavedVersion: 'Samodejno shranjena različica',\n    changed: 'Spremenjeno',\n    changedFieldsCount_one: '{{count}} spremenjeno polje',\n    changedFieldsCount_other: '{{count}} spremenjena polja',\n    compareVersion: 'Primerjaj različico z:',\n    compareVersions: 'Primerjaj različice',\n    comparingAgainst: 'Primerjava z',\n    confirmPublish: 'Potrdi objavo',\n    confirmRevertToSaved: 'Potrdi vrnitev na shranjeno',\n    confirmUnpublish: 'Potrdi umik objave',\n    confirmVersionRestoration: 'Potrdi obnovitev različice',\n    currentDocumentStatus: 'Trenutni {{docStatus}} dokument',\n    currentDraft: 'Trenutni osnutek',\n    currentlyPublished: 'Trenutno objavljeno',\n    currentlyViewing: 'Trenutno pregledujete',\n    currentPublishedVersion: 'Trenutna objavljena različica',\n    draft: 'Osnutek',\n    draftSavedSuccessfully: 'Osnutek uspešno shranjen.',\n    lastSavedAgo: 'Nazadnje shranjeno pred {{distance}}',\n    modifiedOnly: 'Samo spremenjeno',\n    moreVersions: 'Več različic...',\n    noFurtherVersionsFound: 'Ni najdenih nadaljnjih različic',\n    noRowsFound: 'Ni najdenih {{label}}',\n    noRowsSelected: 'Ni izbranih {{label}}',\n    preview: 'Predogled',\n    previouslyDraft: 'Prej osnutek',\n    previouslyPublished: 'Predhodno objavljeno',\n    previousVersion: 'Prejšnja različica',\n    problemRestoringVersion: 'Pri obnavljanju te različice je prišlo do težave',\n    publish: 'Objavi',\n    publishAllLocales: 'Objavi vse jezike',\n    publishChanges: 'Objavi spremembe',\n    published: 'Objavljeno',\n    publishIn: 'Objavi v {{locale}}',\n    publishing: 'Objavljanje',\n    restoreAsDraft: 'Obnovi kot osnutek',\n    restoredSuccessfully: 'Uspešno obnovljeno.',\n    restoreThisVersion: 'Obnovi to različico',\n    restoring: 'Obnavljanje...',\n    reverting: 'Razveljavljanje...',\n    revertToPublished: 'Vrni na objavljeno',\n    saveDraft: 'Shrani osnutek',\n    scheduledSuccessfully: 'Uspešno načrtovano.',\n    schedulePublish: 'Razporedi objavo',\n    selectLocales: 'Izberite jezike za prikaz',\n    selectVersionToCompare: 'Izberite različico za primerjavo',\n    showingVersionsFor: 'Prikaz različic za:',\n    showLocales: 'Prikaži jezike:',\n    specificVersion: 'Specifična različica',\n    status: 'Status',\n    unpublish: 'Razveljavi objavo',\n    unpublishing: 'Razveljavljanje objave...',\n    version: 'Različica',\n    versionAgo: 'pred {{distance}}',\n    versionCount_many: 'Najdenih {{count}} različic',\n    versionCount_none: 'Ni najdenih različic',\n    versionCount_one: 'Najdena {{count}} različica',\n    versionCount_other: 'Najdene {{count}} različice',\n    versionCreatedOn: '{{version}} ustvarjena:',\n    versionID: 'ID različice',\n    versions: 'Različice',\n    viewingVersion: 'Ogled različice za {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Ogled različice za globalni {{entityLabel}}',\n    viewingVersions: 'Ogled različic za {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Ogled različic za globalni {{entityLabel}}',\n  },\n}\n\nexport const sl: Language = {\n  dateFNSKey: 'sl-SI',\n  translations: slTranslations,\n}\n"], "names": ["slTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "sl", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}