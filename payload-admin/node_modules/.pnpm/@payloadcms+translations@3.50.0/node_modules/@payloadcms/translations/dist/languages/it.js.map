{"version": 3, "sources": ["../../src/languages/it.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const itTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Account',\n    accountOfCurrentUser: \"Account dell'utente corrente\",\n    accountVerified: 'Account verificato con successo.',\n    alreadyActivated: '<PERSON><PERSON><PERSON> Attivato',\n    alreadyLoggedIn: 'Sei già loggato',\n    apiKey: 'Chiave API',\n    authenticated: 'Autenticato',\n    backToLogin: 'Torna al login',\n    beginCreateFirstUser: 'Per iniziare, crea il tuo primo utente.',\n    changePassword: 'Cambia Password',\n    checkYourEmailForPasswordReset:\n      \"Se l'indirizzo email è associato a un account, riceverai a breve le istruzioni per reimpostare la tua password. Si prega di controllare la cartella dello spam o della posta indesiderata se non vedi l'email nella tua casella di posta.\",\n    confirmGeneration: 'Conferma Generazione',\n    confirmPassword: 'Conferma Password',\n    createFirstUser: 'Crea il primo utente',\n    emailNotValid: \"L'email fornita non è valida\",\n    emailOrUsername: 'Email o Nome utente',\n    emailSent: 'Email Inviata',\n    emailVerified: 'Email verificata con successo.',\n    enableAPIKey: 'Abilita la Chiave API',\n    failedToUnlock: 'Lo sblocco è fallito',\n    forceUnlock: 'Forza Sblocco',\n    forgotPassword: 'Cambia Password',\n    forgotPasswordEmailInstructions:\n      'Inserisci la tua mail qui sotto. Riceverai un messaggio email con le istruzioni su come cambiare la tua password.',\n    forgotPasswordQuestion: 'Password dimenticata?',\n    forgotPasswordUsernameInstructions:\n      \"Inserisci il tuo nome utente qui sotto. Le istruzioni su come reimpostare la tua password verranno inviate all'indirizzo email associato al tuo nome utente.\",\n    generate: 'Genera',\n    generateNewAPIKey: 'Genera una nuova Chiave API',\n    generatingNewAPIKeyWillInvalidate:\n      'Generando una nuova chiave API si <1>invaliderà</1> la chiave precedente. Sei sicuro di voler continuare?',\n    lockUntil: 'Sblocca Fino',\n    logBackIn: 'Vai al Log in',\n    loggedIn: 'Per accedere con un altro utente, devi prima <0>uscire</0>.',\n    loggedInChangePassword:\n      'Per cambiare la tua password, vai al tuo <0>account</0> e modifica la tua password lì.',\n    loggedOutInactivity: 'Sei stato disconnesso automaticamente per inattività.',\n    loggedOutSuccessfully: 'Sei stato disconnesso con successo.',\n    loggingOut: 'Disconnessione in corso...',\n    login: 'Login',\n    loginAttempts: 'Tentativi di Login',\n    loginUser: 'Utente Login',\n    loginWithAnotherUser: 'Per accedere con un altro utente, devi prima <0>uscire</0>.',\n    logOut: 'Log out',\n    logout: 'Logout',\n    logoutSuccessful: 'Disconnessione riuscita.',\n    logoutUser: 'Logout utente',\n    newAccountCreated:\n      'Un nuovo account è appena stato creato per te per accedere a <a href=\"{{serverURL}}\">{{serverURL}}</a> Clicca sul seguente link o incolla l\\'URL qui sotto nel browser per verificare la tua email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Dopo aver verificato la tua email, sarai in grado di effettuare il log in con successo.',\n    newAPIKeyGenerated: 'Nuova Chiave API Generata.',\n    newPassword: 'Nuova Password',\n    passed: 'Autenticazione Superata',\n    passwordResetSuccessfully: 'Reset della password eseguito con successo.',\n    resetPassword: 'Modifica Password',\n    resetPasswordExpiration: 'Reimposta Scadenza Password',\n    resetPasswordToken: 'Reimposta il Password Token',\n    resetYourPassword: 'Modifica la tua Password',\n    stayLoggedIn: 'Rimani connesso',\n    successfullyRegisteredFirstUser: 'Primo utente registrato con successo.',\n    successfullyUnlocked: 'Sbloccato con successo',\n    tokenRefreshSuccessful: 'Aggiornamento del token riuscito.',\n    unableToVerify: 'Impossibile verificare',\n    username: 'Nome utente',\n    usernameNotValid: 'Il nome utente fornito non è valido',\n    verified: 'Verificato',\n    verifiedSuccessfully: 'Verificato con successo',\n    verify: 'Verifica',\n    verifyUser: 'Verifica Utente',\n    verifyYourEmail: 'Verifica la tua email',\n    youAreInactive:\n      \"Non sei attivo da un po' di tempo e a breve verrai disconnesso automaticamente per la tua sicurezza. Vuoi rimanere connesso?\",\n    youAreReceivingResetPassword:\n      'Ricevi questo messaggio perché tu (o qualcun altro) hai richiesto la modifica della password per il tuo account. Clicca sul seguente link o incollalo nel browser per completare il processo:',\n    youDidNotRequestPassword:\n      \"Se non l'hai richiesto, ignora questa email e la tua password rimarrà invariata.\",\n  },\n  error: {\n    accountAlreadyActivated: 'Questo account è già stato attivato.',\n    autosaving:\n      'Si è verificato un problema durante il salvataggio automatico di questo documento.',\n    correctInvalidFields: 'Per favore correggi i campi non validi.',\n    deletingFile: \"Si è verificato un errore durante l'eleminazione del file.\",\n    deletingTitle:\n      \"Si è verificato un errore durante l'eliminazione di {{title}}. Per favore controlla la tua connessione e riprova.\",\n    documentNotFound:\n      'Il documento con ID {{id}} non è stato trovato. Potrebbe essere stato eliminato o mai esistito, oppure potresti non avere accesso ad esso.',\n    emailOrPasswordIncorrect: \"L'email o la password fornita non è corretta.\",\n    followingFieldsInvalid_one: 'Il seguente campo non è valido:',\n    followingFieldsInvalid_other: 'I seguenti campi non sono validi:',\n    incorrectCollection: 'Collezione non corretta',\n    insufficientClipboardPermissions:\n      'Accesso alla clipboard negato. Verifica i permessi della clipboard.',\n    invalidClipboardData: 'Dati della clipboard non validi.',\n    invalidFileType: 'Tipo di file non valido',\n    invalidFileTypeValue: 'Tipo di file non valido: {{value}}',\n    invalidRequestArgs: 'Argomenti non validi nella richiesta: {{args}}',\n    loadingDocument:\n      'Si è verificato un problema durante il caricamento del documento con ID {{id}}.',\n    localesNotSaved_one: 'Non è stato possibile salvare la seguente impostazione locale:',\n    localesNotSaved_other: 'Non è stato possibile salvare le seguenti impostazioni locali:',\n    logoutFailed: 'Disconnessione non riuscita.',\n    missingEmail: 'Email mancante.',\n    missingIDOfDocument: 'ID del documento da aggiornare mancante.',\n    missingIDOfVersion: 'ID della versione mancante.',\n    missingRequiredData: 'Data mancante.',\n    noFilesUploaded: 'Nessun file è stato caricato.',\n    noMatchedField: 'Nessun campo corrispondente trovato per \"{{label}}\"',\n    notAllowedToAccessPage: 'Non sei autorizzato ad accedere a questa pagina.',\n    notAllowedToPerformAction: 'Non sei autorizzato a eseguire questa azione.',\n    notFound: 'La risorsa richiesta non è stata trovata.',\n    noUser: 'Nessun Utente',\n    previewing: \"Si è verificato un problema durante l'anteprima di questo documento.\",\n    problemUploadingFile: 'Si è verificato un problema durante il caricamento del file.',\n    restoringTitle:\n      'Si è verificato un errore durante il ripristino di {{title}}. Si prega di controllare la connessione e riprovare.',\n    tokenInvalidOrExpired: 'Il token non è valido o è scaduto.',\n    tokenNotProvided: 'Token non fornito.',\n    unableToCopy: 'Impossibile copiare.',\n    unableToDeleteCount: 'Impossibile eliminare {{count}} su {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Errore durante la reindicizzazione della collezione {{collection}}. Operazione annullata.',\n    unableToUpdateCount: 'Impossibile aggiornare {{count}} su {{total}} {{label}}.',\n    unauthorized: 'Non autorizzato, devi essere loggato per effettuare questa richiesta.',\n    unauthorizedAdmin:\n      'Non autorizzato, questo utente non ha accesso al pannello di amministrazione.',\n    unknown: 'Si è verificato un errore sconosciuto.',\n    unPublishingDocument:\n      \"Si è verificato un problema durante l'annullamento della pubblicazione di questo documento.\",\n    unspecific: 'Si è verificato un errore.',\n    unverifiedEmail: 'Verifica la tua email prima di accedere.',\n    userEmailAlreadyRegistered: \"Un utente con l'email fornita è già registrato.\",\n    userLocked: 'Questo utente è bloccato a causa di troppi tentativi di accesso non riusciti.',\n    usernameAlreadyRegistered: 'Un utente con il nome utente fornito è già registrato.',\n    usernameOrPasswordIncorrect: 'Il nome utente o la password forniti sono incorretti.',\n    valueMustBeUnique: 'Il valore deve essere univoco',\n    verificationTokenInvalid: 'Il token di verifica non è valido.',\n  },\n  fields: {\n    addLabel: 'Aggiungi {{label}}',\n    addLink: 'Aggiungi Collegamento',\n    addNew: 'Aggiungi nuovo',\n    addNewLabel: 'Aggiungi nuovo {{label}}',\n    addRelationship: 'Aggiungi Relazione',\n    addUpload: 'aggiungi Carica',\n    block: 'blocco',\n    blocks: 'blocchi',\n    blockType: 'Tipo di Blocco',\n    chooseBetweenCustomTextOrDocument:\n      \"Scegli tra l'inserimento di un URL di testo personalizzato o il collegamento a un altro documento.\",\n    chooseDocumentToLink: 'Scegli un documento a cui collegarti',\n    chooseFromExisting: 'Scegli tra esistente',\n    chooseLabel: 'Scegli {{label}}',\n    collapseAll: 'Comprimi tutto',\n    customURL: 'URL personalizzato',\n    editLabelData: 'Modifica i dati di {{label}}',\n    editLink: 'Modifica Collegamento',\n    editRelationship: 'Modifica Relazione',\n    enterURL: 'Inserisci un URL',\n    internalLink: 'Collegamento interno',\n    itemsAndMore: '{{items}} e altri {{count}}',\n    labelRelationship: 'Relazione {{label}}',\n    latitude: 'Latitudine',\n    linkedTo: 'Collegato a <0>{{label}}</0>',\n    linkType: 'Tipo di collegamento',\n    longitude: 'Longitudine',\n    newLabel: 'Nuovo {{label}}',\n    openInNewTab: 'Apri in una nuova scheda',\n    passwordsDoNotMatch: 'Le password non corrispondono.',\n    relatedDocument: 'Documento Correlato',\n    relationTo: 'Correla a',\n    removeRelationship: 'Rimuovi Relazione',\n    removeUpload: 'Rimuovi Upload',\n    saveChanges: 'Salva modifiche',\n    searchForBlock: 'Cerca un blocco',\n    selectExistingLabel: 'Seleziona {{label}} esistente',\n    selectFieldsToEdit: 'Seleziona i campi da modificare',\n    showAll: 'Mostra tutto',\n    swapRelationship: 'Cambia Relationship',\n    swapUpload: 'Cambia Upload',\n    textToDisplay: 'Testo da visualizzare',\n    toggleBlock: 'Apri/chiudi blocco',\n    uploadNewLabel: 'Carica nuovo {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Sfoglia per Cartella',\n    byFolder: 'Per Cartella',\n    deleteFolder: 'Elimina cartella',\n    folderName: 'Nome Cartella',\n    folders: 'Cartelle',\n    folderTypeDescription:\n      'Seleziona quale tipo di documenti della collezione dovrebbero essere consentiti in questa cartella.',\n    itemHasBeenMoved: '{{title}} è stato spostato in {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} è stato spostato nella cartella principale',\n    itemsMovedToFolder: '{{title}} spostato in {{folderName}}',\n    itemsMovedToRoot: '{{title}} è stato spostato nella cartella principale',\n    moveFolder: 'Sposta Cartella',\n    moveItemsToFolderConfirmation:\n      'Stai per spostare <1>{{count}} {{label}}</1> in <2>{{toFolder}}</2>. Sei sicuro?',\n    moveItemsToRootConfirmation:\n      'Stai per spostare <1>{{count}} {{label}}</1> nella cartella principale. Sei sicuro?',\n    moveItemToFolderConfirmation:\n      'Stai per spostare <1>{{title}}</1> in <2>{{toFolder}}</2>. Sei sicuro?',\n    moveItemToRootConfirmation:\n      'Stai per spostare <1>{{title}}</1> nella cartella principale. Sei sicuro?',\n    movingFromFolder: 'Spostando {{title}} da {{fromFolder}}',\n    newFolder: 'Nuova Cartella',\n    noFolder: 'Nessuna cartella',\n    renameFolder: 'Rinomina Cartella',\n    searchByNameInFolder: 'Cerca per Nome in {{folderName}}',\n    selectFolderForItem: 'Seleziona la cartella per {{title}}',\n  },\n  general: {\n    name: 'Nome',\n    aboutToDelete: 'Stai per eliminare {{label}} <1>{{title}}</1>. Sei sicuro?',\n    aboutToDeleteCount_many: 'Stai per eliminare {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Stai per eliminare {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Stai per eliminare {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      \"Stai per eliminare definitivamente l'{{label}} <1>{{title}}</1>. Sei sicuro?\",\n    aboutToPermanentlyDeleteTrash:\n      'Stai per eliminare definitivamente <0>{{count}}</0> <1>{{label}}</1> dal cestino. Sei sicuro?',\n    aboutToRestore: 'Stai per ripristinare il {{label}} <1>{{title}}</1>. Sei sicuro?',\n    aboutToRestoreAsDraft:\n      \"Stai per ripristinare l'etichetta {{label}} <1>{{title}}</1> come bozza. Sei sicuro?\",\n    aboutToRestoreAsDraftCount: 'Stai per ripristinare {{count}} {{label}} come bozza',\n    aboutToRestoreCount: 'Stai per ripristinare {{count}} {{label}}',\n    aboutToTrash: 'Stai per spostare il {{label}} <1>{{title}}</1> nel cestino. Sei sicuro?',\n    aboutToTrashCount: 'Stai per spostare {{count}} {{label}} nel cestino',\n    addBelow: 'Aggiungi sotto',\n    addFilter: 'Aggiungi Filtro',\n    adminTheme: 'Tema Admin',\n    all: 'Tutto',\n    allCollections: 'Tutte le collezioni',\n    allLocales: 'Tutte le località',\n    and: 'E',\n    anotherUser: 'Un altro utente',\n    anotherUserTakenOver:\n      'Un altro utente ha preso il controllo della modifica di questo documento.',\n    applyChanges: 'Applica modifiche',\n    ascending: 'Ascendente',\n    automatic: 'Automatico',\n    backToDashboard: 'Torna alla Dashboard',\n    cancel: 'Cancella',\n    changesNotSaved: 'Le tue modifiche non sono state salvate. Se esci ora, verranno perse.',\n    clear: 'Chiara',\n    clearAll: 'Cancella Tutto',\n    close: 'Chiudere',\n    collapse: 'Comprimi',\n    collections: 'Collezioni',\n    columns: 'Colonne',\n    columnToSort: 'Colonna da Ordinare',\n    confirm: 'Conferma',\n    confirmCopy: 'Conferma copia',\n    confirmDeletion: \"Conferma l'eliminazione\",\n    confirmDuplication: 'Conferma la duplicazione',\n    confirmMove: 'Conferma spostamento',\n    confirmReindex: \"Rifare l'indice di tutte le {{collections}}?\",\n    confirmReindexAll: \"Rifare l'indice di tutte le collezioni?\",\n    confirmReindexDescription:\n      \"Questo rimuoverà gli indici esistenti e rifarà l'indice dei documenti nelle collezioni {{collections}}.\",\n    confirmReindexDescriptionAll:\n      \"Questo rimuoverà gli indici esistenti e rifarà l'indice dei documenti in tutte le collezioni.\",\n    confirmRestoration: 'Conferma il ripristino',\n    copied: 'Copiato',\n    copy: 'Copia',\n    copyField: 'Copia campo',\n    copying: 'Copia',\n    copyRow: 'Copia riga',\n    copyWarning: 'Stai per sovrascrivere {{to}} con {{from}} per {{label}} {{title}}. Sei sicuro?',\n    create: 'Crea',\n    created: 'Data di creazione',\n    createdAt: 'Creato il',\n    createNew: 'Crea Nuovo',\n    createNewLabel: 'Crea nuovo {{label}}',\n    creating: 'Crea nuovo',\n    creatingNewLabel: 'Creazione di un nuovo {{label}}',\n    currentlyEditing:\n      'sta attualmente modificando questo documento. Se prendi il controllo, verranno bloccati dal continuare a modificare e potrebbero anche perdere le modifiche non salvate.',\n    custom: 'Personalizzato',\n    dark: 'Scuro',\n    dashboard: 'Dashboard',\n    delete: 'Elimina',\n    deleted: 'Cancellato',\n    deletedAt: 'Cancellato Alle',\n    deletedCountSuccessfully: '{{count}} {{label}} eliminato con successo.',\n    deletedSuccessfully: 'Eliminato con successo.',\n    deletePermanently: 'Salta il cestino ed elimina definitivamente',\n    deleting: 'Sto eliminando...',\n    depth: 'Profondità',\n    descending: 'Decrescente',\n    deselectAllRows: 'Deseleziona tutte le righe',\n    document: 'Documento',\n    documentIsTrashed: 'Questo {{label}} è stato cestinato ed è in sola lettura.',\n    documentLocked: 'Documento bloccato',\n    documents: 'Documenti',\n    duplicate: 'Duplica',\n    duplicateWithoutSaving: 'Duplica senza salvare le modifiche',\n    edit: 'Modificare',\n    editAll: 'Modifica Tutto',\n    editedSince: 'Modificato da',\n    editing: 'Modifica',\n    editingLabel_many: 'Modificare {{count}} {{label}}',\n    editingLabel_one: 'Modifica {{count}} {{label}}',\n    editingLabel_other: 'Modificare {{count}} {{label}}',\n    editingTakenOver: 'Modifica presa in carico',\n    editLabel: 'Modifica {{label}}',\n    email: 'Email',\n    emailAddress: 'Indirizzo Email',\n    emptyTrash: 'Svuota cestino',\n    emptyTrashLabel: 'Svuota il cestino {{label}}',\n    enterAValue: 'Inserisci un valore',\n    error: 'Errore',\n    errors: 'Errori',\n    exitLivePreview: \"Esci dall'Anteprima dal Vivo\",\n    export: 'Esportazione',\n    fallbackToDefaultLocale: 'Fallback al locale predefinito',\n    false: 'Falso',\n    filter: 'Filtro',\n    filters: 'Filtri',\n    filterWhere: 'Filtra {{label}} se',\n    globals: 'Globali',\n    goBack: 'Torna indietro',\n    groupByLabel: 'Raggruppa per {{label}}',\n    import: 'Importare',\n    isEditing: 'sta modificando',\n    item: 'articolo',\n    items: 'articoli',\n    language: 'Lingua',\n    lastModified: 'Ultima modifica',\n    leaveAnyway: 'Esci comunque',\n    leaveWithoutSaving: 'Esci senza salvare',\n    light: 'Chiaro',\n    livePreview: 'Anteprima dal vivo',\n    loading: 'Caricamento',\n    locale: 'Locale',\n    locales: 'Localizzazioni',\n    menu: 'Menù',\n    moreOptions: 'Più opzioni',\n    move: 'Muoviti',\n    moveConfirm: 'Stai per spostare {{count}} {{label}} in <1>{{destination}}</1>. Sei sicuro?',\n    moveCount: 'Sposta {{count}} {{label}}',\n    moveDown: 'Sposta sotto',\n    moveUp: 'Sposta sopra',\n    moving: 'In movimento',\n    movingCount: 'Spostando {{count}} {{label}}',\n    newPassword: 'Nuova Password',\n    next: 'Successivo',\n    no: 'No',\n    noDateSelected: 'Nessuna data selezionata',\n    noFiltersSet: 'Nessun filtro impostato',\n    noLabel: '<No {{label}}>',\n    none: 'Nessuno',\n    noOptions: 'Nessuna opzione',\n    noResults:\n      'Nessun {{label}} trovato. Non esiste ancora nessun {{label}} oppure nessuno corrisponde ai filtri che hai specificato sopra.',\n    notFound: 'Non Trovato',\n    nothingFound: 'Non è stato trovato nulla',\n    noTrashResults: 'Nessun {{label}} nel cestino.',\n    noUpcomingEventsScheduled: 'Nessun evento in programma.',\n    noValue: 'Nessun valore',\n    of: 'di',\n    only: 'Solo',\n    open: 'Apri',\n    or: 'Oppure',\n    order: 'Ordine',\n    overwriteExistingData: 'Sovrascrivi i dati del campo esistente',\n    pageNotFound: 'Pagina non trovata',\n    password: 'Password',\n    pasteField: 'Incolla campo',\n    pasteRow: 'Incolla riga',\n    payloadSettings: 'Impostazioni di Payload',\n    permanentlyDelete: 'Elimina Permanentemente',\n    permanentlyDeletedCountSuccessfully:\n      'Eliminato definitivamente {{count}} {{label}} con successo.',\n    perPage: 'Per Pagina: {{limit}}',\n    previous: 'Precedente',\n    reindex: 'Reindicizza',\n    reindexingAll: \"Rifacendo l'indice di tutte le {{collections}}.\",\n    remove: 'Rimuovi',\n    rename: 'Rinomina',\n    reset: 'Ripristina',\n    resetPreferences: 'Ripristina le preferenze',\n    resetPreferencesDescription:\n      'Questo ripristinerà tutte le tue preferenze alle impostazioni predefinite.',\n    resettingPreferences: 'Ripristinando le preferenze.',\n    restore: 'Ripristina',\n    restoreAsPublished: 'Ripristina come versione pubblicata',\n    restoredCountSuccessfully: 'Ripristinato {{count}} {{label}} con successo.',\n    restoring:\n      \"Rispetta il significato del testo originale nel contesto di Payload. Ecco una lista di termini comuni di Payload che hanno significati molto specifici:\\n    - Raccolta: Una raccolta è un gruppo di documenti che condividono una struttura e una finalità comuni. Le raccolte vengono utilizzate per organizzare e gestire i contenuti in Payload.\\n    - Campo: Un campo è un pezzo specifico di dati all'interno di un documento in una raccolta. I campi definiscono la struttura e il tipo di dati che possono essere memorizzati in un documento.\\n    - Documento: Un documento\",\n    row: 'Riga',\n    rows: 'Righe',\n    save: 'Salva',\n    saving: 'Salvo...',\n    schedulePublishFor: 'Pianifica la pubblicazione per {{title}}',\n    searchBy: 'Cerca per {{label}}',\n    select: 'Seleziona',\n    selectAll: 'Seleziona tutto {{count}} {{label}}',\n    selectAllRows: 'Seleziona tutte le righe',\n    selectedCount: '{{count}} {{label}} selezionato',\n    selectLabel: 'Seleziona {{label}}',\n    selectValue: 'Seleziona un valore',\n    showAllLabel: 'Mostra tutti {{label}}',\n    sorryNotFound: \"Siamo spiacenti, non c'è nulla che corrisponda alla tua richiesta.\",\n    sort: 'Ordina',\n    sortByLabelDirection: 'Ordina per {{label}} {{direction}}',\n    stayOnThisPage: 'Rimani su questa pagina',\n    submissionSuccessful: 'Invio riuscito.',\n    submit: 'Invia',\n    submitting: 'Inviando...',\n    success: 'Successo',\n    successfullyCreated: '{{label}} creato con successo.',\n    successfullyDuplicated: '{{label}} duplicato con successo.',\n    successfullyReindexed:\n      'Reindicizzati con successo {{count}} di {{total}} documenti da {{collections}} collezioni.',\n    takeOver: 'Prendi il controllo',\n    thisLanguage: 'Italiano',\n    time: 'Tempo',\n    timezone: 'Fuso orario',\n    titleDeleted: '{{label}} {{title}} eliminato con successo.',\n    titleRestored: '{{label}} \"{{title}}\" ripristinato con successo.',\n    titleTrashed: '{{label}} \"{{title}}\" spostato nel cestino.',\n    trash: 'Cestino',\n    trashedCountSuccessfully: '{{count}} {{label}} spostati nel cestino.',\n    true: 'Vero',\n    unauthorized: 'Non autorizzato',\n    unsavedChanges: 'Hai delle modifiche non salvate. Salva o scarta prima di continuare.',\n    unsavedChangesDuplicate: 'Sono presenti modifiche non salvate. Vuoi continuare a duplicare?',\n    untitled: 'Senza titolo',\n    upcomingEvents: 'Eventi Imminenti',\n    updatedAt: 'Aggiornato il',\n    updatedCountSuccessfully: '{{count}} {{label}} aggiornato con successo.',\n    updatedLabelSuccessfully: '{{label}} aggiornata con successo.',\n    updatedSuccessfully: 'Aggiornato con successo.',\n    updateForEveryone: 'Aggiornamento per tutti',\n    updating: 'Aggiornamento',\n    uploading: 'Caricamento',\n    uploadingBulk: 'Caricamento {{current}} di {{total}}',\n    user: 'Utente',\n    username: 'Nome utente',\n    users: 'Utenti',\n    value: 'Valore',\n    viewing: 'Visualizzazione',\n    viewReadOnly: 'Visualizza solo lettura',\n    welcome: 'Benvenuto',\n    yes: 'Sì',\n  },\n  localization: {\n    cannotCopySameLocale: 'Non è possibile copiare nella stessa posizione',\n    copyFrom: 'Copia da',\n    copyFromTo: 'Copiando da {{from}} a {{to}}',\n    copyTo: 'Copia per',\n    copyToLocale: 'Copia in locale',\n    localeToPublish: 'Località da pubblicare',\n    selectLocaleToCopy: 'Seleziona la località da copiare',\n  },\n  operators: {\n    contains: 'contiene',\n    equals: 'uguale',\n    exists: 'esiste',\n    intersects: 'interseca',\n    isGreaterThan: 'è maggiore di',\n    isGreaterThanOrEqualTo: 'è maggiore o uguale a',\n    isIn: 'è in',\n    isLessThan: 'è minore di',\n    isLessThanOrEqualTo: 'è minore o uguale a',\n    isLike: 'è come',\n    isNotEqualTo: 'non è uguale a',\n    isNotIn: 'non è in',\n    isNotLike: 'non è come',\n    near: 'vicino',\n    within: \"all'interno\",\n  },\n  upload: {\n    addFile: 'Aggiungi file',\n    addFiles: 'Aggiungi File',\n    bulkUpload: 'Caricamento in Blocco',\n    crop: 'Raccolto',\n    cropToolDescription:\n      \"Trascina gli angoli dell'area selezionata, disegna una nuova area o regola i valori qui sotto.\",\n    download: 'Scarica',\n    dragAndDrop: 'Trascina e rilascia un file',\n    dragAndDropHere: 'oppure trascina e rilascia un file qui',\n    editImage: 'Modifica immagine',\n    fileName: 'Nome File',\n    fileSize: 'Dimensione File',\n    filesToUpload: 'File da caricare',\n    fileToUpload: 'File da caricare',\n    focalPoint: 'Punto Focale',\n    focalPointDescription:\n      \"Trascina il punto focale direttamente sull'anteprima o regola i valori sottostanti.\",\n    height: 'Altezza',\n    lessInfo: 'Meno info',\n    moreInfo: 'Più info',\n    noFile: 'Nessun file',\n    pasteURL: 'Incolla URL',\n    previewSizes: 'Anteprime Dimensioni',\n    selectCollectionToBrowse: 'Seleziona una Collezione da Sfogliare',\n    selectFile: 'Seleziona un file',\n    setCropArea: 'Imposta area di ritaglio',\n    setFocalPoint: 'Imposta punto focale',\n    sizes: 'Formati',\n    sizesFor: 'Dimensioni per {{label}}',\n    width: 'Larghezza',\n  },\n  validation: {\n    emailAddress: 'Si prega di inserire un indirizzo email valido.',\n    enterNumber: 'Si prega di inserire un numero valido.',\n    fieldHasNo: 'Questo campo non ha {{label}}',\n    greaterThanMax: '{{value}} è superiore al massimo consentito {{label}} di {{max}}.',\n    invalidInput: 'Questo campo ha un input non valido.',\n    invalidSelection: 'Questo campo ha una selezione non valida.',\n    invalidSelections: \"'In questo campo sono presenti le seguenti selezioni non valide:'\",\n    lessThanMin: '{{value}} è inferiore al minimo consentito {{label}} di {{min}}.',\n    limitReached: 'Raggiunto il limite, possono essere aggiunti solo {{max}} elementi.',\n    longerThanMin:\n      'Questo valore deve essere più lungo della lunghezza minima di {{minLength}} caratteri.',\n    notValidDate: '\"{{value}}\" non è una data valida.',\n    required: 'Questo campo è obbligatorio.',\n    requiresAtLeast: 'Questo campo richiede almeno {{count}} {{label}}.',\n    requiresNoMoreThan: 'Questo campo richiede non più di {{count}} {{label}}.',\n    requiresTwoNumbers: 'Questo campo richiede due numeri.',\n    shorterThanMax:\n      'Questo valore deve essere inferiore alla lunghezza massima di {{maxLength}} caratteri.',\n    timezoneRequired: 'È richiesto un fuso orario.',\n    trueOrFalse: \"Questo campo può essere solo uguale a 'true' o 'false'.\",\n    username:\n      'Inserisci un nome utente valido. Può contenere lettere, numeri, trattini, punti e underscore.',\n    validUploadID: \"'Questo campo non è un ID di Upload valido.'\",\n  },\n  version: {\n    type: 'Tipo',\n    aboutToPublishSelection: 'Stai per pubblicare tutte le {{label}} nella selezione. Sei sicuro?',\n    aboutToRestore:\n      'Stai per ripristinare questo documento {{label}} allo stato in cui si trovava il {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Stai per ripristinare {{label}} allo stato in cui si trovava il {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Stai per ripristinare le modifiche di questo documento al suo stato pubblicato. Sei sicuro?',\n    aboutToUnpublish: 'Stai per annullare la pubblicazione di questo documento. Sei sicuro?',\n    aboutToUnpublishSelection:\n      'Stai per annullare la pubblicazione di tutte le {{label}} nella selezione. Sei sicuro?',\n    autosave: 'Salvataggio automatico',\n    autosavedSuccessfully: 'Salvataggio automatico riuscito.',\n    autosavedVersion: 'Versione salvata automaticamente',\n    changed: 'Modificato',\n    changedFieldsCount_one: '{{count}} campo modificato',\n    changedFieldsCount_other: '{{count}} campi modificati',\n    compareVersion: 'Confronta versione con:',\n    compareVersions: 'Confronta Versioni',\n    comparingAgainst: 'Confrontando con',\n    confirmPublish: 'Conferma la pubblicazione',\n    confirmRevertToSaved: 'Conferma il ripristino dei salvataggi',\n    confirmUnpublish: 'Conferma annullamento della pubblicazione',\n    confirmVersionRestoration: 'Conferma il ripristino della versione',\n    currentDocumentStatus: 'Documento {{docStatus}} corrente',\n    currentDraft: 'Bozza Corrente',\n    currentlyPublished: 'Attualmente Pubblicato',\n    currentlyViewing: 'Visualizzazione attuale',\n    currentPublishedVersion: 'Versione Pubblicata Attuale',\n    draft: 'Bozza',\n    draftSavedSuccessfully: 'Bozza salvata con successo.',\n    lastSavedAgo: 'Ultimo salvataggio {{distance}} fa',\n    modifiedOnly: 'Modificato solo',\n    moreVersions: 'Altre versioni...',\n    noFurtherVersionsFound: 'Non sono state trovate ulteriori versioni',\n    noRowsFound: 'Nessun {{label}} trovato',\n    noRowsSelected: 'Nessuna {{etichetta}} selezionata',\n    preview: 'Anteprima',\n    previouslyDraft: 'Precedentemente una Bozza',\n    previouslyPublished: 'Precedentemente Pubblicato',\n    previousVersion: 'Versione Precedente',\n    problemRestoringVersion: 'Si è verificato un problema durante il ripristino di questa versione',\n    publish: 'Pubblicare',\n    publishAllLocales: 'Pubblica tutte le località',\n    publishChanges: 'Pubblica modifiche',\n    published: 'Pubblicato',\n    publishIn: 'Pubblica in {{locale}}',\n    publishing: 'Pubblicazione',\n    restoreAsDraft: 'Ripristina come bozza',\n    restoredSuccessfully: 'Ripristinato con successo.',\n    restoreThisVersion: 'Ripristina questa versione',\n    restoring: 'Ripristino...',\n    reverting: 'Ritorno...',\n    revertToPublished: 'Ritorna alla versione pubblicata',\n    saveDraft: 'Salva Bozza',\n    scheduledSuccessfully: 'Programmato con successo.',\n    schedulePublish: 'Pubblicazione Programmata',\n    selectLocales: 'Seleziona le lingue da visualizzare',\n    selectVersionToCompare: 'Seleziona una versione da confrontare',\n    showingVersionsFor: 'Mostra le versioni per:',\n    showLocales: 'Mostra localizzazioni:',\n    specificVersion: 'Versione Specifica',\n    status: 'Stato',\n    unpublish: 'Annulla pubblicazione',\n    unpublishing: 'Annullamento pubblicazione...',\n    version: 'Versione',\n    versionAgo: '{{distance}} fa',\n    versionCount_many: '{{count}} versioni trovate',\n    versionCount_none: 'Nessuna versione trovata',\n    versionCount_one: '{{count}} versione trovata',\n    versionCount_other: '{{count}} versioni trovate',\n    versionCreatedOn: '{{version}} creata il:',\n    versionID: 'ID Versione',\n    versions: 'Versioni',\n    viewingVersion: 'Visualizzazione della versione per {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: '`Visualizzazione della versione per {{entityLabel}}',\n    viewingVersions: 'Visualizzazione delle versioni per {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: '`Visualizzazione delle versioni per {{entityLabel}}',\n  },\n}\n\nexport const it: Language = {\n  dateFNSKey: 'it',\n  translations: itTranslations,\n}\n"], "names": ["itTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "it", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YACE;QACFC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBACE;QACFC,SAAS;QACTC,sBACE;QACFC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBACE;QACFC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCACE;QACFC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBACE;QACFC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}