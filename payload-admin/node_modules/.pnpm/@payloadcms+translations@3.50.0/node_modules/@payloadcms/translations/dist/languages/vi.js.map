{"version": 3, "sources": ["../../src/languages/vi.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const viTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Tài khoản',\n    accountOfCurrentUser: 'Tài khoản của người dùng hiện tại',\n    accountVerified: 'Tài khoản đã được xác minh thành công.',\n    alreadyActivated: 'Đã được kích hoạt',\n    alreadyLoggedIn: 'Đã đăng nhập',\n    apiKey: 'API Key',\n    authenticated: 'Đã xác thực',\n    backToLogin: 'Quay lại đăng nhập.',\n    beginCreateFirstUser: '<PERSON><PERSON> bắt đầu, hãy tạo người dùng đầu tiên.',\n    changePassword: 'Đổi mật khẩu',\n    checkYourEmailForPasswordReset:\n      'Nếu địa chỉ email được liên kết với một tài kho<PERSON>, bạn sẽ nhận được hướng dẫn để đặt lại mật khẩu trong thời gian ngắn. Vui lòng kiểm tra thư mục spam hoặc thư rác nếu bạn không thấy email trong hộp thư đến của mình.',\n    confirmGeneration: 'Xác nhận, tạo API Key',\n    confirmPassword: 'Xác nhận mật khẩu',\n    createFirstUser: 'Tạo người dùng đầu tiên',\n    emailNotValid: 'Email không chính xác',\n    emailOrUsername: 'Email hoặc Tên tài khoản',\n    emailSent: 'Email đã được gửi',\n    emailVerified: 'Email đã được xác minh thành công.',\n    enableAPIKey: 'Kích hoạt API Key',\n    failedToUnlock: 'Mở khóa thất bại',\n    forceUnlock: 'Mở khóa tài khoản',\n    forgotPassword: 'Quên mật khẩu',\n    forgotPasswordEmailInstructions: 'Nhập email của bạn để nhận hướng dẫn tạo lại mật khẩu.',\n    forgotPasswordQuestion: 'Quên mật khẩu?',\n    forgotPasswordUsernameInstructions:\n      'Vui lòng nhập tên người dùng của bạn bên dưới. Hướng dẫn về cách đặt lại mật khẩu của bạn sẽ được gửi đến địa chỉ email liên kết với tên người dùng của bạn.',\n    generate: 'Tạo',\n    generateNewAPIKey: 'Tạo API Key mới',\n    generatingNewAPIKeyWillInvalidate:\n      'Việc tạo API Key mới sẽ <1>vô hiệu hóa</1> API Key cũ. Bạn có muốn tiếp tục không?',\n    lockUntil: 'Khóa lại cho tới thời điểm sau',\n    logBackIn: 'Đăng nhập lại',\n    loggedIn:\n      'Để đăng nhập dưới tên người dùng khác, bạn phải <0>đăng xuất</0> người dùng hiện tại.',\n    loggedInChangePassword: 'Để đổi mật khẩu, hãy truy cập cài đặt <0>tài khoản</0>.',\n    loggedOutInactivity: 'Bạn đã tự động đăng xuất sau một khoản thời gian dài không thao tác.',\n    loggedOutSuccessfully: 'Đăng xuất thành công.',\n    loggingOut: 'Đang đăng xuất...',\n    login: 'Đăng nhập',\n    loginAttempts: 'Lần đăng nhập',\n    loginUser: 'Đăng nhập người dùng',\n    loginWithAnotherUser:\n      'Để đăng nhập dưới tên người dùng khác, bạn phải <0>đăng xuất</0> người dùng hiện tại.',\n    logOut: 'Đăng xuất',\n    logout: 'Đăng xuất',\n    logoutSuccessful: 'Đăng xuất thành công.',\n    logoutUser: 'Đăng xuất người dùng',\n    newAccountCreated:\n      'Một tài khoản mới đã được tạo cho bạn. Tài khoản này được dùng để truy cập <a href=\"{{serverURL}}\">{{serverURL}}</a> Hãy nhấp chuột hoặc sao chép đường dẫn sau vào trình duyệt của bạn để xác thực email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Sau khi email được xác thực, bạn sẽ có thể đăng nhập.',\n    newAPIKeyGenerated: 'API Key mới đã được tạo',\n    newPassword: 'Mật khẩu mới',\n    passed: 'Xác thực thành công',\n    passwordResetSuccessfully: 'Đặt lại mật khẩu thành công.',\n    resetPassword: 'Tạo lại mật khẩu',\n    resetPasswordExpiration: 'Hạn tạo lại mật khẩu ',\n    resetPasswordToken: 'Tạo lại token cho mật khẩu',\n    resetYourPassword: 'Tạo lại mật khẩu',\n    stayLoggedIn: 'Duy trì đăng nhập',\n    successfullyRegisteredFirstUser: 'Đã đăng ký thành công người dùng đầu tiên.',\n    successfullyUnlocked: 'Mở khóa thành công',\n    tokenRefreshSuccessful: 'Làm mới token thành công.',\n    unableToVerify: 'Không thể xác thực',\n    username: 'Tên đăng nhập',\n    usernameNotValid: 'Tên người dùng được cung cấp không hợp lệ',\n    verified: 'Đã xác thực',\n    verifiedSuccessfully: 'Đã xác thực thành công',\n    verify: 'Tiến hành xác thực',\n    verifyUser: 'Tiến hành xác thực người dùng',\n    verifyYourEmail: 'Tiến hành xác thực email',\n    youAreInactive:\n      'Bạn đã không thao tác trong một khoảng thời gian, và sẽ bị tự động đăng xuất vì lý do bảo mật. Bạn có muốn tiếp tục phiên đăng nhập.',\n    youAreReceivingResetPassword:\n      'Bạn nhận được tin nhắn này vì bạn (hoặc một người nào khác) đã gửi yêu cầu thay đổi mật khẩu tài khoản của bạn. Xin hãy nhấp chuột vào đường dẫn sau, hoặc sao chép vào trình duyệt của bạn để hoàn tất quá trình:',\n    youDidNotRequestPassword:\n      'Nếu bạn không phải là người yêu cầu thay đổi mật khẩu, xin hãy bỏ qua tin nhắn này và mật khẩu của bạn sẽ được giữ nguyên.',\n  },\n  error: {\n    accountAlreadyActivated: 'Lỗi - Tài khoản này đã được kích hoạt.',\n    autosaving: 'Lỗi - Đã xảy ra vấn đề khi tự động sao lưu bản tài liệu này.',\n    correctInvalidFields: 'Lỗi - Xin hãy sửa lại những fields không hợp lệ.',\n    deletingFile: 'Lỗi - Đã xảy ra vấn đề khi xóa tệp này.',\n    deletingTitle:\n      'Lỗi - Đã xảy ra vấn đề khi xóa {{title}}. Hãy kiểm tra kết nối mạng và thử lại.',\n    documentNotFound:\n      'Tài liệu có ID {{id}} không thể tìm thấy. Nó có thể đã bị xóa hoặc chưa từng tồn tại, hoặc bạn có thể không có quyền truy cập vào nó.',\n    emailOrPasswordIncorrect: 'Lỗi - Email hoặc mật khẩu không chính xác.',\n    followingFieldsInvalid_one: 'Lỗi - Field sau không hợp lệ:',\n    followingFieldsInvalid_other: 'Lỗi - Những fields sau không hợp lệ:',\n    incorrectCollection: 'Lỗi - Collection không hợp lệ.',\n    insufficientClipboardPermissions:\n      'Truy cập vào bộ nhớ tạm bị từ chối. Vui lòng kiểm tra quyền truy cập bộ nhớ tạm của bạn.',\n    invalidClipboardData: 'Dữ liệu bộ nhớ tạm không hợp lệ.',\n    invalidFileType: 'Lỗi - Định dạng tệp không hợp lệ.',\n    invalidFileTypeValue: 'Lỗi - Định dạng tệp không hợp lệ: {{value}}.',\n    invalidRequestArgs: 'Các đối số không hợp lệ đã được truyền trong yêu cầu: {{args}}',\n    loadingDocument: 'Lỗi - Đã xảy ra vấn để khi tải bản tài liệu với ID {{id}}.',\n    localesNotSaved_one: 'Không thể lưu trữ cài đặt vùng sau đây:',\n    localesNotSaved_other: 'Không thể lưu trữ các cài đặt vùng sau đây:',\n    logoutFailed: 'Đăng xuất thất bại.',\n    missingEmail: 'Lỗi - Thiếu email.',\n    missingIDOfDocument: 'Lỗi - Thiếu ID của bản tài liệu cần cập nhật.',\n    missingIDOfVersion: 'Lỗi - Thiếu ID của phiên bản.',\n    missingRequiredData: 'Lỗi - Thiếu dữ liệu cần thiết.',\n    noFilesUploaded: 'Lỗi - File chưa được tải lên.',\n    noMatchedField: 'Lỗi - Không tìm thấy field trùng với \"{{label}}\".',\n    notAllowedToAccessPage: 'Lỗi - Bạn không có quyền truy cập trang này.',\n    notAllowedToPerformAction: 'Lỗi - Bạn không có quyền thực hiện lệnh này.',\n    notFound: 'Lỗi - Không thể tìm thấy.',\n    noUser: 'Lỗi - Request thiếu thông tin người dùng.',\n    previewing: 'Lỗi - Đã xảy ra vấn đề khi xem trước bản tài liệu này.',\n    problemUploadingFile: 'Lỗi - Đã xảy ra vấn để khi tải lên file sau.',\n    restoringTitle:\n      'Đã xảy ra lỗi trong quá trình khôi phục {{title}}. Vui lòng kiểm tra kết nối của bạn và thử lại.',\n    tokenInvalidOrExpired: 'Lỗi - Token không hợp lệ hoặc đã hết hạn.',\n    tokenNotProvided: 'Không cung cấp mã thông báo.',\n    unableToCopy: 'Không thể sao chép.',\n    unableToDeleteCount: 'Không thể xóa {{count}} trong số {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Lỗi khi tái lập chỉ mục bộ sưu tập {{collection}}. Quá trình bị hủy.',\n    unableToUpdateCount: 'Không thể cập nhật {{count}} trên {{total}} {{label}}.',\n    unauthorized: 'Lỗi - Bạn cần phải đăng nhập trước khi gửi request sau.',\n    unauthorizedAdmin: 'Lỗi - Người dùng không có quyền truy cập vào bảng điều khiển.',\n    unknown: 'Lỗi - Không xác định (unknown error).',\n    unPublishingDocument: 'Lỗi - Đã xảy ra vấn để khi ẩn bản tài liệu.',\n    unspecific: 'Lỗi - Đã xảy ra (unspecific error).',\n    unverifiedEmail: 'Vui lòng xác minh email trước khi đăng nhập.',\n    userEmailAlreadyRegistered: 'Người dùng với email đã cho đã được đăng ký.',\n    userLocked: 'Lỗi- Tài khoản đã bị khóa do đăng nhập thất bại nhiều lần.',\n    usernameAlreadyRegistered: 'Một người dùng với tên đăng nhập đã cho đã được đăng ký.',\n    usernameOrPasswordIncorrect: 'Tên người dùng hoặc mật khẩu được cung cấp không chính xác.',\n    valueMustBeUnique: 'Lỗi - Giá trị không được trùng lặp.',\n    verificationTokenInvalid: 'Lỗi - Token dùng để xác thực không hợp lệ.',\n  },\n  fields: {\n    addLabel: 'Thêm: {{label}}',\n    addLink: 'Thêm liên kết',\n    addNew: 'Thêm mới',\n    addNewLabel: 'Thêm mới: {{label}}',\n    addRelationship: 'Thêm mối quan hệ (relationship)',\n    addUpload: 'Thêm tải lên (upload)',\n    block: 'block',\n    blocks: 'blocks',\n    blockType: 'Block Type',\n    chooseBetweenCustomTextOrDocument:\n      'Chọn giữa nhập URL văn bản tùy chỉnh hoặc liên kết đến tài liệu khác.',\n    chooseDocumentToLink: 'Chọn một tài liệu để liên kết đến',\n    chooseFromExisting: 'Chọn từ thư viện',\n    chooseLabel: 'Chọn: {{label}}',\n    collapseAll: 'Ẩn toàn bộ',\n    customURL: 'URL tùy chỉnh',\n    editLabelData: 'Chỉnh sửa nội dung của: {{label}}',\n    editLink: 'Chỉnh sửa liên kết',\n    editRelationship: 'Chỉnh sửa mối quan hệ',\n    enterURL: 'Nhập một URL',\n    internalLink: 'Liên kết nội bộ',\n    itemsAndMore: '{{items}} và {{count}} món nữa',\n    labelRelationship: 'Mối quan hệ của {{label}} (Relationship)',\n    latitude: 'Vĩ độ',\n    linkedTo: 'Được nối với <0>{{label}}</0>',\n    linkType: 'Loại liên kết',\n    longitude: 'Kinh độ',\n    newLabel: 'Tạo {{label}} mới',\n    openInNewTab: 'Mở trong trang mới',\n    passwordsDoNotMatch: 'Mật khẩu không trùng.',\n    relatedDocument: 'bản tài liệu liên quan',\n    relationTo: 'Có quan hệ với',\n    removeRelationship: 'Xóa Mối quan hệ',\n    removeUpload: 'Xóa bản tải lên',\n    saveChanges: 'Luu thay đổi',\n    searchForBlock: 'Tìm block',\n    selectExistingLabel: 'Chọn một {{label}} có sẵn',\n    selectFieldsToEdit: 'Chọn các trường để chỉnh sửa',\n    showAll: 'Hiển thị toàn bộ',\n    swapRelationship: 'Đổi quan hệ',\n    swapUpload: 'Đổi bản tải lên',\n    textToDisplay: 'Văn bản để hiển thị',\n    toggleBlock: 'Bật/tắt block',\n    uploadNewLabel: 'Tải lên bản mới: {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Duyệt theo Thư mục',\n    byFolder: 'Theo Thư mục',\n    deleteFolder: 'Xóa Thư mục',\n    folderName: 'Tên thư mục',\n    folders: 'Thư mục',\n    folderTypeDescription: 'Chọn loại tài liệu bộ sưu tập nào nên được cho phép trong thư mục này.',\n    itemHasBeenMoved: '{{title}} đã được chuyển đến {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} đã được chuyển đến thư mục gốc',\n    itemsMovedToFolder: '{{title}} đã được di chuyển vào {{folderName}}',\n    itemsMovedToRoot: '{{title}} đã được di chuyển vào thư mục gốc',\n    moveFolder: 'Di chuyển thư mục',\n    moveItemsToFolderConfirmation:\n      'Bạn sắp chuyển <1>{{count}} {{label}}</1> tới <2>{{toFolder}}</2>. Bạn có chắc chắn không?',\n    moveItemsToRootConfirmation:\n      'Bạn đang chuẩn bị di chuyển <1>{{count}} {{label}}</1> đến thư mục gốc. Bạn có chắc không?',\n    moveItemToFolderConfirmation:\n      'Bạn sắp chuyển <1>{{title}}</1> đến <2>{{toFolder}}</2>. Bạn có chắc không?',\n    moveItemToRootConfirmation:\n      'Bạn đang chuẩn bị di chuyển <1>{{title}}</1> đến thư mục gốc. Bạn có chắc chắn không?',\n    movingFromFolder: 'Di chuyển {{title}} từ {{fromFolder}}',\n    newFolder: 'Thư mục mới',\n    noFolder: 'Không có Thư mục',\n    renameFolder: 'Đổi tên thư mục',\n    searchByNameInFolder: 'Tìm kiếm theo Tên trong {{folderName}}',\n    selectFolderForItem: 'Chọn thư mục cho {{title}}',\n  },\n  general: {\n    name: 'Tên',\n    aboutToDelete: 'Chuẩn bị xóa {{label}} <1>{{title}}</1>. Bạn có muốn tiếp tục không?',\n    aboutToDeleteCount_many: 'Bạn sắp xóa {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Bạn sắp xóa {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Bạn sắp xóa {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Bạn đang chuẩn bị xóa vĩnh viễn {{label}} <1>{{title}}</1>. Bạn có chắc không?',\n    aboutToPermanentlyDeleteTrash:\n      'Bạn sắp xóa vĩnh viễn <0>{{count}}</0> <1>{{label}}</1> từ thùng rác. Bạn có chắc chắn không?',\n    aboutToRestore: 'Bạn đang chuẩn bị khôi phục {{label}} <1>{{title}}</1>. Bạn có chắc không?',\n    aboutToRestoreAsDraft:\n      'Bạn đang chuẩn bị khôi phục {{label}} <1>{{title}}</1> dưới dạng bản nháp. Bạn có chắc không?',\n    aboutToRestoreAsDraftCount: 'Bạn sắp khôi phục {{count}} {{label}} dưới dạng bản nháp',\n    aboutToRestoreCount: 'Bạn sắp khôi phục {{count}} {{label}}',\n    aboutToTrash:\n      'Bạn đang chuẩn bị di chuyển {{label}} <1>{{title}}</1> vào thùng rác. Bạn có chắc không?',\n    aboutToTrashCount: 'Bạn đang chuẩn bị chuyển {{count}} {{label}} vào thùng rác',\n    addBelow: 'Thêm bên dưới',\n    addFilter: 'Thêm bộ lọc',\n    adminTheme: 'Giao diện bảng điều khiển',\n    all: 'Tất cả',\n    allCollections: 'Tất cả Bộ sưu tập',\n    allLocales: 'Tất cả địa phương',\n    and: 'Và',\n    anotherUser: 'Người dùng khác',\n    anotherUserTakenOver: 'Người dùng khác đã tiếp quản việc chỉnh sửa tài liệu này.',\n    applyChanges: 'Áp dụng Thay đổi',\n    ascending: 'Sắp xếp theo thứ tự tăng dần',\n    automatic: 'Tự động',\n    backToDashboard: 'Quay lại bảng điều khiển',\n    cancel: 'Hủy',\n    changesNotSaved: 'Thay đổi chưa được lưu lại. Bạn sẽ mất bản chỉnh sửa nếu thoát bây giờ.',\n    clear: 'Rõ ràng',\n    clearAll: 'Xóa tất cả',\n    close: 'Gần',\n    collapse: 'Thu gọn',\n    collections: 'Collections',\n    columns: 'Hiển thị cột',\n    columnToSort: 'Sắp xếp cột',\n    confirm: 'Xác nhận',\n    confirmCopy: 'Xác nhận bản sao',\n    confirmDeletion: 'Xác nhận xóa',\n    confirmDuplication: 'Xác nhận tạo bản sao',\n    confirmMove: 'Xác nhận di chuyển',\n    confirmReindex: 'Tái lập chỉ mục tất cả {{collections}}?',\n    confirmReindexAll: 'Tái lập chỉ mục tất cả các bộ sưu tập?',\n    confirmReindexDescription:\n      'Điều này sẽ xóa các chỉ mục hiện tại và tái lập chỉ mục các tài liệu trong các bộ sưu tập {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Điều này sẽ xóa các chỉ mục hiện tại và tái lập chỉ mục các tài liệu trong tất cả các bộ sưu tập.',\n    confirmRestoration: 'Xác nhận khôi phục',\n    copied: 'Đâ sao chép',\n    copy: 'Sao chép',\n    copyField: 'Sao chép trường',\n    copying: 'Sao chép',\n    copyRow: 'Sao chép dòng',\n    copyWarning:\n      'Bạn đang chuẩn bị ghi đè {{to}} bằng {{from}} cho {{label}} {{title}}. Bạn có chắc chắn không?',\n    create: 'Tạo',\n    created: 'Đã tạo',\n    createdAt: 'Ngày tạo',\n    createNew: 'Tạo mới',\n    createNewLabel: 'Tạo mới {{label}}',\n    creating: 'Đang tạo',\n    creatingNewLabel: 'Đang tạo mới {{label}}',\n    currentlyEditing:\n      'hiện đang chỉnh sửa tài liệu này. Nếu bạn tiếp quản, họ sẽ bị chặn tiếp tục chỉnh sửa và cũng có thể mất các thay đổi chưa lưu.',\n    custom: 'Tùy chỉnh',\n    dark: 'Nền tối',\n    dashboard: 'Bảng điều khiển',\n    delete: 'Xóa',\n    deleted: 'Đã xóa',\n    deletedAt: 'Đã Xóa Lúc',\n    deletedCountSuccessfully: 'Đã xóa thành công {{count}} {{label}}.',\n    deletedSuccessfully: 'Đã xoá thành công.',\n    deletePermanently: 'Bỏ qua thùng rác và xóa vĩnh viễn',\n    deleting: 'Đang xóa...',\n    depth: 'Độ sâu',\n    descending: 'Xếp theo thứ tự giảm dần',\n    deselectAllRows: 'Bỏ chọn tất cả các hàng',\n    document: 'Tài liệu',\n    documentIsTrashed: 'Nhãn này {{label}} đã bị xóa và chỉ được phép đọc.',\n    documentLocked: 'Tài liệu bị khóa',\n    documents: 'Tài liệu',\n    duplicate: 'Tạo bản sao',\n    duplicateWithoutSaving: 'Không lưu dữ liệu và tạo bản sao',\n    edit: 'Chỉnh sửa',\n    editAll: 'Chỉnh sửa tất cả',\n    editedSince: 'Được chỉnh sửa từ',\n    editing: 'Đang chỉnh sửa',\n    editingLabel_many: 'Đang chỉnh sửa {{count}} {{label}}',\n    editingLabel_one: 'Đang chỉnh sửa {{count}} {{label}}',\n    editingLabel_other: 'Đang chỉnh sửa {{count}} {{label}}',\n    editingTakenOver: 'Chỉnh sửa đã được tiếp quản',\n    editLabel: 'Chỉnh sửa: {{label}}',\n    email: 'Email',\n    emailAddress: 'Địa chỉ Email',\n    emptyTrash: 'Dọn rác',\n    emptyTrashLabel: 'Dọn rác {{label}}',\n    enterAValue: 'Nhập một giá trị',\n    error: 'Lỗi',\n    errors: 'Lỗi',\n    exitLivePreview: 'Thoát Xem trực tiếp',\n    export: 'Xuất khẩu',\n    fallbackToDefaultLocale: 'Ngôn ngữ mặc định',\n    false: 'Sai',\n    filter: 'Lọc',\n    filters: 'Bộ lọc',\n    filterWhere: 'Lọc {{label}} với điều kiện:',\n    globals: 'Toàn thể (globals)',\n    goBack: 'Quay lại',\n    groupByLabel: 'Nhóm theo {{label}}',\n    import: 'Nhập khẩu',\n    isEditing: 'đang chỉnh sửa',\n    item: 'mặt hàng',\n    items: 'mặt hàng',\n    language: 'Ngôn ngữ',\n    lastModified: 'Chỉnh sửa lần cuối vào lúc',\n    leaveAnyway: 'Tiếp tục thoát',\n    leaveWithoutSaving: 'Thay đổi chưa được lưu',\n    light: 'Nền sáng',\n    livePreview: 'Xem trước',\n    loading: 'Đang tải',\n    locale: 'Ngôn ngữ',\n    locales: 'Khu vực',\n    menu: 'Thực đơn',\n    moreOptions: 'Nhiều lựa chọn hơn',\n    move: 'Di chuyển',\n    moveConfirm:\n      'Bạn sắp chuyển {{count}} {{label}} đến <1>{{destination}}</1>. Bạn có chắc chắn không?',\n    moveCount: 'Di chuyển {{count}} {{label}}',\n    moveDown: 'Di chuyển xuống',\n    moveUp: 'Di chuyển lên',\n    moving: 'Di chuyển',\n    movingCount: 'Di chuyển {{count}} {{label}}',\n    newPassword: 'Mật khảu mới',\n    next: 'Tiếp theo',\n    no: 'Không',\n    noDateSelected: 'Không có ngày nào được chọn',\n    noFiltersSet: 'Không có bộ lọc nào được áp dụng',\n    noLabel: '<Không có {{label}}>',\n    none: 'Không có',\n    noOptions: 'Không có lựa chọn',\n    noResults:\n      'Danh sách rỗng: {{label}}. Có thể {{label}} chưa tồn tại hoặc không có dữ kiện trùng với bộ lọc hiện tại.',\n    notFound: 'Không tìm thấy',\n    nothingFound: 'Không tìm thấy',\n    noTrashResults: 'Không có {{label}} trong thùng rác.',\n    noUpcomingEventsScheduled: 'Không có sự kiện sắp tới được lên lịch.',\n    noValue: 'Không có giá trị',\n    of: 'trong số',\n    only: 'Chỉ',\n    open: 'Mở',\n    or: 'hoặc',\n    order: 'Thứ tự',\n    overwriteExistingData: 'Ghi đè dữ liệu trường hiện tại',\n    pageNotFound: 'Không tìm thấy trang',\n    password: 'Mật khẩu',\n    pasteField: 'Dán trường',\n    pasteRow: 'Dán dòng',\n    payloadSettings: 'Cài đặt',\n    permanentlyDelete: 'Xóa vĩnh viễn',\n    permanentlyDeletedCountSuccessfully: 'Đã xóa vĩnh viễn {{count}} {{label}} thành công.',\n    perPage: 'Hiển thị mỗi trang: {{limit}}',\n    previous: 'Trước đó',\n    reindex: 'Tái lập chỉ mục',\n    reindexingAll: 'Đang tái lập chỉ mục tất cả {{collections}}.',\n    remove: 'Loại bỏ',\n    rename: 'Đổi tên',\n    reset: 'Đặt lại',\n    resetPreferences: 'Đặt lại sở thích',\n    resetPreferencesDescription: 'Điều này sẽ đặt lại tất cả sở thích của bạn về cài đặt mặc định.',\n    resettingPreferences: 'Đang đặt lại sở thích.',\n    restore: 'Khôi phục',\n    restoreAsPublished: 'Khôi phục thành phiên bản đã xuất bản',\n    restoredCountSuccessfully: 'Đã khôi phục {{count}} {{label}} thành công.',\n    restoring:\n      'Tôn trọng ý nghĩa của văn bản gốc trong bối cảnh của Payload. Dưới đây là danh sách các thuật ngữ Payload thông thường mang ý nghĩa rất cụ thể:\\n- Collection: Collection (tạm dịch: Bộ sưu tập) là một nhóm các tài liệu chia sẻ cấu trúc và mục đích chung. Các Collection được sử dụng để tổ chức và quản lý nội dung trong Payload.\\n- Field: Field là một phần cụ th',\n    row: 'Hàng',\n    rows: 'Những hàng',\n    save: 'Luu',\n    saving: 'Đang lưu...',\n    schedulePublishFor: 'Lên lịch xuất bản cho {{title}}',\n    searchBy: 'Tìm với {{label}}',\n    select: 'Chọn',\n    selectAll: 'Chọn tất cả {{count}} {{label}}',\n    selectAllRows: 'Chọn tất cả các hàng',\n    selectedCount: 'Đã chọn {{count}} {{label}}',\n    selectLabel: 'Chọn {{label}}',\n    selectValue: 'Chọn một giá trị',\n    showAllLabel: 'Hiển thị tất cả {{label}}',\n    sorryNotFound: 'Xin lỗi, không có kết quả nào tương ứng với request của bạn.',\n    sort: 'Sắp xếp',\n    sortByLabelDirection: 'Sắp xếp theo {{label}} {{direction}}',\n    stayOnThisPage: 'Ở lại trang này',\n    submissionSuccessful: 'Gửi thành công.',\n    submit: 'Gửi',\n    submitting: 'Đang gửi...',\n    success: 'Thành công',\n    successfullyCreated: '{{label}} đã được tạo thành công.',\n    successfullyDuplicated: '{{label}} đã được sao chép thành công.',\n    successfullyReindexed:\n      'Tái lập chỉ mục thành công {{count}} trong tổng số {{total}} tài liệu từ {{collections}} bộ sưu tập.',\n    takeOver: 'Tiếp quản',\n    thisLanguage: 'Vietnamese (Tiếng Việt)',\n    time: 'Thời gian',\n    timezone: 'Múi giờ',\n    titleDeleted: '{{label}} {{title}} đã được xóa thành công.',\n    titleRestored: '{{label}} \"{{title}}\" được khôi phục thành công.',\n    titleTrashed: '{{label}} \"{{title}}\" đã được chuyển vào thùng rác.',\n    trash: 'Rác',\n    trashedCountSuccessfully: '{{count}} {{label}} đã được chuyển vào thùng rác.',\n    true: 'Thật',\n    unauthorized: 'Không có quyền truy cập.',\n    unsavedChanges: 'Bạn có những thay đổi chưa được lưu. Lưu hoặc hủy trước khi tiếp tục.',\n    unsavedChangesDuplicate: 'Bạn chưa lưu các thay đổi. Bạn có muốn tiếp tục tạo bản sao?',\n    untitled: 'Chưa có tiêu đề',\n    upcomingEvents: 'Sự kiện sắp tới',\n    updatedAt: 'Ngày cập nhật',\n    updatedCountSuccessfully: 'Đã cập nhật thành công {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Đã cập nhật {{label}} thành công.',\n    updatedSuccessfully: 'Cập nhật thành công.',\n    updateForEveryone: 'Cập nhật cho mọi người',\n    updating: 'Đang cập nhật',\n    uploading: 'Đang tải lên',\n    uploadingBulk: 'Đang tải lên {{current}} trong tổng số {{total}}',\n    user: 'Người dùng',\n    username: 'Tên đăng nhập',\n    users: 'Người dùng',\n    value: 'Giá trị',\n    viewing: 'Xem',\n    viewReadOnly: 'Xem chỉ đọc',\n    welcome: 'Xin chào',\n    yes: 'Đúng',\n  },\n  localization: {\n    cannotCopySameLocale: 'Không thể sao chép vào cùng một vị trí',\n    copyFrom: 'Sao chép từ',\n    copyFromTo: 'Sao chép từ {{from}} đến {{to}}',\n    copyTo: 'Sao chép đến',\n    copyToLocale: 'Sao chép vào địa phương',\n    localeToPublish: 'Ngôn ngữ để xuất bản',\n    selectLocaleToCopy: 'Chọn địa phương để sao chép',\n  },\n  operators: {\n    contains: 'có chứa',\n    equals: 'bằng',\n    exists: 'tồn tại',\n    intersects: 'giao nhau',\n    isGreaterThan: 'lớn hơn',\n    isGreaterThanOrEqualTo: 'lớn hơn hoặc bằng',\n    isIn: 'có trong',\n    isLessThan: 'nhỏ hơn',\n    isLessThanOrEqualTo: 'nhỏ hơn hoặc bằng',\n    isLike: 'gần giống',\n    isNotEqualTo: 'không bằng',\n    isNotIn: 'không có trong',\n    isNotLike: 'không giống như',\n    near: 'gần',\n    within: 'trong',\n  },\n  upload: {\n    addFile: 'Thêm tập tin',\n    addFiles: 'Thêm tệp',\n    bulkUpload: 'Tải lên số lượng lớn',\n    crop: 'Mùa vụ',\n    cropToolDescription:\n      'Kéo các góc của khu vực đã chọn, vẽ một khu vực mới hoặc điều chỉnh các giá trị dưới đây.',\n    download: 'Tải xuống',\n    dragAndDrop: 'Kéo và thả một tập tin',\n    dragAndDropHere: 'hoặc kéo và thả file vào đây',\n    editImage: 'Chỉnh sửa hình ảnh',\n    fileName: 'Tên file',\n    fileSize: 'Dung lượng file',\n    filesToUpload: 'Tệp để Tải lên',\n    fileToUpload: 'Tệp để Tải lên',\n    focalPoint: 'Điểm trọng tâm',\n    focalPointDescription:\n      'Kéo điểm tiêu cực trực tiếp trên trình xem trước hoặc điều chỉnh các giá trị bên dưới.',\n    height: 'Chiều cao',\n    lessInfo: 'Hiển thị ít hơn',\n    moreInfo: 'Thêm',\n    noFile: 'Không có tệp',\n    pasteURL: 'Dán URL',\n    previewSizes: 'Kích cỡ xem trước',\n    selectCollectionToBrowse: 'Chọn một Collection để tìm',\n    selectFile: 'Chọn một file',\n    setCropArea: 'Đặt khu vực cắt',\n    setFocalPoint: 'Đặt điểm tiêu điểm',\n    sizes: 'Các độ phân giải',\n    sizesFor: 'Kích thước cho {{label}}',\n    width: 'Chiều rộng',\n  },\n  validation: {\n    emailAddress: 'Địa chỉ email không hợp lệ.',\n    enterNumber: 'Vui lòng nhập số.',\n    fieldHasNo: 'Field này không có: {{label}}',\n    greaterThanMax: '{{value}} lớn hơn giá trị tối đa cho phép của {{label}} là {{max}}.',\n    invalidInput: 'Dữ liệu nhập vào không hợp lệ.',\n    invalidSelection: 'Lựa chọn ở field này không hợp lệ.',\n    invalidSelections: \"'Field này có những lựa chọn không hợp lệ sau:'\",\n    lessThanMin: '{{value}} nhỏ hơn giá trị tối thiểu cho phép của {{label}} là {{min}}.',\n    limitReached: 'Đã đạt giới hạn, chỉ có thể thêm {{max}} mục.',\n    longerThanMin: 'Giá trị này cần có độ dài tối thiểu {{minLength}} ký tự.',\n    notValidDate: '\"{{value}}\" không phải là một ngày (date) hợp lệ.',\n    required: 'Field này cần được diền.',\n    requiresAtLeast: 'Field này cần tối thiểu {{count}} {{label}}.',\n    requiresNoMoreThan: 'Field này không thể vượt quá {{count}} {{label}}.',\n    requiresTwoNumbers: 'Field này cần tối thiểu 2 chữ số.',\n    shorterThanMax: 'Giá trị phải ngắn hơn hoặc bằng {{maxLength}} ký tự.',\n    timezoneRequired: 'Yêu cầu phải có múi giờ.',\n    trueOrFalse: 'Field này chỉ có thể chứa giá trị true hoặc false.',\n    username:\n      'Vui lòng nhập một tên người dùng hợp lệ. Có thể chứa các chữ cái, số, dấu gạch ngang, dấu chấm và dấu gạch dưới.',\n    validUploadID: \"'Field này không chứa ID tải lên hợp lệ.'\",\n  },\n  version: {\n    type: 'Loại',\n    aboutToPublishSelection: 'Bạn có muốn xuất bản tất cả {{label}} không?',\n    aboutToRestore: 'Bạn chuẩn bị khôi phục lại {{label}} về phiên bản {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Bạn chuẩn bị khôi phục lại bản toàn thể (global) của {{label}} về phiên bản {{versionDate}}.',\n    aboutToRevertToPublished: 'Bạn có muốn tái xuất bản bản nháp này không?',\n    aboutToUnpublish: 'Bạn có muốn ngưng xuất bản?',\n    aboutToUnpublishSelection: 'Bạn có muốn ngưng xuất bản tất cả {{label}} không?',\n    autosave: 'Tự động lưu dữ liệu',\n    autosavedSuccessfully: 'Đã tự động lưu thành công.',\n    autosavedVersion: 'Các phiên bản từ việc tự động lưu dữ liệu',\n    changed: 'Đã thay đổi',\n    changedFieldsCount_one: '{{count}} đã thay đổi trường',\n    changedFieldsCount_other: '{{count}} trường đã thay đổi',\n    compareVersion: 'So sánh phiên bản này với:',\n    compareVersions: 'So sánh các phiên bản',\n    comparingAgainst: 'So sánh với',\n    confirmPublish: 'Xác nhận xuất bản',\n    confirmRevertToSaved: 'Xác nhận, quay về trạng thái đã lưu',\n    confirmUnpublish: 'Xác nhận, ngưng xuất bản',\n    confirmVersionRestoration: 'Xác nhận, khôi phục về phiên bản trước',\n    currentDocumentStatus: 'Trạng thái tài liệu hiện tại: {{docStatus}}',\n    currentDraft: 'Bản thảo hiện tại',\n    currentlyPublished: 'Hiện đã xuất bản',\n    currentlyViewing: 'Đang xem',\n    currentPublishedVersion: 'Phiên bản Đã Xuất bản Hiện tại',\n    draft: 'Bản nháp',\n    draftSavedSuccessfully: 'Bản nháp đã được lưu thành công.',\n    lastSavedAgo: 'Lần lưu cuối cùng {{distance}} trước đây',\n    modifiedOnly: 'Chỉ được sửa đổi',\n    moreVersions: 'Thêm phiên bản...',\n    noFurtherVersionsFound: 'Không tìm thấy phiên bản cũ hơn',\n    noRowsFound: 'Không tìm thấy: {{label}}',\n    noRowsSelected: 'Không có {{label}} được chọn',\n    preview: 'Bản xem trước',\n    previouslyDraft: 'Trước đây là Bản nháp',\n    previouslyPublished: 'Đã xuất bản trước đây',\n    previousVersion: 'Phiên bản Trước',\n    problemRestoringVersion: 'Đã xảy ra vấn đề khi khôi phục phiên bản này',\n    publish: 'Công bố',\n    publishAllLocales: 'Xuất bản tất cả địa phương',\n    publishChanges: 'Xuất bản tài liệu',\n    published: 'Đã xuất bản',\n    publishIn: 'Xuất bản trong {{locale}}',\n    publishing: 'Xuất bản',\n    restoreAsDraft: 'Khôi phục như bản nháp',\n    restoredSuccessfully: 'Đã khôi phục thành công.',\n    restoreThisVersion: 'Khôi phục về phiên bản này',\n    restoring: 'Đang khôi phục...',\n    reverting: 'Đang về trạng thái cũ...',\n    revertToPublished: 'Quay về trạng thái đã xuất bản',\n    saveDraft: 'Lưu bản nháp',\n    scheduledSuccessfully: 'Đã lên lịch thành công.',\n    schedulePublish: 'Lịch xuất bản',\n    selectLocales: 'Chọn mã khu vực để hiện thị',\n    selectVersionToCompare: 'Chọn phiên bản để so sánh',\n    showingVersionsFor: 'Hiển thị các phiên bản cho:',\n    showLocales: 'Hiển thị mã khu vực:',\n    specificVersion: 'Phiên bản cụ thể',\n    status: 'Trạng thái',\n    unpublish: 'Ẩn tài liệu',\n    unpublishing: 'Đang ẩn tài liệu...',\n    version: 'Phiên bản',\n    versionAgo: '{{distance}} trước',\n    versionCount_many: '{{count}} phiên bản được tìm thấy',\n    versionCount_none: 'Không có phiên bản nào được tìm thấy',\n    versionCount_one: '{{count}} phiên bản được tìm thấy',\n    versionCount_other: 'Đã tìm thấy {{count}} phiên bản',\n    versionCreatedOn: 'Phiên bản {{version}} được tạo vào lúc:',\n    versionID: 'ID của phiên bản',\n    versions: 'Danh sách phiên bản',\n    viewingVersion: 'Xem phiên bản của {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: '`Xem phiên bản toàn thể (global) của {{entityLabel}}',\n    viewingVersions: 'Xem những phiên bản của {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: '`Xem những phiên bản toàn thể (global) của {{entityLabel}}',\n  },\n}\n\nexport const vi: Language = {\n  dateFNSKey: 'vi',\n  translations: viTranslations,\n}\n"], "names": ["viTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "vi", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCAAiC;QACjCC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UACE;QACFC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBACE;QACFC,0BAA0B;QAC1BC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}