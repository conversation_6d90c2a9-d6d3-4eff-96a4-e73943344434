{"version": 3, "sources": ["../../src/languages/lv.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const lvTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Konts',\n    accountOfCurrentUser: 'Pašreizējā lietotāja konts',\n    accountVerified: 'Ko<PERSON> veiksmīgi verificēts.',\n    alreadyActivated: 'Jau aktivizēts',\n    alreadyLoggedIn: 'Jau pieslēdzies',\n    apiKey: 'API atslēga',\n    authenticated: 'Autentificēts',\n    backToLogin: 'Atpakaļ uz pieslēgšanos',\n    beginCreateFirstUser: '<PERSON> sāktu, izveidojiet savu pirmo lietotāju.',\n    changePassword: 'Mainīt paroli',\n    checkYourEmailForPasswordReset:\n      'Ja e-pasta adrese ir saistīta ar kontu, drīz saņemsiet norādījumus paroles atiestatīšanai. Lūdzu, pārbaudiet arī surogātpasta mapi, ja e-pasts nav iesūtnē.',\n    confirmGeneration: 'Apstiprināt ģenerēšanu',\n    confirmPassword: 'Apstiprināt paroli',\n    createFirstUser: 'Izveidot pirmo lietotāju',\n    emailNotValid: 'Norādītais e-pasts nav derīgs',\n    emailOrUsername: 'E-pasts vai lietotājvārds',\n    emailSent: 'E-pasts nosūtīts',\n    emailVerified: 'E-pasts veiksmīgi verificēts.',\n    enableAPIKey: 'Ieslēgt API atslēgu',\n    failedToUnlock: 'Neizdevās atbloķēt',\n    forceUnlock: 'Piespiedu atbloķēšana',\n    forgotPassword: 'Aizmirsi paroli?',\n    forgotPasswordEmailInstructions:\n      'Lūdzu, ievadiet savu e-pastu zemāk. Saņemsiet ziņojumu ar norādījumiem paroles atiestatīšanai.',\n    forgotPasswordQuestion: 'Aizmirsi paroli?',\n    forgotPasswordUsernameInstructions:\n      'Lūdzu, ievadiet savu lietotājvārdu zemāk. Norādījumi paroles atiestatīšanai tiks nosūtīti uz e-pastu, kas saistīts ar jūsu lietotājvārdu.',\n    generate: 'Ģenerēt',\n    generateNewAPIKey: 'Ģenerēt jaunu API atslēgu',\n    generatingNewAPIKeyWillInvalidate:\n      'Ģenerējot jaunu API atslēgu, <1>iepriekšējā atslēga kļūs nederīga</1>. Vai tiešām vēlaties turpināt?',\n    lockUntil: 'Bloķēts līdz',\n    logBackIn: 'Pieslēgties atkārtoti',\n    loggedIn: 'Lai pieslēgtos ar citu lietotāju, vispirms <0>atslēdzieties</0>.',\n    loggedInChangePassword:\n      'Lai mainītu paroli, dodieties uz savu <0>kontu</0> un rediģējiet paroli tur.',\n    loggedOutInactivity: 'Jūs esat atslēgts neaktivitātes dēļ.',\n    loggedOutSuccessfully: 'Jūs veiksmīgi atslēdzāties.',\n    loggingOut: 'Notiek atslēgšanās...',\n    login: 'Pieslēgties',\n    loginAttempts: 'Pieslēgšanās mēģinājumi',\n    loginUser: 'Pieslēgt lietotāju',\n    loginWithAnotherUser: 'Lai pieslēgtos ar citu lietotāju, vispirms <0>atslēdzieties</0>.',\n    logOut: 'Atslēgties',\n    logout: 'Atslēgties',\n    logoutSuccessful: 'Atslēgšanās veiksmīga.',\n    logoutUser: 'Atslēgt lietotāju',\n    newAccountCreated:\n      'Jums tikko ir izveidots jauns konts piekļuvei <a href=\"{{serverURL}}\">{{serverURL}}</a>. Lūdzu, noklikšķiniet uz šīs saites vai iekopējiet URL pārlūkprogrammā, lai verificētu savu e-pastu: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Pēc e-pasta verificēšanas varēsiet veiksmīgi pieslēgties.',\n    newAPIKeyGenerated: 'Jauna API atslēga ģenerēta.',\n    newPassword: 'Jauna parole',\n    passed: 'Autentifikācija veiksmīga',\n    passwordResetSuccessfully: 'Parole veiksmīgi atiestatīta.',\n    resetPassword: 'Atiestatīt paroli',\n    resetPasswordExpiration: 'Paroles atiestatīšanas termiņš',\n    resetPasswordToken: 'Paroles atiestatīšanas tokens',\n    resetYourPassword: 'Atiestatīt savu paroli',\n    stayLoggedIn: 'Palikt pieslēgtam',\n    successfullyRegisteredFirstUser: 'Pirmais lietotājs veiksmīgi reģistrēts.',\n    successfullyUnlocked: 'Veiksmīgi atbloķēts',\n    tokenRefreshSuccessful: 'Tokens veiksmīgi atjaunots.',\n    unableToVerify: 'Neizdevās verificēt',\n    username: 'Lietotājvārds',\n    usernameNotValid: 'Norādītais lietotājvārds nav derīgs',\n    verified: 'Verificēts',\n    verifiedSuccessfully: 'Veiksmīgi verificēts',\n    verify: 'Verificēt',\n    verifyUser: 'Verificēt lietotāju',\n    verifyYourEmail: 'Verificējiet savu e-pastu',\n    youAreInactive:\n      'Jūs kādu laiku neesat bijis aktīvs, un drošības nolūkos drīz automātiski tiksiet atslēgts. Vai vēlaties palikt pieslēgts?',\n    youAreReceivingResetPassword:\n      'Jūs saņemat šo ziņojumu, jo (vai kāds cits) esat pieprasījis paroles atiestatīšanu savam kontam. Lūdzu, noklikšķiniet uz šīs saites vai iekopējiet to pārlūkprogrammā, lai pabeigtu procesu:',\n    youDidNotRequestPassword:\n      'Ja neesat pieprasījis paroles atiestatīšanu, lūdzu, ignorējiet šo e-pastu, un parole paliks nemainīta.',\n  },\n  error: {\n    accountAlreadyActivated: 'Šis konts jau ir aktivizēts.',\n    autosaving: 'Radās problēma, automātiski saglabājot šo dokumentu.',\n    correctInvalidFields: 'Lūdzu, izlabojiet nederīgos laukus.',\n    deletingFile: 'Radās kļūda, dzēšot failu.',\n    deletingTitle:\n      'Radās kļūda, dzēšot {{title}}. Lūdzu, pārbaudiet savienojumu un mēģiniet vēlreiz.',\n    documentNotFound:\n      'Dokuments ar ID {{id}} netika atrasts. Iespējams, tas ir izdzēsts vai nekad nav eksistējis, vai arī jums nav pieejas tam.',\n    emailOrPasswordIncorrect: 'Norādītais e-pasts vai parole nav pareiza.',\n    followingFieldsInvalid_one: 'Šis lauks nav derīgs:',\n    followingFieldsInvalid_other: 'Šie lauki nav derīgi:',\n    incorrectCollection: 'Nepareiza kolekcija',\n    insufficientClipboardPermissions:\n      'Piekļuve starpliktuvei liegta. Lūdzu, pārbaudiet savas starpliktuves atļaujas.',\n    invalidClipboardData: 'Nederīgi starpliktuves dati.',\n    invalidFileType: 'Nederīgs faila tips',\n    invalidFileTypeValue: 'Nederīgs faila tips: {{value}}',\n    invalidRequestArgs: 'Pieprasījumā nodoti nederīgi argumenti: {{args}}',\n    loadingDocument: 'Radās problēma, ielādējot dokumentu ar ID {{id}}.',\n    localesNotSaved_one: 'Šo lokalizāciju nevarēja saglabāt:',\n    localesNotSaved_other: 'Šīs lokalizācijas nevarēja saglabāt:',\n    logoutFailed: 'Neizdevās atslēgties.',\n    missingEmail: 'Trūkst e-pasta.',\n    missingIDOfDocument: 'Trūkst dokumenta ID, ko atjaunināt.',\n    missingIDOfVersion: 'Trūkst versijas ID.',\n    missingRequiredData: 'Trūkst nepieciešamo datu.',\n    noFilesUploaded: 'Nav augšupielādēti faili.',\n    noMatchedField: 'Nav atrasts atbilstošs lauks \"{{label}}\"',\n    notAllowedToAccessPage: 'Jums nav atļauts piekļūt šai lapai.',\n    notAllowedToPerformAction: 'Jums nav atļauts veikt šo darbību.',\n    notFound: 'Pieprasītais resurss nav atrasts.',\n    noUser: 'Nav lietotāja',\n    previewing: 'Radās problēma, priekšskatot šo dokumentu.',\n    problemUploadingFile: 'Radās problēma, augšupielādējot failu.',\n    restoringTitle:\n      'Notika kļūda, atjaunojot {{title}}. Lūdzu, pārbaudiet savu savienojumu un mēģiniet vēlreiz.',\n    tokenInvalidOrExpired: 'Tokens ir nederīgs vai beidzies.',\n    tokenNotProvided: 'Tokens nav norādīts.',\n    unableToCopy: 'Neizdevās kopēt.',\n    unableToDeleteCount: 'Neizdevās izdzēst {{count}} no {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Radās kļūda, pārindeksējot kolekciju {{collection}}. Operācija pārtraukta.',\n    unableToUpdateCount: 'Neizdevās atjaunināt {{count}} no {{total}} {{label}}.',\n    unauthorized: 'Neautorizēts, jums jāpieslēdzas, lai veiktu šo pieprasījumu.',\n    unauthorizedAdmin: 'Neautorizēts, šim lietotājam nav piekļuves administrācijas panelim.',\n    unknown: 'Radās nezināma kļūda.',\n    unPublishingDocument: 'Radās problēma, atceļot dokumenta publicēšanu.',\n    unspecific: 'Radās kļūda.',\n    unverifiedEmail: 'Lūdzu, verificējiet savu e-pastu pirms pieslēgšanās.',\n    userEmailAlreadyRegistered: 'Lietotājs ar šo e-pastu jau ir reģistrēts.',\n    userLocked: 'Šis lietotājs ir bloķēts pārāk daudzu neveiksmīgu pieslēgšanās mēģinājumu dēļ.',\n    usernameAlreadyRegistered: 'Lietotājs ar šo lietotājvārdu jau ir reģistrēts.',\n    usernameOrPasswordIncorrect: 'Norādītais lietotājvārds vai parole nav pareiza.',\n    valueMustBeUnique: 'Vērtībai jābūt unikālai',\n    verificationTokenInvalid: 'Verifikācijas tokens nav derīgs.',\n  },\n  fields: {\n    addLabel: 'Pievienot {{label}}',\n    addLink: 'Pievienot saiti',\n    addNew: 'Pievienot jaunu',\n    addNewLabel: 'Pievienot jaunu {{label}}',\n    addRelationship: 'Pievienot saistību',\n    addUpload: 'Pievienot augšupielādi',\n    block: 'bloks',\n    blocks: 'bloki',\n    blockType: 'Bloka tips',\n    chooseBetweenCustomTextOrDocument:\n      'Izvēlieties starp pielāgotu teksta URL vai saiti uz citu dokumentu.',\n    chooseDocumentToLink: 'Izvēlieties dokumentu, uz kuru saistīt',\n    chooseFromExisting: 'Izvēlieties no esošajiem',\n    chooseLabel: 'Izvēlieties {{label}}',\n    collapseAll: 'Sakļaut visus',\n    customURL: 'Pielāgots URL',\n    editLabelData: 'Rediģēt {{label}} datus',\n    editLink: 'Rediģēt saiti',\n    editRelationship: 'Rediģēt saistību',\n    enterURL: 'Ievadiet URL',\n    internalLink: 'Iekšēja saite',\n    itemsAndMore: '{{items}} un vēl {{count}}',\n    labelRelationship: '{{label}} saistība',\n    latitude: 'Platums',\n    linkedTo: 'Saistīts ar <0>{{label}}</0>',\n    linkType: 'Saites tips',\n    longitude: 'Garums',\n    newLabel: 'Jauns {{label}}',\n    openInNewTab: 'Atvērt jaunā cilnē',\n    passwordsDoNotMatch: 'Paroles nesakrīt.',\n    relatedDocument: 'Saistītais dokuments',\n    relationTo: 'Saistība ar',\n    removeRelationship: 'Noņemt saistību',\n    removeUpload: 'Noņemt augšupielādi',\n    saveChanges: 'Saglabāt izmaiņas',\n    searchForBlock: 'Meklēt bloku',\n    selectExistingLabel: 'Izvēlēties esošo {{label}}',\n    selectFieldsToEdit: 'Izvēlēties laukus rediģēšanai',\n    showAll: 'Rādīt visus',\n    swapRelationship: 'Mainīt saistību',\n    swapUpload: 'Mainīt augšupielādi',\n    textToDisplay: 'Rādāmais teksts',\n    toggleBlock: 'Pārslēgt bloku',\n    uploadNewLabel: 'Augšupielādēt jaunu {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Pārlūkot pēc mapes',\n    byFolder: 'Pēc mapi',\n    deleteFolder: 'Dzēst mapi',\n    folderName: 'Mapes nosaukums',\n    folders: 'Mapes',\n    folderTypeDescription:\n      'Izvēlieties, kāda veida kolekcijas dokumentiem jābūt atļautiem šajā mapē.',\n    itemHasBeenMoved: '{{title}} ir pārvietots uz {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} ir pārvietots uz saknes mapi',\n    itemsMovedToFolder: '{{title}} pārvietots uz {{folderName}}',\n    itemsMovedToRoot: '{{title}} pārvietots uz saknes mapi',\n    moveFolder: 'Pārvietot mapi',\n    moveItemsToFolderConfirmation:\n      'Jūs esat gatavs pārvietot <1>{{count}} {{label}}</1> uz <2>{{toFolder}}</2>. Vai esat pārliecināts?',\n    moveItemsToRootConfirmation:\n      'Jūs gatavojaties pārvietot <1>{{count}} {{label}}</1> uz saknes mapi. Vai esat pārliecināts?',\n    moveItemToFolderConfirmation:\n      'Jūs gatavojaties pārvietot <1>{{title}}</1> uz <2>{{toFolder}}</2>. Vai esat pārliecināts?',\n    moveItemToRootConfirmation:\n      'Jūs gatavojaties pārvietot <1>{{title}}</1> uz saknes mapi. Vai esat pārliecināts?',\n    movingFromFolder: 'Pārvietojot {{title}} no {{fromFolder}}',\n    newFolder: 'Jauna Mape',\n    noFolder: 'Nav mapes',\n    renameFolder: 'Pārdēvēt mapi',\n    searchByNameInFolder: 'Meklēšana pēc vārda mapē {{folderName}}',\n    selectFolderForItem: 'Izvēlieties mapi priekš {{title}}',\n  },\n  general: {\n    name: 'Vārds',\n    aboutToDelete: 'Jūs grasāties dzēst {{label}} <1>{{title}}</1>. Vai esat pārliecināts?',\n    aboutToDeleteCount_many: 'Jūs grasāties dzēst {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Jūs grasāties dzēst {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Jūs grasāties dzēst {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Jūs esat gatavs neatgriezeniski dzēst {{label}} <1>{{title}}</1>. Vai esat pārliecināts?',\n    aboutToPermanentlyDeleteTrash:\n      'Jūs gatavojaties neatgriezeniski dzēst <0>{{count}}</0> <1>{{label}}</1> no miskastes. Vai esat pārliecināts?',\n    aboutToRestore: 'Jūs esat gatavs atjaunot {{label}} <1>{{title}}</1>. Vai esat pārliecināts?',\n    aboutToRestoreAsDraft:\n      'Jūs gatavojaties atjaunot {{label}} <1>{{title}}</1> kā melnrakstu. Vai esat pārliecināts?',\n    aboutToRestoreAsDraftCount: 'Jūs gatavojaties atjaunot {{count}} {{label}} kā melnrakstu',\n    aboutToRestoreCount: 'Jūs gatavojaties atjaunot {{count}} {{label}}',\n    aboutToTrash:\n      'Jūs gatavojaties pārvietot {{label}} <1>{{title}}</1> uz miskasti. Vai esat pārliecināts?',\n    aboutToTrashCount: 'Jūs gatavojaties pārvietot {{count}} {{label}} uz miskasti',\n    addBelow: 'Pievienot zemāk',\n    addFilter: 'Pievienot filtru',\n    adminTheme: 'Administratora tēma',\n    all: 'Visi',\n    allCollections: 'Visas kolekcijas',\n    allLocales: 'Visi lokalizācijas variants',\n    and: 'Un',\n    anotherUser: 'Cits lietotājs',\n    anotherUserTakenOver: 'Cits lietotājs ir pārņēmis šī dokumenta rediģēšanu.',\n    applyChanges: 'Pielietot izmaiņas',\n    ascending: 'Augošā secībā',\n    automatic: 'Automātiski',\n    backToDashboard: 'Atpakaļ uz paneli',\n    cancel: 'Atcelt',\n    changesNotSaved: 'Jūsu izmaiņas nav saglabātas. Ja tagad pametīsiet, izmaiņas tiks zaudētas.',\n    clear:\n      'Izpratiet oriģinālteksta nozīmi Payload kontekstā. Šeit ir saraksts ar Payload terminiem, kas ir ļoti specifiskas nozīmes:\\n    - Kolekcija: Kolekcija ir dokumentu grupa, kuriem ir kopīga struktūra un mērķis. Kolekcijas tiek izmantotas saturu organizēšanai un pārvaldīšanai Payload.\\n    - Lauks: Lauks ir konkrēts datu fragments dokumentā iekš kolekcijas. Lauki definē struktūru un dat',\n    clearAll: 'Notīrīt visu',\n    close: 'Aizvērt',\n    collapse: 'Sakļaut',\n    collections: 'Kolekcijas',\n    columns: 'Kolonnas',\n    columnToSort: 'Kolonna kārtošanai',\n    confirm: 'Apstiprināt',\n    confirmCopy: 'Apstiprināt kopēšanu',\n    confirmDeletion: 'Apstiprināt dzēšanu',\n    confirmDuplication: 'Apstiprināt dublēšanu',\n    confirmMove: 'Apstiprināt pārvietošanu',\n    confirmReindex: 'Pārindeksēt visus {{collections}}?',\n    confirmReindexAll: 'Pārindeksēt visas kolekcijas?',\n    confirmReindexDescription:\n      'Tas noņems esošos indeksus un pārindeksēs dokumentus kolekcijās {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Tas noņems esošos indeksus un pārindeksēs dokumentus visās kolekcijās.',\n    confirmRestoration: 'Apstipriniet atjaunošanu',\n    copied: 'Nokopēts',\n    copy: 'Kopēt',\n    copyField: 'Kopēt lauku',\n    copying: 'Kopē...',\n    copyRow: 'Kopēt rindu',\n    copyWarning:\n      'Jūs grasāties pārrakstīt {{to}} ar {{from}} priekš {{label}} {{title}}. Vai esat pārliecināts?',\n    create: 'Izveidot',\n    created: 'Izveidots',\n    createdAt: 'Izveidots',\n    createNew: 'Izveidot jaunu',\n    createNewLabel: 'Izveidot jaunu {{label}}',\n    creating: 'Izveido...',\n    creatingNewLabel: 'Izveido jaunu {{label}}',\n    currentlyEditing:\n      'pašlaik rediģē šo dokumentu. Ja pārņemsiet, viņi tiks bloķēti no turpmākas rediģēšanas un var zaudēt nesaglabātās izmaiņas.',\n    custom: 'Pielāgots',\n    dark: 'Tumšs',\n    dashboard: 'Panelis',\n    delete: 'Dzēst',\n    deleted: 'Dzēsts',\n    deletedAt: 'Dzēsts datumā',\n    deletedCountSuccessfully: 'Veiksmīgi izdzēsti {{count}} {{label}}.',\n    deletedSuccessfully: 'Veiksmīgi izdzēsts.',\n    deletePermanently: 'Izlaidiet miskasti un dzēsiet neatgriezeniski',\n    deleting: 'Dzēš...',\n    depth: 'Dziļums',\n    descending: 'Dilstošā secībā',\n    deselectAllRows: 'Atdzēlēt visas rindas',\n    document: 'Dokuments',\n    documentIsTrashed: 'Šis {{label}} ir miskastē un ir tikai lasāms.',\n    documentLocked: 'Dokuments bloķēts',\n    documents: 'Dokumenti',\n    duplicate: 'Dublēt',\n    duplicateWithoutSaving: 'Dublēt bez izmaiņu saglabāšanas',\n    edit: 'Rediģēt',\n    editAll: 'Rediģēt visus',\n    editedSince: 'Rediģēts kopš',\n    editing: 'Rediģē',\n    editingLabel_many: 'Rediģē {{count}} {{label}}',\n    editingLabel_one: 'Rediģē {{count}} {{label}}',\n    editingLabel_other: 'Rediģē {{count}} {{label}}',\n    editingTakenOver: 'Rediģēšana pārņemta',\n    editLabel: 'Rediģēt {{label}}',\n    email: 'E-pasts',\n    emailAddress: 'E-pasta adrese',\n    emptyTrash: 'Iztukšot miskasti',\n    emptyTrashLabel: 'Izrakstīt {{label}} atkritumu',\n    enterAValue: 'Ievadiet vērtību',\n    error: 'Kļūda',\n    errors: 'Kļūdas',\n    exitLivePreview: 'Iziet no tiešā priekšskatījuma',\n    export: 'Eksports',\n    fallbackToDefaultLocale: 'Izmantot noklusēto lokalizāciju',\n    false: 'Nepatiesi',\n    filter: 'Filtrs',\n    filters: 'Filtri',\n    filterWhere: 'Filtrēt {{label}} kur',\n    globals: 'Globālie',\n    goBack: 'Doties atpakaļ',\n    groupByLabel: 'Grupēt pēc {{label}}',\n    import: 'Imports',\n    isEditing: 'redzē',\n    item: 'vienība',\n    items: 'vienības',\n    language: 'Valoda',\n    lastModified: 'Pēdējoreiz mainīts',\n    leaveAnyway: 'Pamest tāpat',\n    leaveWithoutSaving: 'Pamest nesaglabājot',\n    light: 'Gaišs',\n    livePreview: 'Tiešais priekšskatījums',\n    loading: 'Ielādē...',\n    locale: 'Lokalizācija',\n    locales: 'Lokalizācijas',\n    menu: 'Izvēlne',\n    moreOptions: 'Vairāk opciju',\n    move: 'Pārvietoties',\n    moveConfirm:\n      'Jūs gatavojaties pārvietot {{count}} {{label}} uz <1>{{destination}}</1>. Vai esat pārliecināts?',\n    moveCount: 'Pārvietot {{count}} {{label}}',\n    moveDown: 'Pārvietot uz leju',\n    moveUp: 'Pārvietot uz augšu',\n    moving: 'Pārvietojas',\n    movingCount: 'Pārvietojot {{count}} {{label}}',\n    newPassword: 'Jauna parole',\n    next: 'Nākamais',\n    no: 'Nē',\n    noDateSelected: 'Datums nav izvēlēts',\n    noFiltersSet: 'Nav uzstādīti filtri',\n    noLabel: '<Nav {{label}}>',\n    none: 'Nav',\n    noOptions: 'Nav opciju',\n    noResults:\n      'Nav atrasts neviens {{label}}. Vai nu vēl nav izveidots, vai neviens neatbilst augstāk norādītajiem filtriem.',\n    notFound: 'Nav atrasts',\n    nothingFound: 'Nekas nav atrasts',\n    noTrashResults: 'Nav {{label}} miskastē.',\n    noUpcomingEventsScheduled: 'Nav ieplānotu notikumu.',\n    noValue: 'Nav vērtības',\n    of: 'no',\n    only: 'Tikai',\n    open: 'Atvērt',\n    or: 'Vai',\n    order: 'Kārtība',\n    overwriteExistingData: 'Pārrakstīt esošos datus',\n    pageNotFound: 'Lapa nav atrasta',\n    password: 'Parole',\n    pasteField: 'Ielīmēt lauku',\n    pasteRow: 'Ielīmēt rindu',\n    payloadSettings: 'Payload iestatījumi',\n    permanentlyDelete: 'Pastāvīgi Dzēst',\n    permanentlyDeletedCountSuccessfully: 'Veiksmīgi neatgriezeniski izdzēsts {{count}} {{label}}.',\n    perPage: 'Lapas ieraksti: {{limit}}',\n    previous: 'Iepriekšējais',\n    reindex: 'Pārindeksēt',\n    reindexingAll: 'Pārindeksē visus {{collections}}.',\n    remove: 'Noņemt',\n    rename: 'Pārdēvēt',\n    reset: 'Atiestatīt',\n    resetPreferences: 'Atiestatīt iestatījumus',\n    resetPreferencesDescription: 'Tas atjaunos visus jūsu iestatījumus uz noklusētajiem.',\n    resettingPreferences: 'Atiestata iestatījumus...',\n    restore: 'Atjaunot',\n    restoreAsPublished: 'Atjaunot kā publicēto versiju',\n    restoredCountSuccessfully: 'Veiksmīgi atjaunots {{count}} {{label}}.',\n    restoring: 'Atjaunojot...',\n    row: 'Rinda',\n    rows: 'Rindas',\n    save: 'Saglabāt',\n    saving: 'Saglabā...',\n    schedulePublishFor: 'Ieplānot publicēšanu priekš {{title}}',\n    searchBy: 'Meklēt pēc {{label}}',\n    select: 'Izvēlieties',\n    selectAll: 'Atlasīt visus {{count}} {{label}}',\n    selectAllRows: 'Atlasīt visas rindas',\n    selectedCount: 'Atlasīti {{count}} {{label}}',\n    selectLabel: 'Atlasīt {{label}}',\n    selectValue: 'Atlasīt vērtību',\n    showAllLabel: 'Rādīt visus {{label}}',\n    sorryNotFound: 'Atvainojiet — jūsu pieprasījumam neatbilst nekas.',\n    sort: 'Kārtot',\n    sortByLabelDirection: 'Kārtot pēc {{label}} {{direction}}',\n    stayOnThisPage: 'Palikt šajā lapā',\n    submissionSuccessful: 'Iesniegšana veiksmīga.',\n    submit: 'Iesniegt',\n    submitting: 'Iesniedz...',\n    success: 'Veiksmīgi',\n    successfullyCreated: '{{label}} veiksmīgi izveidots.',\n    successfullyDuplicated: '{{label}} veiksmīgi dublēts.',\n    successfullyReindexed:\n      'Veiksmīgi pārindeksēti {{count}} no {{total}} dokumentiem no {{collections}}',\n    takeOver: 'Pārņemt',\n    thisLanguage: 'Latviešu',\n    time: 'Laiks',\n    timezone: 'Laika zona',\n    titleDeleted: '{{label}} \"{{title}}\" veiksmīgi izdzēsts.',\n    titleRestored: '{{label}} \"{{title}}\" veiksmīgi atjaunots.',\n    titleTrashed: '{{label}} \"{{title}}\" pārvietots uz miskasti.',\n    trash: 'Atkritumi',\n    trashedCountSuccessfully: '{{count}} {{label}} pārvietoti uz miskasti.',\n    true: 'Patiesi',\n    unauthorized: 'Neautorizēts',\n    unsavedChanges: 'Jums ir nesaglabātas izmaiņas. Saglabājiet vai atceliet pirms turpināšanas.',\n    unsavedChangesDuplicate: 'Jums ir nesaglabātas izmaiņas. Vai vēlaties turpināt dublēšanu?',\n    untitled: 'Bez nosaukuma',\n    upcomingEvents: 'Gaidāmie notikumi',\n    updatedAt: 'Atjaunināts',\n    updatedCountSuccessfully: 'Veiksmīgi atjaunināti {{count}} {{label}}.',\n    updatedLabelSuccessfully: '{{label}} veiksmīgi atjaunināts.',\n    updatedSuccessfully: 'Veiksmīgi atjaunināts.',\n    updateForEveryone: 'Atjaunināt visiem',\n    updating: 'Atjaunina',\n    uploading: 'Augšupielādē...',\n    uploadingBulk: 'Augšupielādē {{current}} no {{total}}',\n    user: 'Lietotājs',\n    username: 'Lietotājvārds',\n    users: 'Lietotāji',\n    value: 'Vērtība',\n    viewing: 'Skatīšanās',\n    viewReadOnly: 'Skatīt tikai lasāmu',\n    welcome: 'Laipni lūdzam',\n    yes: 'Jā',\n  },\n  localization: {\n    cannotCopySameLocale: 'Nevar kopēt uz to pašu lokalizāciju',\n    copyFrom: 'Kopēt no',\n    copyFromTo: 'Kopē no {{from}} uz {{to}}',\n    copyTo: 'Kopēt uz',\n    copyToLocale: 'Kopēt uz lokalizāciju',\n    localeToPublish: 'Lokalizācija publicēšanai',\n    selectLocaleToCopy: 'Izvēlieties lokalizāciju, no kuras kopēt',\n  },\n  operators: {\n    contains: 'satur',\n    equals: 'ir vienāds ar',\n    exists: 'eksistē',\n    intersects: 'krustojas',\n    isGreaterThan: 'ir lielāks par',\n    isGreaterThanOrEqualTo: 'ir lielāks vai vienāds ar',\n    isIn: 'ir iekšā',\n    isLessThan: 'ir mazāks par',\n    isLessThanOrEqualTo: 'ir mazāks vai vienāds ar',\n    isLike: 'ir līdzīgs',\n    isNotEqualTo: 'nav vienāds ar',\n    isNotIn: 'nav iekšā',\n    isNotLike: 'nav līdzīgs',\n    near: 'tuvu',\n    within: 'iekšā',\n  },\n  upload: {\n    addFile: 'Pievienot failu',\n    addFiles: 'Pievienot failus',\n    bulkUpload: 'Masveida augšupielāde',\n    crop: 'Apgriezt',\n    cropToolDescription:\n      'Velciet atlasītā apgabala stūrus, uzzīmējiet jaunu apgabalu vai pielāgojiet vērtības zemāk.',\n    download: 'Lejupielādēt',\n    dragAndDrop: 'Ievelciet un nometiet failu',\n    dragAndDropHere: 'vai ievelciet un nometiet failu šeit',\n    editImage: 'Rediģēt attēlu',\n    fileName: 'Faila nosaukums',\n    fileSize: 'Faila izmērs',\n    filesToUpload: 'Faili augšupielādei',\n    fileToUpload: 'Fails augšupielādei',\n    focalPoint: 'Fokusa punkts',\n    focalPointDescription:\n      'Velciet fokusa punktu tieši priekšskatījumā vai pielāgojiet vērtības zemāk.',\n    height: 'Augstums',\n    lessInfo: 'Mazāk informācijas',\n    moreInfo: 'Vairāk informācijas',\n    noFile: 'Nav faila',\n    pasteURL: 'Ielīmēt URL',\n    previewSizes: 'Priekšskatījuma izmēri',\n    selectCollectionToBrowse: 'Izvēlieties kolekciju, ko pārlūkot',\n    selectFile: 'Izvēlieties failu',\n    setCropArea: 'Iestatīt apgriešanas apgabalu',\n    setFocalPoint: 'Iestatīt fokusa punktu',\n    sizes: 'Izmēri',\n    sizesFor: 'Izmēri priekš {{label}}',\n    width: 'Platums',\n  },\n  validation: {\n    emailAddress: 'Lūdzu, ievadiet derīgu e-pasta adresi.',\n    enterNumber: 'Lūdzu, ievadiet derīgu numuru.',\n    fieldHasNo: 'Šim laukam nav {{label}}',\n    greaterThanMax: '{{value}} ir lielāks par maksimāli atļauto {{label}}: {{max}}.',\n    invalidInput: 'Šim laukam ir nederīga ievade.',\n    invalidSelection: 'Šim laukam ir nederīga izvēle.',\n    invalidSelections: 'Šim laukam ir šādas nederīgas izvēles:',\n    lessThanMin: '{{value}} ir mazāks par minimāli atļauto {{label}}: {{min}}.',\n    limitReached: 'Sasniegts limits, var pievienot tikai {{max}} vienumus.',\n    longerThanMin: 'Šai vērtībai jābūt garākai par minimālo garumu: {{minLength}} rakstzīmes.',\n    notValidDate: '\"{{value}}\" nav derīgs datums.',\n    required: 'Šis lauks ir obligāts.',\n    requiresAtLeast: 'Šim laukam nepieciešami vismaz {{count}} {{label}}.',\n    requiresNoMoreThan: 'Šim laukam nepieciešams ne vairāk kā {{count}} {{label}}.',\n    requiresTwoNumbers: 'Šim laukam nepieciešami divi skaitļi.',\n    shorterThanMax: 'Šai vērtībai jābūt īsākai par maksimālo garumu: {{maxLength}} rakstzīmes.',\n    timezoneRequired: 'Nepieciešama laika josla.',\n    trueOrFalse: 'Šis lauks var būt tikai \"true\" vai \"false\".',\n    username:\n      'Lūdzu, ievadiet derīgu lietotājvārdu. Drīkst saturēt burtus, ciparus, defises, punktus un pasvītras.',\n    validUploadID: 'Šis lauks nav derīgs augšupielādes ID.',\n  },\n  version: {\n    type: 'Tips',\n    aboutToPublishSelection:\n      'Jūs grasāties publicēt visus {{label}} izvēlētajā sarakstā. Vai esat pārliecināts?',\n    aboutToRestore:\n      'Jūs grasāties atjaunot šo {{label}} dokumentu uz stāvokli, kādā tas bija {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Jūs grasāties atjaunot globālo {{label}} uz stāvokli, kādā tas bija {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Jūs grasāties atsaukt šī dokumenta izmaiņas uz publicēto versiju. Vai esat pārliecināts?',\n    aboutToUnpublish: 'Jūs grasāties atcelt šī dokumenta publicēšanu. Vai esat pārliecināts?',\n    aboutToUnpublishSelection:\n      'Jūs grasāties atcelt publicēšanu visiem {{label}} izvēlētajā sarakstā. Vai esat pārliecināts?',\n    autosave: 'Automātiskā saglabāšana',\n    autosavedSuccessfully: 'Veiksmīgi automātiski saglabāts.',\n    autosavedVersion: 'Automātiski saglabāta versija',\n    changed: 'Mainīts',\n    changedFieldsCount_one: '{{count}} mainīts lauks',\n    changedFieldsCount_other: '{{count}} mainīti lauki',\n    compareVersion: 'Salīdzināt versiju ar:',\n    compareVersions: 'Salīdzināt versijas',\n    comparingAgainst: 'Salīdzinot ar',\n    confirmPublish: 'Apstiprināt publicēšanu',\n    confirmRevertToSaved: 'Apstiprināt atgriešanu uz saglabāto',\n    confirmUnpublish: 'Apstiprināt publicēšanas atcelšanu',\n    confirmVersionRestoration: 'Apstiprināt versijas atjaunošanu',\n    currentDocumentStatus: 'Pašreizējais {{docStatus}} dokuments',\n    currentDraft: 'Pašreizējais melnraksts',\n    currentlyPublished: 'Pašlaik publicēts',\n    currentlyViewing: 'Pašlaik skatās',\n    currentPublishedVersion: 'Pašreizējā publicētā versija',\n    draft: 'Melnraksts',\n    draftSavedSuccessfully: 'Melnraksts veiksmīgi saglabāts.',\n    lastSavedAgo: 'Pēdējo reizi saglabāts pirms {{distance}}',\n    modifiedOnly: 'Tikai modificētie',\n    moreVersions: 'Vairāk versijas...',\n    noFurtherVersionsFound: 'Papildu versijas nav atrastas',\n    noRowsFound: 'Nav atrasts neviens {{label}}',\n    noRowsSelected: 'Nav atlasīts neviens {{label}}',\n    preview: 'Priekšskatījums',\n    previouslyDraft: 'Iepriekšējais melnraksts',\n    previouslyPublished: 'Iepriekš publicēts',\n    previousVersion: 'Iepriekšējā versija',\n    problemRestoringVersion: 'Radās problēma, atjaunojot šo versiju',\n    publish: 'Publicēt',\n    publishAllLocales: 'Publicēt visas lokalizācijas',\n    publishChanges: 'Publicēt izmaiņas',\n    published: 'Publicēts',\n    publishIn: 'Publicēt {{locale}}',\n    publishing: 'Publicē...',\n    restoreAsDraft: 'Atjaunot kā melnrakstu',\n    restoredSuccessfully: 'Veiksmīgi atjaunots.',\n    restoreThisVersion: 'Atjaunot šo versiju',\n    restoring: 'Atjauno...',\n    reverting: 'Atgriež...',\n    revertToPublished: 'Atgriezt uz publicēto',\n    saveDraft: 'Saglabāt melnrakstu',\n    scheduledSuccessfully: 'Veiksmīgi ieplānots.',\n    schedulePublish: 'Ieplānot publicēšanu',\n    selectLocales: 'Izvēlēties lokalizācijas, ko rādīt',\n    selectVersionToCompare: 'Izvēlēties versiju salīdzināšanai',\n    showingVersionsFor: 'Rāda versijas priekš:',\n    showLocales: 'Rādīt lokalizācijas:',\n    specificVersion: 'Konkrētā versija',\n    status: 'Statuss',\n    unpublish: 'Atcelt publicēšanu',\n    unpublishing: 'Atceļ publicēšanu...',\n    version: 'Versija',\n    versionAgo: '{{distance}} pirms',\n    versionCount_many: 'Atrastas {{count}} versijas',\n    versionCount_none: 'Nav atrastu versiju',\n    versionCount_one: 'Atrasta {{count}} versija',\n    versionCount_other: 'Atrastas {{count}} versijas',\n    versionCreatedOn: '{{version}} izveidota:',\n    versionID: 'Versijas ID',\n    versions: 'Versijas',\n    viewingVersion: 'Skatās versiju priekš {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Skatās versiju globālajam {{entityLabel}}',\n    viewingVersions: 'Skatās versijas priekš {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Skatās versijas globālajam {{entityLabel}}',\n  },\n}\n\nexport const lv: Language = {\n  dateFNSKey: 'lv',\n  translations: lvTranslations,\n}\n"], "names": ["lvTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "lv", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OACE;QACFC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}