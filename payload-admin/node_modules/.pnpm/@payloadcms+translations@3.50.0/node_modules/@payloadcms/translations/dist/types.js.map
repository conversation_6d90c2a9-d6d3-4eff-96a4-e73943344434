{"version": 3, "sources": ["../src/types.ts"], "sourcesContent": ["import type { Locale } from 'date-fns'\n\nimport type { clientTranslationKeys } from './clientKeys.js'\nimport type { enTranslations } from './languages/en.js'\nimport type { acceptedLanguages } from './utilities/languages.js'\n\ntype DateFNSKeys =\n  | 'ar'\n  | 'az'\n  | 'bg'\n  | 'bn-BD'\n  | 'bn-IN'\n  | 'ca'\n  | 'cs'\n  | 'da'\n  | 'de'\n  | 'en-US'\n  | 'es'\n  | 'et'\n  | 'fa-IR'\n  | 'fr'\n  | 'he'\n  | 'hr'\n  | 'hu'\n  | 'hy-AM'\n  | 'id'\n  | 'it'\n  | 'ja'\n  | 'ko'\n  | 'lt'\n  | 'lv'\n  | 'nb'\n  | 'nl'\n  | 'pl'\n  | 'pt'\n  | 'ro'\n  | 'rs'\n  | 'rs-Latin'\n  | 'ru'\n  | 'sk'\n  | 'sl-SI'\n  | 'sv'\n  | 'th'\n  | 'tr'\n  | 'uk'\n  | 'vi'\n  | 'zh-CN'\n  | 'zh-TW'\n\nexport type Language<TDefaultTranslations = DefaultTranslationsObject> = {\n  dateFNSKey: DateFNSKeys\n  translations: TDefaultTranslations\n}\n\nexport type GenericTranslationsObject = {\n  [key: string]: GenericTranslationsObject | string\n}\n\nexport type GenericLanguages = {\n  [key in AcceptedLanguages]?: GenericTranslationsObject\n}\n\nexport type AcceptedLanguages = (typeof acceptedLanguages)[number]\n\nexport type SupportedLanguages<TDefaultTranslations = DefaultTranslationsObject> = {\n  [key in AcceptedLanguages]?: Language<TDefaultTranslations>\n}\n\n/**\n * Type utilities for converting between translation objects ( e.g. general: { createNew: 'Create New' } )  and translations keys ( e.g. general:createNew )\n */\n\nexport type NestedKeysUnSanitized<T> = T extends object\n  ? {\n      [K in keyof T]-?: K extends string\n        ? T[K] extends object\n          ? `${K}:${NestedKeysUnSanitized<T[K]>}` | null\n          : `${K}`\n        : never\n    }[keyof T]\n  : ''\n\n// Utility type to strip specific suffixes\nexport type StripCountVariants<TKey> = TKey extends\n  | `${infer Base}_many`\n  | `${infer Base}_one`\n  | `${infer Base}_other`\n  ? Base\n  : TKey\n\nexport type NestedKeysStripped<T> = T extends object\n  ? {\n      [K in keyof T]-?: K extends string\n        ? T[K] extends object\n          ? `${K}:${NestedKeysStripped<T[K]>}`\n          : `${StripCountVariants<K>}`\n        : never\n    }[keyof T]\n  : ''\n\nexport type ReconstructObjectFromTranslationKeys<\n  TPath extends string,\n  TValue = string,\n> = TPath extends `${infer First}:${infer Rest}`\n  ? { [K in First]: ReconstructObjectFromTranslationKeys<Rest, TValue> }\n  : { [K in TPath]: TValue }\n\n/**\n * Default nested translations object\n */\nexport type DefaultTranslationsObject = typeof enTranslations\n\n/**\n * All translation keys unSanitized. E.g. 'general:aboutToDeleteCount_many'\n */\nexport type DefaultTranslationKeysUnSanitized = NestedKeysUnSanitized<DefaultTranslationsObject>\n\n/**\n * All translation keys sanitized. E.g. 'general:aboutToDeleteCount'\n */\nexport type DefaultTranslationKeys = NestedKeysStripped<DefaultTranslationsObject>\n\nexport type ClientTranslationKeys<TExtraProps = (typeof clientTranslationKeys)[number]> =\n  TExtraProps\n\n// Use GenericTranslationsObject instead of reconstructing the object from the client keys. This is because reconstructing the object is\n// A) Expensive on performance.\n// B) Not important to be typed specifically for the client translations. We really only care about the client translation keys to be typed.\n// C) Inaccurate. Client keys which previously had _many, _one or other suffixes have been removed and cannot be reconstructed\nexport type ClientTranslationsObject = GenericTranslationsObject //ReconstructObjectFromTranslationKeys<ClientTranslationKeys>\n\nexport type TFunction<TTranslationKeys = DefaultTranslationKeys> = (\n  key: TTranslationKeys,\n  options?: Record<string, any>,\n) => string\n\nexport type I18n<\n  TTranslations = DefaultTranslationsObject,\n  TTranslationKeys = DefaultTranslationKeys,\n> = {\n  dateFNS: Locale\n  /** Corresponding dateFNS key */\n  dateFNSKey: DateFNSKeys\n  /** The fallback language */\n  fallbackLanguage: string\n  /** The language of the request */\n  language: string\n  /** Translate function */\n  t: TFunction<TTranslationKeys>\n  translations: Language<TTranslations>['translations']\n}\n\nexport type I18nOptions<TTranslations = DefaultTranslationsObject> = {\n  fallbackLanguage?: AcceptedLanguages\n  supportedLanguages?: SupportedLanguages\n  translations?: Partial<{\n    [key in AcceptedLanguages]?: Language<TTranslations>['translations']\n  }>\n}\n\nexport type InitTFunction<\n  TTranslations = DefaultTranslationsObject,\n  TTranslationKeys = DefaultTranslationKeys,\n> = (args: {\n  config: I18nOptions<TTranslations>\n  language?: string\n  translations: Language<TTranslations>['translations']\n}) => {\n  t: TFunction<TTranslationKeys>\n  translations: Language<TTranslations>['translations']\n}\n\nexport type InitI18n =\n  | ((args: { config: I18nOptions; context: 'api'; language: AcceptedLanguages }) => Promise<I18n>)\n  | ((args: {\n      config: I18nOptions<ClientTranslationsObject>\n      context: 'client'\n      language: AcceptedLanguages\n    }) => Promise<I18n<ClientTranslationsObject, ClientTranslationKeys>>)\n\nexport type LanguagePreference = {\n  language: AcceptedLanguages\n  quality?: number\n}\n\nexport type I18nClient<TAdditionalTranslations = {}, TAdditionalKeys extends string = never> = I18n<\n  TAdditionalTranslations extends object\n    ? ClientTranslationsObject & TAdditionalTranslations\n    : ClientTranslationsObject,\n  [TAdditionalKeys] extends [never]\n    ? ClientTranslationKeys\n    : ClientTranslationKeys | TAdditionalKeys\n>\nexport type I18nServer<TAdditionalTranslations = {}, TAdditionalKeys extends string = never> = I18n<\n  TAdditionalTranslations extends object\n    ? DefaultTranslationsObject & TAdditionalTranslations\n    : DefaultTranslationsObject,\n  [TAdditionalKeys] extends [never]\n    ? DefaultTranslationKeys\n    : DefaultTranslationKeys | TAdditionalKeys\n>\n"], "names": [], "mappings": "AAiMA,WAOC"}