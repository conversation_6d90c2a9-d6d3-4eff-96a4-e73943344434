{"version": 3, "sources": ["../../src/languages/uk.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const ukTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Обліковий запис',\n    accountOfCurrentUser: 'Обліковий запис поточного користувача',\n    accountVerified: 'Рахунок успішно перевірено.',\n    alreadyActivated: 'Вже активований',\n    alreadyLoggedIn: 'Вже увійшли в систему',\n    apiKey: 'API ключ',\n    authenticated: 'Автентифікований',\n    backToLogin: 'Повернутися до входу',\n    beginCreateFirstUser: 'Щоб розпочати — створість першого користувача',\n    changePassword: 'Змінити пароль',\n    checkYourEmailForPasswordReset:\n      'Якщо адреса електронної пошти пов\\'язана з обліковим записом, незабаром ви отримаєте інструкції щодо скидання пароля. Будь ласка, перевірте папку \"Спам\" або \"Небажана пошта\", якщо ви не бачите цього електронного листа у своїй вхідній пошті.',\n    confirmGeneration: 'Підтвердити генерацію',\n    confirmPassword: 'Підтвердження паролю',\n    createFirstUser: 'Створення першого користувача',\n    emailNotValid: 'Вказана адреса електронної пошти недійсна',\n    emailOrUsername: \"Електронна пошта або Ім'я користувача\",\n    emailSent: 'Лист відправлено',\n    emailVerified: 'Електронну пошту успішно підтверджено.',\n    enableAPIKey: 'Активувати API ключ',\n    failedToUnlock: 'Не вдалось розблокувати',\n    forceUnlock: 'Примусове розблокування',\n    forgotPassword: 'Забули пароль?',\n    forgotPasswordEmailInstructions:\n      'Будь ласка, вкажіть адресу вашої електронної пошти нижче. Ви отримаєте лист на вашу електронну пошту з інструкціями щодо скидання пароля.',\n    forgotPasswordQuestion: 'Забули пароль?',\n    forgotPasswordUsernameInstructions:\n      \"Будь ласка, введіть нижче своє ім'я користувача. Інструкції щодо скидання пароля буде відправлено на адресу електронної пошти, пов'язану з вашим ім'ям користувача.\",\n    generate: 'Згенерувати',\n    generateNewAPIKey: 'Згенерувати новий API ключ',\n    generatingNewAPIKeyWillInvalidate:\n      'Генерація нового API ключа зробить попередній <1>недійсним</1>. Ви впевнені, що бажаєте продовжити?',\n    lockUntil: 'Заблокувати до',\n    logBackIn: 'Увійти знову',\n    loggedIn: 'Щоб увйти в систему з іншого облікового запису, спочатку <0>вийдіть з системи</0>.',\n    loggedInChangePassword:\n      'Щоб змінити ваш пароль, перейдіть до <0>сторінки облікового запису</0> і змініть ваш пароль.',\n    loggedOutInactivity: 'Ви вийшли з системи через бездіяльність.',\n    loggedOutSuccessfully: 'Ви успішно вийшли з системи.',\n    loggingOut: 'Вихід...',\n    login: 'Увійти',\n    loginAttempts: 'Спроби входу',\n    loginUser: 'Вхід користувача в систему',\n    loginWithAnotherUser:\n      'Щоб увйти в систему з іншого облікового запису, спочатку <0>вийдіть з системи</0>.',\n    logOut: 'Вийти',\n    logout: 'Вийти',\n    logoutSuccessful: 'Вихід успішний.',\n    logoutUser: 'Вийти з системи',\n    newAccountCreated:\n      'Новий обліковий запис було створено, щоб отримати доступ до <a href=\"{{serverURL}}\">{{serverURL}}</a>, будь ласка, натисніть на наступне посилання, або вставте його в адресний рядок браузера, щоб підтвердити вашу електронну пошту: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Після підтвердження вашої електронно пошти, ви зможете увійти в систему.',\n    newAPIKeyGenerated: 'Новий API ключ згенеровано.',\n    newPassword: 'Новий пароль',\n    passed: 'Аутентифікація пройшла успішно',\n    passwordResetSuccessfully: 'Пароль успішно скинуто.',\n    resetPassword: 'Скинути пароль',\n    resetPasswordExpiration: 'Скинути пароль після закінчення строку дії',\n    resetPasswordToken: 'Токет для скидання пароля',\n    resetYourPassword: 'Скинути ваш пароль',\n    stayLoggedIn: 'Залишитись в системі',\n    successfullyRegisteredFirstUser: 'Успішно зареєстровано першого користувача.',\n    successfullyUnlocked: 'Успішно розблоковано',\n    tokenRefreshSuccessful: 'Оновлення токену успішне.',\n    unableToVerify: 'Неможливо підтвердити',\n    username: \"Ім'я користувача\",\n    usernameNotValid: \"Вказане ім'я користувача недійсне\",\n    verified: 'Підтверджено',\n    verifiedSuccessfully: 'Успішно підтверджено',\n    verify: 'Підтвердити',\n    verifyUser: 'Підтвердити користувача',\n    verifyYourEmail: 'Підтвердити пошту',\n    youAreInactive:\n      'Ви були неактивні певний час і скоро, в цілях вашої безпеки, вас буде розлогінено. Чи бажаєте ви залишитись в системі?',\n    youAreReceivingResetPassword:\n      'Ви отримали це повідомлення, бо ви (або хтось інший) створив запит на скидання пароля до вашого облікового запису. Будь ласка, натисніть на наступне посилання, або вставте посилання в адресний рядок браузера, щоб завершити процес:',\n    youDidNotRequestPassword:\n      'Якщо ви не сторювали цей запит, будь ласка, проігноруйте це повідомлення',\n  },\n  error: {\n    accountAlreadyActivated: 'Цей обліковий запис вже активований',\n    autosaving: 'Виникла проблема під час автозбереження цього документа.',\n    correctInvalidFields: 'Будь ласка, виправте невірні поля.',\n    deletingFile: 'Виникла помилка під час видалення файлу',\n    deletingTitle:\n      \"Виникла помилка під час видалення {{title}}. Будь ласка, перевірте ваше з'єднання та спробуйте ще раз.\",\n    documentNotFound:\n      'Документ з ID {{id}} не вдалося знайти. Можливо, він був видалений або ніколи не існував, або у вас немає доступу до нього.',\n    emailOrPasswordIncorrect: 'Вказана адреса електронної пошти або пароль є невірними',\n    followingFieldsInvalid_one: 'Наступне поле невірне:',\n    followingFieldsInvalid_other: 'Наступні поля невірні',\n    incorrectCollection: 'Неправильна колекція',\n    insufficientClipboardPermissions:\n      'Доступ до буфера обміну відхилено. Перевірте свої дозволи на буфер обміну.',\n    invalidClipboardData: 'Невірні дані в буфері обміну.',\n    invalidFileType: 'Невірний тип файлу',\n    invalidFileTypeValue: 'Невірний тип файлу: {{value}}',\n    invalidRequestArgs: 'Неправильні аргументи передано в запиті: {{args}}',\n    loadingDocument: 'Виникла помилка під час завантаження документа з ID {{id}}.',\n    localesNotSaved_one: 'Не вдалося зберегти наступну мову:',\n    localesNotSaved_other: 'Не вдалося зберегти такі мови:',\n    logoutFailed: 'Вихід не вдався.',\n    missingEmail: 'Відсутній email.',\n    missingIDOfDocument: 'Відсутній ID документа для оновлення.',\n    missingIDOfVersion: 'Відсутній ID версії.',\n    missingRequiredData: \"Відсусті обов'язкові дані.\",\n    noFilesUploaded: 'Жодного файлу не було завантажено.',\n    noMatchedField: 'Не знайдено відповідного поля для \"{{label}}\"',\n    notAllowedToAccessPage: 'Ви не маєте доступу до цієї сторінки.',\n    notAllowedToPerformAction: 'Ви не маєте дозволу виконувати цю дію.',\n    notFound: 'Запитуваний ресурс не знайдено.',\n    noUser: 'Немає користувача',\n    previewing: 'Виникла помилка під час попереднього перегляду цього документа.',\n    problemUploadingFile: 'Виникла помилка під час завантаження файлу.',\n    restoringTitle:\n      \"Виникла помилка при відновленні {{title}}. Будь ласка, перевірте своє з'єднання і спробуйте ще раз.\",\n    tokenInvalidOrExpired: 'Токен недійсний, або його строк дії закінчився.',\n    tokenNotProvided: 'Токен не надано.',\n    unableToCopy: 'Неможливо скопіювати.',\n    unableToDeleteCount: 'Не вдалося видалити {{count}} із {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Помилка при повторному індексуванні колекції {{collection}}. Операцію скасовано.',\n    unableToUpdateCount: 'Не вдалося оновити {{count}} із {{total}} {{label}}.',\n    unauthorized: 'Немає доступу, ви повинні увійти, щоб виконати цей запит.',\n    unauthorizedAdmin: 'Немає доступу, цей користувач не має доступу до панелі адміністратора.',\n    unknown: 'Виникла невідома помилка.',\n    unPublishingDocument: 'Під час скасування публікації даного документа виникла помилка.',\n    unspecific: 'Виникла помилка.',\n    unverifiedEmail: 'Будь ласка, підтвердьте свою електронну пошту перед входом.',\n    userEmailAlreadyRegistered: 'Користувач із вказаною електронною поштою вже зареєстрований.',\n    userLocked: 'Цей користувач заблокований через велику кількість невдалих спроб входу.',\n    usernameAlreadyRegistered: 'Користувач з вказаним іменем користувача вже зареєстрований.',\n    usernameOrPasswordIncorrect: \"Введене ім'я користувача або пароль неправильні.\",\n    valueMustBeUnique: 'Значення має бути унікальним.',\n    verificationTokenInvalid: 'Токен верифікації недійсний.',\n  },\n  fields: {\n    addLabel: 'Додати {{label}}',\n    addLink: 'Додати посилання',\n    addNew: 'Додати новий',\n    addNewLabel: 'Створити {{label}}',\n    addRelationship: \"Додати взаємозв'язок\",\n    addUpload: 'Додати завантаження',\n    block: 'блок',\n    blocks: 'блоки',\n    blockType: 'Тип блока',\n    chooseBetweenCustomTextOrDocument:\n      'Виберіть між введенням власної URL-адреси та посиланням на інший документ.',\n    chooseDocumentToLink: 'Оберіть документ, на який потрібно зробити посилання',\n    chooseFromExisting: 'Обрати з існуючих',\n    chooseLabel: 'Обрати {{label}}',\n    collapseAll: 'Згорнути все',\n    customURL: 'Власний URL',\n    editLabelData: 'Редагувати дані {{label}}',\n    editLink: 'Редагувати посилання',\n    editRelationship: \"Редагувати взаємозв'язок\",\n    enterURL: 'Введіть URL',\n    internalLink: 'Внутрішнє посилання',\n    itemsAndMore: '{{items}} і ще {{count}}',\n    labelRelationship: \"{{label}} взаємов'язок\",\n    latitude: 'Широта',\n    linkedTo: \"Зв'язано з <0>{{label}}</0>\",\n    linkType: 'Тип посилання',\n    longitude: 'Довгота',\n    newLabel: 'Новий {{label}}',\n    openInNewTab: 'Відкривати в новій вкладці',\n    passwordsDoNotMatch: 'Паролі не співпадають.',\n    relatedDocument: \"Пов'язаний документ\",\n    relationTo: \"Пов'язано з\",\n    removeRelationship: \"Видалити взаємозв'язок\",\n    removeUpload: 'Видалити завантаження',\n    saveChanges: 'Зберегти зміни',\n    searchForBlock: 'Знайти блок',\n    selectExistingLabel: 'Вибрати існуючий {{label}}',\n    selectFieldsToEdit: 'Виберіть поля для редагування',\n    showAll: 'Показати все',\n    swapRelationship: \"Замінити зв'язок\",\n    swapUpload: 'Замінити завантаження',\n    textToDisplay: 'Текст для відображення',\n    toggleBlock: 'Перемкнути блок',\n    uploadNewLabel: 'Завантажити новий {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Переглянути за папкою',\n    byFolder: 'За папкою',\n    deleteFolder: 'Видалити папку',\n    folderName: 'Назва папки',\n    folders: 'Папки',\n    folderTypeDescription:\n      'Виберіть, який тип документів колекції повинен бути дозволений у цій папці.',\n    itemHasBeenMoved: '{{title}} було переміщено до {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} був переміщений до кореневої папки',\n    itemsMovedToFolder: '{{title}} перенесено до {{folderName}}',\n    itemsMovedToRoot: '{{title}} переміщено до кореневої папки',\n    moveFolder: 'Перемістити папку',\n    moveItemsToFolderConfirmation:\n      'Ви збираєтесь перемістити <1>{{count}} {{label}}</1> до <2>{{toFolder}}</2>. Ви впевнені?',\n    moveItemsToRootConfirmation:\n      'Ви збираєтеся перемістити <1>{{count}} {{label}}</1> до кореневої папки. Ви впевнені?',\n    moveItemToFolderConfirmation:\n      'Ви збираєтеся перемістити <1>{{title}}</1> до <2>{{toFolder}}</2>. Ви впевнені?',\n    moveItemToRootConfirmation:\n      'Ви збираєтеся перемістити <1>{{title}}</1> до кореневої папки. Ви впевнені?',\n    movingFromFolder: 'Переміщення {{title}} з {{fromFolder}}',\n    newFolder: 'Нова папка',\n    noFolder: 'Немає папки',\n    renameFolder: 'Перейменувати папку',\n    searchByNameInFolder: 'Пошук за назвою у {{folderName}}',\n    selectFolderForItem: 'Виберіть папку для {{title}}',\n  },\n  general: {\n    name: \"Ім'я\",\n    aboutToDelete: 'Ви бажаєте видалити {{label}} <1>{{title}}</1>. Ви впевнені?',\n    aboutToDeleteCount_many: 'Ви бажаєте видалити {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Ви бажаєте видалити {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Ви бажаєте видалити {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Ви збираєтесь остаточно видалити {{label}} <1>{{title}}</1>. Ви впевнені?',\n    aboutToPermanentlyDeleteTrash:\n      'Ви збираєтеся назавжди видалити <0>{{count}}</0> <1>{{label}}</1> з кошика. Ви впевнені?',\n    aboutToRestore: 'Ви збираєтеся відновити {{label}} <1>{{title}}</1>. Ви впевнені?',\n    aboutToRestoreAsDraft:\n      'Ви збираєтеся відновити {{label}} <1>{{title}}</1> як чернетку. Ви впевнені?',\n    aboutToRestoreAsDraftCount: 'Ви збираєтеся відновити {{count}} {{label}} як чернетку',\n    aboutToRestoreCount: 'Ви збираєтеся відновити {{count}} {{label}}',\n    aboutToTrash: 'Ви збираєтеся перемістити {{label}} <1>{{title}}</1> у смітник. Ви впевнені?',\n    aboutToTrashCount: 'Ви збираєтеся перемістити {{count}} {{label}} до кошика',\n    addBelow: 'Додати нижче',\n    addFilter: 'Додати фільтр',\n    adminTheme: 'Тема адмін панелі',\n    all: 'Все',\n    allCollections: 'Усі Колекції',\n    allLocales: 'Всі локалі',\n    and: 'і',\n    anotherUser: 'Інший користувач',\n    anotherUserTakenOver: 'Інший користувач взяв на себе редагування цього документа.',\n    applyChanges: 'Застосувати зміни',\n    ascending: 'В порядку зростання',\n    automatic: 'Автоматично',\n    backToDashboard: 'Повернутись до головної сторінки',\n    cancel: 'Скасувати',\n    changesNotSaved: 'Ваши зміни не були збережені. Якщо ви вийдете зараз, то втратите свої зміни.',\n    clear: 'Чітко',\n    clearAll: 'Очистити все',\n    close: 'Закрити',\n    collapse: 'Згорнути',\n    collections: 'Колекції',\n    columns: 'Колонки',\n    columnToSort: 'Колонка для сортування',\n    confirm: 'Підтвердити',\n    confirmCopy: 'Підтвердіть копію',\n    confirmDeletion: 'Підтвердити видалення',\n    confirmDuplication: 'Підтвердити копіювання',\n    confirmMove: 'Підтвердити переїзд',\n    confirmReindex: 'Перебудувати індекс для всіх {{collections}}?',\n    confirmReindexAll: 'Перебудувати індекс для всіх колекцій?',\n    confirmReindexDescription:\n      'Це видалить наявні індекси та перебудує індекси документів у колекціях {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Це видалить наявні індекси та перебудує індекси документів у всіх колекціях.',\n    confirmRestoration: 'Підтвердіть відновлення',\n    copied: 'Скопійовано',\n    copy: 'Скопіювати',\n    copyField: 'Копіювати поле',\n    copying: 'Копіювання',\n    copyRow: 'Копіювати рядок',\n    copyWarning: 'Ви збираєтесь замінити {{to}} на {{from}} для {{label}} {{title}}. Ви впевнені?',\n    create: 'Створити',\n    created: 'Створено',\n    createdAt: 'Дата створення',\n    createNew: 'Створити',\n    createNewLabel: 'Створити новий {{label}}',\n    creating: 'Створення',\n    creatingNewLabel: 'Створення нового {{label}}',\n    currentlyEditing:\n      'зараз редагує цей документ. Якщо ви переберете контроль, їм буде заблоковано продовження редагування, і вони також можуть втратити незбережені зміни.',\n    custom: 'Спеціальне замовлення',\n    dark: 'Темна',\n    dashboard: 'Головна',\n    delete: 'Видалити',\n    deleted: 'Видалено',\n    deletedAt: 'Видалено в',\n    deletedCountSuccessfully: 'Успішно видалено {{count}} {{label}}.',\n    deletedSuccessfully: 'Успішно видалено.',\n    deletePermanently: 'Пропустити кошик та видалити назавжди',\n    deleting: 'Видалення...',\n    depth: 'Глибина',\n    descending: 'В порядку спадання',\n    deselectAllRows: 'Скасувати вибір всіх рядків',\n    document: 'Документ',\n    documentIsTrashed: 'Цей {{label}} видалено та доступний лише для читання.',\n    documentLocked: 'Документ заблоковано',\n    documents: 'Документи',\n    duplicate: 'Дублювати',\n    duplicateWithoutSaving: 'Дублювання без збереження змін',\n    edit: 'Редагувати',\n    editAll: 'Редагувати все',\n    editedSince: 'Відредаговано з',\n    editing: 'Редагування',\n    editingLabel_many: 'Редагування {{count}} {{label}}',\n    editingLabel_one: 'Редагування {{count}} {{label}}',\n    editingLabel_other: 'Редагування {{count}} {{label}}',\n    editingTakenOver: 'Редагування взято на себе',\n    editLabel: 'Редагувати {{label}}',\n    email: 'Електронна пошта',\n    emailAddress: 'Адреса електронної пошти',\n    emptyTrash: 'Спорожнити кошик',\n    emptyTrashLabel: 'Спорожнити кошик для {{label}}',\n    enterAValue: 'Введіть значення',\n    error: 'Помилка',\n    errors: 'Помилки',\n    exitLivePreview: 'Вийти з режиму \"Наживо\"',\n    export: 'Експорт',\n    fallbackToDefaultLocale: 'Відновлення локалі за замовчуванням',\n    false: 'Неправда',\n    filter: 'Фільтрувати',\n    filters: 'Фільтри',\n    filterWhere: 'Де фільтрувати {{label}}',\n    globals: 'Глобальні',\n    goBack: 'Повернутися',\n    groupByLabel: 'Групувати за {{label}}',\n    import: 'Імпорт',\n    isEditing: 'редагує',\n    item: 'предмет',\n    items: 'предмети',\n    language: 'Мова',\n    lastModified: 'Останні зміни',\n    leaveAnyway: 'Все одно вийти',\n    leaveWithoutSaving: 'Вийти без збереження',\n    light: 'Світла',\n    livePreview: 'Попередній перегляд',\n    loading: 'Завантаження',\n    locale: 'Локаль',\n    locales: 'Локалі',\n    menu: 'Меню',\n    moreOptions: 'Більше варіантів',\n    move: 'Рухайтесь',\n    moveConfirm:\n      'Ви збираєтесь перемістити {{count}} {{label}} до <1>{{destination}}</1>. Ви впевнені?',\n    moveCount: 'Перемістити {{count}} {{label}}',\n    moveDown: 'Перемістити нижче',\n    moveUp: 'Перемістити вище',\n    moving: 'Переїзд',\n    movingCount: 'Переміщення {{count}} {{label}}',\n    newPassword: 'Новий пароль',\n    next: 'Наступний',\n    no: 'Ні',\n    noDateSelected: 'Не вибрано жодної дати',\n    noFiltersSet: 'Відсусті фільтри',\n    noLabel: '<без {{label}}>',\n    none: 'Ніхто',\n    noOptions: 'Немає варіантів',\n    noResults:\n      'Жодного {{label}} не знайдено. Або {{label}} ще не існує, або жодна з них не відповідає фільтрам, що ви задали више.',\n    notFound: 'Не знайдено',\n    nothingFound: 'Нічого не знайдено',\n    noTrashResults: 'Немає {{label}} у смітнику.',\n    noUpcomingEventsScheduled: 'Не заплановано жодних майбутніх подій.',\n    noValue: 'Немає значення',\n    of: 'з',\n    only: 'Лише',\n    open: 'Відкрити',\n    or: 'або',\n    order: 'Порядок',\n    overwriteExistingData: 'Перезаписати існуючі дані поля',\n    pageNotFound: 'Сторінка не знайдена',\n    password: 'Пароль',\n    pasteField: 'Вставити поле',\n    pasteRow: 'Вставити рядок',\n    payloadSettings: 'Налаштування Payload',\n    permanentlyDelete: 'Назавжди видалити',\n    permanentlyDeletedCountSuccessfully: 'Успішно видалено назавжди {{count}} {{label}}.',\n    perPage: 'На сторінці: {{limit}}',\n    previous: 'Попередній',\n    reindex: 'Повторне індексування',\n    reindexingAll: 'Перебудова індексів для всіх {{collections}}.',\n    remove: 'Видалити',\n    rename: 'Перейменувати',\n    reset: 'Скидання',\n    resetPreferences: 'Скинути налаштування',\n    resetPreferencesDescription: 'Це скине всі ваші налаштування до значень за замовчуванням.',\n    resettingPreferences: 'Скидання налаштувань.',\n    restore: 'Відновити',\n    restoreAsPublished: 'Відновити як опубліковану версію',\n    restoredCountSuccessfully: 'Відновлено {{count}} {{label}} успішно.',\n    restoring:\n      'Поважайте сенс оригінального тексту в контексті Payload. Ось список поширених термінів Payload, які мають дуже специфічні значення:\\n    - Колекція: Колекцією є група документів, які мають спільну структуру та сенс. Колекції використовуються для організації й керування контент',\n    row: 'Рядок',\n    rows: 'Рядки',\n    save: 'Зберегти',\n    saving: 'Збереження...',\n    schedulePublishFor: 'Запланувати публікацію для {{title}}',\n    searchBy: 'Шукати по {{label}}',\n    select: 'Вибрати',\n    selectAll: 'Вибрати всі {{count}} {{label}}',\n    selectAllRows: 'Обрати всі рядки',\n    selectedCount: 'Обрано {{count}} {{label}}',\n    selectLabel: 'Виберіть {{label}}',\n    selectValue: 'Обрати значення',\n    showAllLabel: 'Показати всі {{label}}',\n    sorryNotFound: 'Вибачте, немає нічого, що відповідало б Вашому запиту.',\n    sort: 'Сортувати',\n    sortByLabelDirection: 'Сортувати за {{label}} {{direction}}',\n    stayOnThisPage: 'Залишитись на цій сторінці',\n    submissionSuccessful: 'Успішно відправлено.',\n    submit: 'Відправити',\n    submitting: 'Надсилаємо...',\n    success: 'Успіх',\n    successfullyCreated: '{{label}} успішно створено.',\n    successfullyDuplicated: '{{label}} успішно продубльовано.',\n    successfullyReindexed:\n      'Успішно повторно індексовано {{count}} з {{total}} документів з колекцій {{collections}}.',\n    takeOver: 'Перейняти',\n    thisLanguage: 'Українська',\n    time: 'Час',\n    timezone: 'Часовий пояс',\n    titleDeleted: '{{label}} \"{{title}}\" успішно видалено.',\n    titleRestored: '{{label}} \"{{title}}\" успішно відновлено.',\n    titleTrashed: '{{label}} \"{{title}}\" переміщено до кошика.',\n    trash: 'Сміття',\n    trashedCountSuccessfully: '{{count}} {{label}} перенесено в кошик.',\n    true: 'Правда',\n    unauthorized: 'Немає доступу',\n    unsavedChanges: 'У вас є незбережені зміни. Збережіть або скасуйте перед продовженням.',\n    unsavedChangesDuplicate: 'Ви маєте незбережені зміни. Чи бажаєте ви продовжити дублювання?',\n    untitled: 'Без назви',\n    upcomingEvents: 'Майбутні події',\n    updatedAt: 'Змінено',\n    updatedCountSuccessfully: 'Успішно оновлено {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Успішно оновлено {{label}}.',\n    updatedSuccessfully: 'Успішно відредаговано.',\n    updateForEveryone: 'Оновлення для всіх',\n    updating: 'оновлення',\n    uploading: 'завантаження',\n    uploadingBulk: 'Завантаження {{current}} з {{total}}',\n    user: 'Користувач',\n    username: \"Ім'я користувача\",\n    users: 'Користувачі',\n    value: 'Значення',\n    viewing: 'Перегляд',\n    viewReadOnly: 'Перегляд тільки для читання',\n    welcome: 'Вітаю',\n    yes: 'Так',\n  },\n  localization: {\n    cannotCopySameLocale: 'Не можна копіювати в ту ж саму локалізацію',\n    copyFrom: 'Копіювати з',\n    copyFromTo: 'Копіювання з {{from}} до {{to}}',\n    copyTo: 'Копіювати в',\n    copyToLocale: 'Копіювати до локалі',\n    localeToPublish: 'Місце публікації',\n    selectLocaleToCopy: 'Виберіть локалізацію для копіювання',\n  },\n  operators: {\n    contains: 'містить',\n    equals: 'дорівнює',\n    exists: 'існує',\n    intersects: 'перетинається',\n    isGreaterThan: 'більше ніж',\n    isGreaterThanOrEqualTo: 'більше або дорівнює',\n    isIn: 'є в',\n    isLessThan: 'менше ніж',\n    isLessThanOrEqualTo: 'менше або дорівнює',\n    isLike: 'схоже',\n    isNotEqualTo: 'не дорівнює',\n    isNotIn: 'не в',\n    isNotLike: 'не такий як',\n    near: 'поруч',\n    within: 'в межах',\n  },\n  upload: {\n    addFile: 'Додати файл',\n    addFiles: 'Додати файли',\n    bulkUpload: 'Масове завантаження',\n    crop: 'Обрізати',\n    cropToolDescription:\n      'Перетягніть кути обраної області, намалюйте нову область або скоригуйте значення нижче.',\n    download: 'Завантажити',\n    dragAndDrop: 'Перемістіть файл',\n    dragAndDropHere: 'або перемістіть сюди файл',\n    editImage: 'Редагувати зображення',\n    fileName: 'Назва файлу',\n    fileSize: 'Розмір файлу',\n    filesToUpload: 'Файли для завантаження',\n    fileToUpload: 'Файл для завантаження',\n    focalPoint: 'Точка фокусу',\n    focalPointDescription:\n      'Перетягніть точку фокусу безпосередньо на попередньому перегляді або налаштуйте значення нижче.',\n    height: 'Висота',\n    lessInfo: 'Менше інформації',\n    moreInfo: 'Більше інформації',\n    noFile: 'Немає файлу',\n    pasteURL: 'Вставити URL',\n    previewSizes: 'Попередній перегляд розмірів',\n    selectCollectionToBrowse: 'Оберіть колекцію для перегляду',\n    selectFile: 'Оберіть файл',\n    setCropArea: 'Встановити область обрізки',\n    setFocalPoint: 'Встановити точку фокусу',\n    sizes: 'Розміри',\n    sizesFor: 'Розміри для {{label}}',\n    width: 'Ширина',\n  },\n  validation: {\n    emailAddress: 'Будь ласка, введіть валідну адресу електронної пошти.',\n    enterNumber: 'Будь ласка, введіть валідне число.',\n    fieldHasNo: 'В цього полі немає {{label}}',\n    greaterThanMax: '{{value}} більше, ніж припустиме максимальне значення {{label}} в {{max}}.',\n    invalidInput: 'У цьому полі введено некоректне значення.',\n    invalidSelection: 'Це поле має некоректний вибір.',\n    invalidSelections: 'Це поле має наступні невірні варіанти вибору:',\n    lessThanMin: '{{value}} менше, ніж мінімальне припустиме значення {{label}} в {{min}}.',\n    limitReached: 'Досягнуто межі, можна додати лише {{max}} елементів.',\n    longerThanMin: 'Це значення має дорівнювати або бути довшим, ніж {{minLength}} символів.',\n    notValidDate: '\"{{value}}\" - некоректна дата.',\n    required: \"Це поле є обов'язковим.\",\n    requiresAtLeast: 'Це поле потребує не менше {{count}} {{label}}.',\n    requiresNoMoreThan: 'Це поле потребує не більше {{count}} {{label}}.',\n    requiresTwoNumbers: 'У цьому полі потрібно ввести два числа.',\n    shorterThanMax: 'Це значення має дорівнювати або бути коротшим, ніж {{maxLength}} символів.',\n    timezoneRequired: 'Потрібний часовий пояс.',\n    trueOrFalse: 'Це поле може мати значення тільки true або false.',\n    username:\n      \"Будь ласка, введіть дійсне ім'я користувача. Може містити літери, цифри, дефіси, крапки та підкреслення.\",\n    validUploadID: 'Це поле не є дійсним ID завантаження.',\n  },\n  version: {\n    type: 'Тип',\n    aboutToPublishSelection: 'Ви бажаєте опублікувати всі {{label}} у вибірці. Ви впевнені?',\n    aboutToRestore:\n      'Ви бажаєте відновити цей документ {{label}} до стану, в якому він знаходився {{versionDate}}. Ви впевнені?',\n    aboutToRestoreGlobal:\n      'Ви бажаєте відновити глобальний запис {{label}} до стану, в якому він знаходився {{versionDate}}. Ви впевнені?',\n    aboutToRevertToPublished:\n      'Ви бажаєте повернути зміни цього документа до його опублікованого стану. Ви впевнені?',\n    aboutToUnpublish: 'Ви бажаєте скасувати публікацю цього документа. Ви впевнені?',\n    aboutToUnpublishSelection:\n      'Ви бажаєте скасувати публікацію всіх {{label}} у вибірці. Ви впевнені?',\n    autosave: 'Автозбереження',\n    autosavedSuccessfully: 'Автозбереження успішно виконано.',\n    autosavedVersion: 'Автозбереження',\n    changed: 'Змінено',\n    changedFieldsCount_one: '{{count}} змінене поле',\n    changedFieldsCount_other: '{{count}} змінених полів',\n    compareVersion: 'Порівняти версію з:',\n    compareVersions: 'Порівняти версії',\n    comparingAgainst: 'Порівнюючи з',\n    confirmPublish: 'Підтвердити публікацію',\n    confirmRevertToSaved: 'Підтвердити повернення до збереженого стану',\n    confirmUnpublish: 'Підвтердити скасування публікації',\n    confirmVersionRestoration: 'Підтвердити відновлення версії',\n    currentDocumentStatus: 'Поточний статус {{docStatus}} документа',\n    currentDraft: 'Поточний проект',\n    currentlyPublished: 'Наразі опубліковано',\n    currentlyViewing: 'Поточний перегляд',\n    currentPublishedVersion: 'Поточна опублікована версія',\n    draft: 'Чернетка',\n    draftSavedSuccessfully: 'Чернетку успішно збережено.',\n    lastSavedAgo: 'Востаннє збережено {{distance}} тому',\n    modifiedOnly: 'Модифіковано тільки',\n    moreVersions: 'Більше версій...',\n    noFurtherVersionsFound: 'Інших версій не знайдено',\n    noRowsFound: 'Не знайдено {{label}}',\n    noRowsSelected: 'Не вибрано {{label}}',\n    preview: 'Попередній перегляд',\n    previouslyDraft: 'Раніше був проект',\n    previouslyPublished: 'Раніше опубліковано',\n    previousVersion: 'Попередня версія',\n    problemRestoringVersion: 'Виникла проблема з відновленням цієї версії',\n    publish: 'Опублікувати',\n    publishAllLocales: 'Опублікуйте всі локалі',\n    publishChanges: 'Опублікувати зміни',\n    published: 'Опубліковано',\n    publishIn: 'Опублікувати в {{locale}}',\n    publishing: 'Публікація',\n    restoreAsDraft: 'Відновити як чернетку',\n    restoredSuccessfully: 'Відновлено успішно.',\n    restoreThisVersion: 'Відновити цю версію',\n    restoring: 'Відновлення...',\n    reverting: 'Повернення до опублікованого стану...',\n    revertToPublished: 'Повернутися до опублікованого стану',\n    saveDraft: 'Зберегти чернетку',\n    scheduledSuccessfully: 'Успішно заплановано.',\n    schedulePublish: 'Розклад публікації',\n    selectLocales: 'Оберіть локаль для відображення',\n    selectVersionToCompare: 'Оберіть версію для порівняння',\n    showingVersionsFor: 'Показані версії для:',\n    showLocales: 'Показати локалі:',\n    specificVersion: 'Специфічна версія',\n    status: 'Статус',\n    unpublish: 'Скасувати публікацію',\n    unpublishing: 'Скасування публікації...',\n    version: 'Версія',\n    versionAgo: '{{distance}} тому',\n    versionCount_many: '{{count}} версій знайдено',\n    versionCount_none: 'Версій не знайдено',\n    versionCount_one: '{{count}} версія знайдена',\n    versionCount_other: '{{count}} версій знайдено',\n    versionCreatedOn: '{{version}} створена:',\n    versionID: 'ID версії',\n    versions: 'Версії',\n    viewingVersion: 'Перенляд версії для {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Перегляд версій для глобальної колекції {{entityLabel}}',\n    viewingVersions: 'Перегляд версій для {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Перегляд версій для глобальної колекції {{entityLabel}}',\n  },\n}\n\nexport const uk: Language = {\n  dateFNSKey: 'uk',\n  translations: ukTranslations,\n}\n"], "names": ["ukTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "uk", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}