{"version": 3, "sources": ["../../src/utilities/getTranslationsByContext.ts"], "sourcesContent": ["import type { Language } from '../types.js'\n\nimport { clientTranslationKeys } from '../clientKeys.js'\n\nfunction filterKeys(obj: Record<string, unknown>, parentGroupKey = '', keys: string[]) {\n  const result: Record<string, unknown> = {}\n\n  for (const [namespaceKey, value] of Object.entries(obj)) {\n    // Skip $schema key\n    if (namespaceKey === '$schema') {\n      result[namespaceKey] = value\n      continue\n    }\n\n    if (typeof value === 'object') {\n      const filteredObject = filterKeys(value as Record<string, unknown>, namespaceKey, keys)\n      if (Object.keys(filteredObject).length > 0) {\n        result[namespaceKey] = filteredObject\n      }\n    } else {\n      for (const key of keys) {\n        const [groupKey, selector] = key.split(':')\n\n        if (parentGroupKey === groupKey) {\n          if (namespaceKey === selector) {\n            result[selector] = value\n          } else {\n            const pluralKeys = ['zero', 'one', 'two', 'few', 'many', 'other']\n            pluralKeys.forEach((pluralKey) => {\n              if (namespaceKey === `${selector}_${pluralKey}`) {\n                result[`${selector}_${pluralKey}`] = value\n              }\n            })\n          }\n        }\n      }\n    }\n  }\n\n  return result\n}\n\nfunction sortObject(obj: Record<string, unknown>) {\n  const sortedObject: Record<string, unknown> = {}\n  Object.keys(obj)\n    .sort()\n    .forEach((key) => {\n      if (typeof obj[key] === 'object') {\n        sortedObject[key] = sortObject(obj[key] as Record<string, unknown>)\n      } else {\n        sortedObject[key] = obj[key]\n      }\n    })\n  return sortedObject\n}\n\nexport const getTranslationsByContext = (selectedLanguage: Language, context: 'api' | 'client') => {\n  if (context === 'client') {\n    return sortObject(filterKeys(selectedLanguage.translations, '', clientTranslationKeys))\n  } else {\n    return selectedLanguage.translations\n  }\n}\n"], "names": ["clientTranslationKeys", "filterKeys", "obj", "parentGroupKey", "keys", "result", "namespaceKey", "value", "Object", "entries", "filteredObject", "length", "key", "groupKey", "selector", "split", "pluralKeys", "for<PERSON>ach", "pluralKey", "sortObject", "sortedObject", "sort", "getTranslationsByContext", "selectedLanguage", "context", "translations"], "mappings": "AAEA,SAASA,qBAAqB,QAAQ,mBAAkB;AAExD,SAASC,WAAWC,GAA4B,EAAEC,iBAAiB,EAAE,EAAEC,IAAc;IACnF,MAAMC,SAAkC,CAAC;IAEzC,KAAK,MAAM,CAACC,cAAcC,MAAM,IAAIC,OAAOC,OAAO,CAACP,KAAM;QACvD,mBAAmB;QACnB,IAAII,iBAAiB,WAAW;YAC9BD,MAAM,CAACC,aAAa,GAAGC;YACvB;QACF;QAEA,IAAI,OAAOA,UAAU,UAAU;YAC7B,MAAMG,iBAAiBT,WAAWM,OAAkCD,cAAcF;YAClF,IAAII,OAAOJ,IAAI,CAACM,gBAAgBC,MAAM,GAAG,GAAG;gBAC1CN,MAAM,CAACC,aAAa,GAAGI;YACzB;QACF,OAAO;YACL,KAAK,MAAME,OAAOR,KAAM;gBACtB,MAAM,CAACS,UAAUC,SAAS,GAAGF,IAAIG,KAAK,CAAC;gBAEvC,IAAIZ,mBAAmBU,UAAU;oBAC/B,IAAIP,iBAAiBQ,UAAU;wBAC7BT,MAAM,CAACS,SAAS,GAAGP;oBACrB,OAAO;wBACL,MAAMS,aAAa;4BAAC;4BAAQ;4BAAO;4BAAO;4BAAO;4BAAQ;yBAAQ;wBACjEA,WAAWC,OAAO,CAAC,CAACC;4BAClB,IAAIZ,iBAAiB,GAAGQ,SAAS,CAAC,EAAEI,WAAW,EAAE;gCAC/Cb,MAAM,CAAC,GAAGS,SAAS,CAAC,EAAEI,WAAW,CAAC,GAAGX;4BACvC;wBACF;oBACF;gBACF;YACF;QACF;IACF;IAEA,OAAOF;AACT;AAEA,SAASc,WAAWjB,GAA4B;IAC9C,MAAMkB,eAAwC,CAAC;IAC/CZ,OAAOJ,IAAI,CAACF,KACTmB,IAAI,GACJJ,OAAO,CAAC,CAACL;QACR,IAAI,OAAOV,GAAG,CAACU,IAAI,KAAK,UAAU;YAChCQ,YAAY,CAACR,IAAI,GAAGO,WAAWjB,GAAG,CAACU,IAAI;QACzC,OAAO;YACLQ,YAAY,CAACR,IAAI,GAAGV,GAAG,CAACU,IAAI;QAC9B;IACF;IACF,OAAOQ;AACT;AAEA,OAAO,MAAME,2BAA2B,CAACC,kBAA4BC;IACnE,IAAIA,YAAY,UAAU;QACxB,OAAOL,WAAWlB,WAAWsB,iBAAiBE,YAAY,EAAE,IAAIzB;IAClE,OAAO;QACL,OAAOuB,iBAAiBE,YAAY;IACtC;AACF,EAAC"}