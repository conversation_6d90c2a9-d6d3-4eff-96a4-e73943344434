{"version": 3, "sources": ["../../src/exports/index.ts"], "sourcesContent": ["export { importDateFNSLocale } from '../importDateFNSLocale.js'\nexport type * from '../types.js'\nexport { getTranslation } from '../utilities/getTranslation.js'\nexport { initI18n, t } from '../utilities/init.js'\nexport { acceptedLanguages, extractHeaderLanguage, rtlLanguages } from '../utilities/languages.js'\n"], "names": ["importDateFNSLocale", "getTranslation", "initI18n", "t", "acceptedLanguages", "extractHeaderLanguage", "rtlLanguages"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,4BAA2B;AAE/D,SAASC,cAAc,QAAQ,iCAAgC;AAC/D,SAASC,QAAQ,EAAEC,CAAC,QAAQ,uBAAsB;AAClD,SAASC,iBAAiB,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,4BAA2B"}