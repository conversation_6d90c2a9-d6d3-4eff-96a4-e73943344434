export const azTranslations = {
    authentication: {
        account: 'Hesa<PERSON>',
        accountOfCurrentUser: '<PERSON>i istifadəçinin hesabı',
        accountVerified: 'Hesa<PERSON> uğurla doğrulandı.',
        alreadyActivated: 'Artıq Aktivləşdirilib',
        alreadyLoggedIn: 'Artıq daxil olunub',
        apiKey: 'API Açarı',
        authenticated: 'Doğrulandı',
        backToLogin: 'Giriş<PERSON> qayıt',
        beginCreateFirstUser: 'Başlamaq üçün ilk istifadəçinizi yaradın.',
        changePassword: 'Parolu dəyişdir',
        checkYourEmailForPasswordReset: 'Əgər e-poçt ünvanı bir hesabla əlaqəli olsa, tezliklə şifrənizi yenidən qurmaq üçün təlimatlari alacaqsınız. E-poçtu giriş qutunuzda görmürsəniz, zəhmət olmasa spam və ya zibil poçt qovluğunu yoxlayın.',
        confirmGeneration: '<PERSON>ras<PERSON>ni təsdiqlə',
        confirmPassword: 'Şifrəni təsdiq et',
        createFirstUser: 'İlk istifadəçini yaradın',
        emailNotValid: 'Təqdim olunan e-poçt etibarlı deyil',
        emailOrUsername: 'E-poçt və ya İstifadəçi adı',
        emailSent: 'E-poçt göndərildi',
        emailVerified: 'Email uğurla təsdiqləndi.',
        enableAPIKey: 'API açarını aktivləşdir',
        failedToUnlock: 'Kilidi açmaq alınmadı',
        forceUnlock: 'Kilidi zorla aç',
        forgotPassword: 'Şifrəni unutmusan',
        forgotPasswordEmailInstructions: 'Zəhmət olmasa, e-poçt ünvanınızı aşağıda daxil edin. Siz parolunuzu necə sıfırlamaq barədə təlimatları olan e-poçt mesajı alacaqsınız.',
        forgotPasswordQuestion: 'Şifrəni unutmusan?',
        forgotPasswordUsernameInstructions: 'Zəhmət olmasa, aşağıda istifadəçi adınızı daxil edin. İstifadəçi adınıza uyğun e-poçt ünvanınıza əlavə proqramın sıfırlanması ilə əlaqəli təlimatlar göndəriləcək.',
        generate: 'Yarad',
        generateNewAPIKey: 'Yeni API açarı yarad',
        generatingNewAPIKeyWillInvalidate: 'Yeni API açarının yaradılması əvvəlki açarı etibarsız edəcək. Davam etmək istədiyinizə əminsiniz?',
        lockUntil: 'Klidklə',
        logBackIn: 'Yenidən daxil ol',
        loggedIn: 'Başqa istifadəçi ilə daxil olmaq üçün əvvəlcə çıxış etməlisiniz.',
        loggedInChangePassword: 'Parolu dəyişdirmək üçün hesabınıza get və orada şifrənizi redaktə edin.',
        loggedOutInactivity: 'Hərəkətsizlik səbəbindən sistemdən çıxmısınız.',
        loggedOutSuccessfully: 'Siz uğurla çıxış etdiniz.',
        loggingOut: 'Çıxış edilir...',
        login: 'Giriş',
        loginAttempts: 'Giriş cəhdləri',
        loginUser: 'Giriş istifadəçisi',
        loginWithAnotherUser: 'Başqa istifadəçi ilə daxil olmaq üçün əvvəlcə çıxış etməlisiniz.',
        logOut: 'Çıxış et',
        logout: 'Çıxış',
        logoutSuccessful: 'Uğurlu çıxış.',
        logoutUser: 'Sistemdən çıxış',
        newAccountCreated: 'Sizin üçün yeni hesab yaradıldı. Zəhmət olmasa, e-poçtunuzu doğrulamaq üçün aşağıdakı linke klikləyin: <a href="{{verificationURL}}">{{verificationURL}}</a>. E-poçtunuzu doğruladıqdan sonra uğurla daxil ola bilərsiniz.',
        newAPIKeyGenerated: 'Yeni API Açarı Yaradıldı.',
        newPassword: 'Yeni şifrə',
        passed: 'Doğrulama Keçildi',
        passwordResetSuccessfully: 'Parol uğurla yenidən quruldu.',
        resetPassword: 'Şifrəni sıfırla',
        resetPasswordExpiration: 'Şifrənin müddətini sıfırla',
        resetPasswordToken: 'Şifrə Tokenini Sıfırla',
        resetYourPassword: 'Şifrənizi sıfırlayın',
        stayLoggedIn: 'Sistemdə qal',
        successfullyRegisteredFirstUser: 'İlk istifadəçiyi uğurla qeyd etdik.',
        successfullyUnlocked: 'Uğurla kilidi açıldı',
        tokenRefreshSuccessful: 'Tokenin yenilənməsi uğurlu oldu.',
        unableToVerify: 'Doğrulamaq mümkün deyil',
        username: 'İstifadəçi adı',
        usernameNotValid: 'Təqdim edilən istifadəçi adı düzgün deyil',
        verified: 'Doğrulanmış',
        verifiedSuccessfully: 'Uğurla doğrulandı',
        verify: 'Doğrula',
        verifyUser: 'İstifadəçini doğrula',
        verifyYourEmail: 'E-poçtunuzu doğrulayın',
        youAreInactive: 'Siz bir müddətdir aktiv deyilsiniz və tezliklə öz təhlükəsizliyiniz üçün avtomatik olaraq sistemdən çıxacaqsınız. Daxil olmaq istərdinizmi?',
        youAreReceivingResetPassword: 'Siz (və ya başqası) hesabınız üçün parolun sıfırlanmasını tələb etdiyiniz üçün bunu alırsınız. Prosesi tamamlamaq üçün zəhmət olmasa aşağıdakı linkə klikləyin:',
        youDidNotRequestPassword: 'Əgər siz bunu tələb etməmisinizsə, lütfən, bu e-poçtu nəzərə almayın və şifrəniz dəyişilməz qalacaq.'
    },
    error: {
        accountAlreadyActivated: 'Bu hesab artıq aktivləşdirilib.',
        autosaving: 'Bu sənədin avto yadda saxlanılması zamanı problem yarandı.',
        correctInvalidFields: 'Zəhmət olmasa, yanlış sahələri düzəlt.',
        deletingFile: 'Faylın silinməsində xəta baş verdi.',
        deletingTitle: '{{title}} silinərkən xəta baş verdi. Zəhmət olmasa, bağlantınızı yoxlayın və yenidən cəhd edin.',
        documentNotFound: '{{id}} ID-li sənəd tapılmadı. Bu, onun silinmiş və ya heç vaxt mövcud olmamış ola bilər və ya sizin ona giriş hüququnuz olmayabilir.',
        emailOrPasswordIncorrect: 'Təqdim olunan e-poçt və ya şifrə yanlışdır.',
        followingFieldsInvalid_one: 'Aşağıdakı sahə yanlışdır:',
        followingFieldsInvalid_other: 'Aşağıdaki sahələr yanlışdır:',
        incorrectCollection: 'Yanlış Kolleksiya',
        insufficientClipboardPermissions: 'Mübadilə buferinə giriş rədd edildi. Zəhmət olmasa, icazələri yoxlayın.',
        invalidClipboardData: 'Yanlış mübadilə buferi məlumatı.',
        invalidFileType: 'Yanlış fayl növü',
        invalidFileTypeValue: 'Yanlış fayl növü: {{value}}',
        invalidRequestArgs: 'Sorguda etibarsız arqumentlər təqdim edildi: {{args}}',
        loadingDocument: '{{id}} ID-li sənədin yüklənməsində problem baş verdi.',
        localesNotSaved_one: 'Aşağıdakı yerləşdirmə saxlanıla bilmədi:',
        localesNotSaved_other: 'Aşağıdakı yerləşdirmələr saxlanıla bilmədi:',
        logoutFailed: 'Çıxış uğursuz oldu.',
        missingEmail: 'E-poçt adresi çatışmır.',
        missingIDOfDocument: 'Yeniləmək üçün sənədin ID-si çatışmır.',
        missingIDOfVersion: 'Versiyanın ID-si çatışmır.',
        missingRequiredData: 'Tələb olunan məlumat çatışmır.',
        noFilesUploaded: 'Heç bir fayl yüklənilməyib.',
        noMatchedField: '"{{label}}" üçün uyğun sahə tapılmadı',
        notAllowedToAccessPage: 'Bu səhifəyə girməyə icazəniz yoxdur.',
        notAllowedToPerformAction: 'Bu əməliyyatı icra etməyə icazəniz yoxdur.',
        notFound: 'Tələb olunan resurs tapılmadı.',
        noUser: 'İstifadəçi Yoxdur',
        previewing: 'Bu sənədin ön baxışı zamanı problem yarandı.',
        problemUploadingFile: 'Faylın yüklənməsi zamanı problem yarandı.',
        restoringTitle: '{{title}} bərpa olunarkən xəta baş verdi. Zəhmət olmasa, internet bağlantınızı yoxlayın və yenidən cəhd edin.',
        tokenInvalidOrExpired: 'Token ya yanlışdır və ya müddəti bitib.',
        tokenNotProvided: 'Token təqdim edilməyib.',
        unableToCopy: 'Kopyalama mümkün deyil.',
        unableToDeleteCount: '{{count}} dən {{total}} {{label}} silinə bilmir.',
        unableToReindexCollection: '{{collection}} kolleksiyasının yenidən indekslənməsi zamanı səhv baş verdi. Əməliyyat dayandırıldı.',
        unableToUpdateCount: '{{count}} dən {{total}} {{label}} yenilənə bilmir.',
        unauthorized: 'İcazəniz yoxdur, bu tələbi yerinə yetirmək üçün daxil olmalısınız.',
        unauthorizedAdmin: 'Bu əməliyyatı yerinə yetirmək üçün admin olmalısınız.',
        unknown: 'Naməlum bir xəta baş verdi.',
        unPublishingDocument: 'Bu sənədin nəşrini ləğv etmək zamanı problem baş verdi.',
        unspecific: 'Xəta baş verdi.',
        unverifiedEmail: 'Zəhmət olmasa, daxil olmadan əvvəl e-poçtunuzu təsdiqləyin.',
        userEmailAlreadyRegistered: 'Verilən e-poçt ünvanı ilə artıq istifadəçi qeydiyyatdan keçib.',
        userLocked: 'Bu istifadəçi çoxsaylı uğursuz giriş cəhdləri səbəbindən kilidlənib.',
        usernameAlreadyRegistered: 'Verilən istifadəçi adı ilə artıq qeydiyyatdan keçmişdir.',
        usernameOrPasswordIncorrect: 'Təqdim edilən istifadəçi adı və ya şifrə yanlışdır.',
        valueMustBeUnique: 'Dəyər təkrar olmamalıdır',
        verificationTokenInvalid: 'Doğrulama tokenı yanlışdır.'
    },
    fields: {
        addLabel: '{{label}} əlavə et',
        addLink: 'Keçid əlavə et',
        addNew: 'Yenisini əlavə et',
        addNewLabel: 'Yeni {{label}} əlavə et',
        addRelationship: 'Relationship əlavə et',
        addUpload: 'Yükləmə əlavə et',
        block: 'blok',
        blocks: 'bloklar',
        blockType: 'Blok Növü',
        chooseBetweenCustomTextOrDocument: "Xüsusi mətn URL'si daxil etmək və ya başqa bir sənədə keçid yaratmaq arasında seçim edin.",
        chooseDocumentToLink: 'Keçid yaratmaq üçün sənəd seçin',
        chooseFromExisting: 'Mövcuddan seçin',
        chooseLabel: '{{label}} seçin',
        collapseAll: 'Hamısını Bağla',
        customURL: 'Xüsusi URL',
        editLabelData: '{{label}} məlumatını redaktə et',
        editLink: 'Keçidi redaktə et',
        editRelationship: 'Relationship redaktə et',
        enterURL: 'URL daxil edin',
        internalLink: 'Daxili Keçid',
        itemsAndMore: '{{items}} və daha {{count}} nəfər',
        labelRelationship: '{{label}} Relationship',
        latitude: 'Enlik',
        linkedTo: '<0>{{label}}</0> ilə əlaqəli',
        linkType: 'Keçid Növü',
        longitude: 'Uzunluq',
        newLabel: 'Yeni {{label}}',
        openInNewTab: 'Yeni sekmede aç',
        passwordsDoNotMatch: 'Şifrələr uyğun gəlmir.',
        relatedDocument: 'Əlaqəli Sənəd',
        relationTo: 'Relationship',
        removeRelationship: 'Relationship sil',
        removeUpload: 'Yükləməni sil',
        saveChanges: 'Dəyişiklikləri saxla',
        searchForBlock: 'Blok üçün axtarış',
        selectExistingLabel: 'Mövcud {{label}} seçin',
        selectFieldsToEdit: 'Redaktə ediləcək sahələri seçin',
        showAll: 'Hamısını Göstər',
        swapRelationship: 'Relationship dəyiş',
        swapUpload: 'Yükləməni dəyiş',
        textToDisplay: 'Göstəriləcək mətn',
        toggleBlock: 'Bloku keç',
        uploadNewLabel: 'Yeni {{label}} yüklə'
    },
    folder: {
        browseByFolder: 'Qovluqlara görə gözdən keçirin',
        byFolder: 'Qovluğa görə',
        deleteFolder: 'Qovluğu Sil',
        folderName: 'Qovluq Adı',
        folders: 'Qovluqlar',
        folderTypeDescription: 'Bu qovluqda hangi tip kolleksiya sənədlərinə icazə verilməlidir seçin.',
        itemHasBeenMoved: '{{title}} {{folderName}} qovluğuna köçürüldü.',
        itemHasBeenMovedToRoot: '{{title}} kök qovluğa köçürüldü.',
        itemsMovedToFolder: '{{title}} {{folderName}} qovluğuna köçürüldü',
        itemsMovedToRoot: '{{title}} kök qovluğa köçürdü',
        moveFolder: 'Qovluğu Köçür',
        moveItemsToFolderConfirmation: 'Siz <1>{{count}} {{label}}</1> -i <2>{{toFolder}}</2> -ə köçürmək üzərəsiniz. Eminsiniz?',
        moveItemsToRootConfirmation: 'Siz <1>{{count}} {{label}}</1> əsas qovluğa köçürmək üzərəsiniz. Eminsiniz?',
        moveItemToFolderConfirmation: 'Siz <1>{{title}}</1>-i <2>{{toFolder}}</2>ə köçürmək barədəsiniz. Eminsinizmi?',
        moveItemToRootConfirmation: "Siz <1>{{title}}</1>'i kök qovluğa köçürmək barədəsindəsiniz. Əminsiniz?",
        movingFromFolder: '{{title}}-i {{fromFolder}}-dən köçürmək',
        newFolder: 'Yeni Qovluq',
        noFolder: 'Qovluq Yoxdur',
        renameFolder: 'Qovluğun adını dəyişdirin',
        searchByNameInFolder: '{{folderName}} qovluğunda adla axtarış',
        selectFolderForItem: '{{title}} üçün qovluğu seçin'
    },
    general: {
        name: 'Ad',
        aboutToDelete: 'Siz {{label}} <1>{{title}}</1> silməyə hazırsınız. Eminsiniz?',
        aboutToDeleteCount_many: 'Siz {{count}} {{label}} silməyə hazırsınız.',
        aboutToDeleteCount_one: 'Siz {{count}} {{label}} silməyə hazırsınız.',
        aboutToDeleteCount_other: 'Siz {{count}} {{label}} silməyə hazırsınız.',
        aboutToPermanentlyDelete: 'Siz əbədi olaraq {{label}} <1>{{title}}</1> silmək üzrəsiniz. Eminsiniz?',
        aboutToPermanentlyDeleteTrash: 'Siz müllifdən daimi olaraq <0>{{count}}</0> <1>{{label}}</1> silinəcəkdir. Eminsiniz?',
        aboutToRestore: '{{label}} <1>{{title}}</1> bərpa edilmək üzrədir. Eminsiniz?',
        aboutToRestoreAsDraft: 'Siz {{label}} <1>{{title}}</1> draft kimi bərpa etmək əzəldəsiniz. Eminsinizmi?',
        aboutToRestoreAsDraftCount: 'Siz {{count}} {{label}}-ni qaralamak üçün bərpa etməyə hazırlaşırsınız',
        aboutToRestoreCount: 'Siz {{count}} {{label}} bərpa etməyə hazırlaşırsınız.',
        aboutToTrash: 'Siz {{label}} <1>{{title}}</1> elementini zibilliyə köçürmək barədəsiniz. Eminsiniz?',
        aboutToTrashCount: 'Siz {{count}} {{label}}-i zibilə köçürmək barədəsiz.',
        addBelow: 'Aşağıya əlavə et',
        addFilter: 'Filter əlavə et',
        adminTheme: 'Admin Mövzusu',
        all: 'Hamısı',
        allCollections: 'Bütün kolleksiyalar',
        allLocales: 'Bütün lokal məkanlar',
        and: 'Və',
        anotherUser: 'Başqa bir istifadəçi',
        anotherUserTakenOver: 'Başqa bir istifadəçi bu sənədin redaktəsini ələ keçirdi.',
        applyChanges: 'Dəyişiklikləri Tətbiq Edin',
        ascending: 'Artan',
        automatic: 'Avtomatik',
        backToDashboard: 'Panelə qayıdın',
        cancel: 'Ləğv et',
        changesNotSaved: 'Dəyişiklikləriniz saxlanılmayıb. İndi çıxsanız, dəyişikliklərinizi itirəcəksiniz.',
        clear: 'Payload kontekstində orijinal mətnin mənasını qoruya. İşte Payload terminləri siyahısıdır ki, onlar üzərində çox xüsusi mənalar gəlir:\n    - Kolleksiya: Kolleksiya sənədlərin hamıya ortaq struktur və məqsəd sərbəst olan bir qrupdur. Kolleksiyalar Payload-da məzmunu təşkil etmək və idarə etmək üçün istifadə edilir.\n    - Sahə: Sahə',
        clearAll: 'Hamısını təmizlə',
        close: 'Bağla',
        collapse: 'Bağla',
        collections: 'Kolleksiyalar',
        columns: 'Sütunlar',
        columnToSort: 'Sıralamağa sütun',
        confirm: 'Təsdiqlə',
        confirmCopy: 'Kopyanı təsdiqləyin',
        confirmDeletion: 'Silməni təsdiqlə',
        confirmDuplication: 'Dublikasiyanı təsdiqlə',
        confirmMove: 'Hərəkəti təsdiqləyin',
        confirmReindex: 'Bütün {{collections}} yenidən indekslənsin?',
        confirmReindexAll: 'Bütün kolleksiyalar yenidən indekslənsin?',
        confirmReindexDescription: 'Bu, mövcud indeksləri siləcək və {{collections}} kolleksiyalarında sənədləri yenidən indeksləyəcək.',
        confirmReindexDescriptionAll: 'Bu, mövcud indeksləri siləcək və bütün kolleksiyalardakı sənədləri yenidən indeksləyəcək.',
        confirmRestoration: 'Bərpa etməni təsdiqləyin',
        copied: 'Kopyalandı',
        copy: 'Kopyala',
        copyField: 'Sahəni kopyala',
        copying: 'Kopyalama',
        copyRow: 'Sətiri kopyala',
        copyWarning: 'Siz {{label}} {{title}} üçün {{from}} ilə {{to}} -nu üzərindən yazmaq ətrafındasınız. Eminsiniz?',
        create: 'Yarat',
        created: 'Yaradıldı',
        createdAt: 'Yaradıldığı tarix',
        createNew: 'Yeni yarat',
        createNewLabel: 'Yeni {{label}} yarat',
        creating: 'Yaradılır',
        creatingNewLabel: 'Yeni {{label}} yaradılır',
        currentlyEditing: 'hazırda bu sənədi redaktə edir. Siz öhdəliyi götürsəniz, redaktəni davam etdirməkdən bloklanacaqlar və qeydə alınmamış dəyişiklikləri itirə bilərlər.',
        custom: 'Xüsusi',
        dark: 'Tünd',
        dashboard: 'Panel',
        delete: 'Sil',
        deleted: 'Silinmiş',
        deletedAt: 'Silinib Tarixi',
        deletedCountSuccessfully: '{{count}} {{label}} uğurla silindi.',
        deletedSuccessfully: 'Uğurla silindi.',
        deletePermanently: 'Çöplüyü atlayın və daimi olaraq silin',
        deleting: 'Silinir...',
        depth: 'Dərinlik',
        descending: 'Azalan',
        deselectAllRows: 'Bütün sıraları seçimi ləğv edin',
        document: 'Sənəd',
        documentIsTrashed: 'Bu {{label}} zibil qutusuna atılıb və yalnız oxuna bilər.',
        documentLocked: 'Sənəd kilidləndi',
        documents: 'Sənədlər',
        duplicate: 'Dublikat',
        duplicateWithoutSaving: 'Dəyişiklikləri saxlamadan dublikatla',
        edit: 'Redaktə et',
        editAll: 'Hamısını redaktə et',
        editedSince: 'Redaktə edilib',
        editing: 'Redaktə olunur',
        editingLabel_many: '{{count}} {{label}} redaktə olunur',
        editingLabel_one: '{{count}} {{label}} redaktə olunur',
        editingLabel_other: '{{count}} {{label}} redaktə olunur',
        editingTakenOver: 'Redaktə ələ keçirildi',
        editLabel: '{{label}} redaktə et',
        email: 'Elektron poçt',
        emailAddress: 'Elektron poçt ünvanı',
        emptyTrash: 'Zibil qutusunu boşaltın',
        emptyTrashLabel: '{{label}} zibilini boşaltın',
        enterAValue: 'Bir dəyər daxil edin',
        error: 'Xəta',
        errors: 'Xətalar',
        exitLivePreview: 'Canlı Önizləmədən Çıxın',
        export: 'İxrac',
        fallbackToDefaultLocale: 'Standart lokalə keçid',
        false: 'Yalan',
        filter: 'Filter',
        filters: 'Filtərlər',
        filterWhere: '{{label}} filtrlə',
        globals: 'Qloballar',
        goBack: 'Geri qayıt',
        groupByLabel: '{{label}} ilə qruplaşdırın',
        import: 'İdxal',
        isEditing: 'redaktə edir',
        item: 'əşya',
        items: 'maddələr',
        language: 'Dil',
        lastModified: 'Son dəyişdirildi',
        leaveAnyway: 'Heç olmasa çıx',
        leaveWithoutSaving: 'Saxlamadan çıx',
        light: 'Açıq',
        livePreview: 'Öncədən baxış',
        loading: 'Yüklənir',
        locale: 'Lokal',
        locales: 'Dillər',
        menu: 'Menyu',
        moreOptions: 'Daha çox seçimlər',
        move: 'Hərəkət et',
        moveConfirm: 'Siz <1>{{destination}}</1> -ə {{count}} {{label}} köçürmək ətrafında. Eminsinizmi?',
        moveCount: '{{count}} {{label}} hərəkət etdirin',
        moveDown: 'Aşağı hərəkət et',
        moveUp: 'Yuxarı hərəkət et',
        moving: 'Hərəkət edir',
        movingCount: '{{count}} {{label}} köçürülür',
        newPassword: 'Yeni şifrə',
        next: 'Növbəti',
        no: 'Xeyr',
        noDateSelected: 'Heç bir tarix seçilməyib',
        noFiltersSet: 'Filter təyin edilməyib',
        noLabel: '<Heç bir {{label}}>',
        none: 'Heç bir',
        noOptions: 'Heç bir seçim yoxdur',
        noResults: 'Heç bir {{label}} tapılmadı. Ya hələ {{label}} yoxdur, ya da yuxarıda göstərdiyiniz filtrlərə uyğun gəlmir.',
        notFound: 'Tapılmadı',
        nothingFound: 'Heç nə tapılmadı',
        noTrashResults: 'Çöplükdə heç bir {{label}} yoxdur.',
        noUpcomingEventsScheduled: 'Heç bir gələcək tədbir cədvələ alınmayıb.',
        noValue: 'Dəyər yoxdur',
        of: 'dən',
        only: 'Yalnız',
        open: 'Aç',
        or: 'Və ya',
        order: 'Sıra',
        overwriteExistingData: 'Mövcud sahə məlumatlarını yenidən yazın',
        pageNotFound: 'Səhifə tapılmadı',
        password: 'Şifrə',
        pasteField: 'Sahəni yapışdır',
        pasteRow: 'Sətiri yapışdır',
        payloadSettings: 'Payload Parametrləri',
        permanentlyDelete: 'Daimi Olaraq Sil',
        permanentlyDeletedCountSuccessfully: '{{count}} {{label}} uğurla daimi olaraq silindi.',
        perPage: 'Hər səhifədə: {{limit}}',
        previous: 'Əvvəlki',
        reindex: 'Yenidən indekslə',
        reindexingAll: 'Bütün {{collections}} yenidən indekslənir.',
        remove: 'Sil',
        rename: 'Yenidən adlandırın',
        reset: 'Yenidən başlat',
        resetPreferences: 'Təhlükəsizlik parametrlərini sıfırlamaq',
        resetPreferencesDescription: 'Bu, bütün parametrlərinizi standart vəziyyətlərinə sıfırlayacaq.',
        resettingPreferences: 'Təhlükəsizlik parametrləri sıfırlanır.',
        restore: 'Bərpa et',
        restoreAsPublished: 'Nəşr edilmiş versiya kimi bərpa et',
        restoredCountSuccessfully: '{{count}} {{label}} uğurla bərpa edildi.',
        restoring: 'Orijinal mətnin mənasını Payload kontekstində qoruyun. Ən əhəmiyyətli Payload ifadələrinin siyahısı aşağıdakı kimi dir:\n\n    - Collection: "Collection" bir sıra sənədlərin əməkdaş olduğu, ortaq struktur və məqsədi olan bir qrupdur. "Collections", Payload-də məzmunu təşkil etmək və idarə etmək üçün istifadə edilir.\n    - Field: "Field", kolle',
        row: 'Sətir',
        rows: 'Sətirlər',
        save: 'Saxla',
        saving: 'Saxlanılır...',
        schedulePublishFor: '{{title}} üçün nəşr cədvəlini təyin edin',
        searchBy: '{{label}} ilə axtar',
        select: 'Seçin',
        selectAll: 'Bütün {{count}} {{label}} seç',
        selectAllRows: 'Bütün sıraları seçin',
        selectedCount: '{{count}} {{label}} seçildi',
        selectLabel: '{{label}} seçin',
        selectValue: 'Dəyər seçin',
        showAllLabel: 'Bütün {{label}}-ı göstər',
        sorryNotFound: 'Üzr istəyirik - sizin tələbinizə uyğun heç nə yoxdur.',
        sort: 'Sırala',
        sortByLabelDirection: '{{label}} {{direction}} ilə sırala',
        stayOnThisPage: 'Bu səhifədə qal',
        submissionSuccessful: 'Təqdimat uğurlu oldu.',
        submit: 'Təqdim et',
        submitting: 'Təqdim olunur...',
        success: 'Uğur',
        successfullyCreated: '{{label}} uğurla yaradıldı.',
        successfullyDuplicated: '{{label}} uğurla dublikatlandı.',
        successfullyReindexed: '{{collections}} kolleksiyalarından {{total}} sənəddən {{count}} sənəd uğurla yenidən indeksləndi.',
        takeOver: 'Əvvəl',
        thisLanguage: 'Azərbaycan dili',
        time: 'Vaxt',
        timezone: 'Saat qurşağı',
        titleDeleted: '{{label}} "{{title}}" uğurla silindi.',
        titleRestored: '"{{title}}" "{{label}}" uğurla bərpa edildi.',
        titleTrashed: '{{label}} "{{title}}" zibilə köçürüldü.',
        trash: 'Zibil',
        trashedCountSuccessfully: '{{count}} {{label}} zibilə köçürüldü.',
        true: 'Doğru',
        unauthorized: 'İcazəsiz',
        unsavedChanges: 'Sizin saxlanılmamış dəyişiklikləriniz var. Davam etmədən əvvəl saxlayın və ya atın.',
        unsavedChangesDuplicate: 'Saxlanılmamış dəyişiklikləriniz var. Dublikatla davam etmək istəyirsiniz?',
        untitled: 'Başlıqsız',
        upcomingEvents: 'Gələcək Tədbirlər',
        updatedAt: 'Yeniləndiyi tarix',
        updatedCountSuccessfully: '{{count}} {{label}} uğurla yeniləndi.',
        updatedLabelSuccessfully: '{{label}} uğurla yeniləndi.',
        updatedSuccessfully: 'Uğurla yeniləndi.',
        updateForEveryone: 'Hər kəs üçün yeniləmə',
        updating: 'Yenilənir',
        uploading: 'Yüklənir',
        uploadingBulk: '{{total}}-dan {{current}}-un yüklənməsi',
        user: 'İstifadəçi',
        username: 'İstifadəçi adı',
        users: 'İstifadəçilər',
        value: 'Dəyər',
        viewing: 'Baxış',
        viewReadOnly: 'Yalnız oxu rejimində bax',
        welcome: 'Xoş gəldiniz',
        yes: 'Bəli'
    },
    localization: {
        cannotCopySameLocale: 'Eyni dildə köçürmək mümkün deyil',
        copyFrom: 'Kopyalayın',
        copyFromTo: '{{from}}-dan {{to}}-ya kopyalama',
        copyTo: 'Köçür',
        copyToLocale: 'Yerliyə köçürün',
        localeToPublish: 'Yayımlamaq üçün yerləşdirin',
        selectLocaleToCopy: 'Köçürmək üçün yerli seçin'
    },
    operators: {
        contains: 'daxilində',
        equals: 'bərabərdir',
        exists: 'mövcuddur',
        intersects: 'kəsişir',
        isGreaterThan: 'dən böyük',
        isGreaterThanOrEqualTo: 'böyük və ya bərabər',
        isIn: 'daxildir',
        isLessThan: 'dən kiçik',
        isLessThanOrEqualTo: 'kiçik və ya bərabər',
        isLike: 'kimi',
        isNotEqualTo: 'bərabər deyil',
        isNotIn: 'daxil deyil',
        isNotLike: 'deyil kimi',
        near: 'yaxın',
        within: 'daxilinde'
    },
    upload: {
        addFile: 'Fayl əlavə et',
        addFiles: 'Faylları Əlavə Edin',
        bulkUpload: 'Kütləvi Yükləmə',
        crop: 'Məhsul',
        cropToolDescription: 'Seçilmiş sahənin köşələrini sürükləyin, yeni bir sahə çəkin və ya aşağıdakı dəyərləri düzəltin.',
        download: 'Yükləyin',
        dragAndDrop: 'Faylı buraya sürükləyin və buraxın',
        dragAndDropHere: 'və ya faylı buraya sürükləyin və buraxın',
        editImage: 'Şəkili Redaktə Et',
        fileName: 'Faylın Adı',
        fileSize: 'Faylım Ölçüsü',
        filesToUpload: 'Yükləmək üçün fayllar',
        fileToUpload: 'Yükləmək üçün Fayl',
        focalPoint: 'Mərkəzi Nöqtə',
        focalPointDescription: 'Fokus nöqtəsini birbaşa önizləməyə sürükləyin və ya aşağıdakı dəyərləri düzəltin.',
        height: 'Hündürlük',
        lessInfo: 'Daha az məlumat',
        moreInfo: 'Daha çox məlumat',
        noFile: 'Heç bir fayl',
        pasteURL: 'URL yapışdır',
        previewSizes: 'Öncədən baxış ölçüləri',
        selectCollectionToBrowse: 'Gözdən keçirmək üçün bir Kolleksiya seçin',
        selectFile: 'Fayl seçin',
        setCropArea: 'Məhsul sahəsini təyin et',
        setFocalPoint: 'Fokus nöqtəsi təyin et',
        sizes: 'Ölçülər',
        sizesFor: '{{label}} üçün ölçülər',
        width: 'En'
    },
    validation: {
        emailAddress: 'Xahiş edirik doğru elektron poçt ünvanını daxil edin.',
        enterNumber: 'Xahiş edirik doğru nömrəni daxil edin.',
        fieldHasNo: 'Bu sahədə heç bir {{label}} yoxdur',
        greaterThanMax: '{{value}} icazə verilən maksimal {{label}} olan {{max}}-dən böyükdür.',
        invalidInput: 'Bu sahə yanlış daxil edilmişdir.',
        invalidSelection: 'Bu sahədə yanlış seçim edilmişdir.',
        invalidSelections: 'Bu sahədə aşağıdakı yanlış seçimlər edilmişdir:',
        lessThanMin: '{{value}} icazə verilən minimal {{label}} olan {{min}}-dən kiçikdir.',
        limitReached: 'Limitə çatdınız, yalnız {{max}} element əlavə edilə bilər.',
        longerThanMin: 'Bu dəyər {{minLength}} simvoldan uzun olmalıdır.',
        notValidDate: '"{{value}}" doğru tarix deyil.',
        required: 'Bu sahə mütləq doldurulmalıdır.',
        requiresAtLeast: 'Bu sahə ən azı {{count}} {{label}} tələb edir.',
        requiresNoMoreThan: 'Bu sahə {{count}} {{label}}-dan çox olmamalıdır.',
        requiresTwoNumbers: 'Bu sahə iki nömrə tələb edir.',
        shorterThanMax: 'Bu dəyər {{maxLength}} simvoldan qısa olmalıdır.',
        timezoneRequired: 'Vaxt zonası tələb olunur.',
        trueOrFalse: 'Bu sahə yalnız doğru və ya yanlış ola bilər.',
        username: 'Zəhmət olmasa, etibarlı bir istifadəçi adı daxil edin. Hərflər, rəqəmlər, tire, nöqtə və alt xəttlər ola bilər.',
        validUploadID: 'Bu sahə doğru yükləmə ID-si deyil.'
    },
    version: {
        type: 'Növ',
        aboutToPublishSelection: 'Seçimdə olan bütün {{label}}-i dərc etməyə hazırsınız. Əminsiniz?',
        aboutToRestore: 'Bu {{label}} sənədini {{versionDate}} tarixindəki vəziyyətinə bərpa etmək üzrəsiniz.',
        aboutToRestoreGlobal: 'Qlobal {{label}}-i {{versionDate}} tarixindəki vəziyyətinə bərpa etmək üzrəsiniz.',
        aboutToRevertToPublished: 'Bu sənədin dəyişikliklərini dərc edilmiş vəziyyətinə qaytarmağa hazırsınız. Əminsiniz?',
        aboutToUnpublish: 'Bu sənədi dərcdən çıxartmağa hazırsınız. Əminsiniz?',
        aboutToUnpublishSelection: 'Seçimdə olan bütün {{label}}-i dərcdən çıxartmağa hazırsınız. Əminsiniz?',
        autosave: 'Avtomatik yadda saxlama',
        autosavedSuccessfully: 'Uğurla avtomatik olaraq yadda saxlandı.',
        autosavedVersion: 'Avtomatik yadda saxlanmış versiya',
        changed: 'Dəyişdirildi',
        changedFieldsCount_one: '{{count}} sahə dəyişdi',
        changedFieldsCount_other: '{{count}} dəyişdirilmiş sahələr',
        compareVersion: 'Versiyanı müqayisə et:',
        compareVersions: 'Versiyaları Müqayisə Edin',
        comparingAgainst: 'Müqayisə etmək',
        confirmPublish: 'Dərci təsdiq edin',
        confirmRevertToSaved: 'Yadda saxlanana qayıtmağı təsdiq edin',
        confirmUnpublish: 'Dərcdən çıxartmağı təsdiq edin',
        confirmVersionRestoration: 'Versiyanın bərpasını təsdiq edin',
        currentDocumentStatus: 'Cari {{docStatus}} sənədi',
        currentDraft: 'Hazırki Layihə',
        currentlyPublished: 'Hazırda Nəşr Olunmuş',
        currentlyViewing: 'Hazırda baxılır',
        currentPublishedVersion: 'Hazırki Nəşr Versiyası',
        draft: 'Qaralama',
        draftSavedSuccessfully: 'Qaralama uğurla yadda saxlandı.',
        lastSavedAgo: '{{distance}} əvvəl son yadda saxlanıldı',
        modifiedOnly: 'Yalnızca dəyişdirilmişdir',
        moreVersions: 'Daha çox versiyalar...',
        noFurtherVersionsFound: 'Başqa versiyalar tapılmadı',
        noRowsFound: 'Heç bir {{label}} tapılmadı',
        noRowsSelected: 'Heç bir {{label}} seçilməyib',
        preview: 'Öncədən baxış',
        previouslyDraft: 'Daha öncə bir Qaralama',
        previouslyPublished: 'Daha əvvəl nəşr olunmuş',
        previousVersion: 'Əvvəlki Versiya',
        problemRestoringVersion: 'Bu versiyanın bərpasında problem yaşandı',
        publish: 'Dərc et',
        publishAllLocales: 'Bütün lokalizasiyaları dərc edin',
        publishChanges: 'Dəyişiklikləri dərc et',
        published: 'Dərc edilmiş',
        publishIn: '{{locale}} dili ilə nəşr edin',
        publishing: 'Nəşr',
        restoreAsDraft: 'Qaralamalar kimi bərpa et',
        restoredSuccessfully: 'Uğurla bərpa edildi.',
        restoreThisVersion: 'Bu versiyanı bərpa et',
        restoring: 'Bərpa olunur...',
        reverting: 'Qayıdılır...',
        revertToPublished: 'Dərc edilmişə qayıt',
        saveDraft: 'Qaralamayı yadda saxla',
        scheduledSuccessfully: 'Uğurla cədvələ qoyuldu.',
        schedulePublish: 'Nəşr Cədvəli',
        selectLocales: 'Göstərmək üçün lokalları seçin',
        selectVersionToCompare: 'Müqayisə üçün bir versiya seçin',
        showingVersionsFor: 'Göstərilən versiyalar üçün:',
        showLocales: 'Lokalları göstər:',
        specificVersion: 'Xüsusi Versiya',
        status: 'Status',
        unpublish: 'Dərcdən çıxart',
        unpublishing: 'Dərcdən çıxarılır...',
        version: 'Versiya',
        versionAgo: '{{distance}} əvvəl',
        versionCount_many: '{{count}} versiya tapıldı',
        versionCount_none: 'Versiya tapılmadı',
        versionCount_one: '{{count}} versiya tapıldı',
        versionCount_other: '{{count}} versiya tapıldı',
        versionCreatedOn: '{{version}} tarixində yaradıldı:',
        versionID: 'Versiyanın ID-si',
        versions: 'Versiyalar',
        viewingVersion: '{{entityLabel}} {{documentTitle}} üçün versiyanı göstərir',
        viewingVersionGlobal: 'Qlobal {{entityLabel}} üçün versiyanı göstərir',
        viewingVersions: '{{entityLabel}} {{documentTitle}} üçün versiyaları göstərir',
        viewingVersionsGlobal: 'Qlobal {{entityLabel}} üçün versiyaları göstərir'
    }
};
export const az = {
    dateFNSKey: 'az',
    translations: azTranslations
};

//# sourceMappingURL=az.js.map