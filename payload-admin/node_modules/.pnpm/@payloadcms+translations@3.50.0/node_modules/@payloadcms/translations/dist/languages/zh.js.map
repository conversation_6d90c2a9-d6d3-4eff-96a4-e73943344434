{"version": 3, "sources": ["../../src/languages/zh.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const zhTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: '账号',\n    accountOfCurrentUser: '当前用户的账号',\n    accountVerified: '账号验证成功。',\n    alreadyActivated: '已经激活了',\n    alreadyLoggedIn: '已经登入了',\n    apiKey: 'API密钥',\n    authenticated: '已通过身份验证的请求',\n    backToLogin: '回到登录页面',\n    beginCreateFirstUser: '首先，请创建您的第一个用户。',\n    changePassword: '更改密码',\n    checkYourEmailForPasswordReset:\n      '如果此电子邮件地址已关联到一个账号，您将会很快收到重置密码的说明。如果您在收件箱中看不到此电子邮件，请检查您的垃圾邮件或垃圾邮件夹。',\n    confirmGeneration: '确认生成',\n    confirmPassword: '确认密码',\n    createFirstUser: '创建第一个用户',\n    emailNotValid: '所提供的电子邮件是无效的',\n    emailOrUsername: '电子邮件或用户名',\n    emailSent: '电子邮件已发送',\n    emailVerified: '电子邮件验证成功。',\n    enableAPIKey: '启用API密钥',\n    failedToUnlock: '解锁失败',\n    forceUnlock: '强制解锁',\n    forgotPassword: '忘记密码',\n    forgotPasswordEmailInstructions:\n      '请在下方输入您的电子邮件地址。您将会收到一封有关如何重置密码说明的电子邮件。',\n    forgotPasswordQuestion: '忘记密码？',\n    forgotPasswordUsernameInstructions:\n      '请在下方输入您的用户名。密码重置的说明将发送到与您的用户名相关联的电子邮箱。',\n    generate: '生成',\n    generateNewAPIKey: '生成新的API密钥',\n    generatingNewAPIKeyWillInvalidate: '生成新的API密钥将使之前的密钥<1>失效</1>。您确定要继续吗？',\n    lockUntil: '锁定至',\n    logBackIn: '重新登入',\n    loggedIn: '要使用另一个用户登录前，您需要先<0>登出</0>。',\n    loggedInChangePassword: '要更改您的密码，请到您的<0>账号</0>页面并在那里编辑您的密码。',\n    loggedOutInactivity: '您由于不活跃而被登出了。',\n    loggedOutSuccessfully: '您已成功登出。',\n    loggingOut: '正在登出...',\n    login: '登录',\n    loginAttempts: '登录次数',\n    loginUser: '登录用户',\n    loginWithAnotherUser: '要使用另一个用户登录前，您需要先<0>登出</0>。',\n    logOut: '登出',\n    logout: '登出',\n    logoutSuccessful: '成功登出。',\n    logoutUser: '登出用户',\n    newAccountCreated:\n      '刚刚为您创建了一个可以访问 <a href=\"{{serverURL}}\">{{serverURL}}</a> 的新账号 请点击以下链接或在浏览器中粘贴以下网址，以验证您的电子邮件: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> 验证您的电子邮件后，您将能够成功登录。',\n    newAPIKeyGenerated: '新的API密钥已经生成。',\n    newPassword: '新的密码',\n    passed: '身份验证通过',\n    passwordResetSuccessfully: '密码重置成功。',\n    resetPassword: '重置密码',\n    resetPasswordExpiration: '重置密码的有效期',\n    resetPasswordToken: '重置密码令牌',\n    resetYourPassword: '重置您的密码',\n    stayLoggedIn: '保持登录状态',\n    successfullyRegisteredFirstUser: '成功注册了第一个用户。',\n    successfullyUnlocked: '已成功解锁',\n    tokenRefreshSuccessful: '令牌刷新成功。',\n    unableToVerify: '无法验证',\n    username: '用户名',\n    usernameNotValid: '提供的用户名无效',\n    verified: '已验证',\n    verifiedSuccessfully: '成功验证',\n    verify: '验证',\n    verifyUser: '验证用户',\n    verifyYourEmail: '验证您的电子邮件',\n    youAreInactive:\n      '您已经有一段时间没有活动了，为了您的安全，很快就会自动登出。您想保持登录状态吗？',\n    youAreReceivingResetPassword:\n      '您收到此邮件是因为您（或其他人）已请求重置您账号的密码。请点击以下链接，或将其粘贴到您的浏览器中以完成该过程：',\n    youDidNotRequestPassword: '如果您没有要求这样做，请忽略这封邮件，您的密码将保持不变。',\n  },\n  error: {\n    accountAlreadyActivated: '该账号已被激活。',\n    autosaving: '自动保存该文档时出现了问题。',\n    correctInvalidFields: '请更正无效字段。',\n    deletingFile: '删除文件时出现了错误。',\n    deletingTitle: '删除{{title}}时出现了错误。请检查您的连接并重试。',\n    documentNotFound:\n      '无法找到ID为{{id}}的文档。可能是已经被删除，或者从未存在，或者您可能无法访问它。',\n    emailOrPasswordIncorrect: '提供的电子邮件或密码不正确。',\n    followingFieldsInvalid_one: '下面的字段是无效的：',\n    followingFieldsInvalid_other: '以下字段是无效的：',\n    incorrectCollection: '不正确的集合',\n    insufficientClipboardPermissions: '剪贴板访问被拒绝。请检查您的剪贴板权限。',\n    invalidClipboardData: '剪贴板数据无效。',\n    invalidFileType: '无效的文件类型',\n    invalidFileTypeValue: '无效的文件类型： {{value}}',\n    invalidRequestArgs: '请求中传递了无效的参数：{{args}}',\n    loadingDocument: '加载ID为{{id}}的文档时出现了问题。',\n    localesNotSaved_one: '无法保存以下语言环境设置：',\n    localesNotSaved_other: '无法保存以下语言环境设置：',\n    logoutFailed: '登出失败。',\n    missingEmail: '缺少电子邮件。',\n    missingIDOfDocument: '缺少需要更新的文档的ID。',\n    missingIDOfVersion: '缺少版本的ID。',\n    missingRequiredData: '缺少必要的数据。',\n    noFilesUploaded: '没有上传文件。',\n    noMatchedField: '找不到与\"{{label}}\"匹配的字段',\n    notAllowedToAccessPage: '您无权访问此页面。',\n    notAllowedToPerformAction: '您不被允许执行此操作。',\n    notFound: '没有找到请求的资源。',\n    noUser: '没有该用户',\n    previewing: '预览文档时出现了问题。',\n    problemUploadingFile: '上传文件时出现了问题。',\n    restoringTitle: '恢复{{title}}时出现错误。请检查您的连接并再试一次。',\n    tokenInvalidOrExpired: '令牌无效或已过期。',\n    tokenNotProvided: '未提供令牌。',\n    unableToCopy: '无法复制。',\n    unableToDeleteCount: '无法从 {{total}} {{label}} 中删除 {{count}}。',\n    unableToReindexCollection: '重新索引集合 {{collection}} 时出错。操作已中止。',\n    unableToUpdateCount: '无法更新 {{count}} 个，共 {{total}} 个 {{label}}。',\n    unauthorized: '未经授权，您必须登录才能提出这个请求。',\n    unauthorizedAdmin: '未经授权，此用户无权访问管理面板。',\n    unknown: '发生了一个未知的错误。',\n    unPublishingDocument: '取消发布此文档时出现了问题。',\n    unspecific: '发生了一个错误。',\n    unverifiedEmail: '请在登录前验证您的电子邮件。',\n    userEmailAlreadyRegistered: '给定电子邮件的用户已经注册。',\n    userLocked: '该用户由于有太多次失败的登录尝试而被锁定。',\n    usernameAlreadyRegistered: '已有用户使用了该用户名进行注册。',\n    usernameOrPasswordIncorrect: '提供的用户名或密码不正确。',\n    valueMustBeUnique: '值必须是唯一的',\n    verificationTokenInvalid: '验证令牌无效。',\n  },\n  fields: {\n    addLabel: '添加{{label}}',\n    addLink: '添加链接',\n    addNew: '添加新的',\n    addNewLabel: '添加新的{{label}}',\n    addRelationship: '添加关系',\n    addUpload: '添加上传',\n    block: '区块',\n    blocks: '区块',\n    blockType: '区块类型',\n    chooseBetweenCustomTextOrDocument: '选择输入一个自定义的文本URL或链接到另一个文档。',\n    chooseDocumentToLink: '选择一个要链接的文档',\n    chooseFromExisting: '从现有中选择',\n    chooseLabel: '选择{{label}}',\n    collapseAll: '全部折叠',\n    customURL: '自定义URL',\n    editLabelData: '编辑{{label}}数据',\n    editLink: '编辑链接',\n    editRelationship: '编辑关系',\n    enterURL: '输入一个URL',\n    internalLink: '内部链接',\n    itemsAndMore: '{{items}}和{{count}}更多',\n    labelRelationship: '{{label}}关系',\n    latitude: '纬度',\n    linkedTo: '链接到<0>{{label}}</0>',\n    linkType: '链接类型',\n    longitude: '经度',\n    newLabel: '新的{{label}}',\n    openInNewTab: '在新标签中打开',\n    passwordsDoNotMatch: '密码不匹配。',\n    relatedDocument: '相关文档',\n    relationTo: '关系到',\n    removeRelationship: '移除关系',\n    removeUpload: '移除上传',\n    saveChanges: '保存更改',\n    searchForBlock: '搜索一个区块',\n    selectExistingLabel: '选择现有的{{label}}',\n    selectFieldsToEdit: '选择要编辑的字段',\n    showAll: '显示全部',\n    swapRelationship: '交换关系',\n    swapUpload: '交换上传',\n    textToDisplay: '要显示的文本',\n    toggleBlock: '切换区块',\n    uploadNewLabel: '上传新的{{label}}',\n  },\n  folder: {\n    browseByFolder: '按文件夹浏览',\n    byFolder: '按文件夹',\n    deleteFolder: '删除文件夹',\n    folderName: '文件夹名称',\n    folders: '文件夹',\n    folderTypeDescription: '在此文件夹中选择应允许哪种类型的集合文档。',\n    itemHasBeenMoved: '{{title}}已被移至{{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}}已被移至根文件夹',\n    itemsMovedToFolder: '{{title}}已移至{{folderName}}',\n    itemsMovedToRoot: '{{title}}已移至根文件夹',\n    moveFolder: '移动文件夹',\n    moveItemsToFolderConfirmation:\n      '您即将把<1>{{count}} {{label}}</1>移动到<2>{{toFolder}}</2>。您确定吗？',\n    moveItemsToRootConfirmation: '您即将把<1>{{count}} {{label}}</1>移动到根文件夹。您确定吗？',\n    moveItemToFolderConfirmation: '您即将把<1>{{title}}</1>移动至<2>{{toFolder}}</2>。您确定吗？',\n    moveItemToRootConfirmation: '您即将把<1>{{title}}</1>移动到根文件夹。您确定吗？',\n    movingFromFolder: '正在将{{title}}从{{fromFolder}}文件夹中移动',\n    newFolder: '新文件夹',\n    noFolder: '没有文件夹',\n    renameFolder: '重命名文件夹',\n    searchByNameInFolder: '在{{folderName}}中按名称搜索',\n    selectFolderForItem: '选择文件夹用于{{title}}',\n  },\n  general: {\n    name: '名称',\n    aboutToDelete: '您即将删除{{label}} <1>{{title}}</1>。您确定要继续吗？',\n    aboutToDeleteCount_many: '您即将删除 {{count}}个{{label}}',\n    aboutToDeleteCount_one: '您即将删除 {{count}}个{{label}}',\n    aboutToDeleteCount_other: '您即将删除 {{count}}个{{label}}',\n    aboutToPermanentlyDelete: '您即将永久删除{{label}} <1>{{title}}</1>。你确定吗？',\n    aboutToPermanentlyDeleteTrash:\n      '您即将从垃圾箱中永久删除<0>{{count}}</0> <1>{{label}}</1>。你确定吗？',\n    aboutToRestore: '您即将恢复{{label}} <1>{{title}}</1>。你确定吗？',\n    aboutToRestoreAsDraft: '您即将将{{label}} <1>{{title}}</1> 恢复为草稿。您确定吗？',\n    aboutToRestoreAsDraftCount: '您即将将 {{count}} {{label}} 恢复为草稿',\n    aboutToRestoreCount: '您即将恢复 {{count}} {{label}}',\n    aboutToTrash: '您即将将 {{label}} <1>{{title}}</1> 移至垃圾箱。您确定吗？',\n    aboutToTrashCount: '您即将将{{count}}个{{label}}移至垃圾箱',\n    addBelow: '添加到下面',\n    addFilter: '添加过滤条件',\n    adminTheme: '管理页面主题',\n    all: '所有',\n    allCollections: '所有集合',\n    allLocales: '所有语言环境',\n    and: '和',\n    anotherUser: '另一位用户',\n    anotherUserTakenOver: '另一位用户接管了此文档的编辑。',\n    applyChanges: '应用更改',\n    ascending: '升序',\n    automatic: '自动',\n    backToDashboard: '返回到仪表板',\n    cancel: '取消',\n    changesNotSaved: '您的更改尚未保存。您确定要离开吗？',\n    clear: '清晰',\n    clearAll: '清除全部',\n    close: '关闭',\n    collapse: '折叠',\n    collections: '集合',\n    columns: '列',\n    columnToSort: '要排序的列',\n    confirm: '确认',\n    confirmCopy: '确认复制',\n    confirmDeletion: '确认删除',\n    confirmDuplication: '确认重复',\n    confirmMove: '确认移动',\n    confirmReindex: '重新索引所有{{collections}}?',\n    confirmReindexAll: '重新索引所有集合?',\n    confirmReindexDescription: '此操作将删除现有索引，并重新索引{{collections}}集合中的文档。',\n    confirmReindexDescriptionAll: '此操作将删除现有索引，并重新索引所有集合中的文档。',\n    confirmRestoration: '确认恢复',\n    copied: '已复制',\n    copy: '复制',\n    copyField: '复制字段',\n    copying: '复制中',\n    copyRow: '复制行',\n    copyWarning: '您即将用{{from}}覆盖{{to}}，用于{{label}} {{title}}。您确定吗？',\n    create: '创建',\n    created: '已创建',\n    createdAt: '创建于',\n    createNew: '创建新条目',\n    createNewLabel: '创建新的{{label}}',\n    creating: '创建中',\n    creatingNewLabel: '正在创建新的{{label}}',\n    currentlyEditing:\n      '当前正在编辑此文档。如果您接管，他们将无法继续编辑，并且可能会丢失未保存的更改。',\n    custom: '自定义',\n    dark: '深色',\n    dashboard: '仪表板',\n    delete: '删除',\n    deleted: '已删除',\n    deletedAt: '已删除时间',\n    deletedCountSuccessfully: '已成功删除 {{count}} {{label}}。',\n    deletedSuccessfully: '已成功删除。',\n    deletePermanently: '跳过垃圾箱并永久删除',\n    deleting: '删除中...',\n    depth: '深度',\n    descending: '降序',\n    deselectAllRows: '取消选择所有行',\n    document: '文档',\n    documentIsTrashed: '此 {{label}} 已被丢弃，为只读状态。',\n    documentLocked: '文档已锁定',\n    documents: '文档',\n    duplicate: '复制',\n    duplicateWithoutSaving: '复制副本(不保存更改)。',\n    edit: '编辑',\n    editAll: '编辑全部',\n    editedSince: '自...以来编辑',\n    editing: '编辑中',\n    editingLabel_many: '正在编辑 {{count}}个{{label}}',\n    editingLabel_one: '正在编辑 {{count}}个{{label}}',\n    editingLabel_other: '正在编辑 {{count}}个{{label}}',\n    editingTakenOver: '编辑已被接管',\n    editLabel: '编辑{{label}}',\n    email: '电子邮件',\n    emailAddress: '电子邮件地址',\n    emptyTrash: '清空垃圾桶',\n    emptyTrashLabel: '清空 {{label}} 垃圾箱',\n    enterAValue: '输入一个值',\n    error: '错误',\n    errors: '错误',\n    exitLivePreview: '退出实时预览',\n    export: '导出',\n    fallbackToDefaultLocale: '回退到默认语言环境',\n    false: '否',\n    filter: '过滤条件',\n    filters: '过滤条件',\n    filterWhere: '过滤{{label}}',\n    globals: '全局',\n    goBack: '返回',\n    groupByLabel: '按{{label}}分组',\n    import: '导入',\n    isEditing: '正在编辑',\n    item: '条目',\n    items: '条目',\n    language: '语言',\n    lastModified: '最后修改',\n    leaveAnyway: '无论如何都要离开',\n    leaveWithoutSaving: '离开而不保存',\n    light: '亮色',\n    livePreview: '实时预览',\n    loading: '加载中...',\n    locale: '语言环境',\n    locales: '语言环境',\n    menu: '菜单',\n    moreOptions: '更多选项',\n    move: '移动',\n    moveConfirm: '您即将把{{count}}个{{label}}移动到<1>{{destination}}</1>。您确定吗？',\n    moveCount: '移动 {{count}}个{{label}}',\n    moveDown: '向下移动',\n    moveUp: '向上移动',\n    moving: '移动',\n    movingCount: '移动 {{count}}个{{label}}',\n    newPassword: '新密码',\n    next: '下一个',\n    no: '否',\n    noDateSelected: '未选择日期',\n    noFiltersSet: '没有设置过滤条件',\n    noLabel: '<没有{{label}}>',\n    none: '无',\n    noOptions: '没有选项',\n    noResults: '没有找到{{label}}。{{label}}并不存在或没有符合您上面所指定的过滤条件。',\n    notFound: '未找到',\n    nothingFound: '没有找到任何东西',\n    noTrashResults: '回收站中没有 {{label}}。',\n    noUpcomingEventsScheduled: '没有即将进行的活动计划。',\n    noValue: '没有值',\n    of: '共',\n    only: '仅',\n    open: '打开',\n    or: '或',\n    order: '排序',\n    overwriteExistingData: '覆盖现有字段数据',\n    pageNotFound: '未找到页面',\n    password: '密码',\n    pasteField: '粘贴字段',\n    pasteRow: '粘贴行',\n    payloadSettings: 'Payload设置',\n    permanentlyDelete: '永久删除',\n    permanentlyDeletedCountSuccessfully: '已成功永久删除 {{count}} {{label}}。',\n    perPage: '每一页: {{limit}}',\n    previous: '前一个',\n    reindex: '重新索引',\n    reindexingAll: '正在重新索引所有{{collections}}。',\n    remove: '移除',\n    rename: '重命名',\n    reset: '重置',\n    resetPreferences: '重置偏好设置',\n    resetPreferencesDescription: '这将把您的所有偏好设置恢复为默认值。',\n    resettingPreferences: '正在重置偏好设置。',\n    restore: '恢复',\n    restoreAsPublished: '恢复为已发布版本',\n    restoredCountSuccessfully: '成功恢复了{{count}} {{label}}。',\n    restoring: '恢复中...',\n    row: '行',\n    rows: '行',\n    save: '保存',\n    saving: '保存中...',\n    schedulePublishFor: '为{{title}}安排发布时间',\n    searchBy: '搜索{{label}}',\n    select: '选择',\n    selectAll: '选择所有 {{count}}个{{label}}',\n    selectAllRows: '选择所有行',\n    selectedCount: '已选择 {{count}}个{{label}}',\n    selectLabel: '选择{{label}}',\n    selectValue: '选择一个值',\n    showAllLabel: '显示所有{{label}}',\n    sorryNotFound: '对不起，没有与您的请求相对应的东西。',\n    sort: '排序',\n    sortByLabelDirection: '按{{label}} {{direction}}排序',\n    stayOnThisPage: '停留在此页面',\n    submissionSuccessful: '提交成功。',\n    submit: '提交',\n    submitting: '提交中...',\n    success: '成功',\n    successfullyCreated: '成功创建{{label}}',\n    successfullyDuplicated: '成功复制{{label}}',\n    successfullyReindexed:\n      '成功重新索引了 {{collections}} 集合中 {{total}} 个文档中的 {{count}} 个。',\n    takeOver: '接管',\n    thisLanguage: '中文 (简体)',\n    time: '时间',\n    timezone: '时区',\n    titleDeleted: '{{label}} \"{{title}}\"已被成功删除。',\n    titleRestored: '\"{{label}}\" \"{{title}}\" 成功恢复。',\n    titleTrashed: '{{label}} \"{{title}}\" 已移至垃圾桶。',\n    trash: '垃圾',\n    trashedCountSuccessfully: '{{count}} {{label}} 被移至垃圾桶。',\n    true: '是',\n    unauthorized: '未经授权',\n    unsavedChanges: '您有未保存的更改。请在继续之前保存或放弃。',\n    unsavedChangesDuplicate: '您有未保存的修改。您确定要继续复制吗？',\n    untitled: '无标题',\n    upcomingEvents: '即将到来的活动',\n    updatedAt: '更新于',\n    updatedCountSuccessfully: '已成功更新 {{count}}个{{label}}。',\n    updatedLabelSuccessfully: '成功更新了 {{label}}。',\n    updatedSuccessfully: '更新成功。',\n    updateForEveryone: '更新给所有用户',\n    updating: '更新中',\n    uploading: '上传中',\n    uploadingBulk: '正在上传{{current}}，共{{total}}',\n    user: '用户',\n    username: '用户名',\n    users: '用户',\n    value: '值',\n    viewing: '查看',\n    viewReadOnly: '只读查看',\n    welcome: '欢迎',\n    yes: '是的',\n  },\n  localization: {\n    cannotCopySameLocale: '无法复制到相同的语言环境',\n    copyFrom: '复制自',\n    copyFromTo: '从{{from}}复制到{{to}}',\n    copyTo: '复制到',\n    copyToLocale: '复制到指定语言环境',\n    localeToPublish: '发布的语言环境',\n    selectLocaleToCopy: '选择要复制的语言环境',\n  },\n  operators: {\n    contains: '包含',\n    equals: '等于',\n    exists: '是否存在',\n    intersects: '相交',\n    isGreaterThan: '大于',\n    isGreaterThanOrEqualTo: '大于等于',\n    isIn: '在...中',\n    isLessThan: '小于',\n    isLessThanOrEqualTo: '小于或等于',\n    isLike: '模糊匹配',\n    isNotEqualTo: '不等于',\n    isNotIn: '不在...中',\n    isNotLike: '模糊不匹配',\n    near: '在...附近',\n    within: '在...之内',\n  },\n  upload: {\n    addFile: '添加文件',\n    addFiles: '添加文件',\n    bulkUpload: '批量上传',\n    crop: '裁剪',\n    cropToolDescription: '拖动所选区域的角落，绘制一个新区域或调整以下的值。',\n    download: '下载',\n    dragAndDrop: '拖放一个文件',\n    dragAndDropHere: '或在这里拖放一个文件',\n    editImage: '编辑图像',\n    fileName: '文件名',\n    fileSize: '文件大小',\n    filesToUpload: '要上传的文件',\n    fileToUpload: '上传文件',\n    focalPoint: '焦点',\n    focalPointDescription: '直接在预览中拖动焦点或调整下面的值。',\n    height: '高度',\n    lessInfo: '更少信息',\n    moreInfo: '更多信息',\n    noFile: '没有文件',\n    pasteURL: '粘贴网址',\n    previewSizes: '预览尺寸',\n    selectCollectionToBrowse: '选择一个要浏览的集合',\n    selectFile: '选择一个文件',\n    setCropArea: '设置裁剪区域',\n    setFocalPoint: '设置焦点',\n    sizes: '尺寸',\n    sizesFor: '{{label}}的尺寸',\n    width: '宽度',\n  },\n  validation: {\n    emailAddress: '请输入一个有效的电子邮件地址。',\n    enterNumber: '请输入一个有效的数字。',\n    fieldHasNo: '这个字段没有{{label}}',\n    greaterThanMax: '{{value}}超过了允许的最大{{label}}，该最大值为{{max}}。',\n    invalidInput: '这个字段有一个无效的输入。',\n    invalidSelection: '这个字段有一个无效的选择。',\n    invalidSelections: '这个字段有以下无效的选择：',\n    lessThanMin: '{{value}}小于允许的最小{{label}}，该最小值为{{min}}。',\n    limitReached: '已达到最大限制，只能添加{{max}}个条目。',\n    longerThanMin: '该值必须大于{{minLength}}字符的最小长度',\n    notValidDate: '\"{{value}}\"不是一个有效的日期。',\n    required: '该字段为必填项目。',\n    requiresAtLeast: '该字段至少需要{{count}}个{{label}}。',\n    requiresNoMoreThan: '该字段要求不超过{{count}}个{{label}}。',\n    requiresTwoNumbers: '该字段需要两个数字。',\n    shorterThanMax: '该值必须小于{{maxLength}}字符的最大长度',\n    timezoneRequired: '需要选择一个时区。',\n    trueOrFalse: '此项仅可选择\"是\"或\"否\"。',\n    username: '请输入一个有效的用户名。可包含字母，数字，连字符，句点和下划线。',\n    validUploadID: '该字段不是有效的上传ID。',\n  },\n  version: {\n    type: '类型',\n    aboutToPublishSelection: '您即将发布所选内容中的所有 {{label}}。 您确定吗？',\n    aboutToRestore: '您将把这个{{label}}文档恢复到{{versionDate}}时的状态',\n    aboutToRestoreGlobal: '您要将全局的{{label}}恢复到{{versionDate}}时的状态',\n    aboutToRevertToPublished: '您将要把这个文档的内容还原到它的发布状态。您确定吗？',\n    aboutToUnpublish: '您即将取消发布这个文档。您确定吗？',\n    aboutToUnpublishSelection: '您即将取消发布所选内容中的所有 {{label}}。 您确定吗？',\n    autosave: '自动保存',\n    autosavedSuccessfully: '自动保存成功。',\n    autosavedVersion: '自动保存的版本',\n    changed: '已更改',\n    changedFieldsCount_one: '{{count}}个字段已更改',\n    changedFieldsCount_other: '{{count}}个字段已更改',\n    compareVersion: '对比版本：',\n    compareVersions: '比较版本',\n    comparingAgainst: '与之比较',\n    confirmPublish: '确认发布',\n    confirmRevertToSaved: '确认恢复到保存状态',\n    confirmUnpublish: '确认取消发布',\n    confirmVersionRestoration: '确认版本恢复',\n    currentDocumentStatus: '当前{{docStatus}}文档',\n    currentDraft: '当前草稿',\n    currentlyPublished: '当前已发布',\n    currentlyViewing: '当前正在查看',\n    currentPublishedVersion: '当前发布的版本',\n    draft: '草稿',\n    draftSavedSuccessfully: '草稿成功保存。',\n    lastSavedAgo: '上次保存{{distance}}之前',\n    modifiedOnly: '仅修改过的',\n    moreVersions: '更多版本...',\n    noFurtherVersionsFound: '没有发现其他版本',\n    noRowsFound: '没有发现{{label}}',\n    noRowsSelected: '未选择{{label}}',\n    preview: '预览',\n    previouslyDraft: '以前的草稿',\n    previouslyPublished: '先前发布过的',\n    previousVersion: '以前的版本',\n    problemRestoringVersion: '恢复这个版本时发生了问题',\n    publish: '发布',\n    publishAllLocales: '发布所有语言环境',\n    publishChanges: '发布修改',\n    published: '已发布',\n    publishIn: '在{{locale}}发布',\n    publishing: '发布',\n    restoreAsDraft: '恢复为草稿',\n    restoredSuccessfully: '恢复成功。',\n    restoreThisVersion: '恢复此版本',\n    restoring: '恢复中...',\n    reverting: '还原中...',\n    revertToPublished: '还原到已发布的版本',\n    saveDraft: '保存草稿',\n    scheduledSuccessfully: '预约发布成功。',\n    schedulePublish: '预约发布',\n    selectLocales: '选择要显示的语言环境',\n    selectVersionToCompare: '选择要比较的版本',\n    showingVersionsFor: '显示版本为：',\n    showLocales: '显示语言环境：',\n    specificVersion: '特定版本',\n    status: '状态',\n    unpublish: '取消发布',\n    unpublishing: '取消发布中...',\n    version: '版本',\n    versionAgo: '{{distance}}前',\n    versionCount_many: '发现{{count}}版本',\n    versionCount_none: '没有发现任何版本',\n    versionCount_one: '找到{{count}}版本',\n    versionCount_other: '找到{{count}}版本',\n    versionCreatedOn: '{{version}}创建于：',\n    versionID: '版本ID',\n    versions: '版本',\n    viewingVersion: '正在查看{{entityLabel}} {{documentTitle}}的版本',\n    viewingVersionGlobal: '正在查看全局{{entityLabel}}的版本',\n    viewingVersions: '正在查看{{entityLabel}} {{documentTitle}}的版本',\n    viewingVersionsGlobal: '正在查看全局{{entityLabel}}的版本',\n  },\n}\n\nexport const zh: Language = {\n  dateFNSKey: 'zh-CN',\n  translations: zhTranslations,\n}\n"], "names": ["zhTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "zh", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCAAmC;QACnCC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BAA0B;IAC5B;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eAAe;QACfC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCAAkC;QAClCC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBAAgB;QAChBC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BAA2B;QAC3BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCAAmC;QACnCC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BAA6B;QAC7BC,8BAA8B;QAC9BC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,+BACE;QACFC,gBAAgB;QAChBC,uBAAuB;QACvBC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BAA2B;QAC3BC,8BAA8B;QAC9BC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WAAW;QACX9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBAAqB;QACrBC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UAAU;QACVga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BAA0B;QAC1BC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}