export const deTranslations = {
    authentication: {
        account: '<PERSON>utzerkonto',
        accountOfCurrentUser: 'Aktuel<PERSON> Benutzerkonto',
        accountVerified: 'Benutzerkonto erfolgreich verifiziert.',
        alreadyActivated: 'Bereits aktiviert',
        alreadyLoggedIn: 'Bereits angemeldet',
        apiKey: 'API-Key',
        authenticated: 'Authentifiziert',
        backToLogin: 'Zurück zur Anmeldung',
        beginCreateFirstUser: 'Erstelle deinen ersten <PERSON>utzer, um zu beginnen',
        changePassword: 'Passwort ändern',
        checkYourEmailForPasswordReset: 'Wenn die E-Mail-Adresse mit einem Benutzerkonto verknüpft ist, erhältst du in Kürze Anweisungen zur Zurücksetzung deines Passworts. Bitte überprüfe deinen Spam-Ordner, wenn du die E-Mail nicht in deinem Posteingang siehst.',
        confirmGeneration: 'Generierung bestätigen',
        confirmPassword: 'Passwort bestätigen',
        createFirstUser: '<PERSON><PERSON> erste<PERSON>',
        emailNotValid: 'Die angegebene E-Mail-Adresse ist ungültig',
        emailOrUsername: 'E-Mail oder Benutzername',
        emailSent: 'E-Mail versendet',
        emailVerified: 'E-Mail erfolgreich verifiziert.',
        enableAPIKey: 'API-Key aktivieren',
        failedToUnlock: 'Entsperrung fehlgeschlagen',
        forceUnlock: 'Entsperrung erzwingen',
        forgotPassword: 'Passwort vergessen',
        forgotPasswordEmailInstructions: 'Bitte gib deine E-Mail-Adresse an. Du wirst eine E-Mail mit Instruktionen zum Zurücksetzen deines Passworts erhalten.',
        forgotPasswordQuestion: 'Passwort vergessen?',
        forgotPasswordUsernameInstructions: 'Bitte gib deinen Benutzernamen ein. Anweisungen zum Zurücksetzen deines Passworts werden an die mit deinem Benutzernamen verknüpfte E-Mail-Adresse gesendet.',
        generate: 'Generieren',
        generateNewAPIKey: 'Neuen API-Key generieren',
        generatingNewAPIKeyWillInvalidate: 'Wenn du einen neuen Key generierst, wird der vorherige <1>ungültig</1>. Bist du sicher, dass du fortfahren möchtest?',
        lockUntil: 'Sperren bis',
        logBackIn: 'Wieder anmelden',
        loggedIn: 'Um dich mit einem anderen Benutzerkonto anzumelden, musst du dich zuerst <0>abmelden</0>.',
        loggedInChangePassword: 'Um dein Passwort zu ändern, gehe zu deinem <0>Benutzerkonto</0> und ändere dort dein Passwort.',
        loggedOutInactivity: 'Du wurdest aufgrund von Inaktivität abgemeldet.',
        loggedOutSuccessfully: 'Du wurdest erfolgreich abgemeldet.',
        loggingOut: 'Abmelden...',
        login: 'Anmelden',
        loginAttempts: 'Anmelde-Versuche',
        loginUser: 'Benutzeranmeldung',
        loginWithAnotherUser: 'Um dich mit einem anderen Benutzer anzumelden, musst du dich zuerst <0>abmelden</0>.',
        logOut: 'Abmelden',
        logout: 'Abmelden',
        logoutSuccessful: 'Abmeldung erfolgreich.',
        logoutUser: 'Benutzer abmelden',
        newAccountCreated: 'Ein neues Konto wurde gerade für dich auf <a href="{{serverURL}}">{{serverURL}}</a> erstellt. Bitte klicke auf den folgenden Link oder kopiere die URL in deinen Browser, um deine E-Mail-Adresse zu verifizieren: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Nachdem du deine E-Mail-Adresse verifiziert hast, kannst du dich erfolgreich anmelden.',
        newAPIKeyGenerated: 'Neuer API-Key wurde generiert',
        newPassword: 'Neues Passwort',
        passed: 'Authentifizierung erfolgreich',
        passwordResetSuccessfully: 'Passwort erfolgreich zurückgesetzt.',
        resetPassword: 'Passwort zurücksetzen',
        resetPasswordExpiration: 'Passwort-Gültigkeitsdauer zurücksetzen',
        resetPasswordToken: 'Passwort-Token zurücksetzen',
        resetYourPassword: 'Dein Passwort zurücksetzen',
        stayLoggedIn: 'Angemeldet bleiben',
        successfullyRegisteredFirstUser: 'Ersten Benutzer erfolgreich registriert.',
        successfullyUnlocked: 'Erfolgreich entsperrt',
        tokenRefreshSuccessful: 'Token-Aktualisierung erfolgreich.',
        unableToVerify: 'Konnte nicht verifiziert werden',
        username: 'Benutzername',
        usernameNotValid: 'Der angegebene Benutzername ist nicht gültig.',
        verified: 'Verifiziert',
        verifiedSuccessfully: 'Erfolgreich verifiziert',
        verify: 'Verifizieren',
        verifyUser: 'Benutzer verifizieren',
        verifyYourEmail: 'Deine E-Mail-Adresse verifizieren',
        youAreInactive: 'Du warst einige Zeit inaktiv und wirst in Kürze zu deiner eigenen Sicherheit abgemeldet. Möchtest du angemeldet bleiben?',
        youAreReceivingResetPassword: 'Du erhältst diese Nachricht, weil du (oder jemand anderes) das Zurücksetzen deines Passworts für dein Benutzerkonto angefordert hat. Bitte klicke auf den folgenden Link, oder kopiere die URL in deinen Browser, um den Prozess abzuschließen:',
        youDidNotRequestPassword: 'Solltest du dies nicht angefordert haben, ignoriere diese E-Mail und dein Passwort bleibt unverändert.'
    },
    error: {
        accountAlreadyActivated: 'Dieses Benutzerkonto wurde bereits aktiviert',
        autosaving: 'Es gab ein Problem bei der automatischen Speicherung für dieses Dokument',
        correctInvalidFields: 'Bitte ungültige Felder korrigieren.',
        deletingFile: 'Beim Löschen der Datei ist ein Fehler aufgetreten.',
        deletingTitle: 'Es gab ein Problem während der Löschung von {{title}}. Bitte überprüfe deine Verbindung und versuche es erneut.',
        documentNotFound: 'Das Dokument mit der ID {{id}} konnte nicht gefunden werden. Es könnte gelöscht oder niemals existiert haben, oder Sie haben möglicherweise keinen Zugang dazu.',
        emailOrPasswordIncorrect: 'Die E-Mail-Adresse oder das Passwort sind nicht korrekt.',
        followingFieldsInvalid_one: 'Das folgende Feld ist nicht korrekt:',
        followingFieldsInvalid_other: 'Die folgenden Felder sind nicht korrekt:',
        incorrectCollection: 'Falsche Sammlung',
        insufficientClipboardPermissions: 'Zugriff auf die Zwischenablage verweigert. Bitte überprüfen Sie die Berechtigungen.',
        invalidClipboardData: 'Ungültige Zwischenablagedaten.',
        invalidFileType: 'Ungültiger Datei-Typ',
        invalidFileTypeValue: 'Ungültiger Datei-Typ: {{value}}',
        invalidRequestArgs: 'Ungültige Argumente in der Anfrage: {{args}}',
        loadingDocument: 'Es gab ein Problem, das Dokument mit der ID {{id}} zu laden.',
        localesNotSaved_one: 'Die folgende Sprache konnte nicht gespeichert werden:',
        localesNotSaved_other: 'Die folgenden Sprachen konnten nicht gespeichert werden:',
        logoutFailed: 'Abmeldung fehlgeschlagen.',
        missingEmail: 'E-Mail-Adresse fehlt.',
        missingIDOfDocument: 'ID des zu speichernden Dokuments fehlt.',
        missingIDOfVersion: 'ID der Version fehlt.',
        missingRequiredData: 'Erforderliche Daten fehlen.',
        noFilesUploaded: 'Es wurden keine Dateien hochgeladen.',
        noMatchedField: 'Kein übereinstimmendes Feld für "{{label}}" gefunden',
        notAllowedToAccessPage: 'Du hast keine Berechtigung, auf diese Seite zuzugreifen.',
        notAllowedToPerformAction: 'Du hast keine Berechtigung, diese Aktion auszuführen.',
        notFound: 'Die angeforderte Ressource wurde nicht gefunden.',
        noUser: 'Kein Benutzer',
        previewing: 'Bei der Vorschau dieses Dokuments ist ein Fehler aufgetreten.',
        problemUploadingFile: 'Beim Hochladen der Datei ist ein Fehler aufgetreten.',
        restoringTitle: 'Es gab einen Fehler beim Wiederherstellen von {{title}}. Bitte überprüfen Sie Ihre Verbindung und versuchen Sie es erneut.',
        tokenInvalidOrExpired: 'Token ist entweder ungültig oder abgelaufen.',
        tokenNotProvided: 'Token nicht bereitgestellt.',
        unableToCopy: 'Kopieren nicht möglich.',
        unableToDeleteCount: '{{count}} von {{total}} {{label}} konnte nicht gelöscht werden.',
        unableToReindexCollection: 'Fehler beim Neuindizieren der Sammlung {{collection}}. Vorgang abgebrochen.',
        unableToUpdateCount: '{{count}} von {{total}} {{label}} konnten nicht aktualisiert werden.',
        unauthorized: 'Nicht autorisiert - du musst angemeldet sein, um diese Anfrage zu stellen.',
        unauthorizedAdmin: 'Nicht autorisiert, dieser Benutzer hat keinen Zugriff auf das Admin-Panel.',
        unknown: 'Ein unbekannter Fehler ist aufgetreten.',
        unPublishingDocument: 'Bei der Aufhebung der Veröffentlichung dieses Dokuments ist ein Fehler aufgetreten.',
        unspecific: 'Ein Fehler ist aufgetreten.',
        unverifiedEmail: 'Bitte verifiziere deine E-Mail, bevor du dich anmeldest.',
        userEmailAlreadyRegistered: 'Ein Benutzer mit der angegebenen E-Mail ist bereits registriert.',
        userLocked: 'Dieser Benutzer ist gesperrt, weil er zu viele fehlgeschlagene Anmeldeversuche hat.',
        usernameAlreadyRegistered: 'Ein Benutzer mit dem angegebenen Benutzernamen ist bereits registriert.',
        usernameOrPasswordIncorrect: 'Der angegebene Benutzername oder das Passwort ist falsch.',
        valueMustBeUnique: 'Wert muss einzigartig sein',
        verificationTokenInvalid: 'Verifizierungs-Token ist nicht korrekt.'
    },
    fields: {
        addLabel: '{{label}} hinzufügen',
        addLink: 'Link hinzufügen',
        addNew: 'Neuen Eintrag hinzufügen',
        addNewLabel: '{{label}} erstellen',
        addRelationship: 'Verknüpfung hinzufügen',
        addUpload: 'Neue Datei hochladen',
        block: 'Block',
        blocks: 'Blöcke',
        blockType: 'Block-Typ',
        chooseBetweenCustomTextOrDocument: 'Wähle zwischen der Eingabe einer benutzerdefinierten URL oder verknüpfe ein anderes Dokument.',
        chooseDocumentToLink: 'Wähle ein Dokument, das du verlinken möchtest',
        chooseFromExisting: 'Aus vorhandenen auswählen',
        chooseLabel: '{{label}} auswählen',
        collapseAll: 'Alle einklappen',
        customURL: 'Eigene URL',
        editLabelData: '{{label}} bearbeiten',
        editLink: 'Link bearbeiten',
        editRelationship: 'Verknüpfung bearbeiten',
        enterURL: 'URL eingeben',
        internalLink: 'Interne Verlinkung',
        itemsAndMore: '{{items}} und {{count}} mehr',
        labelRelationship: '{{label}}-Verknüpfung',
        latitude: 'Breitengrad',
        linkedTo: 'Verweist auf <0>{{label}}</0>',
        linkType: 'Linktyp',
        longitude: 'Längengrad',
        newLabel: '{{label}} erstellen',
        openInNewTab: 'In neuem Tab öffnen',
        passwordsDoNotMatch: 'Passwörter stimmen nicht überein.',
        relatedDocument: 'Verknüpftes Dokument',
        relationTo: 'Verknüpfung zu',
        removeRelationship: 'Verknüpfung entfernen',
        removeUpload: 'Hochgeladene Datei löschen',
        saveChanges: 'Änderungen speichern',
        searchForBlock: 'Nach Block suchen',
        selectExistingLabel: '{{label}} auswählen (vorhandene)',
        selectFieldsToEdit: 'Wähle die zu bearbeitenden Felder aus',
        showAll: 'Alle anzeigen',
        swapRelationship: 'Verknüpfung tauschen',
        swapUpload: 'Datei austauschen',
        textToDisplay: 'Angezeigter Text',
        toggleBlock: 'Block umschalten',
        uploadNewLabel: '{{label}} neu hochladen'
    },
    folder: {
        browseByFolder: 'Durchsuchen nach Ordner',
        byFolder: 'Nach Ordner',
        deleteFolder: 'Ordner löschen',
        folderName: 'Ordnername',
        folders: 'Ordner',
        folderTypeDescription: 'Wählen Sie aus, welche Art von Sammlungsdokumenten in diesem Ordner zugelassen sein sollte.',
        itemHasBeenMoved: '{{title}} wurde in {{folderName}} verschoben.',
        itemHasBeenMovedToRoot: '{{title}} wurde in den Hauptordner verschoben',
        itemsMovedToFolder: '{{title}} wurde in {{folderName}} verschoben.',
        itemsMovedToRoot: '{{title}} wurde in den Stammordner verschoben',
        moveFolder: 'Ordner verschieben',
        moveItemsToFolderConfirmation: 'Sie sind dabei, <1>{{count}} {{label}}</1> nach <2>{{toFolder}}</2> zu verschieben. Sind Sie sicher?',
        moveItemsToRootConfirmation: 'Sie sind dabei, <1>{{count}} {{label}}</1> in den Hauptordner zu verschieben. Sind Sie sicher?',
        moveItemToFolderConfirmation: 'Sie sind dabei, <1>{{title}}</1> zu <2>{{toFolder}}</2> zu verschieben. Sind Sie sicher?',
        moveItemToRootConfirmation: 'Sie sind dabei, <1>{{title}}</1> in den Hauptordner zu verschieben. Sind Sie sicher?',
        movingFromFolder: 'Verschieben von {{title}} aus {{fromFolder}}',
        newFolder: 'Neuer Ordner',
        noFolder: 'Kein Ordner',
        renameFolder: 'Ordner umbenennen',
        searchByNameInFolder: 'Suche nach Name in {{folderName}}',
        selectFolderForItem: 'Wählen Sie den Ordner für {{title}}'
    },
    general: {
        name: 'Name',
        aboutToDelete: 'Du bist dabei {{label}} <1>{{title}}</1> zu löschen. Bist du dir sicher?',
        aboutToDeleteCount_many: 'Du bist dabei, {{count}} {{label}} zu löschen',
        aboutToDeleteCount_one: 'Du bist dabei, {{count}} {{label}} zu löschen',
        aboutToDeleteCount_other: 'Du bist dabei, {{count}} {{label}} zu löschen',
        aboutToPermanentlyDelete: 'Sie sind im Begriff, das {{label}} <1>{{title}}</1> dauerhaft zu löschen. Sind Sie sicher?',
        aboutToPermanentlyDeleteTrash: 'Sie sind dabei, <0>{{count}}</0> <1>{{label}}</1> endgültig aus dem Papierkorb zu löschen. Sind Sie sicher?',
        aboutToRestore: 'Sie sind dabei, das {{label}} <1>{{title}}</1> wiederherzustellen. Sind Sie sicher?',
        aboutToRestoreAsDraft: 'Sie sind dabei, das {{label}} <1>{{title}}</1> als Entwurf wiederherzustellen. Sind Sie sicher?',
        aboutToRestoreAsDraftCount: 'Sie sind dabei, {{count}} {{label}} als Entwurf wiederherzustellen.',
        aboutToRestoreCount: 'Sie sind dabei {{count}} {{label}} wiederherzustellen',
        aboutToTrash: 'Sie sind dabei, das {{label}} <1>{{title}}</1> in den Papierkorb zu verschieben. Sind Sie sicher?',
        aboutToTrashCount: 'Sie sind dabei, {{count}} {{label}} in den Papierkorb zu verschieben.',
        addBelow: 'Unterhalb hinzufügen',
        addFilter: 'Filter hinzufügen',
        adminTheme: 'Admin-Erscheinungsbild',
        all: 'Alle',
        allCollections: 'Alle Sammlungen',
        allLocales: 'Alle Standorte',
        and: 'Und',
        anotherUser: 'Ein anderer Benutzer',
        anotherUserTakenOver: 'Ein anderer Benutzer hat die Bearbeitung dieses Dokuments übernommen.',
        applyChanges: 'Änderungen anwenden',
        ascending: 'Aufsteigend',
        automatic: 'Automatisch',
        backToDashboard: 'Zurück zur Übersicht',
        cancel: 'Abbrechen',
        changesNotSaved: 'Deine Änderungen wurden nicht gespeichert. Wenn du diese Seite verlässt, gehen deine Änderungen verloren.',
        clear: 'Respektieren Sie die Bedeutung des ursprünglichen Textes im Kontext von Payload. Hier ist eine Liste von gängigen Payload-Begriffen, die sehr spezifische Bedeutungen tragen:\n    - Sammlung: Eine Sammlung ist eine Gruppe von Dokumenten, die eine gemeinsame Struktur und Funktion teilen. Sammlungen werden verwendet, um Inhalte in Payload zu organisieren und zu verwalten.\n    - Feld: Ein Feld ist ein spezifisches Datenstück innerhalb eines Dokuments in einer Sammlung. Felder definieren die Struktur und den Datentyp, der in einem Dokument gespeichert werden kann.\n    -',
        clearAll: 'Alles löschen',
        close: 'Schließen',
        collapse: 'Einklappen',
        collections: 'Sammlungen',
        columns: 'Spalten',
        columnToSort: 'Spalten zum Sortieren',
        confirm: 'Bestätigen',
        confirmCopy: 'Kopie bestätigen',
        confirmDeletion: 'Löschen bestätigen',
        confirmDuplication: 'Duplizieren bestätigen',
        confirmMove: 'Bestätigen Sie den Umzug.',
        confirmReindex: 'Alle {{collections}} neu indizieren?',
        confirmReindexAll: 'Alle Sammlungen neu indizieren?',
        confirmReindexDescription: 'Dies entfernt bestehende Indizes und indiziert die Dokumente in den {{collections}}-Sammlungen neu.',
        confirmReindexDescriptionAll: 'Dies entfernt bestehende Indizes und indiziert die Dokumente in allen Sammlungen neu.',
        confirmRestoration: 'Bestätigen Sie die Wiederherstellung',
        copied: 'Kopiert',
        copy: 'Kopieren',
        copyField: 'Feld kopieren',
        copying: 'Kopieren',
        copyRow: 'Zeile kopieren',
        copyWarning: 'Du bist dabei, {{to}} mit {{from}} für {{label}} {{title}} zu überschreiben. Bist du dir sicher?',
        create: 'Erstellen',
        created: 'Erstellt',
        createdAt: 'Erstellt am',
        createNew: 'Neu erstellen',
        createNewLabel: '{{label}} neu erstellen',
        creating: 'Erstelle',
        creatingNewLabel: 'Erstelle {{label}}',
        currentlyEditing: 'bearbeitet gerade dieses Dokument. Wenn du übernimmst, wird die Bearbeitung blockiert und nicht gespeicherte Änderungen können verloren gehen.',
        custom: 'Benutzerdefiniert',
        dark: 'Dunkel',
        dashboard: 'Übersicht',
        delete: 'Löschen',
        deleted: 'Gelöscht',
        deletedAt: 'Gelöscht am',
        deletedCountSuccessfully: '{{count}} {{label}} erfolgreich gelöscht.',
        deletedSuccessfully: 'Erfolgreich gelöscht.',
        deletePermanently: 'Überspringen Sie den Papierkorb und löschen Sie dauerhaft.',
        deleting: 'Löschen...',
        depth: 'Tiefe',
        descending: 'Absteigend',
        deselectAllRows: 'Alle Zeilen abwählen',
        document: 'Dokument',
        documentIsTrashed: 'Dieses {{label}} wurde gelöscht und ist nur lesbar.',
        documentLocked: 'Dokument gesperrt',
        documents: 'Dokumente',
        duplicate: 'Duplizieren',
        duplicateWithoutSaving: 'Duplizieren ohne Änderungen zu speichern',
        edit: 'Bearbeiten',
        editAll: 'Bearbeite alle',
        editedSince: 'Bearbeitet seit',
        editing: 'Bearbeite',
        editingLabel_many: 'Bearbeiten von {{count}} {{label}}',
        editingLabel_one: 'Bearbeiten von {{count}} {{label}}',
        editingLabel_other: 'Bearbeiten von {{count}} {{label}}',
        editingTakenOver: 'Bearbeitung übernommen',
        editLabel: '{{label}} bearbeiten',
        email: 'E-Mail',
        emailAddress: 'E-Mail-Adresse',
        emptyTrash: 'Papierkorb leeren',
        emptyTrashLabel: 'Leeren Sie den {{label}} Papierkorb',
        enterAValue: 'Gib einen Wert ein',
        error: 'Fehler',
        errors: 'Fehler',
        exitLivePreview: 'Beenden Sie die Live-Vorschau',
        export: 'Export',
        fallbackToDefaultLocale: 'Auf die Standardsprache zurückfallen',
        false: 'Falsch',
        filter: 'Filter',
        filters: 'Filter',
        filterWhere: 'Filter {{label}}, wo',
        globals: 'Globale Dokumente',
        goBack: 'Zurück',
        groupByLabel: 'Gruppieren nach {{label}}',
        import: 'Importieren',
        isEditing: 'bearbeitet gerade',
        item: 'Artikel',
        items: 'Artikel',
        language: 'Sprache',
        lastModified: 'Zuletzt geändert',
        leaveAnyway: 'Trotzdem verlassen',
        leaveWithoutSaving: 'Ohne speichern verlassen',
        light: 'Hell',
        livePreview: 'Live-Vorschau',
        loading: 'Lädt',
        locale: 'Sprache',
        locales: 'Sprachen',
        menu: 'Menü',
        moreOptions: 'Mehr Optionen',
        move: 'Bewegen',
        moveConfirm: 'Sie sind dabei {{count}} {{label}} nach <1>{{destination}}</1> zu verschieben. Sind Sie sicher?',
        moveCount: 'Bewegen Sie {{count}} {{label}}',
        moveDown: 'Nach unten bewegen',
        moveUp: 'Nach oben bewegen',
        moving: 'Umziehen',
        movingCount: 'Verschieben {{count}} {{label}}',
        newPassword: 'Neues Passwort',
        next: 'Nächste',
        no: 'Nein',
        noDateSelected: 'Kein Datum ausgewählt',
        noFiltersSet: 'Keine Filter gesetzt',
        noLabel: '<Kein {{label}}>',
        none: 'Kein',
        noOptions: 'Keine Optionen',
        noResults: 'Keine {{label}} gefunden. Entweder es existieren keine {{label}} oder es gibt keine Übereinstimmung zu den von dir verwendeten Filtern.',
        notFound: 'Nicht gefunden',
        nothingFound: 'Keine Ergebnisse',
        noTrashResults: 'Kein {{label}} im Papierkorb.',
        noUpcomingEventsScheduled: 'Keine bevorstehenden Veranstaltungen geplant.',
        noValue: 'Kein Wert',
        of: 'von',
        only: 'Nur',
        open: 'Öffnen',
        or: 'oder',
        order: 'Reihenfolge',
        overwriteExistingData: 'Vorhandene Eingaben überschreiben',
        pageNotFound: 'Seite nicht gefunden',
        password: 'Passwort',
        pasteField: 'Feld einfügen',
        pasteRow: 'Zeile einfügen',
        payloadSettings: 'Payload-Einstellungen',
        permanentlyDelete: 'Dauerhaft löschen',
        permanentlyDeletedCountSuccessfully: '{{count}} {{label}} erfolgreich dauerhaft gelöscht.',
        perPage: 'Pro Seite: {{limit}}',
        previous: 'Vorherige',
        reindex: 'Neuindizieren',
        reindexingAll: 'Alle {{collections}} werden neu indiziert.',
        remove: 'Entfernen',
        rename: 'Umbenennen',
        reset: 'Zurücksetzen',
        resetPreferences: 'Präferenzen zurücksetzen',
        resetPreferencesDescription: 'Alle Präferenzen werden auf die Standardwerte zurückgesetzt.',
        resettingPreferences: 'Präferenzen werden zurückgesetzt.',
        restore: 'Wiederherstellen',
        restoreAsPublished: 'Wiederherstellen als veröffentlichte Version',
        restoredCountSuccessfully: '{{count}} {{label}} erfolgreich wiederhergestellt.',
        restoring: 'Respektieren Sie die Bedeutung des Originaltextes im Kontext von Payload. Hier ist eine Liste häufiger Payload-Begriffe, die sehr spezifische Bedeutungen haben:\n    - Sammlung: Eine Sammlung ist eine Gruppe von Dokumenten, die eine gemeinsame Struktur und einen gemeinsamen Zweck teilen. Sammlungen werden verwendet, um Inhalte in Payload zu organisieren und zu verwalten.\n    - Feld: Ein Feld ist ein spezifisches Datenstück innerhalb eines Dokuments in einer Sammlung. Felder definieren die Struktur und den Datentyp, der in einem Dokument gespeichert werden kann.\n    - Dokument:',
        row: 'Zeile',
        rows: 'Zeilen',
        save: 'Speichern',
        saving: 'Speichern...',
        schedulePublishFor: 'Plane die Veröffentlichung für {{title}}',
        searchBy: 'Suche nach {{label}}',
        select: 'Auswählen',
        selectAll: 'Alle {{count}} {{label}} auswählen',
        selectAllRows: 'Alle Zeilen auswählen',
        selectedCount: '{{count}} {{label}} ausgewählt',
        selectLabel: 'Wähle {{label}}',
        selectValue: 'Wert auswählen',
        showAllLabel: 'Zeige alle {{label}}',
        sorryNotFound: 'Es tut uns leid, aber wir haben nichts gefunden, was deiner Anfrage entspricht.',
        sort: 'Sortieren',
        sortByLabelDirection: 'Sortieren nach {{label}} {{direction}}',
        stayOnThisPage: 'Auf dieser Seite bleiben',
        submissionSuccessful: 'Übermittlung erfolgreich.',
        submit: 'Senden',
        submitting: 'Wird aktualisiert...',
        success: 'Erfolg',
        successfullyCreated: '{{label}} erfolgreich erstellt.',
        successfullyDuplicated: '{{label}} wurde erfolgreich dupliziert.',
        successfullyReindexed: 'Erfolgreich {{count}} von {{total}} Dokumenten aus {{collections}} Sammlungen neu indiziert.',
        takeOver: 'Übernehmen',
        thisLanguage: 'Deutsch',
        time: 'Zeit',
        timezone: 'Zeitzone',
        titleDeleted: '{{label}} {{title}} wurde erfolgreich gelöscht.',
        titleRestored: '{{label}} "{{title}}" erfolgreich wiederhergestellt.',
        titleTrashed: '{{label}} "{{title}}" wurde in den Papierkorb verschoben.',
        trash: 'Müll',
        trashedCountSuccessfully: '{{count}} {{label}} wurde in den Papierkorb verschoben.',
        true: 'Wahr',
        unauthorized: 'Nicht autorisiert',
        unsavedChanges: 'Du hast ungespeicherte Änderungen. Speichern oder verwerfe sie, bevor du fortfahrst.',
        unsavedChangesDuplicate: 'Du hast ungespeicherte Änderungen, möchtest du mit dem Duplizieren fortfahren?',
        untitled: 'ohne Titel',
        upcomingEvents: 'Bevorstehende Veranstaltungen',
        updatedAt: 'Aktualisiert am',
        updatedCountSuccessfully: '{{count}} {{label}} erfolgreich aktualisiert.',
        updatedLabelSuccessfully: '{{label}} erfolgreich aktualisiert.',
        updatedSuccessfully: 'Erfolgreich aktualisiert.',
        updateForEveryone: 'Für alle aktualisieren',
        updating: 'Aktualisierung',
        uploading: 'Hochladen',
        uploadingBulk: 'Hochladen von {{current}} von {{total}}',
        user: 'Benutzer',
        username: 'Benutzername',
        users: 'Benutzer',
        value: 'Wert',
        viewing: 'Ansehen',
        viewReadOnly: 'Nur-Lese-Ansicht',
        welcome: 'Willkommen',
        yes: 'Ja'
    },
    localization: {
        cannotCopySameLocale: 'Kann nicht in dieselbe Sprache kopiert werden',
        copyFrom: 'Kopieren von',
        copyFromTo: 'Kopieren von {{from}} zu {{to}}',
        copyTo: 'Kopieren nach',
        copyToLocale: 'Erstelle Kopie für Sprach-Variante',
        localeToPublish: 'Zu veröffentlichende Sprache',
        selectLocaleToCopy: 'Wähle den Ort zum Kopieren aus'
    },
    operators: {
        contains: 'enthält',
        equals: 'gleich',
        exists: 'existiert',
        intersects: 'schneidet sich',
        isGreaterThan: 'ist größer als',
        isGreaterThanOrEqualTo: 'ist größer oder gleich',
        isIn: 'ist drin',
        isLessThan: 'ist kleiner als',
        isLessThanOrEqualTo: 'ist kleiner oder gleich',
        isLike: 'ist wie',
        isNotEqualTo: 'ist nicht gleich',
        isNotIn: 'ist nicht drin',
        isNotLike: 'ist nicht wie',
        near: 'in der Nähe',
        within: 'innerhalb'
    },
    upload: {
        addFile: 'Datei hinzufügen',
        addFiles: 'Dateien hinzufügen',
        bulkUpload: 'Mehrere Dateien hochladen',
        crop: 'Zuschneiden',
        cropToolDescription: 'Ziehe die Ecken des ausgewählten Bereichs, zeichne einen neuen Bereich oder passe die Werte unten an.',
        download: 'Herunterladen',
        dragAndDrop: 'Bewege eine Datei per Drag-and-Drop',
        dragAndDropHere: 'oder lege eine Datei hier ab',
        editImage: 'Bild bearbeiten',
        fileName: 'Dateiname',
        fileSize: 'Dateigröße',
        filesToUpload: 'Dateien zum Hochladen',
        fileToUpload: 'Datei zum Hochladen',
        focalPoint: 'Brennpunkt',
        focalPointDescription: 'Setze den Fokuspunkt direkt auf der Vorschau oder passe die Werte unten an.',
        height: 'Höhe',
        lessInfo: 'Weniger Info',
        moreInfo: 'Mehr Info',
        noFile: 'Keine Datei',
        pasteURL: 'URL einfügen',
        previewSizes: 'Vorschaugrößen',
        selectCollectionToBrowse: 'Wähle eine Sammlung zum Durchsuchen aus',
        selectFile: 'Datei auswählen',
        setCropArea: 'Bereich zum Zuschneiden festlegen',
        setFocalPoint: 'Fokuspunkt setzen',
        sizes: 'Größen',
        sizesFor: 'Größen für {{label}}',
        width: 'Breite'
    },
    validation: {
        emailAddress: 'Bitte gib eine korrekte E-Mail-Adresse an.',
        enterNumber: 'Bitte gib eine gültige Nummer an.',
        fieldHasNo: 'Dieses Feld hat kein {{label}}',
        greaterThanMax: '{{value}} ist größer als der maximal erlaubte {{label}} von {{max}}.',
        invalidInput: 'Dieses Feld hat einen inkorrekten Wert.',
        invalidSelection: 'Dieses Feld hat eine inkorrekte Auswahl.',
        invalidSelections: 'Dieses Feld enthält die folgenden inkorrekten Auswahlmöglichkeiten:',
        lessThanMin: '{{value}} ist kleiner als der minimal erlaubte {{label}} von {{min}}.',
        limitReached: 'Limit erreicht, es können nur {{max}} Elemente hinzugefügt werden.',
        longerThanMin: 'Dieser Wert muss länger als die minimale Länge von {{minLength}} Zeichen sein.',
        notValidDate: '"{{value}}" ist kein gültiges Datum.',
        required: 'Pflichtfeld',
        requiresAtLeast: 'Dieses Feld muss mindestens {{count}} {{label}} enthalten.',
        requiresNoMoreThan: 'Dieses Feld kann nicht mehr als {{count}} {{label}} enthalten.',
        requiresTwoNumbers: 'Dieses Feld muss zwei Zahlen enthalten.',
        shorterThanMax: 'Dieser Wert muss kürzer als die maximale Länge von {{maxLength}} sein.',
        timezoneRequired: 'Eine Zeitzone ist erforderlich.',
        trueOrFalse: 'Dieses Feld kann nur wahr oder falsch sein.',
        username: 'Bitte gib einen gültigen Benutzernamen ein. Dieser kann Buchstaben, Zahlen, Bindestriche, Punkte und Unterstriche enthalten.',
        validUploadID: 'Dieses Feld enthält keine valide Upload-ID.'
    },
    version: {
        type: 'Typ',
        aboutToPublishSelection: 'Du bist dabei, alle {{label}} in der Auswahl zu veröffentlichen. Bist du dir sicher?',
        aboutToRestore: 'Du bist dabei, {{label}} auf den Stand vom {{versionDate}} zurücksetzen.',
        aboutToRestoreGlobal: 'Du bist dabei, das Globale Dokument {{label}} auf den Stand vom {{versionDate}} zurückzusetzen.',
        aboutToRevertToPublished: 'Du bist dabei, dieses Dokument auf den Stand des ersten Veröffentlichungsdatums zurückzusetzen. Bist du sicher?',
        aboutToUnpublish: 'Du bist dabei dieses Dokument auf Entwurf zu setzen. Bist du dir sicher?',
        aboutToUnpublishSelection: 'Du bist dabei, die Veröffentlichung aller {{label}} in der Auswahl aufzuheben. Bist du dir sicher?',
        autosave: 'Automatische Speicherung',
        autosavedSuccessfully: 'Erfolgreich automatisch gespeichert.',
        autosavedVersion: 'Automatisch gespeicherte Version',
        changed: 'Geändert',
        changedFieldsCount_one: '{{count}} geändertes Feld',
        changedFieldsCount_other: '{{count}} geänderte Felder',
        compareVersion: 'Vergleiche Version zu:',
        compareVersions: 'Versionen vergleichen',
        comparingAgainst: 'Im Vergleich zu',
        confirmPublish: 'Veröffentlichung bestätigen',
        confirmRevertToSaved: 'Zurücksetzen auf die letzte Speicherung bestätigen',
        confirmUnpublish: 'Aufhebung der Veröffentlichung bestätigen',
        confirmVersionRestoration: 'Wiederherstellung der Version bestätigen',
        currentDocumentStatus: 'Aktueller Dokumentenstatus: {{docStatus}}',
        currentDraft: 'Aktueller Entwurf',
        currentlyPublished: 'Derzeit veröffentlicht',
        currentlyViewing: 'Derzeitige Ansicht',
        currentPublishedVersion: 'Aktuell veröffentlichte Version',
        draft: 'Entwurf',
        draftSavedSuccessfully: 'Entwurf erfolgreich gespeichert.',
        lastSavedAgo: 'Zuletzt vor {{distance}} gespeichert',
        modifiedOnly: 'Nur modifiziert',
        moreVersions: 'Mehr Versionen...',
        noFurtherVersionsFound: 'Keine weiteren Versionen vorhanden',
        noRowsFound: 'Kein {{label}} gefunden',
        noRowsSelected: 'Kein {{label}} ausgewählt',
        preview: 'Vorschau',
        previouslyDraft: 'Früher ein Entwurf',
        previouslyPublished: 'Zuvor veröffentlicht',
        previousVersion: 'Frühere Version',
        problemRestoringVersion: 'Bei der Wiederherstellung der Version ist ein Fehler aufgetreten',
        publish: 'Veröffentlichen',
        publishAllLocales: 'Alle Sprachen veröffentlichen',
        publishChanges: 'Änderungen veröffentlichen',
        published: 'Veröffentlicht',
        publishIn: 'Veröffentlichen auf {{locale}}',
        publishing: 'Veröffentlichung',
        restoreAsDraft: 'Als Entwurf wiederherstellen',
        restoredSuccessfully: 'Erfolgreich wiederhergestellt.',
        restoreThisVersion: 'Diese Version wiederherstellen',
        restoring: 'Wiederherstellen...',
        reverting: 'Zurücksetzen...',
        revertToPublished: 'Auf veröffentlichte Version zurücksetzen',
        saveDraft: 'Entwurf speichern',
        scheduledSuccessfully: 'Erfolgreich geplant.',
        schedulePublish: 'Veröffentlichungsplan',
        selectLocales: 'Wähle anzuzeigende Sprachen',
        selectVersionToCompare: 'Wähle Version zum Vergleich',
        showingVersionsFor: 'Versionen anzeigen für:',
        showLocales: 'Sprachen anzeigen:',
        specificVersion: 'Spezifische Version',
        status: 'Status',
        unpublish: 'Veröffentlichung aufheben',
        unpublishing: 'Veröffentlichung aufheben...',
        version: 'Version',
        versionAgo: 'vor {{distance}}',
        versionCount_many: '{{count}} Versionen gefunden',
        versionCount_none: 'Keine Versionen gefunden',
        versionCount_one: '{{count}} Version gefunden',
        versionCount_other: '{{count}} Versionen gefunden',
        versionCreatedOn: '{{version}} erstellt am:',
        versionID: 'Version-ID',
        versions: 'Versionen',
        viewingVersion: 'Version für {{entityLabel}} {{documentTitle}} ansehen',
        viewingVersionGlobal: 'Version für das Globale Dokument {{entityLabel}} ansehen',
        viewingVersions: 'Versionen für {{entityLabel}} {{documentTitle}} ansehen',
        viewingVersionsGlobal: 'Versionen für das Globale Dokument {{entityLabel}} ansehen'
    }
};
export const de = {
    dateFNSKey: 'de',
    translations: deTranslations
};

//# sourceMappingURL=de.js.map