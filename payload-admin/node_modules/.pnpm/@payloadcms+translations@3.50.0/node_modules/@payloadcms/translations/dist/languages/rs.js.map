{"version": 3, "sources": ["../../src/languages/rs.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const rsTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Налог',\n    accountOfCurrentUser: 'Налог тренутног корисника',\n    accountVerified: 'Nalog je uspešno verifikovan.',\n    alreadyActivated: 'Већ активирано',\n    alreadyLoggedIn: 'Већ пријављен',\n    apiKey: 'АПИ кључ',\n    authenticated: 'Autentifikovan',\n    backToLogin: 'Назад на пријаву',\n    beginCreateFirstUser: 'На самом почетку креирај свог првог корисника',\n    changePassword: 'Промени лозинку',\n    checkYourEmailForPasswordReset:\n      'Ako je e-mail adresa povezana sa nalogom, uskoro ćete dobiti uputstva za resetovanje vaše lozinke. Ako ne vidite e-mail u vašem inboxu, molimo vas da proverite vašu folder za spam ili neželjene poruke.',\n    confirmGeneration: 'Потврди креирање',\n    confirmPassword: 'Потврди лозинку',\n    createFirstUser: 'Креирај првог корисника',\n    emailNotValid: 'Адреса е-поште није валидна',\n    emailOrUsername: 'Email ili Korisničko ime',\n    emailSent: 'Порука е-поште прослеђена',\n    emailVerified: 'Uspešno verifikovan email.',\n    enableAPIKey: 'Омогући API кључ',\n    failedToUnlock: 'Неуспешно откључавање.',\n    forceUnlock: 'Принудно откључај',\n    forgotPassword: 'Заборављена лозинка',\n    forgotPasswordEmailInstructions:\n      'Молимо Вас да унесете својy адресy е-поште. Примићете поруку са упутством за поновно постављање лозинке.',\n    forgotPasswordQuestion: 'Заборављена лозинка?',\n    forgotPasswordUsernameInstructions:\n      'Unesite svoje korisničko ime ispod. Uputstva o tome kako da resetujete svoju lozinku biće poslata na e-mail adresu koja je povezana sa vašim korisničkim imenom.',\n    generate: 'Генериши',\n    generateNewAPIKey: 'Генериши нови АПИ кључ',\n    generatingNewAPIKeyWillInvalidate:\n      'Генерисање новог АПИ кључа ће <1>поништити</1> претходни кључ. Да ли сте сигурни да желите наставити?',\n    lockUntil: 'Закључај док',\n    logBackIn: 'Поновна пријава',\n    loggedIn: 'За пријаву са другим корисничким налогом потребно је прво <0>одјавити се</0>',\n    loggedInChangePassword:\n      'Да бисте променили лозинку, отворите свој <0>налог</0> и промените лозинку.',\n    loggedOutInactivity: 'Одјављени се због неактивности.',\n    loggedOutSuccessfully: 'Успешно сте одјављени',\n    loggingOut: 'Odjavljuje se...',\n    login: 'Пријава',\n    loginAttempts: 'Покушаји пријаве',\n    loginUser: 'Пријава корисника',\n    loginWithAnotherUser:\n      'За пријаву са другим корисничким налогом потребно је прво <0>одјавити се</0>',\n    logOut: 'Одјава',\n    logout: 'Одјава',\n    logoutSuccessful: 'Uspešno ste se odjavili.',\n    logoutUser: 'Одјава корисника',\n    newAccountCreated:\n      'Нови налог је креиран. Приступите налогу кликом на <a href=\"{{serverURL}}\">{{serverURL}}</a>. Молимо Вас кликните на следећи линк или залепите адресу која се налази испод у претраживач да бисте потврдили адресу е-поште: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Након што потврдите адресу е-поште можете се улоговати.',\n    newAPIKeyGenerated: 'Нови АПИ кључ генерисан.',\n    newPassword: 'Нова лозинка',\n    passed: 'Autentifikacija uspela',\n    passwordResetSuccessfully: 'Успешно ресетована лозинка.',\n    resetPassword: 'Промена лозинке',\n    resetPasswordExpiration: 'Промена рока трајања лозинке',\n    resetPasswordToken: 'Промена лозинке токена',\n    resetYourPassword: 'Промени своју лозинку',\n    stayLoggedIn: 'Остани пријављен',\n    successfullyRegisteredFirstUser: 'Uspešno registrovan prvi korisnik.',\n    successfullyUnlocked: 'Успешно откључано',\n    tokenRefreshSuccessful: 'Osvežavanje tokena je uspešno.',\n    unableToVerify: 'Није могуће потврдити',\n    username: 'Korisničko ime',\n    usernameNotValid: 'Korisničko ime koje ste uneli nije važeće.',\n    verified: 'Потврђено',\n    verifiedSuccessfully: 'Успешно потврђено',\n    verify: 'Потврди',\n    verifyUser: 'Потврди корисника',\n    verifyYourEmail: 'Потврди своју адресу е-поште',\n    youAreInactive:\n      'Неактивни сте већ неко време и ускоро ћете бити аутоматски одјављени због сигурности. Да ли желите остати пријављени?',\n    youAreReceivingResetPassword:\n      'Примили сте поруку пошто сте Ви (или неко у ваше име) затражили промену лозинке налога. Молимо Вас кликните на линк или залепите адресу у свој претраживач да бисте завршили процес:',\n    youDidNotRequestPassword:\n      'Ако нисте затражили промену лозинке игноришите ову поруку и лозинка ће остати непромењена.',\n  },\n  error: {\n    accountAlreadyActivated: 'Овај налог је већ активиран.',\n    autosaving: 'Настао је проблем при аутоматском чувању овог документа.',\n    correctInvalidFields: 'Молимо исправите невалидна поља.',\n    deletingFile: 'Догодила се грешка при брисању датотеке.',\n    deletingTitle:\n      'Догодила се грешка при брисању {{title}}. Проверите интернет конекцију и покушајте поново.',\n    documentNotFound:\n      'Dokument sa ID-om {{id}} nije mogao biti pronađen. Moguće je da je obrisan ili nikada nije postojao, ili možda nemate pristup njemu.',\n    emailOrPasswordIncorrect: 'Емаил или лозинка су неисправни.',\n    followingFieldsInvalid_one: 'Ово поље је невалидно:',\n    followingFieldsInvalid_other: 'Ова поља су невалидна:',\n    incorrectCollection: 'Невалидна колекција',\n    insufficientClipboardPermissions:\n      'Приступ к клипборду је одбијен. Провјерите своја овлашћења за клипборд.',\n    invalidClipboardData: 'Неважећи подаци у клипборду.',\n    invalidFileType: 'Невалидан тип датотеке',\n    invalidFileTypeValue: 'Невалидан тип датотеке: {{value}}',\n    invalidRequestArgs: 'Неважећи аргументи прослеђени у захтеву: {{args}}',\n    loadingDocument: 'Постоји проблем при учитавању документа чији је ИД {{id}}.',\n    localesNotSaved_one: 'Следеће локалне поставке није могло бити сачувано:',\n    localesNotSaved_other: 'Следеће локалне поставке нису могле бити сачуване:',\n    logoutFailed: 'Odjava nije uspela.',\n    missingEmail: 'Недостаје емаил.',\n    missingIDOfDocument: 'Недостаје ИД документа да би се ажурирао.',\n    missingIDOfVersion: 'Недостаје ИД верзије.',\n    missingRequiredData: 'Недостају обавезни подаци.',\n    noFilesUploaded: 'Ниједна датотека није учитана.',\n    noMatchedField: 'Нема подударајућих поља за \"{{label}}\"',\n    notAllowedToAccessPage: 'Немате дозволу за приступ овој страници.',\n    notAllowedToPerformAction: 'Немате дозволу за извршење ове радње.',\n    notFound: 'Тражени ресурс није пронађен.',\n    noUser: 'Нема корисника',\n    previewing: 'Постоји проблем при прегледу овог документа.',\n    problemUploadingFile: 'Постоји проблем при учитавању датотеке.',\n    restoringTitle:\n      'Došlo je do greške prilikom vraćanja {{title}}. Proverite svoju vezu i pokušajte ponovo.',\n    tokenInvalidOrExpired: 'Токен је невалидан или је истекао.',\n    tokenNotProvided: 'Token nije dostavljen.',\n    unableToCopy: 'Није могуће копирати.',\n    unableToDeleteCount: 'Није могуће избрисати {{count}} од {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Грешка при реиндексирању колекције {{collection}}. Операција је прекинута.',\n    unableToUpdateCount: 'Није могуће ажурирати {{count}} од {{total}} {{label}}.',\n    unauthorized: 'Нисте ауторизовани да бисте упутили овај захтев.',\n    unauthorizedAdmin: 'Немате приступ администраторском панелу.',\n    unknown: 'Дошло је до непознате грешке.',\n    unPublishingDocument: 'Постоји проблем при поништавању објаве овог документа.',\n    unspecific: 'Дошло је до грешке.',\n    unverifiedEmail: 'Молимо вас да верификујете своју е-пошту пре пријаве.',\n    userEmailAlreadyRegistered: 'Корисник са датом имејл адресом је већ регистрован.',\n    userLocked: 'Овај корисник је закључан због превеликог броја неуспешних покушаја пријаве.',\n    usernameAlreadyRegistered: 'Korisnik sa datim korisničkim imenom je već registrovan.',\n    usernameOrPasswordIncorrect: 'Korisničko ime ili lozinka koje ste uneli su netačni.',\n    valueMustBeUnique: 'Вредност мора бити јединствена.',\n    verificationTokenInvalid: 'Верификациони токен је невалидан.',\n  },\n  fields: {\n    addLabel: 'Додај {{label}}',\n    addLink: 'Додај линк',\n    addNew: 'Додај нови',\n    addNewLabel: 'Додај нови {{label}}',\n    addRelationship: 'Додај релацију',\n    addUpload: 'Додај учитавање',\n    block: 'блокирање',\n    blocks: 'блокирања',\n    blockType: 'Врста блокирања',\n    chooseBetweenCustomTextOrDocument:\n      'Изаберите између уноса прилагођеног текста адресе или линка на други документ.',\n    chooseDocumentToLink: 'Одаберите документ који желите линковати.',\n    chooseFromExisting: 'Одаберите из постојећих.',\n    chooseLabel: 'Одаберите {{label}}',\n    collapseAll: 'Скупи све',\n    customURL: 'Прилагођени линк',\n    editLabelData: 'Уреди {{label}} податке',\n    editLink: 'Измени линк',\n    editRelationship: 'Измени однос',\n    enterURL: 'Унеси адресу',\n    internalLink: 'Интерни линк',\n    itemsAndMore: '{{items}} и {{count}} више',\n    labelRelationship: '{{label}} веза',\n    latitude: 'Географска ширина',\n    linkedTo: 'Повезани са <0>{{label}}</0>',\n    linkType: 'Тип линка',\n    longitude: 'Географска дужина',\n    newLabel: 'Ново {{label}}',\n    openInNewTab: 'Отвори у новој картици.',\n    passwordsDoNotMatch: 'Лозинке нису исте.',\n    relatedDocument: 'Повезани документ',\n    relationTo: 'Веза са',\n    removeRelationship: 'Уклони везу',\n    removeUpload: 'Уклони пренос',\n    saveChanges: 'Сачувај промене',\n    searchForBlock: 'Претражи блок',\n    selectExistingLabel: 'Одабери постојећу {{label}}',\n    selectFieldsToEdit: 'Одаберите поља за промену',\n    showAll: 'Покажи све',\n    swapRelationship: 'Замени везу',\n    swapUpload: 'Замени пренос',\n    textToDisplay: 'Текст за приказ',\n    toggleBlock: 'Пребаци блок',\n    uploadNewLabel: 'Учитај нови {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Pregledajte po folderu',\n    byFolder: 'Po folderu',\n    deleteFolder: 'Obriši fasciklu',\n    folderName: 'Ime fascikle',\n    folders: 'Fascikle',\n    folderTypeDescription:\n      'Odaberite koja vrsta dokumenata iz kolekcije treba biti dozvoljena u ovom folderu.',\n    itemHasBeenMoved: '{{title}} je premješten u {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} je premešten u osnovni direktorijum.',\n    itemsMovedToFolder: '{{title}} premešten u {{folderName}}',\n    itemsMovedToRoot: '{{title}} pomeren u koreni direktorijum',\n    moveFolder: 'Premesti fasciklu',\n    moveItemsToFolderConfirmation:\n      'Upravo ste na korak da premestite <1>{{count}} {{label}}</1> u <2>{{toFolder}}</2>. Da li ste sigurni?',\n    moveItemsToRootConfirmation:\n      'Управо ћете преместити <1>{{count}} {{label}}</1> у основну фасциклу. Да ли сте сигурни?',\n    moveItemToFolderConfirmation:\n      'Uskoro ćete premestiti <1>{{title}}</1> u <2>{{toFolder}}</2>. Da li ste sigurni?',\n    moveItemToRootConfirmation:\n      'Upravo ćete premestiti <1>{{title}}</1> u osnovnu fasciklu. Da li ste sigurni?',\n    movingFromFolder: 'Premeštanje {{title}} iz {{fromFolder}}',\n    newFolder: 'Novi folder',\n    noFolder: 'Nema foldera',\n    renameFolder: 'Preimenuj folder',\n    searchByNameInFolder: 'Pretraži po imenu u {{folderName}}',\n    selectFolderForItem: 'Izaberite fasciklu za {{title}}',\n  },\n  general: {\n    name: 'Ime',\n    aboutToDelete: 'Избрисаћете {{label}} <1>{{title}}</1>. Да ли сте сигурни?',\n    aboutToDeleteCount_many: 'Избрисаћете {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Избрисаћете {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Избрисаћете {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Управо ћете заувек избрисати {{label}} <1>{{title}}</1>. Јесте ли сигурни?',\n    aboutToPermanentlyDeleteTrash:\n      'На путу сте да трајно обришете <0>{{count}}</0> <1>{{label}}</1> из смећа. Да ли сте сигурни?',\n    aboutToRestore: 'На путу сте да вратите {{label}} <1>{{title}}</1>. Јесте ли сигурни?',\n    aboutToRestoreAsDraft:\n      'Na korak ste od obnavljanja {{label}} <1>{{title}}</1> kao nacrta. Da li ste sigurni?',\n    aboutToRestoreAsDraftCount: 'Upravo ste na koraku da povratite {{count}} {{label}} kao skicu',\n    aboutToRestoreCount: 'Uskoro ćete obnoviti {{count}} {{label}}',\n    aboutToTrash:\n      'Na korak ste da premestite {{label}} <1>{{title}}</1> u otpad. Da li ste sigurni?',\n    aboutToTrashCount: 'Upravo ćete premestiti {{count}} {{label}} u smeće',\n    addBelow: 'Додај испод',\n    addFilter: 'Додај филтер',\n    adminTheme: 'Администраторска тема',\n    all: 'Svi',\n    allCollections: 'Све Колекције',\n    allLocales: 'Sve lokacije',\n    and: 'И',\n    anotherUser: 'Други корисник',\n    anotherUserTakenOver: 'Други корисник је преузео уређивање овог документа.',\n    applyChanges: 'Примени промене',\n    ascending: 'Узлазно',\n    automatic: 'Аутоматско',\n    backToDashboard: 'Назад на контролни панел',\n    cancel: 'Откажи',\n    changesNotSaved: 'Ваше промене нису сачуване. Ако изађете сада, изгубићете промене.',\n    clear: 'Jasno',\n    clearAll: 'Obriši sve',\n    close: 'Затвори',\n    collapse: 'Скупи',\n    collections: 'Колекције',\n    columns: 'Колоне',\n    columnToSort: 'Колона за сортирање',\n    confirm: 'Потврди',\n    confirmCopy: 'Potvrda kopiranja',\n    confirmDeletion: 'Потврди брисање',\n    confirmDuplication: 'Потврди дупликацију',\n    confirmMove: 'Potvrdite pomeranje',\n    confirmReindex: 'Ponovo indeksirati sve {{collections}}?',\n    confirmReindexAll: 'Ponovo indeksirati sve kolekcije?',\n    confirmReindexDescription:\n      'Ovo će ukloniti postojeće indekse i ponovo indeksirati dokumente u kolekcijama {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Ovo će ukloniti postojeće indekse i ponovo indeksirati dokumente u svim kolekcijama.',\n    confirmRestoration: 'Potvrdite obnovu',\n    copied: 'Копирано',\n    copy: 'Копирај',\n    copyField: 'Копирај поље',\n    copying: 'Kopiranje',\n    copyRow: 'Копирај ред',\n    copyWarning:\n      'На путу сте да препишете {{to}} са {{from}} за {{label}} {{title}}. Да ли сте сигурни?',\n    create: 'Креирај',\n    created: 'Креирано',\n    createdAt: 'Креирано у',\n    createNew: 'Креирај ново',\n    createNewLabel: 'Креирај ново {{label}}',\n    creating: 'Креира се',\n    creatingNewLabel: 'Креирање новог {{label}}',\n    currentlyEditing:\n      'тренутно уређује овај документ. Ако преузмете контролу, биће блокирани да наставе са уређивањем и могу изгубити несачуване измене.',\n    custom: 'Prilagođeno',\n    dark: 'Тамно',\n    dashboard: 'Контролни панел',\n    delete: 'Обриши',\n    deleted: 'Obrisano',\n    deletedAt: 'Obrisano u',\n    deletedCountSuccessfully: 'Успешно избрисано {{count}} {{label}}.',\n    deletedSuccessfully: 'Успешно избрисано.',\n    deletePermanently: 'Preskoči otpad i trajno izbriši',\n    deleting: 'Брисање...',\n    depth: 'Dubina',\n    descending: 'Опадајуће',\n    deselectAllRows: 'Деселектујте све редове',\n    document: 'Dokument',\n    documentIsTrashed: 'Ova {{label}} je odbačena i samo je za čitanje.',\n    documentLocked: 'Документ је закључан',\n    documents: 'Dokumenti',\n    duplicate: 'Дупликат',\n    duplicateWithoutSaving: 'Понови без чувања промена',\n    edit: 'Уреди',\n    editAll: 'Уреди све',\n    editedSince: 'Измењено од',\n    editing: 'Уређивање',\n    editingLabel_many: 'Уређивање {{count}} {{label}}',\n    editingLabel_one: 'Уређивање {{count}} {{label}}',\n    editingLabel_other: 'Уређивање {{count}} {{label}}',\n    editingTakenOver: 'Уређивање преузето',\n    editLabel: 'Уреди {{label}}',\n    email: 'Е-пошта',\n    emailAddress: 'Адреса е-поште',\n    emptyTrash: 'Isprazni korpu',\n    emptyTrashLabel: 'Isprazni {{label}} korpu za smeće',\n    enterAValue: 'Унеси вредност',\n    error: 'Грешка',\n    errors: 'Грешке',\n    exitLivePreview: 'Izađi iz prikaza uživo',\n    export: 'Izvoz',\n    fallbackToDefaultLocale: 'Враћање на задати језик',\n    false: 'Lažno',\n    filter: 'Филтер',\n    filters: 'Филтери',\n    filterWhere: 'Филтер {{label}} где',\n    globals: 'Глобали',\n    goBack: 'Врати се',\n    groupByLabel: 'Grupiši po {{label}}',\n    import: 'Uvoz',\n    isEditing: 'уређује',\n    item: 'artikal',\n    items: 'artikli',\n    language: 'Језик',\n    lastModified: 'Задња промена',\n    leaveAnyway: 'Свеједно напусти',\n    leaveWithoutSaving: 'Напусти без чувања',\n    light: 'Светло',\n    livePreview: 'Преглед',\n    loading: 'Учитавање',\n    locale: 'Језик',\n    locales: 'Преводи',\n    menu: 'Мени',\n    moreOptions: 'Više opcija',\n    move: 'Pomeri',\n    moveConfirm:\n      'Upravo ćete premestiti {{count}} {{label}} u <1>{{destination}}</1>. Da li ste sigurni?',\n    moveCount: 'Pomeri {{count}} {{label}}',\n    moveDown: 'Помери доле',\n    moveUp: 'Помери горе',\n    moving: 'Pomeranje',\n    movingCount: 'Pomeranje {{count}} {{label}}',\n    newPassword: 'Нова лозинка',\n    next: 'Следећи',\n    no: 'Не',\n    noDateSelected: 'Nije odabran datum',\n    noFiltersSet: 'Нема постављених филтера',\n    noLabel: '<Нема {{label}}>',\n    none: 'Ниједан',\n    noOptions: 'Нема опција',\n    noResults:\n      'Нема пронађених {{label}}. Могуће да {{label}} још увек не постоји или нема резултата у складу са постављеним филтерима.',\n    notFound: 'Није пронађено',\n    nothingFound: 'Ништа није пронађено',\n    noTrashResults: 'Nema {{label}} u otpadu.',\n    noUpcomingEventsScheduled: 'Nema zakazanih predstojećih događaja.',\n    noValue: 'Без вредности',\n    of: 'Од',\n    only: 'Samo',\n    open: 'Отвори',\n    or: 'Или',\n    order: 'Редослед',\n    overwriteExistingData: 'Prepišite postojeće podatke u polju',\n    pageNotFound: 'Страница није пронађена',\n    password: 'Лозинка',\n    pasteField: 'Залепи поље',\n    pasteRow: 'Залепи ред',\n    payloadSettings: 'Payload поставке',\n    permanentlyDelete: 'Trajno Izbriši',\n    permanentlyDeletedCountSuccessfully: 'Trajno obrisano {{count}} {{label}} uspešno.',\n    perPage: 'По страници: {{limit}}',\n    previous: 'Prethodni',\n    reindex: 'Реиндексирај',\n    reindexingAll: 'Ponovno indeksiranje svih {{collections}}.',\n    remove: 'Уклони',\n    rename: 'Preimenujte',\n    reset: 'Поново постави',\n    resetPreferences: 'Поништи подешавања',\n    resetPreferencesDescription: 'Ово ће поништити сва ваша подешавања на подразумеване вредности.',\n    resettingPreferences: 'Поништавање подешавања.',\n    restore: 'Vrati',\n    restoreAsPublished: 'Vrati na objavljenu verziju',\n    restoredCountSuccessfully: 'Uspješno obnovljeno {{count}} {{label}}.',\n    restoring: 'Vraćanje u prvobitno stanje...',\n    row: 'Ред',\n    rows: 'Редови',\n    save: 'Сачувај',\n    saving: 'Чување у току...',\n    schedulePublishFor: 'Zakažite objavljivanje za {{title}}',\n    searchBy: 'Тражи по {{label}}',\n    select: 'Izaberite',\n    selectAll: 'Одаберите све {{count}} {{label}}',\n    selectAllRows: 'Одаберите све редове',\n    selectedCount: '{{count}} {{label}} одабрано',\n    selectLabel: 'Izaberite {{label}}',\n    selectValue: 'Одабери вредност',\n    showAllLabel: 'Прикажи све {{label}}',\n    sorryNotFound: 'Нажалост, не постоји ништа што одговара вашем захтеву.',\n    sort: 'Сортирај',\n    sortByLabelDirection: 'Сортирај према {{label}} {{дирецтион}}',\n    stayOnThisPage: 'Остани на овој страници',\n    submissionSuccessful: 'Успешно слање',\n    submit: 'Потврди',\n    submitting: 'Podnošenje...',\n    success: 'Uspeh',\n    successfullyCreated: '{{label}} успешно креирано.',\n    successfullyDuplicated: '{{label}} успешно дуплицирано.',\n    successfullyReindexed:\n      'Успешно је реиндексирано {{count}} од {{total}} докумената из {{collections}} колекција.',\n    takeOver: 'Превузети',\n    thisLanguage: 'Српски (ћирилица)',\n    time: 'Vreme',\n    timezone: 'Vremenska zona',\n    titleDeleted: '{{label}} \"{{title}}\" успешно обрисано.',\n    titleRestored: '{{label}} \"{{title}}\" uspešno obnovljen.',\n    titleTrashed: '{{label}} \"{{title}}\" premešten u otpad.',\n    trash: 'Smeće',\n    trashedCountSuccessfully: '{{count}} {{label}} premješteno u smeće.',\n    true: 'Istinito',\n    unauthorized: 'Нисте ауторизовани',\n    unsavedChanges: 'Imate nesačuvane izmene. Sačuvajte ili odbacite pre nego što nastavite.',\n    unsavedChangesDuplicate: 'Имате несачуване промене. Да ли желите наставити са дуплицирањем?',\n    untitled: 'Без наслова',\n    upcomingEvents: 'Predstojeći događaji',\n    updatedAt: 'Ажурирано у',\n    updatedCountSuccessfully: 'Успешно ажурирано {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Uspešno ažurirano {{label}}.',\n    updatedSuccessfully: 'Успешно ажурирано.',\n    updateForEveryone: 'Ažuriranje za sve',\n    updating: 'Ажурирање',\n    uploading: 'Пренос',\n    uploadingBulk: 'Отпремање {{current}} од {{total}}',\n    user: 'Корисник',\n    username: 'Korisničko ime',\n    users: 'Корисници',\n    value: 'Вредност',\n    viewing: 'Pregled',\n    viewReadOnly: 'Прегледај само за читање',\n    welcome: 'Добродошли',\n    yes: 'Да',\n  },\n  localization: {\n    cannotCopySameLocale: 'Не може се копирати на исту локацију.',\n    copyFrom: 'Kopiraj iz',\n    copyFromTo: 'Kopiranje iz {{from}} u {{to}}',\n    copyTo: 'Kopiraj na',\n    copyToLocale: 'Kopiraj na lokaciju',\n    localeToPublish: 'Lokalitet za objavljivanje',\n    selectLocaleToCopy: 'Izaberite lokalitet za kopiranje',\n  },\n  operators: {\n    contains: 'садржи',\n    equals: 'једнако',\n    exists: 'постоји',\n    intersects: 'preseca',\n    isGreaterThan: 'је веће од',\n    isGreaterThanOrEqualTo: 'је веће од или једнако',\n    isIn: 'је у',\n    isLessThan: 'мање је од',\n    isLessThanOrEqualTo: 'мање је или једнако',\n    isLike: 'је као',\n    isNotEqualTo: 'није једнако',\n    isNotIn: 'није у',\n    isNotLike: 'nije kao',\n    near: 'близу',\n    within: 'unutar',\n  },\n  upload: {\n    addFile: 'Додај датотеку',\n    addFiles: 'Dodaj datoteke',\n    bulkUpload: 'Masovno otpremanje',\n    crop: 'Исеците слику',\n    cropToolDescription:\n      'Превуците углове изабраног подручја, нацртајте ново подручје или прилагодите вредности испод.',\n    download: 'Preuzmi',\n    dragAndDrop: 'Превуците и испустите датотеку',\n    dragAndDropHere: 'или превуците и испустите датотеку овде',\n    editImage: 'Уреди слику',\n    fileName: 'Име датотеке',\n    fileSize: 'Величина датотеке',\n    filesToUpload: 'Fajlovi za otpremanje',\n    fileToUpload: 'Fajl za otpremanje',\n    focalPoint: 'Централна тачка',\n    focalPointDescription:\n      'Превуците средишњу тачку директно на преглед или прилагодите вредности испод.',\n    height: 'Висина',\n    lessInfo: 'Мање информација',\n    moreInfo: 'Више информација',\n    noFile: 'Nema fajla',\n    pasteURL: 'Налепи URL',\n    previewSizes: 'Величине прегледа',\n    selectCollectionToBrowse: 'Одаберите колекцију за преглед',\n    selectFile: 'Одаберите датотеку',\n    setCropArea: 'Поставите подручје за исечену слику',\n    setFocalPoint: 'Поставите централну тачку',\n    sizes: 'Величине',\n    sizesFor: 'Величине за {{label}}',\n    width: 'Ширина',\n  },\n  validation: {\n    emailAddress: 'Молимо Вас унесите валидну емаил адресу.',\n    enterNumber: 'Молимо Вас унесите валидан број.',\n    fieldHasNo: 'Ово поље нема {{label}}',\n    greaterThanMax: '{{value}} прекорачује максималан дозвољени {{label}} лимит од {{max}}.',\n    invalidInput: 'Ово поље садржи невалидан унос.',\n    invalidSelection: 'Ово поље садржи невалидан одабир.',\n    invalidSelections: 'Ово поље има следеће невалидне одабире:',\n    lessThanMin: '{{value}} је испод дозвољеног минимума за {{label}} (доњи лимит је {{min}}).',\n    limitReached: 'Досегнут је лимит, може се додати само {{max}} ставки.',\n    longerThanMin: 'Ова вредност мора бити дужа од минималне дужине од {{минЛенгтх}} карактера',\n    notValidDate: '\"{{value}}\" није валидан датум.',\n    required: 'Ово поље је обавезно.',\n    requiresAtLeast: 'Ово поље захтева минимално {{count}} {{label}}.',\n    requiresNoMoreThan: 'Ово поље захтева не више од {{count}} {{label}}.',\n    requiresTwoNumbers: 'Ово поље захтева два броја.',\n    shorterThanMax: 'Ова вредност мора бити краћа од максималне дужине од {{maxLength}} карактера',\n    timezoneRequired: 'Потребна је временска зона.',\n    trueOrFalse: 'Ово поље може бити само тачно или нетачно',\n    username:\n      'Molimo unesite važeće korisničko ime. Može sadržati slova, brojeve, crtice, tačke i donje crte.',\n    validUploadID: 'Ово поље не садржи валидан ИД преноса.',\n  },\n  version: {\n    type: 'Тип',\n    aboutToPublishSelection: 'Управо ћете објавити све {{label}} у избору. Да ли сте сигурни?',\n    aboutToRestore: 'Вратићете {{label}} документ у стање у којем је био {{versionDate}}',\n    aboutToRestoreGlobal: 'Вратићете глобални {{label}} у стање у којем је био {{versionDate}}.',\n    aboutToRevertToPublished: 'Вратићете промене у документу у објављено стање. Да ли сте сигурни?',\n    aboutToUnpublish: 'Поништићете објаву овог документа. Да ли сте сигурни?',\n    aboutToUnpublishSelection:\n      'Управо ћете поништити објаву свих {{label}} у одабиру. Да ли сте сигурни?',\n    autosave: 'Аутоматско чување',\n    autosavedSuccessfully: 'Аутоматско чување успешно.',\n    autosavedVersion: 'Верзија аутоматски сачуваног документа',\n    changed: 'Промењено',\n    changedFieldsCount_one: '{{count}} promenjeno polje',\n    changedFieldsCount_other: '{{count}} promenjena polja',\n    compareVersion: 'Упореди верзију са:',\n    compareVersions: 'Uporedi verzije',\n    comparingAgainst: 'Upoređivanje sa',\n    confirmPublish: 'Потврди објаву',\n    confirmRevertToSaved: 'Потврдите враћање на сачувано',\n    confirmUnpublish: 'Потврдите поништавање објаве',\n    confirmVersionRestoration: 'Потврдите враћање верзије',\n    currentDocumentStatus: 'Тренутни {{docStatus}} документа',\n    currentDraft: 'Trenutni nacrt',\n    currentlyPublished: 'Trenutno objavljeno',\n    currentlyViewing: 'Trenutno gledate',\n    currentPublishedVersion: 'Trenutno Objavljena Verzija',\n    draft: 'Нацрт',\n    draftSavedSuccessfully: 'Нацрт успешно сачуван.',\n    lastSavedAgo: 'Задњи пут сачувано пре {{distance}',\n    modifiedOnly: 'Samo izmenjen',\n    moreVersions: 'Više verzija...',\n    noFurtherVersionsFound: 'Нису пронађене наредне верзије',\n    noRowsFound: '{{label}} није пронађено',\n    noRowsSelected: 'Nije odabrana {{label}}',\n    preview: 'Преглед',\n    previouslyDraft: 'Prethodno Nacrt',\n    previouslyPublished: 'Prethodno objavljeno',\n    previousVersion: 'Prethodna verzija',\n    problemRestoringVersion: 'Настао је проблем при враћању ове верзије',\n    publish: 'Објавити',\n    publishAllLocales: 'Objavi sve lokalitete',\n    publishChanges: 'Објави промене',\n    published: 'Објављено',\n    publishIn: 'Objavi na {{locale}}',\n    publishing: 'Objavljivanje',\n    restoreAsDraft: 'Vrati kao nacrt',\n    restoredSuccessfully: 'Успешно враћено.',\n    restoreThisVersion: 'Врати ову верзију',\n    restoring: 'Враћање...',\n    reverting: 'Враћање...',\n    revertToPublished: 'Врати на објављено',\n    saveDraft: 'Сачувај нацрт',\n    scheduledSuccessfully: 'Успешно заказано.',\n    schedulePublish: 'Planiranje publikovanja',\n    selectLocales: 'Одаберите језике',\n    selectVersionToCompare: 'Одаберите верзију за упоређивање',\n    showingVersionsFor: 'Показујем верзије за:',\n    showLocales: 'Прикажи језике:',\n    specificVersion: 'Specifična verzija',\n    status: 'Статус',\n    unpublish: 'Поништи објаву',\n    unpublishing: 'Поништавање објаве...',\n    version: 'Верзија',\n    versionAgo: 'pre {{distance}}',\n    versionCount_many: '{{count}} пронађених верзија',\n    versionCount_none: 'Нема пронађених верзија',\n    versionCount_one: '{{count}} пронађена верзија',\n    versionCount_other: '{{count}} пронађених верзија',\n    versionCreatedOn: '{{version}} креираних:',\n    versionID: 'Идентификатор верзије',\n    versions: 'Верзије',\n    viewingVersion: 'Преглед верзије за {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Преглед верзије за глобални {{entityLabel}}',\n    viewingVersions: 'Преглед верзија за {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Преглед верзије за глобални {{entityLabel}}',\n  },\n}\n\nexport const rs: Language = {\n  dateFNSKey: 'rs',\n  translations: rsTranslations,\n}\n"], "names": ["rsTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "rs", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BAA0B;QAC1BC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}