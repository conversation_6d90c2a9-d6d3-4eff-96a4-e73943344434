export const enTranslations = {
    authentication: {
        account: 'Account',
        accountOfCurrentUser: 'Account of current user',
        accountVerified: 'Account verified successfully.',
        alreadyActivated: 'Already Activated',
        alreadyLoggedIn: 'Already logged in',
        apiKey: 'API Key',
        authenticated: 'Authenticated',
        backToLogin: 'Back to login',
        beginCreateFirstUser: 'To begin, create your first user.',
        changePassword: 'Change Password',
        checkYourEmailForPasswordReset: "If the email address is associated with an account, you will receive instructions to reset your password shortly. Please check your spam or junk mail folder if you don't see the email in your inbox.",
        confirmGeneration: 'Confirm Generation',
        confirmPassword: 'Confirm Password',
        createFirstUser: 'Create first user',
        emailNotValid: 'The email provided is not valid',
        emailOrUsername: '<PERSON>ail or Username',
        emailSent: 'Email Sent',
        emailVerified: 'Email verified successfully.',
        enableAPIKey: 'Enable API Key',
        failedToUnlock: 'Failed to unlock',
        forceUnlock: 'Force Unlock',
        forgotPassword: 'Forgot Password',
        forgotPasswordEmailInstructions: 'Please enter your email below. You will receive an email message with instructions on how to reset your password.',
        forgotPasswordUsernameInstructions: 'Please enter your username below. Instructions on how to reset your password will be sent to email address associated with your username.',
        usernameNotValid: 'The username provided is not valid',
        forgotPasswordQuestion: 'Forgot password?',
        generate: 'Generate',
        generateNewAPIKey: 'Generate new API key',
        generatingNewAPIKeyWillInvalidate: 'Generating a new API key will <1>invalidate</1> the previous key. Are you sure you wish to continue?',
        lockUntil: 'Lock Until',
        logBackIn: 'Log back in',
        loggedIn: 'To log in with another user, you should <0>log out</0> first.',
        loggedInChangePassword: 'To change your password, go to your <0>account</0> and edit your password there.',
        loggedOutInactivity: 'You have been logged out due to inactivity.',
        loggedOutSuccessfully: 'You have been logged out successfully.',
        loggingOut: 'Logging out...',
        login: 'Login',
        loginAttempts: 'Login Attempts',
        loginUser: 'Login user',
        loginWithAnotherUser: 'To log in with another user, you should <0>log out</0> first.',
        logOut: 'Log out',
        logout: 'Logout',
        logoutSuccessful: 'Logout successful.',
        logoutUser: 'Logout user',
        newAccountCreated: 'A new account has just been created for you to access <a href="{{serverURL}}">{{serverURL}}</a> Please click on the following link or paste the URL below into your browser to verify your email: <a href="{{verificationURL}}">{{verificationURL}}</a><br> After verifying your email, you will be able to log in successfully.',
        newAPIKeyGenerated: 'New API Key Generated.',
        newPassword: 'New Password',
        passed: 'Authentication Passed',
        passwordResetSuccessfully: 'Password reset successfully.',
        resetPassword: 'Reset Password',
        resetPasswordExpiration: 'Reset Password Expiration',
        resetPasswordToken: 'Reset Password Token',
        resetYourPassword: 'Reset Your Password',
        stayLoggedIn: 'Stay logged in',
        successfullyRegisteredFirstUser: 'Successfully registered first user.',
        successfullyUnlocked: 'Successfully unlocked',
        tokenRefreshSuccessful: 'Token refresh successful.',
        unableToVerify: 'Unable to Verify',
        username: 'Username',
        verified: 'Verified',
        verifiedSuccessfully: 'Verified Successfully',
        verify: 'Verify',
        verifyUser: 'Verify User',
        verifyYourEmail: 'Verify your email',
        youAreInactive: "You haven't been active in a little while and will shortly be automatically logged out for your own security. Would you like to stay logged in?",
        youAreReceivingResetPassword: 'You are receiving this because you (or someone else) have requested the reset of the password for your account. Please click on the following link, or paste this into your browser to complete the process:',
        youDidNotRequestPassword: 'If you did not request this, please ignore this email and your password will remain unchanged.'
    },
    error: {
        accountAlreadyActivated: 'This account has already been activated.',
        autosaving: 'There was a problem while autosaving this document.',
        correctInvalidFields: 'Please correct invalid fields.',
        deletingFile: 'There was an error deleting file.',
        deletingTitle: 'There was an error while deleting {{title}}. Please check your connection and try again.',
        documentNotFound: 'The document with ID {{id}} could not be found. It may have been deleted or never existed, or you may not have access to it.',
        emailOrPasswordIncorrect: 'The email or password provided is incorrect.',
        followingFieldsInvalid_one: 'The following field is invalid:',
        followingFieldsInvalid_other: 'The following fields are invalid:',
        incorrectCollection: 'Incorrect Collection',
        insufficientClipboardPermissions: 'Clipboard access denied. Please check your clipboard permissions.',
        invalidClipboardData: 'Invalid clipboard data.',
        invalidFileType: 'Invalid file type',
        invalidFileTypeValue: 'Invalid file type: {{value}}',
        invalidRequestArgs: 'Invalid arguments passed in request: {{args}}',
        loadingDocument: 'There was a problem loading the document with ID of {{id}}.',
        localesNotSaved_one: 'The following locale could not be saved:',
        localesNotSaved_other: 'The following locales could not be saved:',
        logoutFailed: 'Logout failed.',
        missingEmail: 'Missing email.',
        missingIDOfDocument: 'Missing ID of document to update.',
        missingIDOfVersion: 'Missing ID of version.',
        missingRequiredData: 'Missing required data.',
        noFilesUploaded: 'No files were uploaded.',
        noMatchedField: 'No matched field found for "{{label}}"',
        notAllowedToAccessPage: 'You are not allowed to access this page.',
        notAllowedToPerformAction: 'You are not allowed to perform this action.',
        notFound: 'The requested resource was not found.',
        noUser: 'No User',
        previewing: 'There was a problem previewing this document.',
        problemUploadingFile: 'There was a problem while uploading the file.',
        restoringTitle: 'There was an error while restoring {{title}}. Please check your connection and try again.',
        tokenInvalidOrExpired: 'Token is either invalid or has expired.',
        tokenNotProvided: 'Token not provided.',
        unableToCopy: 'Unable to copy.',
        unableToDeleteCount: 'Unable to delete {{count}} out of {{total}} {{label}}.',
        unableToReindexCollection: 'Error reindexing collection {{collection}}. Operation aborted.',
        unableToUpdateCount: 'Unable to update {{count}} out of {{total}} {{label}}.',
        unauthorized: 'Unauthorized, you must be logged in to make this request.',
        unauthorizedAdmin: 'Unauthorized, this user does not have access to the admin panel.',
        unknown: 'An unknown error has occurred.',
        unPublishingDocument: 'There was a problem while un-publishing this document.',
        unspecific: 'An error has occurred.',
        unverifiedEmail: 'Please verify your email before logging in.',
        userEmailAlreadyRegistered: 'A user with the given email is already registered.',
        userLocked: 'This user is locked due to having too many failed login attempts.',
        usernameAlreadyRegistered: 'A user with the given username is already registered.',
        usernameOrPasswordIncorrect: 'The username or password provided is incorrect.',
        valueMustBeUnique: 'Value must be unique',
        verificationTokenInvalid: 'Verification token is invalid.'
    },
    fields: {
        addLabel: 'Add {{label}}',
        addLink: 'Add Link',
        addNew: 'Add new',
        addNewLabel: 'Add new {{label}}',
        addRelationship: 'Add Relationship',
        addUpload: 'Add Upload',
        block: 'block',
        blocks: 'blocks',
        blockType: 'Block Type',
        chooseBetweenCustomTextOrDocument: 'Choose between entering a custom text URL or linking to another document.',
        chooseDocumentToLink: 'Choose a document to link to',
        chooseFromExisting: 'Choose from existing',
        chooseLabel: 'Choose {{label}}',
        collapseAll: 'Collapse All',
        customURL: 'Custom URL',
        editLabelData: 'Edit {{label}} data',
        editLink: 'Edit Link',
        editRelationship: 'Edit Relationship',
        enterURL: 'Enter a URL',
        internalLink: 'Internal Link',
        itemsAndMore: '{{items}} and {{count}} more',
        labelRelationship: '{{label}} Relationship',
        latitude: 'Latitude',
        linkedTo: 'Linked to <0>{{label}}</0>',
        linkType: 'Link Type',
        longitude: 'Longitude',
        newLabel: 'New {{label}}',
        openInNewTab: 'Open in new tab',
        passwordsDoNotMatch: 'Passwords do not match.',
        relatedDocument: 'Related Document',
        relationTo: 'Relation To',
        removeRelationship: 'Remove Relationship',
        removeUpload: 'Remove Upload',
        saveChanges: 'Save changes',
        searchForBlock: 'Search for a block',
        selectExistingLabel: 'Select existing {{label}}',
        selectFieldsToEdit: 'Select fields to edit',
        showAll: 'Show All',
        swapRelationship: 'Swap Relationship',
        swapUpload: 'Swap Upload',
        textToDisplay: 'Text to display',
        toggleBlock: 'Toggle block',
        uploadNewLabel: 'Upload new {{label}}'
    },
    folder: {
        browseByFolder: 'Browse by Folder',
        byFolder: 'By Folder',
        deleteFolder: 'Delete Folder',
        folderName: 'Folder Name',
        folders: 'Folders',
        folderTypeDescription: 'Select which type of collection documents should be allowed in this folder.',
        itemHasBeenMoved: '{{title}} has been moved to {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} has been moved to the root folder',
        itemsMovedToFolder: '{{title}} moved to {{folderName}}',
        itemsMovedToRoot: '{{title}} moved to the root folder',
        moveFolder: 'Move Folder',
        moveItemsToFolderConfirmation: 'You are about to move <1>{{count}} {{label}}</1> to <2>{{toFolder}}</2>. Are you sure?',
        moveItemsToRootConfirmation: 'You are about to move <1>{{count}} {{label}}</1> to the root folder. Are you sure?',
        moveItemToFolderConfirmation: 'You are about to move <1>{{title}}</1> to <2>{{toFolder}}</2>. Are you sure?',
        moveItemToRootConfirmation: 'You are about to move <1>{{title}}</1> to the root folder. Are you sure?',
        movingFromFolder: 'Moving {{title}} from {{fromFolder}}',
        newFolder: 'New Folder',
        noFolder: 'No Folder',
        renameFolder: 'Rename Folder',
        searchByNameInFolder: 'Search by Name in {{folderName}}',
        selectFolderForItem: 'Select folder for {{title}}'
    },
    general: {
        name: 'Name',
        aboutToDelete: 'You are about to delete the {{label}} <1>{{title}}</1>. Are you sure?',
        aboutToDeleteCount_many: 'You are about to delete {{count}} {{label}}',
        aboutToDeleteCount_one: 'You are about to delete {{count}} {{label}}',
        aboutToDeleteCount_other: 'You are about to delete {{count}} {{label}}',
        aboutToPermanentlyDelete: 'You are about to permanently delete the {{label}} <1>{{title}}</1>. Are you sure?',
        aboutToPermanentlyDeleteTrash: 'You are about to permanently delete <0>{{count}}</0> <1>{{label}}</1> from the trash. Are you sure?',
        aboutToRestore: 'You are about to restore the {{label}} <1>{{title}}</1>. Are you sure?',
        aboutToRestoreAsDraft: 'You are about to restore the {{label}} <1>{{title}}</1> as a draft. Are you sure?',
        aboutToRestoreAsDraftCount: 'You are about to restore {{count}} {{label}} as draft',
        aboutToRestoreCount: 'You are about to restore {{count}} {{label}}',
        aboutToTrash: 'You are about to move the {{label}} <1>{{title}}</1> to the trash. Are you sure?',
        aboutToTrashCount: 'You are about to move {{count}} {{label}} to the trash',
        addBelow: 'Add Below',
        addFilter: 'Add Filter',
        adminTheme: 'Admin Theme',
        all: 'All',
        allCollections: 'All Collections',
        allLocales: 'All locales',
        and: 'And',
        anotherUser: 'Another user',
        anotherUserTakenOver: 'Another user has taken over editing this document.',
        applyChanges: 'Apply Changes',
        ascending: 'Ascending',
        automatic: 'Automatic',
        backToDashboard: 'Back to Dashboard',
        cancel: 'Cancel',
        changesNotSaved: 'Your changes have not been saved. If you leave now, you will lose your changes.',
        clear: 'Clear',
        clearAll: 'Clear All',
        close: 'Close',
        collapse: 'Collapse',
        collections: 'Collections',
        columns: 'Columns',
        columnToSort: 'Column to Sort',
        confirm: 'Confirm',
        confirmCopy: 'Confirm copy',
        confirmDeletion: 'Confirm deletion',
        confirmDuplication: 'Confirm duplication',
        confirmMove: 'Confirm move',
        confirmReindex: 'Reindex all {{collections}}?',
        confirmReindexAll: 'Reindex all collections?',
        confirmReindexDescription: 'This will remove existing indexes and reindex documents in the {{collections}} collections.',
        confirmReindexDescriptionAll: 'This will remove existing indexes and reindex documents in all collections.',
        confirmRestoration: 'Confirm restoration',
        copied: 'Copied',
        copy: 'Copy',
        copyField: 'Copy Field',
        copying: 'Copying',
        copyRow: 'Copy Row',
        copyWarning: 'You are about to overwrite {{to}} with {{from}} for {{label}} {{title}}. Are you sure?',
        create: 'Create',
        created: 'Created',
        createdAt: 'Created At',
        createNew: 'Create New',
        createNewLabel: 'Create new {{label}}',
        creating: 'Creating',
        creatingNewLabel: 'Creating new {{label}}',
        currentlyEditing: 'is currently editing this document. If you take over, they will be blocked from continuing to edit, and may also lose unsaved changes.',
        custom: 'Custom',
        dark: 'Dark',
        dashboard: 'Dashboard',
        delete: 'Delete',
        deleted: 'Deleted',
        deletedAt: 'Deleted At',
        deletedCountSuccessfully: 'Deleted {{count}} {{label}} successfully.',
        deletedSuccessfully: 'Deleted successfully.',
        deletePermanently: 'Skip trash and delete permanently',
        deleting: 'Deleting...',
        depth: 'Depth',
        descending: 'Descending',
        deselectAllRows: 'Deselect all rows',
        document: 'Document',
        documentIsTrashed: 'This {{label}} is trashed and is read-only.',
        documentLocked: 'Document locked',
        documents: 'Documents',
        duplicate: 'Duplicate',
        duplicateWithoutSaving: 'Duplicate without saving changes',
        edit: 'Edit',
        editAll: 'Edit all',
        editedSince: 'Edited since',
        editing: 'Editing',
        editingLabel_many: 'Editing {{count}} {{label}}',
        editingLabel_one: 'Editing {{count}} {{label}}',
        editingLabel_other: 'Editing {{count}} {{label}}',
        editingTakenOver: 'Editing taken over',
        editLabel: 'Edit {{label}}',
        email: 'Email',
        emailAddress: 'Email Address',
        emptyTrash: 'Empty trash',
        emptyTrashLabel: 'Empty {{label}} trash',
        enterAValue: 'Enter a value',
        error: 'Error',
        errors: 'Errors',
        exitLivePreview: 'Exit Live Preview',
        export: 'Export',
        fallbackToDefaultLocale: 'Fallback to default locale',
        false: 'False',
        filter: 'Filter',
        filters: 'Filters',
        filterWhere: 'Filter {{label}} where',
        globals: 'Globals',
        goBack: 'Go back',
        groupByLabel: 'Group by {{label}}',
        import: 'Import',
        isEditing: 'is editing',
        item: 'item',
        items: 'items',
        language: 'Language',
        lastModified: 'Last Modified',
        leaveAnyway: 'Leave anyway',
        leaveWithoutSaving: 'Leave without saving',
        light: 'Light',
        livePreview: 'Live Preview',
        loading: 'Loading',
        locale: 'Locale',
        locales: 'Locales',
        menu: 'Menu',
        moreOptions: 'More options',
        move: 'Move',
        moveConfirm: 'You are about to move {{count}} {{label}} to <1>{{destination}}</1>. Are you sure?',
        moveCount: 'Move {{count}} {{label}}',
        moveDown: 'Move Down',
        moveUp: 'Move Up',
        moving: 'Moving',
        movingCount: 'Moving {{count}} {{label}}',
        newPassword: 'New Password',
        next: 'Next',
        no: 'No',
        noDateSelected: 'No date selected',
        noFiltersSet: 'No filters set',
        noLabel: '<No {{label}}>',
        none: 'None',
        noOptions: 'No options',
        noResults: "No {{label}} found. Either no {{label}} exist yet or none match the filters you've specified above.",
        notFound: 'Not Found',
        nothingFound: 'Nothing found',
        noTrashResults: 'No {{label}} in trash.',
        noUpcomingEventsScheduled: 'No upcoming events scheduled.',
        noValue: 'No value',
        of: 'of',
        only: 'Only',
        open: 'Open',
        or: 'Or',
        order: 'Order',
        overwriteExistingData: 'Overwrite existing field data',
        pageNotFound: 'Page not found',
        password: 'Password',
        pasteField: 'Paste Field',
        pasteRow: 'Paste Row',
        payloadSettings: 'Payload Settings',
        permanentlyDelete: 'Permanently Delete',
        permanentlyDeletedCountSuccessfully: 'Permanently deleted {{count}} {{label}} successfully.',
        perPage: 'Per Page: {{limit}}',
        previous: 'Previous',
        reindex: 'Reindex',
        reindexingAll: 'Reindexing all {{collections}}.',
        remove: 'Remove',
        rename: 'Rename',
        reset: 'Reset',
        resetPreferences: 'Reset Preferences',
        resetPreferencesDescription: 'This will reset all of your preferences to their default settings.',
        resettingPreferences: 'Resetting Preferences.',
        restore: 'Restore',
        restoreAsPublished: 'Restore as published version',
        restoredCountSuccessfully: 'Restored {{count}} {{label}} successfully.',
        restoring: 'Restoring...',
        row: 'Row',
        rows: 'Rows',
        save: 'Save',
        saving: 'Saving...',
        schedulePublishFor: 'Schedule publish for {{title}}',
        searchBy: 'Search by {{label}}',
        select: 'Select',
        selectAll: 'Select all {{count}} {{label}}',
        selectAllRows: 'Select all rows',
        selectedCount: '{{count}} {{label}} selected',
        selectLabel: 'Select {{label}}',
        selectValue: 'Select a value',
        showAllLabel: 'Show all {{label}}',
        sorryNotFound: 'Sorry—there is nothing to correspond with your request.',
        sort: 'Sort',
        sortByLabelDirection: 'Sort by {{label}} {{direction}}',
        stayOnThisPage: 'Stay on this page',
        submissionSuccessful: 'Submission Successful.',
        submit: 'Submit',
        submitting: 'Submitting...',
        success: 'Success',
        successfullyCreated: '{{label}} successfully created.',
        successfullyDuplicated: '{{label}} successfully duplicated.',
        successfullyReindexed: 'Successfully reindexed {{count}} of {{total}} documents from {{collections}}',
        takeOver: 'Take over',
        thisLanguage: 'English',
        time: 'Time',
        timezone: 'Timezone',
        titleDeleted: '{{label}} "{{title}}" successfully deleted.',
        titleRestored: '{{label}} "{{title}}" successfully restored.',
        titleTrashed: '{{label}} "{{title}}" moved to trash.',
        trash: 'Trash',
        trashedCountSuccessfully: '{{count}} {{label}} moved to trash.',
        true: 'True',
        unauthorized: 'Unauthorized',
        unsavedChanges: 'You have unsaved changes. Save or discard before continuing.',
        unsavedChangesDuplicate: 'You have unsaved changes. Would you like to continue to duplicate?',
        untitled: 'Untitled',
        upcomingEvents: 'Upcoming Events',
        updatedAt: 'Updated At',
        updatedCountSuccessfully: 'Updated {{count}} {{label}} successfully.',
        updatedLabelSuccessfully: 'Updated {{label}} successfully.',
        updatedSuccessfully: 'Updated successfully.',
        updateForEveryone: 'Update for everyone',
        updating: 'Updating',
        uploading: 'Uploading',
        uploadingBulk: 'Uploading {{current}} of {{total}}',
        user: 'User',
        username: 'Username',
        users: 'Users',
        value: 'Value',
        viewing: 'Viewing',
        viewReadOnly: 'View read-only',
        welcome: 'Welcome',
        yes: 'Yes'
    },
    localization: {
        cannotCopySameLocale: 'Cannot copy to the same locale',
        copyFrom: 'Copy from',
        copyFromTo: 'Copying from {{from}} to {{to}}',
        copyTo: 'Copy to',
        copyToLocale: 'Copy to locale',
        localeToPublish: 'Locale to publish',
        selectLocaleToCopy: 'Select locale to copy'
    },
    operators: {
        contains: 'contains',
        equals: 'equals',
        exists: 'exists',
        intersects: 'intersects',
        isGreaterThan: 'is greater than',
        isGreaterThanOrEqualTo: 'is greater than or equal to',
        isIn: 'is in',
        isLessThan: 'is less than',
        isLessThanOrEqualTo: 'is less than or equal to',
        isLike: 'is like',
        isNotEqualTo: 'is not equal to',
        isNotIn: 'is not in',
        isNotLike: 'is not like',
        near: 'near',
        within: 'within'
    },
    upload: {
        addFile: 'Add file',
        addFiles: 'Add files',
        bulkUpload: 'Bulk Upload',
        crop: 'Crop',
        cropToolDescription: 'Drag the corners of the selected area, draw a new area or adjust the values below.',
        download: 'Download',
        dragAndDrop: 'Drag and drop a file',
        dragAndDropHere: 'or drag and drop a file here',
        editImage: 'Edit Image',
        fileName: 'File Name',
        fileSize: 'File Size',
        filesToUpload: 'Files to Upload',
        fileToUpload: 'File to Upload',
        focalPoint: 'Focal Point',
        focalPointDescription: 'Drag the focal point directly on the preview or adjust the values below.',
        height: 'Height',
        lessInfo: 'Less info',
        moreInfo: 'More info',
        noFile: 'No file',
        pasteURL: 'Paste URL',
        previewSizes: 'Preview Sizes',
        selectCollectionToBrowse: 'Select a Collection to Browse',
        selectFile: 'Select a file',
        setCropArea: 'Set crop area',
        setFocalPoint: 'Set focal point',
        sizes: 'Sizes',
        sizesFor: 'Sizes for {{label}}',
        width: 'Width'
    },
    validation: {
        emailAddress: 'Please enter a valid email address.',
        enterNumber: 'Please enter a valid number.',
        fieldHasNo: 'This field has no {{label}}',
        greaterThanMax: '{{value}} is greater than the max allowed {{label}} of {{max}}.',
        invalidInput: 'This field has an invalid input.',
        invalidSelection: 'This field has an invalid selection.',
        invalidSelections: 'This field has the following invalid selections:',
        lessThanMin: '{{value}} is less than the min allowed {{label}} of {{min}}.',
        limitReached: 'Limit reached, only {{max}} items can be added.',
        longerThanMin: 'This value must be longer than the minimum length of {{minLength}} characters.',
        notValidDate: '"{{value}}" is not a valid date.',
        required: 'This field is required.',
        requiresAtLeast: 'This field requires at least {{count}} {{label}}.',
        requiresNoMoreThan: 'This field requires no more than {{count}} {{label}}.',
        requiresTwoNumbers: 'This field requires two numbers.',
        shorterThanMax: 'This value must be shorter than the max length of {{maxLength}} characters.',
        timezoneRequired: 'A timezone is required.',
        trueOrFalse: 'This field can only be equal to true or false.',
        username: 'Please enter a valid username. Can contain letters, numbers, hyphens, periods and underscores.',
        validUploadID: 'This field is not a valid upload ID.'
    },
    version: {
        type: 'Type',
        aboutToPublishSelection: 'You are about to publish all {{label}} in the selection. Are you sure?',
        aboutToRestore: 'You are about to restore this {{label}} document to the state that it was in on {{versionDate}}.',
        aboutToRestoreGlobal: 'You are about to restore the global {{label}} to the state that it was in on {{versionDate}}.',
        aboutToRevertToPublished: "You are about to revert this document's changes to its published state. Are you sure?",
        aboutToUnpublish: 'You are about to unpublish this document. Are you sure?',
        aboutToUnpublishSelection: 'You are about to unpublish all {{label}} in the selection. Are you sure?',
        autosave: 'Autosave',
        autosavedSuccessfully: 'Autosaved successfully.',
        autosavedVersion: 'Autosaved version',
        changed: 'Changed',
        changedFieldsCount_one: '{{count}} changed field',
        changedFieldsCount_other: '{{count}} changed fields',
        compareVersion: 'Compare version against:',
        compareVersions: 'Compare Versions',
        comparingAgainst: 'Comparing against',
        confirmPublish: 'Confirm publish',
        confirmRevertToSaved: 'Confirm revert to saved',
        confirmUnpublish: 'Confirm unpublish',
        confirmVersionRestoration: 'Confirm Version Restoration',
        currentDocumentStatus: 'Current {{docStatus}} document',
        currentDraft: 'Current Draft',
        currentlyPublished: 'Currently Published',
        currentlyViewing: 'Currently viewing',
        currentPublishedVersion: 'Current Published Version',
        draft: 'Draft',
        draftSavedSuccessfully: 'Draft saved successfully.',
        lastSavedAgo: 'Last saved {{distance}} ago',
        modifiedOnly: 'Modified only',
        moreVersions: 'More versions...',
        noFurtherVersionsFound: 'No further versions found',
        noRowsFound: 'No {{label}} found',
        noRowsSelected: 'No {{label}} selected',
        preview: 'Preview',
        previouslyDraft: 'Previously a Draft',
        previouslyPublished: 'Previously Published',
        previousVersion: 'Previous Version',
        problemRestoringVersion: 'There was a problem restoring this version',
        publish: 'Publish',
        publishAllLocales: 'Publish all locales',
        publishChanges: 'Publish changes',
        published: 'Published',
        publishIn: 'Publish in {{locale}}',
        publishing: 'Publishing',
        restoreAsDraft: 'Restore as draft',
        restoredSuccessfully: 'Restored Successfully.',
        restoreThisVersion: 'Restore this version',
        restoring: 'Restoring...',
        reverting: 'Reverting...',
        revertToPublished: 'Revert to published',
        saveDraft: 'Save Draft',
        scheduledSuccessfully: 'Scheduled successfully.',
        schedulePublish: 'Schedule Publish',
        selectLocales: 'Select locales to display',
        selectVersionToCompare: 'Select a version to compare',
        showingVersionsFor: 'Showing versions for:',
        showLocales: 'Show locales:',
        specificVersion: 'Specific Version',
        status: 'Status',
        unpublish: 'Unpublish',
        unpublishing: 'Unpublishing...',
        version: 'Version',
        versionAgo: '{{distance}} ago',
        versionCount_many: '{{count}} versions found',
        versionCount_none: 'No versions found',
        versionCount_one: '{{count}} version found',
        versionCount_other: '{{count}} versions found',
        versionCreatedOn: '{{version}} created on:',
        versionID: 'Version ID',
        versions: 'Versions',
        viewingVersion: 'Viewing version for the {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Viewing version for the global {{entityLabel}}',
        viewingVersions: 'Viewing versions for the {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Viewing versions for the global {{entityLabel}}'
    }
};
export const en = {
    dateFNSKey: 'en-US',
    translations: enTranslations
};

//# sourceMappingURL=en.js.map