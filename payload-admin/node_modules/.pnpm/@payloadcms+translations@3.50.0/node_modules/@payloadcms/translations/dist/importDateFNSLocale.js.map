{"version": 3, "sources": ["../src/importDateFNSLocale.ts"], "sourcesContent": ["import type { Locale } from 'date-fns'\n\nexport const importDateFNSLocale = async (locale: string): Promise<Locale> => {\n  let result\n\n  switch (locale) {\n    case 'ar':\n      result = (await import('date-fns/locale/ar')).ar\n\n      break\n    case 'az':\n      result = (await import('date-fns/locale/az')).az\n\n      break\n    case 'bg':\n      result = (await import('date-fns/locale/bg')).bg\n\n      break\n    case 'bn-BD':\n      result = (await import('date-fns/locale/bn')).bn\n\n      break\n    case 'bn-IN':\n      result = (await import('date-fns/locale/bn')).bn\n\n      break\n    case 'ca':\n      result = (await import('date-fns/locale/ca')).ca\n\n      break\n    case 'cs':\n      result = (await import('date-fns/locale/cs')).cs\n\n      break\n    case 'da':\n      result = (await import('date-fns/locale/da')).da\n\n      break\n    case 'de':\n      result = (await import('date-fns/locale/de')).de\n\n      break\n    case 'en-US':\n      result = (await import('date-fns/locale/en-US')).enUS\n\n      break\n    case 'es':\n      result = (await import('date-fns/locale/es')).es\n\n      break\n    case 'et':\n      result = (await import('date-fns/locale/et')).et\n\n      break\n    case 'fa-IR':\n      result = (await import('date-fns/locale/fa-IR')).faIR\n\n      break\n    case 'fr':\n      result = (await import('date-fns/locale/fr')).fr\n\n      break\n    case 'he':\n      result = (await import('date-fns/locale/he')).he\n\n      break\n    case 'hr':\n      result = (await import('date-fns/locale/hr')).hr\n\n      break\n    case 'hu':\n      result = (await import('date-fns/locale/hu')).hu\n\n      break\n    case 'id':\n      result = (await import('date-fns/locale/id')).id\n\n      break\n    case 'it':\n      result = (await import('date-fns/locale/it')).it\n\n      break\n    case 'ja':\n      result = (await import('date-fns/locale/ja')).ja\n\n      break\n    case 'ko':\n      result = (await import('date-fns/locale/ko')).ko\n\n      break\n    case 'lt':\n      result = (await import('date-fns/locale/lt')).lt\n\n      break\n    case 'lv':\n      result = (await import('date-fns/locale/lv')).lv\n\n      break\n\n    case 'nb':\n      result = (await import('date-fns/locale/nb')).nb\n\n      break\n    case 'nl':\n      result = (await import('date-fns/locale/nl')).nl\n\n      break\n    case 'pl':\n      result = (await import('date-fns/locale/pl')).pl\n\n      break\n    case 'pt':\n      result = (await import('date-fns/locale/pt')).pt\n\n      break\n    case 'ro':\n      result = (await import('date-fns/locale/ro')).ro\n\n      break\n    case 'rs':\n      result = (await import('date-fns/locale/sr')).sr\n\n      break\n    case 'rs-Latin':\n      result = (await import('date-fns/locale/sr-Latn')).srLatn\n\n      break\n    case 'ru':\n      result = (await import('date-fns/locale/ru')).ru\n\n      break\n    case 'sk':\n      result = (await import('date-fns/locale/sk')).sk\n\n      break\n    case 'sl-SI':\n      result = (await import('date-fns/locale/sl')).sl\n\n      break\n    case 'sv':\n      result = (await import('date-fns/locale/sv')).sv\n\n      break\n    case 'th':\n      result = (await import('date-fns/locale/th')).th\n\n      break\n    case 'tr':\n      result = (await import('date-fns/locale/tr')).tr\n\n      break\n    case 'uk':\n      result = (await import('date-fns/locale/uk')).uk\n\n      break\n    case 'vi':\n      result = (await import('date-fns/locale/vi')).vi\n\n      break\n    case 'zh-CN':\n      result = (await import('date-fns/locale/zh-CN')).zhCN\n\n      break\n    case 'zh-TW':\n      result = (await import('date-fns/locale/zh-TW')).zhTW\n\n      break\n  }\n\n  // @ts-expect-error - I'm not sure if this is still necessary.\n  if (result?.default) {\n    // @ts-expect-error - I'm not sure if this is still necessary.\n    return result.default\n  }\n\n  return result as Locale\n}\n"], "names": ["importDateFNSLocale", "locale", "result", "ar", "az", "bg", "bn", "ca", "cs", "da", "de", "enUS", "es", "et", "faIR", "fr", "he", "hr", "hu", "id", "it", "ja", "ko", "lt", "lv", "nb", "nl", "pl", "pt", "ro", "sr", "srLatn", "ru", "sk", "sl", "sv", "th", "tr", "uk", "vi", "zhCN", "zhTW", "default"], "mappings": "AAEA,OAAO,MAAMA,sBAAsB,OAAOC;IACxC,IAAIC;IAEJ,OAAQD;QACN,KAAK;YACHC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGC,EAAE;YAEhD;QACF,KAAK;YACHD,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGE,EAAE;YAEhD;QACF,KAAK;YACHF,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGG,EAAE;YAEhD;QACF,KAAK;YACHH,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGI,EAAE;YAEhD;QACF,KAAK;YACHJ,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGI,EAAE;YAEhD;QACF,KAAK;YACHJ,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGK,EAAE;YAEhD;QACF,KAAK;YACHL,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGM,EAAE;YAEhD;QACF,KAAK;YACHN,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGO,EAAE;YAEhD;QACF,KAAK;YACHP,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGQ,EAAE;YAEhD;QACF,KAAK;YACHR,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,wBAAuB,EAAGS,IAAI;YAErD;QACF,KAAK;YACHT,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGU,EAAE;YAEhD;QACF,KAAK;YACHV,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGW,EAAE;YAEhD;QACF,KAAK;YACHX,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,wBAAuB,EAAGY,IAAI;YAErD;QACF,KAAK;YACHZ,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGa,EAAE;YAEhD;QACF,KAAK;YACHb,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGc,EAAE;YAEhD;QACF,KAAK;YACHd,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGe,EAAE;YAEhD;QACF,KAAK;YACHf,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGgB,EAAE;YAEhD;QACF,KAAK;YACHhB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGiB,EAAE;YAEhD;QACF,KAAK;YACHjB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGkB,EAAE;YAEhD;QACF,KAAK;YACHlB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGmB,EAAE;YAEhD;QACF,KAAK;YACHnB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGoB,EAAE;YAEhD;QACF,KAAK;YACHpB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGqB,EAAE;YAEhD;QACF,KAAK;YACHrB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGsB,EAAE;YAEhD;QAEF,KAAK;YACHtB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGuB,EAAE;YAEhD;QACF,KAAK;YACHvB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGwB,EAAE;YAEhD;QACF,KAAK;YACHxB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGyB,EAAE;YAEhD;QACF,KAAK;YACHzB,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAG0B,EAAE;YAEhD;QACF,KAAK;YACH1B,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAG2B,EAAE;YAEhD;QACF,KAAK;YACH3B,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAG4B,EAAE;YAEhD;QACF,KAAK;YACH5B,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,0BAAyB,EAAG6B,MAAM;YAEzD;QACF,KAAK;YACH7B,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAG8B,EAAE;YAEhD;QACF,KAAK;YACH9B,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAG+B,EAAE;YAEhD;QACF,KAAK;YACH/B,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGgC,EAAE;YAEhD;QACF,KAAK;YACHhC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGiC,EAAE;YAEhD;QACF,KAAK;YACHjC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGkC,EAAE;YAEhD;QACF,KAAK;YACHlC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGmC,EAAE;YAEhD;QACF,KAAK;YACHnC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGoC,EAAE;YAEhD;QACF,KAAK;YACHpC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,qBAAoB,EAAGqC,EAAE;YAEhD;QACF,KAAK;YACHrC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,wBAAuB,EAAGsC,IAAI;YAErD;QACF,KAAK;YACHtC,SAAS,AAAC,CAAA,MAAM,MAAM,CAAC,wBAAuB,EAAGuC,IAAI;YAErD;IACJ;IAEA,8DAA8D;IAC9D,IAAIvC,QAAQwC,SAAS;QACnB,8DAA8D;QAC9D,OAAOxC,OAAOwC,OAAO;IACvB;IAEA,OAAOxC;AACT,EAAC"}