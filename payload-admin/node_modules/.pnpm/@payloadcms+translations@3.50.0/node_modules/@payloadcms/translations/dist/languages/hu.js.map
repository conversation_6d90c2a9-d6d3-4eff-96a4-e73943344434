{"version": 3, "sources": ["../../src/languages/hu.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const huTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: '<PERSON>ó<PERSON>',\n    accountOfCurrentUser: 'Az aktuális felhasz<PERSON>ló fiókja',\n    accountVerified: 'A fiók sikeresen hitelesítve.',\n    alreadyActivated: '<PERSON><PERSON><PERSON> akt<PERSON> van',\n    alreadyLoggedIn: '<PERSON><PERSON><PERSON>',\n    apiKey: 'API-kulcs',\n    authenticated: 'Hitelesített',\n    backToLogin: 'Vissza a bejelentkezéshez',\n    beginCreateFirstUser: 'Kezdésként hozza létre az első felhasználót.',\n    changePassword: '<PERSON><PERSON><PERSON><PERSON> módosítás<PERSON>',\n    checkYourEmailForPasswordReset:\n      'Ha az e-mail cím egy fiókhoz van társítva, hamarosan kapni fog utasításokat a j<PERSON>z<PERSON> visszaállításához. K<PERSON>rj<PERSON><PERSON>, ellenőrizze a spam vagy a levélsze<PERSON><PERSON> mappát, ha nem látja az e-mailt a bejövő üzenetek között.',\n    confirmGeneration: 'Generálás megerősítése',\n    confirmPassword: 'Jelszó megerősítése',\n    createFirstUser: 'Első felhasználó létrehozása',\n    emailNotValid: 'A megadott e-mail cím érvénytelen',\n    emailOrUsername: 'E-mail vagy Felhasználónév',\n    emailSent: 'E-mail elküldve',\n    emailVerified: 'Az email sikeresen megerősítve.',\n    enableAPIKey: 'API-kulcs engedélyezése',\n    failedToUnlock: 'Nem sikerült feloldani',\n    forceUnlock: 'Kényszerített feloldás',\n    forgotPassword: 'Elfelejtett jelszó',\n    forgotPasswordEmailInstructions:\n      'Kérjük, adja meg e-mail címét alább. Kapni fog egy e-mail üzenetet a jelszó visszaállításához szükséges utasításokkal.',\n    forgotPasswordQuestion: 'Elfelejtette jelszavát?',\n    forgotPasswordUsernameInstructions:\n      'Kérjük, adja meg felhasználónevét lentebb. A jelszó visszaállításáról szóló utasításokat a felhasználónevéhez tartozó e-mail címre küldjük.',\n    generate: 'Generálás',\n    generateNewAPIKey: 'Új API-kulcs generálása',\n    generatingNewAPIKeyWillInvalidate:\n      'Az új API-kulcs generálása <1>érvényteleníti</1> az előző kulcsot. Biztos, hogy folytatni szeretné?',\n    lockUntil: 'Zárolás eddig',\n    logBackIn: 'Jelentkezzen be újra',\n    loggedIn:\n      'Ha egy másik felhasználóval szeretne bejelentkezni, először <0>ki kell jelentkeznie</0>.',\n    loggedInChangePassword:\n      'Jelszavának megváltoztatásához lépjen be <0>fiókjába</0>, és ott szerkessze jelszavát.',\n    loggedOutInactivity: 'Inaktivitás miatt kijelentkeztünk.',\n    loggedOutSuccessfully: 'Sikeresen kijelentkezett.',\n    loggingOut: 'Kijelentkezés...',\n    login: 'Bejelentkezés',\n    loginAttempts: 'Bejelentkezési kísérletek',\n    loginUser: 'Bejelentkezés felhasználó',\n    loginWithAnotherUser:\n      'Ha egy másik felhasználóval szeretne bejelentkezni, először <0>ki kell jelentkeznie</0>.',\n    logOut: 'Kijelentkezés',\n    logout: 'Kijelentkezés',\n    logoutSuccessful: 'Sikeres kijelentkezés.',\n    logoutUser: 'Felhasználó kijelentkezése',\n    newAccountCreated:\n      'Létrehoztunk egy új fiókot, amellyel hozzáférhet a következőhöz <a href=\"{{serverURL}}\"> {{serverURL}} </a> Kérjük, kattintson a következő linkre, vagy illessze be az alábbi URL-t a böngészőbe az e-mail-cím ellenőrzéséhez : <a href=\"{{verificationURL}}\"> {{verificationURL}} </a> <br> Az e-mail-cím ellenőrzése után sikeresen be tud majd jelentkezni.',\n    newAPIKeyGenerated: 'Új API-kulcs generálva.',\n    newPassword: 'Új jelszó',\n    passed: 'Hitelesítés sikeres',\n    passwordResetSuccessfully: 'A jelszó sikeresen vissza lett állítva.',\n    resetPassword: 'Jelszó visszaállítása',\n    resetPasswordExpiration: 'Jelszóvisszaállítás lejárata',\n    resetPasswordToken: 'Jelszóvisszaállító token',\n    resetYourPassword: 'Jelszó visszaállítása',\n    stayLoggedIn: 'Maradjon bejelentkezve',\n    successfullyRegisteredFirstUser: 'Sikeresen regisztráltuk az első felhasználót.',\n    successfullyUnlocked: 'Sikeresen feloldva',\n    tokenRefreshSuccessful: 'A token frissítése sikeres.',\n    unableToVerify: 'Sikertelen megerősítés',\n    username: 'Felhasználónév',\n    usernameNotValid: 'A megadott felhasználónév nem érvényes.',\n    verified: 'Megerősítve',\n    verifiedSuccessfully: 'Sikeresen megerősítve',\n    verify: 'Megerősítés',\n    verifyUser: 'Felhasználó megerősítése',\n    verifyYourEmail: 'Erősítse meg az e-mail címét',\n    youAreInactive:\n      'Már egy ideje nem volt aktív, és hamarosan automatikusan kijelentkeztetjük saját biztonsága érdekében. Szeretne bejelentkezve maradni?',\n    youAreReceivingResetPassword:\n      'Ezt azért kapja, mert Ön (vagy valaki más) kérte fiókja jelszavának visszaállítását. A folyamat befejezéséhez kattintson a következő linkre, vagy illessze be böngészőjébe:',\n    youDidNotRequestPassword:\n      'Ha nem Ön kérte ezt, kérjük, hagyja figyelmen kívül ezt az e-mailt, és jelszava változatlan marad.',\n  },\n  error: {\n    accountAlreadyActivated: 'Ez a fiók már aktiválva van.',\n    autosaving: 'Hiba történt a dokumentum automatikus mentése közben.',\n    correctInvalidFields: 'Kérjük, javítsa ki az érvénytelen mezőket.',\n    deletingFile: 'Hiba történt a fájl törlésekor.',\n    deletingTitle:\n      'Hiba történt a {{title}} törlése közben. Kérjük, ellenőrizze a kapcsolatot, és próbálja meg újra.',\n    documentNotFound:\n      'A dokumentum azonosítóval {{id}} nem található. Lehet, hogy törölték, soha nem létezett, vagy Önnek nincs hozzáférése hozzá.',\n    emailOrPasswordIncorrect: 'A megadott e-mail-cím vagy jelszó helytelen.',\n    followingFieldsInvalid_one: 'A következő mező érvénytelen:',\n    followingFieldsInvalid_other: 'A következő mezők érvénytelenek:',\n    incorrectCollection: 'Helytelen gyűjtemény',\n    insufficientClipboardPermissions:\n      'A vágólaphoz való hozzáférés elutasítva. Kérjük, ellenőrizze a vágólap engedélyeit.',\n    invalidClipboardData: 'Érvénytelen vágólap adat.',\n    invalidFileType: 'Érvénytelen fájltípus',\n    invalidFileTypeValue: 'Érvénytelen fájltípus: {{value}}',\n    invalidRequestArgs: 'Érvénytelen argumentumok a kérésben: {{args}}',\n    loadingDocument: 'Hiba történt a {{id}} azonosítójú dokumentum betöltésekor.',\n    localesNotSaved_one: 'Az alábbi helyi beállítást nem sikerült menteni:',\n    localesNotSaved_other: 'Az alábbi helyi beállításokat nem sikerült menteni:',\n    logoutFailed: 'Kijelentkezés sikertelen.',\n    missingEmail: 'Hiányzó e-mail.',\n    missingIDOfDocument: 'Hiányzik a frissítendő dokumentum azonosítója.',\n    missingIDOfVersion: 'A verzió azonosítója hiányzik.',\n    missingRequiredData: 'Hiányoznak kötelező adatok.',\n    noFilesUploaded: 'Nem került fájl feltöltésre.',\n    noMatchedField: 'Nem található egyező mező a következőhöz: \"{{label}}\"',\n    notAllowedToAccessPage: 'Ehhez az oldalhoz nem férhet hozzá.',\n    notAllowedToPerformAction: 'Ezt a műveletet nem hajthatja végre.',\n    notFound: 'A kért erőforrás nem található.',\n    noUser: 'Nincs felhasználó',\n    previewing: 'Hiba történt a dokumentum előnézetének megtekintése közben.',\n    problemUploadingFile: 'Hiba történt a fájl feltöltése közben.',\n    restoringTitle:\n      'Hiba történt a {{title}} visszaállítása közben. Kérjük, ellenőrizze az internetkapcsolatát, és próbálkozzon újra.',\n    tokenInvalidOrExpired: 'A token érvénytelen vagy lejárt.',\n    tokenNotProvided: 'Token nem biztosított.',\n    unableToCopy: 'Másolás nem lehetséges.',\n    unableToDeleteCount: 'Nem sikerült törölni {{count}}/{{total}} {{label}}.',\n    unableToReindexCollection:\n      'Hiba a(z) {{collection}} gyűjtemény újraindexelésekor. A művelet megszakítva.',\n    unableToUpdateCount: 'Nem sikerült frissíteni {{count}}/{{total}} {{label}}.',\n    unauthorized: 'Jogosulatlan, a kéréshez be kell jelentkeznie.',\n    unauthorizedAdmin: 'Jogosulatlan, ez a felhasználó nem fér hozzá az admin panelhez.',\n    unknown: 'Ismeretlen hiba történt.',\n    unPublishingDocument: 'Hiba történt a dokumentum közzétételének visszavonása közben.',\n    unspecific: 'Hiba történt.',\n    unverifiedEmail: 'Kérjük, erősítse meg e-mail címét, mielőtt bejelentkezik.',\n    userEmailAlreadyRegistered: 'A megadott email címmel már regisztráltak egy felhasználót.',\n    userLocked: 'Ez a felhasználó túl sok sikertelen bejelentkezési kísérlet miatt zárolva van.',\n    usernameAlreadyRegistered: 'Egy felhasználó a megadott felhasználónévvel már regisztrált.',\n    usernameOrPasswordIncorrect: 'A megadott felhasználónév vagy jelszó helytelen.',\n    valueMustBeUnique: 'Az értéknek egyedinek kell lennie',\n    verificationTokenInvalid: 'Az ellenőrző token érvénytelen.',\n  },\n  fields: {\n    addLabel: '{{label}} hozzáadása',\n    addLink: 'Link hozzáadása',\n    addNew: 'Új hozzáadása',\n    addNewLabel: 'Új {{label}} hozzáadása',\n    addRelationship: 'Kapcsolat hozzáadása',\n    addUpload: 'Feltöltés hozzáadása',\n    block: 'blokk',\n    blocks: 'blokkok',\n    blockType: 'Blokk típusa',\n    chooseBetweenCustomTextOrDocument:\n      'Válasszon egy egyéni szöveges URL-cím megadása vagy egy másik dokumentumra való hivatkozás között.',\n    chooseDocumentToLink: 'Válassza ki a dokumentumot, amelyre hivatkozni kíván',\n    chooseFromExisting: 'Válasszon a meglévők közül',\n    chooseLabel: 'Válassza ki a {{label}}',\n    collapseAll: 'Mindet összecsuk',\n    customURL: 'Egyéni URL',\n    editLabelData: '{{label}} adatok szerkesztése',\n    editLink: 'Link szerkesztése',\n    editRelationship: 'Kapcsolat hozzáadása',\n    enterURL: 'Adjon meg egy URL-t',\n    internalLink: 'Belső link',\n    itemsAndMore: '{{items}} és további {{count}}',\n    labelRelationship: '{{label}} Kapcsolat',\n    latitude: 'Szélesség',\n    linkedTo: 'Kapcsolódik a <0>{{label}}</0>',\n    linkType: 'Link típusa',\n    longitude: 'Hosszúság',\n    newLabel: 'Új {{label}}',\n    openInNewTab: 'Megnyitás új lapon',\n    passwordsDoNotMatch: 'A jelszavak nem egyeznek.',\n    relatedDocument: 'Kapcsolódó dokumentum',\n    relationTo: 'Kapcsolat a következővel:',\n    removeRelationship: 'Kapcsolat eltávolítása',\n    removeUpload: 'Feltöltés eltávolítása',\n    saveChanges: 'Módosítások mentése',\n    searchForBlock: 'Blokk keresése',\n    selectExistingLabel: 'Meglévő {{label}} kiválasztása',\n    selectFieldsToEdit: 'Válassza ki a szerkeszteni kívánt mezőket',\n    showAll: 'Az összes megjelenítése',\n    swapRelationship: 'Kapcsolat csere',\n    swapUpload: 'Feltöltés csere',\n    textToDisplay: 'Megjelenítendő szöveg',\n    toggleBlock: 'Blokk kinyitása',\n    uploadNewLabel: 'Új {{label}} feltöltése',\n  },\n  folder: {\n    browseByFolder: 'Mappa szerint böngészés',\n    byFolder: 'Mappánként',\n    deleteFolder: 'Mappa törlése',\n    folderName: 'Mappa neve',\n    folders: 'Mappák',\n    folderTypeDescription:\n      'Válassza ki, hogy milyen típusú dokumentumokat engedélyez ebben a mappában.',\n    itemHasBeenMoved: '{{title}} át lett helyezve a {{folderName}} nevű mappába.',\n    itemHasBeenMovedToRoot: 'A(z) {{title}} át lett helyezve a gyökérmappába.',\n    itemsMovedToFolder: '{{title}} áthelyezve a(z) {{folderName}} mappába',\n    itemsMovedToRoot: 'A {{title}} átkerült a gyökérmappába',\n    moveFolder: 'Mappa áthelyezése',\n    moveItemsToFolderConfirmation:\n      'Ön <1>{{count}} {{label}}</1>-t készül áthelyezni a <2>{{toFolder}}</2> mappába. Biztos benne?',\n    moveItemsToRootConfirmation:\n      'Ön éppen azért készül, hogy <1>{{count}} {{label}}</1>-t a gyökérmappába helyezzen át. Biztos benne?',\n    moveItemToFolderConfirmation:\n      'Ön készül áthelyezni <1>{{title}}</1>-t <2>{{toFolder}}</2>-be. Biztos benne?',\n    moveItemToRootConfirmation:\n      'Arra készül, hogy a <1>{{title}}</1> elemet a gyökérmappába helyezi. Biztos benne?',\n    movingFromFolder: '{{title}} áthelyezése a(z) {{fromFolder}} mappából',\n    newFolder: 'Új Mappa',\n    noFolder: 'Nincs mappa',\n    renameFolder: 'Mappa átnevezése',\n    searchByNameInFolder: 'Keresés név alapján a(z) {{folderName}} mappában',\n    selectFolderForItem: 'Válassz mappát a {{title}} számára',\n  },\n  general: {\n    name: 'Név',\n    aboutToDelete: 'A {{label}} <1>{{title}}</1> törlésére készül. Biztos benne?',\n    aboutToDeleteCount_many: 'Törölni készül {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Törölni készül {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Törölni készül {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Ön véglegesen törölni készül a következőt: {{label}} <1>{{title}}</1>. Biztos benne?',\n    aboutToPermanentlyDeleteTrash:\n      'Ön véglegesen törölni készül <0>{{count}}</0> <1>{{label}}</1> elemet a szemetesből. Biztos benne?',\n    aboutToRestore:\n      'Ön helyreállítja a következőt: {{label}} <1>{{title}}</1>. Biztosan így szeretné?',\n    aboutToRestoreAsDraft:\n      'Ön azzal készül, hogy a következőt: {{label}} <1>{{title}}</1>, vázlatként állítja vissza. Biztos benne?',\n    aboutToRestoreAsDraftCount: 'Ön hamarosan visszaállít {{count}} {{label}}-t mint vázlat',\n    aboutToRestoreCount: 'Ön a következők visszaállítására készül: {{count}} {{label}}',\n    aboutToTrash:\n      'Ön azon van, hogy a következőt: {{label}} <1>{{title}}</1> áthelyezze a szemetesbe. Biztos benne?',\n    aboutToTrashCount: 'Ön a(z) {{count}} {{label}} elemet készül a kukába helyezni.',\n    addBelow: 'Hozzáadás lent',\n    addFilter: 'Szűrő hozzáadása',\n    adminTheme: 'Admin téma',\n    all: 'Mind',\n    allCollections: 'Minden gyűjtemény',\n    allLocales: 'Minden helyszín',\n    and: 'És',\n    anotherUser: 'Egy másik felhasználó',\n    anotherUserTakenOver: 'Egy másik felhasználó átvette ennek a dokumentumnak a szerkesztését.',\n    applyChanges: 'Változtatások alkalmazása',\n    ascending: 'Növekvő',\n    automatic: 'Automatikus',\n    backToDashboard: 'Vissza az irányítópultra',\n    cancel: 'Mégsem',\n    changesNotSaved:\n      'A módosítások nem lettek mentve. Ha most távozik, elveszíti a változtatásokat.',\n    clear: 'Tiszta',\n    clearAll: 'Törölj mindent',\n    close: 'Bezárás',\n    collapse: 'Összecsukás',\n    collections: 'Gyűjtemények',\n    columns: 'Oszlopok',\n    columnToSort: 'Rendezendő oszlop',\n    confirm: 'Megerősítés',\n    confirmCopy: 'Jóváhagyott másolat',\n    confirmDeletion: 'Törlés megerősítése',\n    confirmDuplication: 'Duplikáció megerősítése',\n    confirmMove: 'Megerősíti a költözést',\n    confirmReindex: 'Újraindexálja az összes {{collections}}-t?',\n    confirmReindexAll: 'Újraindexálja az összes gyűjteményt?',\n    confirmReindexDescription:\n      'Ez eltávolítja a meglévő indexeket, és újraindexálja a dokumentumokat a {{collections}} gyűjteményekben.',\n    confirmReindexDescriptionAll:\n      'Ez eltávolítja a meglévő indexeket, és újraindexálja a dokumentumokat az összes gyűjteményben.',\n    confirmRestoration: 'Megerősíti a helyreállítást?',\n    copied: 'Másolva',\n    copy: 'Másolás',\n    copyField: 'Mező másolása',\n    copying: 'Másolás',\n    copyRow: 'Sor másolása',\n    copyWarning:\n      'Ön azzal készül felülírni {{to}} -t {{from}} -mal a {{label}} {{title}} számára. Biztos benne?',\n    create: 'Létrehozás',\n    created: 'Létrehozva',\n    createdAt: 'Létrehozva:',\n    createNew: 'Új létrehozása',\n    createNewLabel: 'Új {{label}} létrehozása',\n    creating: 'Létrehozás',\n    creatingNewLabel: 'Új {{label}} létrehozása',\n    currentlyEditing:\n      'jelenleg szerkeszti ezt a dokumentumot. Ha átveszed, nem tudja folytatni a szerkesztést, és elveszítheti a mentetlen módosításokat.',\n    custom: 'Egyéni',\n    dark: 'Sötét',\n    dashboard: 'Irányítópult',\n    delete: 'Törlés',\n    deleted: 'Törölt',\n    deletedAt: 'Törölve Ekkor',\n    deletedCountSuccessfully: '{{count}} {{label}} sikeresen törölve.',\n    deletedSuccessfully: 'Sikeresen törölve.',\n    deletePermanently: 'Hagyja ki a kukát és törölje véglegesen',\n    deleting: 'Törlés...',\n    depth: 'Mélység',\n    descending: 'Csökkenő',\n    deselectAllRows: 'Jelölje ki az összes sort',\n    document: 'Dokumentum',\n    documentIsTrashed: 'Ez a {{label}} szemétdobozba került, és csak olvasható.',\n    documentLocked: 'A dokumentum zárolva van',\n    documents: 'Dokumentumok',\n    duplicate: 'Duplikálás',\n    duplicateWithoutSaving: 'Duplikálás a módosítások mentése nélkül',\n    edit: 'Szerkesztés',\n    editAll: 'Összes szerkesztése',\n    editedSince: 'Szerkesztve',\n    editing: 'Szerkesztés',\n    editingLabel_many: '{{count}} {{label}} szerkesztése',\n    editingLabel_one: '{{count}} {{label}} szerkesztése',\n    editingLabel_other: '{{count}} {{label}} szerkesztése',\n    editingTakenOver: 'A szerkesztést átvették',\n    editLabel: '{{label}} szerkesztése',\n    email: 'E-mail',\n    emailAddress: 'E-mail cím',\n    emptyTrash: 'Ürítse ki a szemetet',\n    emptyTrashLabel: 'Ürítse ki a {{label}} szemetest',\n    enterAValue: 'Adjon meg egy értéket',\n    error: 'Hiba',\n    errors: 'Hibák',\n    exitLivePreview: 'Kilépés az Élő Előnézetből',\n    export: 'Export',\n    fallbackToDefaultLocale: 'Visszatérés az alapértelmezett nyelvhez',\n    false: 'Hamis',\n    filter: 'Szűrő',\n    filters: 'Szűrők',\n    filterWhere: 'Szűrő {{label}} ahol',\n    globals: 'Globálisok',\n    goBack: 'Vissza',\n    groupByLabel: 'Csoportosítás {{label}} szerint',\n    import: 'Behozatal',\n    isEditing: 'szerkeszt',\n    item: 'tétel',\n    items: 'tételek',\n    language: 'Nyelv',\n    lastModified: 'Utoljára módosítva',\n    leaveAnyway: 'Távozás mindenképp',\n    leaveWithoutSaving: 'Távozás mentés nélkül',\n    light: 'Világos',\n    livePreview: 'Előnézet',\n    loading: 'Betöltés',\n    locale: 'Nyelv',\n    locales: 'Nyelvek',\n    menu: 'Menü',\n    moreOptions: 'Több opció',\n    move: 'Mozdulj',\n    moveConfirm: 'Ön <1>{{destination}}</1>-re fogja {{count}} {{label}}-t mozgatni. Biztos benne?',\n    moveCount: 'Mozduljon {{count}} {{label}}',\n    moveDown: 'Mozgatás lefelé',\n    moveUp: 'Mozgatás felfelé',\n    moving: 'Költözés',\n    movingCount: '{{Count}} {{label}} mozgatása',\n    newPassword: 'Új jelszó',\n    next: 'Következő',\n    no: 'Nem',\n    noDateSelected: 'Nincs kiválasztott dátum',\n    noFiltersSet: 'Nincs beállítva szűrő',\n    noLabel: '<No {{label}}>',\n    none: 'Semmi',\n    noOptions: 'Nincs lehetőség',\n    noResults:\n      'Nem találtunk {{label}}. Vagy még nem létezik {{label}}, vagy egyik sem felel meg a fent megadott szűrőknek.',\n    notFound: 'Nem található',\n    nothingFound: 'Nincs találat',\n    noTrashResults: 'Nincs {{label}} a szemetesben.',\n    noUpcomingEventsScheduled: 'Nincsenek közelgő események.',\n    noValue: 'Nincs érték',\n    of: 'a',\n    only: 'Csak',\n    open: 'Megnyitás',\n    or: 'Vagy',\n    order: 'Sorrend',\n    overwriteExistingData: 'Írja felül a meglévő mezőadatokat',\n    pageNotFound: 'Az oldal nem található',\n    password: 'Jelszó',\n    pasteField: 'Mező beillesztése',\n    pasteRow: 'Sor beillesztése',\n    payloadSettings: 'Payload beállítások',\n    permanentlyDelete: 'Végleges Törlés',\n    permanentlyDeletedCountSuccessfully: 'Véglegesen törölt {{count}} {{label}} sikeresen.',\n    perPage: 'Oldalanként: {{limit}}',\n    previous: 'Előző',\n    reindex: 'Újraindexelés',\n    reindexingAll: 'Az összes {{collections}} újraindexálása folyamatban.',\n    remove: 'Törlés',\n    rename: 'Átnevez',\n    reset: 'Visszaállítás',\n    resetPreferences: 'Beállítások visszaállítása',\n    resetPreferencesDescription:\n      'Ez visszaállítja az összes beállítást az alapértelmezett értékekre.',\n    resettingPreferences: 'Beállítások visszaállítása.',\n    restore: 'Visszaállítás',\n    restoreAsPublished: 'Állítsa vissza közzétett változatként',\n    restoredCountSuccessfully: 'Sikeresen visszaállított {{count}} {{label}}.',\n    restoring:\n      'Tartsa tiszteletben az eredeti szöveg jelentését a Payload kontextusában. Íme egy lista a Payloadban gyakran használt kifejezésekről, amelyek rendkívül specifikus jelentéssel bírnak:\\n    - Gyűjtemény: A gyűjtemény egy olyan dokumentumcsoport, amelyek közös struktúrával és céllal rendelkeznek. A gyűjteményeket a tartalom szervezésére és kezelésére használjuk a Payloadban.\\n    - Mező',\n    row: 'Sor',\n    rows: 'Sorok',\n    save: 'Mentés',\n    saving: 'Mentés...',\n    schedulePublishFor: 'Tervezett közzététel a(z) {{title}} című számára',\n    searchBy: 'Keresés a következő szerint: {{label}}',\n    select: 'Válasszon',\n    selectAll: 'Az összes kijelölése: {{count}} {{label}}',\n    selectAllRows: 'Válassza ki az összes sort',\n    selectedCount: '{{count}} {{label}} kiválasztva',\n    selectLabel: 'Válassza ki a(z) {{label}} opciót',\n    selectValue: 'Válasszon ki egy értéket',\n    showAllLabel: 'Mutasd az összes {{címke}}',\n    sorryNotFound: 'Sajnáljuk – nincs semmi, ami megfelelne a kérésének.',\n    sort: 'Rendezés',\n    sortByLabelDirection: 'Rendezés {{label}} {{direction}} szerint',\n    stayOnThisPage: 'Maradjon ezen az oldalon',\n    submissionSuccessful: 'Beküldés sikeres.',\n    submit: 'Beküldés',\n    submitting: 'Beküldés...',\n    success: 'Siker',\n    successfullyCreated: '{{label}} sikeresen létrehozva.',\n    successfullyDuplicated: '{{label}} sikeresen duplikálódott.',\n    successfullyReindexed:\n      'Sikeresen újraindexelve {{total}} dokumentumból {{count}} a(z) {{collections}} gyűjteményekből.',\n    takeOver: 'Átvétel',\n    thisLanguage: 'Magyar',\n    time: 'Idő',\n    timezone: 'Időzóna',\n    titleDeleted: '{{label}} \"{{title}}\" sikeresen törölve.',\n    titleRestored: '\"{{label}}\" \"{{title}}\" sikeresen visszaállítva.',\n    titleTrashed: '\"{{label}}\" \"{{title}}\" a szemétbe került.',\n    trash: 'Szemét',\n    trashedCountSuccessfully: '{{count}} {{label}} átkerült a szemeteskukába.',\n    true: 'Igaz',\n    unauthorized: 'Jogosulatlan',\n    unsavedChanges: 'Vannak mentetlen változtatásai. Mentsen vagy dobja el mielőtt folytatja.',\n    unsavedChangesDuplicate: 'Nem mentett módosításai vannak. Szeretné folytatni a duplikációt?',\n    untitled: 'Névtelen',\n    upcomingEvents: 'Közelgő események',\n    updatedAt: 'Frissítve:',\n    updatedCountSuccessfully: '{{count}} {{label}} sikeresen frissítve.',\n    updatedLabelSuccessfully: 'A(z) {{label}} sikeresen frissült.',\n    updatedSuccessfully: 'Sikeresen frissítve.',\n    updateForEveryone: 'Frissítés mindenkinek',\n    updating: 'Frissítés',\n    uploading: 'Feltöltés',\n    uploadingBulk: 'Feltöltés: {{current}} / {{total}}',\n    user: 'Felhasználó',\n    username: 'Felhasználónév',\n    users: 'Felhasználók',\n    value: 'Érték',\n    viewing: 'Megtekintés',\n    viewReadOnly: 'Csak olvasható nézet',\n    welcome: 'Üdvözöljük',\n    yes: 'Igen',\n  },\n  localization: {\n    cannotCopySameLocale: 'Nem lehet ugyanarra a helyre másolni',\n    copyFrom: 'Másolás innen',\n    copyFromTo: 'Másolás {{from}}-ról {{to}}-ra',\n    copyTo: 'Másolja ide',\n    copyToLocale: 'Másolás a helyi verzióba',\n    localeToPublish: 'Közzététel helye',\n    selectLocaleToCopy: 'Válassza ki a másolni kívánt területet.',\n  },\n  operators: {\n    contains: 'tartalmaz',\n    equals: 'egyenlő',\n    exists: 'létezik',\n    intersects: 'metszéspontokban',\n    isGreaterThan: 'nagyobb, mint',\n    isGreaterThanOrEqualTo: 'nagyobb vagy egyenlő, mint',\n    isIn: 'benne van',\n    isLessThan: 'kisebb, mint',\n    isLessThanOrEqualTo: 'kisebb vagy egyenlő, mint',\n    isLike: 'olyan, mint',\n    isNotEqualTo: 'nem egyenlő',\n    isNotIn: 'nincs benne',\n    isNotLike: 'nem olyan mint',\n    near: 'közel',\n    within: 'belül',\n  },\n  upload: {\n    addFile: 'Fájl hozzáadása',\n    addFiles: 'Fájlok hozzáadása',\n    bulkUpload: 'Tömeges feltöltés',\n    crop: 'Termés',\n    cropToolDescription:\n      'Húzza a kijelölt terület sarkait, rajzoljon új területet, vagy igazítsa a lentebb található értékeket.',\n    download: 'Letöltés',\n    dragAndDrop: 'Húzzon ide egy fájlt',\n    dragAndDropHere: 'vagy húzzon ide egy fájlt',\n    editImage: 'Kép szerkesztése',\n    fileName: 'Fájlnév',\n    fileSize: 'Fájl mérete',\n    filesToUpload: 'Feltöltendő fájlok',\n    fileToUpload: 'Feltöltendő fájl',\n    focalPoint: 'Fókuszpont',\n    focalPointDescription:\n      'Húzza az érdekes pontot közvetlenül az előnézetre, vagy állítsa be az alábbi értékeket.',\n    height: 'Magasság',\n    lessInfo: 'Kevesebb információ',\n    moreInfo: 'További információ',\n    noFile: 'Nincs fájl',\n    pasteURL: 'URL beillesztése',\n    previewSizes: 'Előnézeti méretek',\n    selectCollectionToBrowse: 'Válassza ki a böngészni kívánt gyűjteményt',\n    selectFile: 'Válasszon ki egy fájlt',\n    setCropArea: 'Állítsa be a vágási területet',\n    setFocalPoint: 'Állítsa be a fókuszpontot',\n    sizes: 'Méretek',\n    sizesFor: 'Méretek a {{címke}} számára',\n    width: 'Szélesség',\n  },\n  validation: {\n    emailAddress: 'Kérjük, adjon meg egy érvényes e-mail címet.',\n    enterNumber: 'Kérjük, adjon meg egy érvényes számot.',\n    fieldHasNo: 'Ennek a mezőnek nincs {{label}}',\n    greaterThanMax: '{{value}} nagyobb, mint a megengedett maximum {{label}} érték, ami {{max}}.',\n    invalidInput: 'Ez a mező érvénytelen értéket tartalmaz.',\n    invalidSelection: 'Ez a mező érvénytelen kijelöléssel rendelkezik.',\n    invalidSelections: 'Ez a mező a következő érvénytelen kijelöléseket tartalmazza:',\n    lessThanMin: '{{value}} kisebb, mint a megengedett minimum {{label}} érték, ami {{min}}.',\n    limitReached: 'Elérte a korlátot, csak {{max}} elem adható hozzá.',\n    longerThanMin:\n      'Ennek az értéknek hosszabbnak kell lennie, mint a minimális {{minLength}} karakter hosszúság.',\n    notValidDate: '\" {{value}} \" nem érvényes dátum.',\n    required: 'Ez a mező kötelező.',\n    requiresAtLeast: 'Ehhez a mezőhöz legalább {{count}} {{label}} szükséges.',\n    requiresNoMoreThan: 'Ehhez a mezőhöz legfeljebb {{count}} {{label}} szükséges.',\n    requiresTwoNumbers: 'Ehhez a mezőhöz két szám szükséges.',\n    shorterThanMax:\n      'Ennek az értéknek rövidebbnek kell lennie, mint a maximálisan megengedett {{maxLength}} karakter.',\n    timezoneRequired: 'Időzóna szükséges.',\n    trueOrFalse: 'Ez a mező csak igaz vagy hamis lehet.',\n    username:\n      'Adjon meg egy érvényes felhasználónevet. Tartalmazhat betűket, számokat, kötőjeleket, pontokat és aláhúzásokat.',\n    validUploadID: 'Ez a mező nem érvényes feltöltési azonosító.',\n  },\n  version: {\n    type: 'Típus',\n    aboutToPublishSelection:\n      'Arra készül, hogy az összes {{label}} elemet közzétegye a kijelölésben. biztos vagy ebben?',\n    aboutToRestore:\n      'Arra készül, hogy visszaállítsa ezt a {{label}} dokumentumot arra az állapotra, amelyben {{versionDate}}  napon volt.',\n    aboutToRestoreGlobal:\n      'Arra készül, hogy visszaállítsa a {{label}} arra az állapotra, amelyben {{versionDate}} napon volt.',\n    aboutToRevertToPublished:\n      'Arra készül, hogy visszaállítsa a dokumentum módosításait a közzétett állapotába. Biztos benne?',\n    aboutToUnpublish: 'A dokumentum közzétételének visszavonására készül. Biztos benne?',\n    aboutToUnpublishSelection:\n      'Arra készül, hogy visszavonja a kijelölésben szereplő összes {{label}} közzétételét. biztos vagy ebben?',\n    autosave: 'Automatikus mentés',\n    autosavedSuccessfully: 'Automatikus mentés sikeres.',\n    autosavedVersion: 'Automatikusan mentett verzió',\n    changed: 'Megváltozott',\n    changedFieldsCount_one: '{{count}} megváltozott mező',\n    changedFieldsCount_other: '{{count}} módosított mező',\n    compareVersion: 'Hasonlítsa össze a verziót a következőkkel:',\n    compareVersions: 'Verziók összehasonlítása',\n    comparingAgainst: 'Összehasonlítva',\n    confirmPublish: 'A közzététel megerősítése',\n    confirmRevertToSaved: 'Erősítse meg a mentett verzióra való visszatérést',\n    confirmUnpublish: 'A közzététel visszavonásának megerősítése',\n    confirmVersionRestoration: 'Verzió-visszaállítás megerősítése',\n    currentDocumentStatus: 'Jelenlegi {{docStatus}} dokumentum',\n    currentDraft: 'Aktuális tervezet',\n    currentlyPublished: 'Jelenleg közzétéve',\n    currentlyViewing: 'Jelenlegi megtekintés',\n    currentPublishedVersion: 'Jelenleg Közzétett Verzió',\n    draft: 'Piszkozat',\n    draftSavedSuccessfully: 'A piszkozat sikeresen mentve.',\n    lastSavedAgo: 'Utoljára mentve {{distance}} órája',\n    modifiedOnly: 'Módosítva csak',\n    moreVersions: 'További verziók...',\n    noFurtherVersionsFound: 'További verziók nem találhatók',\n    noRowsFound: 'Nem található {{label}}',\n    noRowsSelected: 'Nincs {{címke}} kiválasztva',\n    preview: 'Előnézet',\n    previouslyDraft: 'Korábban egy Vázlat',\n    previouslyPublished: 'Korábban Közzétéve',\n    previousVersion: 'Előző Verzió',\n    problemRestoringVersion: 'Hiba történt a verzió visszaállításakor',\n    publish: 'Közzététel',\n    publishAllLocales: 'Közzétesz az összes helyszínen',\n    publishChanges: 'Módosítások közzététele',\n    published: 'Közzétett',\n    publishIn: 'Közzététel ebben a {{locale}} területkódban',\n    publishing: 'Közzététel',\n    restoreAsDraft: 'Visszaállítás piszkozatként',\n    restoredSuccessfully: 'Sikeresen visszaállítva.',\n    restoreThisVersion: 'A verzió visszaállítása',\n    restoring: 'Visszaállítás...',\n    reverting: 'Visszaállítás...',\n    revertToPublished: 'Visszatérés a közzétetthez',\n    saveDraft: 'Piszkozat mentése',\n    scheduledSuccessfully: 'Sikeresen ütemezve.',\n    schedulePublish: 'Közzététel ütemezése',\n    selectLocales: 'Megjelenítendő nyelvek kiválasztása',\n    selectVersionToCompare: 'Válassza ki az összehasonlítani kívánt verziót',\n    showingVersionsFor: 'Verziók megjelenítése a következőkhöz:',\n    showLocales: 'Nyelvek megjelenítése:',\n    specificVersion: 'Specifikus verzió',\n    status: 'Állapot',\n    unpublish: 'Közzététel visszavonása',\n    unpublishing: 'Közzététel visszavonása...',\n    version: 'Verzió',\n    versionAgo: '{{distance}} ezelőtt',\n    versionCount_many: '{{count}} verzió található',\n    versionCount_none: 'Nem található verzió',\n    versionCount_one: '{{count}} verzió található',\n    versionCount_other: '{{count}} verzió található',\n    versionCreatedOn: '{{version}} létrehozva:',\n    versionID: 'Verzióazonosító',\n    versions: 'Verziók',\n    viewingVersion: 'A(z) {{entityLabel}} {{documentTitle}} verziójának megtekintése',\n    viewingVersionGlobal: 'A globális {{entityLabel}} verziójának megtekintése',\n    viewingVersions: 'A {{entityLabel}} {{documentTitle}} verzióinak megtekintése',\n    viewingVersionsGlobal: 'A globális {{entityLabel}} verzióinak megtekintése',\n  },\n}\n\nexport const hu: Language = {\n  dateFNSKey: 'hu',\n  translations: huTranslations,\n}\n"], "names": ["huTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "hu", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UACE;QACFC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBACE;QACFC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBACE;QACFC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}