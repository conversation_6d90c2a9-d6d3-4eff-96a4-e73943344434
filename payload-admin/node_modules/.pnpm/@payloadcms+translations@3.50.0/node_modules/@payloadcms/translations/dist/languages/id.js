export const idTranslations = {
    authentication: {
        account: 'Akun',
        accountOfCurrentUser: 'Akun pengguna saat ini',
        accountVerified: 'Akun berhasil diverifikasi.',
        alreadyActivated: 'Sudah Diaktifkan',
        alreadyLoggedIn: 'Sudah masuk',
        apiKey: 'API Key',
        authenticated: 'Terautentikasi',
        backToLogin: 'Ke<PERSON>li ke halaman masuk',
        beginCreateFirstUser: 'Untuk memulai, buat pengguna pertama Anda.',
        changePassword: 'Ubah Kata Sandi',
        checkYourEmailForPasswordReset: 'Jika alamat email dikaitkan dengan sebuah akun, Anda akan segera menerima instruksi untuk mengatur ulang kata sandi Anda. Silakan periksa folder spam atau junk mail Anda jika Anda tidak melihat email di kotak masuk Anda.',
        confirmGeneration: 'Konfirmasi Pembuatan',
        confirmPassword: 'Konfirmasi Kata Sandi',
        createFirstUser: 'Buat pengguna pertama',
        emailNotValid: 'Email yang diberikan tidak valid',
        emailOrUsername: 'Email atau Nama Pengguna',
        emailSent: 'Email Terkirim',
        emailVerified: 'Email berhasil diverifikasi.',
        enableAPIKey: 'Aktifkan API Key',
        failedToUnlock: 'Gagal membuka kunci',
        forceUnlock: 'Paksa Buka Kunci',
        forgotPassword: 'Lupa Kata Sandi',
        forgotPasswordEmailInstructions: 'Silakan masukkan email Anda di bawah ini. Anda akan menerima pesan email dengan instruksi tentang cara mengatur ulang kata sandi Anda.',
        forgotPasswordUsernameInstructions: 'Silakan masukkan nama pengguna Anda di bawah ini. Instruksi tentang cara mengatur ulang kata sandi Anda akan dikirim ke alamat email yang terkait dengan nama pengguna Anda.',
        usernameNotValid: 'Nama pengguna yang diberikan tidak valid',
        forgotPasswordQuestion: 'Lupa kata sandi?',
        generate: 'Buat',
        generateNewAPIKey: 'Buat kunci API baru',
        generatingNewAPIKeyWillInvalidate: 'Membuat API Key baru akan <1>membatalkan</1> kunci sebelumnya. Apakah Anda yakin ingin melanjutkan?',
        lockUntil: 'Kunci Hingga',
        logBackIn: 'Masuk kembali',
        loggedIn: 'Untuk masuk dengan pengguna lain, Anda harus <0>keluar</0> terlebih dahulu.',
        loggedInChangePassword: 'Untuk mengubah kata sandi Anda, buka <0>akun</0> Anda dan edit kata sandi Anda di sana.',
        loggedOutInactivity: 'Anda telah dikeluarkan karena tidak ada aktivitas.',
        loggedOutSuccessfully: 'Anda telah berhasil keluar.',
        loggingOut: 'Mengeluarkan...',
        login: 'Masuk',
        loginAttempts: 'Upaya Masuk',
        loginUser: 'Masuk pengguna',
        loginWithAnotherUser: 'Untuk masuk dengan pengguna lain, Anda harus <0>keluar</0> terlebih dahulu.',
        logOut: 'Keluar',
        logout: 'Keluar',
        logoutSuccessful: 'Berhasil keluar.',
        logoutUser: 'Keluar pengguna',
        newAccountCreated: 'Akun baru telah dibuat untuk Anda agar dapat mengakses <a href="{{serverURL}}">{{serverURL}}</a> Silakan klik tautan berikut atau tempel URL di bawah ini ke browser Anda untuk memverifikasi email Anda: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Setelah memverifikasi email, Anda akan dapat masuk dengan sukses.',
        newAPIKeyGenerated: 'API Key Baru Telah Dibuat.',
        newPassword: 'Kata Sandi Baru',
        passed: 'Autentikasi Lulus',
        passwordResetSuccessfully: 'Kata sandi berhasil diatur ulang.',
        resetPassword: 'Atur Ulang Kata Sandi',
        resetPasswordExpiration: 'Masa Berlaku Token Atur Ulang Kata Sandi',
        resetPasswordToken: 'Token Atur Ulang Kata Sandi',
        resetYourPassword: 'Atur Ulang Kata Sandi Anda',
        stayLoggedIn: 'Tetap masuk',
        successfullyRegisteredFirstUser: 'Berhasil mendaftarkan pengguna pertama.',
        successfullyUnlocked: 'Berhasil dibuka kuncinya',
        tokenRefreshSuccessful: 'Penyegaran token berhasil.',
        unableToVerify: 'Tidak Dapat Memverifikasi',
        username: 'Nama Pengguna',
        verified: 'Terverifikasi',
        verifiedSuccessfully: 'Berhasil Diverifikasi',
        verify: 'Verifikasi',
        verifyUser: 'Verifikasi Pengguna',
        verifyYourEmail: 'Verifikasi email Anda',
        youAreInactive: 'Anda sudah beberapa saat tidak aktif dan akan segera dikeluarkan secara otomatis demi keamanan Anda. Apakah Anda ingin tetap masuk?',
        youAreReceivingResetPassword: 'Anda menerima ini karena Anda (atau orang lain) telah meminta pengaturan ulang kata sandi untuk akun Anda. Silakan klik tautan berikut, atau tempel ini ke browser Anda untuk menyelesaikan proses:',
        youDidNotRequestPassword: 'Jika Anda tidak meminta ini, harap abaikan email ini dan kata sandi Anda akan tetap tidak berubah.'
    },
    error: {
        accountAlreadyActivated: 'Akun ini sudah diaktifkan.',
        autosaving: 'Terjadi masalah saat menyimpan otomatis dokumen ini.',
        correctInvalidFields: 'Harap perbaiki isian yang tidak valid.',
        deletingFile: 'Terjadi kesalahan saat menghapus file.',
        deletingTitle: 'Terjadi kesalahan saat menghapus {{title}}. Harap periksa koneksi Anda dan coba lagi.',
        documentNotFound: 'Dokumen dengan ID {{id}} tidak dapat ditemukan. Mungkin telah dihapus atau tidak pernah ada, atau Anda mungkin tidak memiliki akses ke sana.',
        emailOrPasswordIncorrect: 'Email atau kata sandi yang diberikan salah.',
        followingFieldsInvalid_one: 'Isian berikut tidak valid:',
        followingFieldsInvalid_other: 'Isian-isian berikut tidak valid:',
        incorrectCollection: 'Koleksi Salah',
        insufficientClipboardPermissions: 'Akses papan klip ditolak. Silakan periksa izin papan klip Anda.',
        invalidClipboardData: 'Data papan klip tidak valid.',
        invalidFileType: 'Jenis file tidak valid',
        invalidFileTypeValue: 'Jenis file tidak valid: {{value}}',
        invalidRequestArgs: 'Argumen yang diteruskan dalam permintaan tidak valid: {{args}}',
        loadingDocument: 'Terjadi masalah saat memuat dokumen dengan ID {{id}}.',
        localesNotSaved_one: 'Lokal berikut tidak dapat disimpan:',
        localesNotSaved_other: 'Lokal-lokal berikut tidak dapat disimpan:',
        logoutFailed: 'Gagal keluar.',
        missingEmail: 'Email tidak ada.',
        missingIDOfDocument: 'ID dokumen yang akan diperbarui tidak ada.',
        missingIDOfVersion: 'ID versi tidak ada.',
        missingRequiredData: 'Data yang diperlukan tidak ada.',
        noFilesUploaded: 'Tidak ada file yang diunggah.',
        noMatchedField: 'Tidak ada isian yang cocok ditemukan untuk "{{label}}"',
        notAllowedToAccessPage: 'Anda tidak diizinkan mengakses halaman ini.',
        notAllowedToPerformAction: 'Anda tidak diizinkan melakukan tindakan ini.',
        notFound: 'Data yang diminta tidak ditemukan.',
        noUser: 'Tidak Ada Pengguna',
        previewing: 'Terjadi masalah saat mempratinjau dokumen ini.',
        problemUploadingFile: 'Terjadi masalah saat mengunggah file.',
        restoringTitle: 'Terjadi kesalahan saat memulihkan {{title}}. Harap periksa koneksi Anda dan coba lagi.',
        tokenInvalidOrExpired: 'Token tidak valid atau telah kedaluwarsa.',
        tokenNotProvided: 'Token tidak disediakan.',
        unableToCopy: 'Tidak dapat menyalin.',
        unableToDeleteCount: 'Tidak dapat menghapus {{count}} dari {{total}} {{label}}.',
        unableToReindexCollection: 'Kesalahan mengindeks ulang koleksi {{collection}}. Operasi dibatalkan.',
        unableToUpdateCount: 'Tidak dapat memperbarui {{count}} dari {{total}} {{label}}.',
        unauthorized: 'Tidak sah, Anda harus masuk untuk membuat permintaan ini.',
        unauthorizedAdmin: 'Tidak sah, pengguna ini tidak memiliki akses ke panel admin.',
        unknown: 'Terjadi kesalahan yang tidak diketahui.',
        unPublishingDocument: 'Terjadi masalah saat membatalkan publikasi dokumen ini.',
        unspecific: 'Terjadi kesalahan.',
        unverifiedEmail: 'Harap verifikasi email Anda sebelum masuk.',
        userEmailAlreadyRegistered: 'Pengguna dengan email yang diberikan sudah terdaftar.',
        userLocked: 'Pengguna ini terkunci karena terlalu banyak upaya masuk yang gagal.',
        usernameAlreadyRegistered: 'Pengguna dengan nama pengguna yang diberikan sudah terdaftar.',
        usernameOrPasswordIncorrect: 'Nama pengguna atau kata sandi yang diberikan salah.',
        valueMustBeUnique: 'Nilai harus unik',
        verificationTokenInvalid: 'Token verifikasi tidak valid.'
    },
    fields: {
        addLabel: 'Tambah {{label}}',
        addLink: 'Tambah Tautan',
        addNew: 'Tambah baru',
        addNewLabel: 'Tambah {{label}} baru',
        addRelationship: 'Tambah Hubungan',
        addUpload: 'Tambah Unggahan',
        block: 'blok',
        blocks: 'blok',
        blockType: 'Tipe Blok',
        chooseBetweenCustomTextOrDocument: 'Pilih antara memasukkan URL teks kustom atau menautkan ke dokumen lain.',
        chooseDocumentToLink: 'Pilih dokumen untuk ditautkan',
        chooseFromExisting: 'Pilih dari yang sudah ada',
        chooseLabel: 'Pilih {{label}}',
        collapseAll: 'Ciutkan Semua',
        customURL: 'URL Kustom',
        editLabelData: 'Edit data {{label}}',
        editLink: 'Edit Tautan',
        editRelationship: 'Edit Hubungan',
        enterURL: 'Masukkan URL',
        internalLink: 'Tautan Internal',
        itemsAndMore: '{{items}} dan {{count}} lainnya',
        labelRelationship: 'Hubungan {{label}}',
        latitude: 'Lintang',
        linkedTo: 'Tertaut ke <0>{{label}}</0>',
        linkType: 'Jenis Tautan',
        longitude: 'Bujur',
        newLabel: '{{label}} Baru',
        openInNewTab: 'Buka di tab baru',
        passwordsDoNotMatch: 'Kata sandi tidak cocok.',
        relatedDocument: 'Dokumen Terkait',
        relationTo: 'Hubungan Ke',
        removeRelationship: 'Hapus Hubungan',
        removeUpload: 'Hapus Unggahan',
        saveChanges: 'Simpan perubahan',
        searchForBlock: 'Cari blok',
        selectExistingLabel: 'Pilih {{label}} yang ada',
        selectFieldsToEdit: 'Pilih isian untuk diedit',
        showAll: 'Tampilkan Semua',
        swapRelationship: 'Tukar Hubungan',
        swapUpload: 'Tukar Unggahan',
        textToDisplay: 'Teks untuk ditampilkan',
        toggleBlock: 'Beralih blok',
        uploadNewLabel: 'Unggah {{label}} baru'
    },
    folder: {
        browseByFolder: 'Jelajahi berdasarkan Folder',
        byFolder: 'Berdasarkan Folder',
        deleteFolder: 'Hapus Folder',
        folderName: 'Nama Folder',
        folders: 'Folder',
        folderTypeDescription: 'Pilih jenis dokumen koleksi yang diizinkan di folder ini.',
        itemHasBeenMoved: '{{title}} telah dipindahkan ke {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} telah dipindahkan ke folder root',
        itemsMovedToFolder: '{{title}} dipindahkan ke {{folderName}}',
        itemsMovedToRoot: '{{title}} dipindahkan ke folder root',
        moveFolder: 'Pindahkan Folder',
        moveItemsToFolderConfirmation: 'Anda akan memindahkan <1>{{count}} {{label}}</1> ke <2>{{toFolder}}</2>. Apakah Anda yakin?',
        moveItemsToRootConfirmation: 'Anda akan memindahkan <1>{{count}} {{label}}</1> ke folder root. Apakah Anda yakin?',
        moveItemToFolderConfirmation: 'Anda akan memindahkan <1>{{title}}</1> ke <2>{{toFolder}}</2>. Apakah Anda yakin?',
        moveItemToRootConfirmation: 'Anda akan memindahkan <1>{{title}}</1> ke folder root. Apakah Anda yakin?',
        movingFromFolder: 'Memindahkan {{title}} dari {{fromFolder}}',
        newFolder: 'Folder Baru',
        noFolder: 'Tidak Ada Folder',
        renameFolder: 'Ganti Nama Folder',
        searchByNameInFolder: 'Cari berdasarkan Nama di {{folderName}}',
        selectFolderForItem: 'Pilih folder untuk {{title}}'
    },
    general: {
        name: 'Nama',
        aboutToDelete: 'Anda akan menghapus {{label}} <1>{{title}}</1>. Apakah Anda yakin?',
        aboutToDeleteCount_many: 'Anda akan menghapus {{count}} {{label}}',
        aboutToDeleteCount_one: 'Anda akan menghapus {{count}} {{label}}',
        aboutToDeleteCount_other: 'Anda akan menghapus {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Anda akan menghapus secara permanen {{label}} <1>{{title}}</1>. Apakah Anda yakin?',
        aboutToPermanentlyDeleteTrash: 'Anda akan menghapus secara permanen <0>{{count}}</0> <1>{{label}}</1> dari tempat sampah. Apakah Anda yakin?',
        aboutToRestore: 'Anda akan memulihkan {{label}} <1>{{title}}</1>. Apakah Anda yakin?',
        aboutToRestoreAsDraft: 'Anda akan memulihkan {{label}} <1>{{title}}</1> sebagai draf. Apakah Anda yakin?',
        aboutToRestoreAsDraftCount: 'Anda akan memulihkan {{count}} {{label}} sebagai draf',
        aboutToRestoreCount: 'Anda akan memulihkan {{count}} {{label}}',
        aboutToTrash: 'Anda akan memindahkan {{label}} <1>{{title}}</1> ke tempat sampah. Apakah Anda yakin?',
        aboutToTrashCount: 'Anda akan memindahkan {{count}} {{label}} ke tempat sampah',
        addBelow: 'Tambah di Bawah',
        addFilter: 'Tambah Filter',
        adminTheme: 'Tema Admin',
        all: 'Semua',
        allCollections: 'Semua Koleksi',
        allLocales: 'Semua lokal',
        and: 'Dan',
        anotherUser: 'Pengguna lain',
        anotherUserTakenOver: 'Pengguna lain telah mengambil alih pengeditan dokumen ini.',
        applyChanges: 'Terapkan Perubahan',
        ascending: 'Naik',
        automatic: 'Otomatis',
        backToDashboard: 'Kembali ke Dasbor',
        cancel: 'Batal',
        changesNotSaved: 'Perubahan Anda belum disimpan. Jika Anda pergi sekarang, Anda akan kehilangan perubahan Anda.',
        clear: 'Hapus',
        clearAll: 'Hapus Semua',
        close: 'Tutup',
        collapse: 'Ciutkan',
        collections: 'Koleksi',
        columns: 'Kolom',
        columnToSort: 'Kolom untuk Diurutkan',
        confirm: 'Konfirmasi',
        confirmCopy: 'Konfirmasi salin',
        confirmDeletion: 'Konfirmasi penghapusan',
        confirmDuplication: 'Konfirmasi duplikasi',
        confirmMove: 'Konfirmasi pindah',
        confirmReindex: 'Indeks ulang semua {{collections}}?',
        confirmReindexAll: 'Indeks ulang semua koleksi?',
        confirmReindexDescription: 'Ini akan menghapus indeks yang ada dan mengindeks ulang dokumen di koleksi {{collections}}.',
        confirmReindexDescriptionAll: 'Ini akan menghapus indeks yang ada dan mengindeks ulang dokumen di semua koleksi.',
        confirmRestoration: 'Konfirmasi pemulihan',
        copied: 'Disalin',
        copy: 'Salin',
        copyField: 'Salin Isian',
        copying: 'Menyalin',
        copyRow: 'Salin Baris',
        copyWarning: 'Anda akan menimpa {{to}} dengan {{from}} untuk {{label}} {{title}}. Apakah Anda yakin?',
        create: 'Buat',
        created: 'Dibuat',
        createdAt: 'Dibuat Pada',
        createNew: 'Buat Baru',
        createNewLabel: 'Buat {{label}} baru',
        creating: 'Membuat',
        creatingNewLabel: 'Membuat {{label}} baru',
        currentlyEditing: 'sedang mengedit dokumen ini. Jika Anda mengambil alih, mereka akan diblokir untuk melanjutkan pengeditan, dan mungkin juga kehilangan perubahan yang belum disimpan.',
        custom: 'Kustom',
        dark: 'Gelap',
        dashboard: 'Dasbor',
        delete: 'Hapus',
        deleted: 'Dihapus',
        deletedAt: 'Dihapus Pada',
        deletedCountSuccessfully: 'Berhasil menghapus {{count}} {{label}}.',
        deletedSuccessfully: 'Berhasil dihapus.',
        deletePermanently: 'Lewati tempat sampah dan hapus secara permanen',
        deleting: 'Menghapus...',
        depth: 'Kedalaman',
        descending: 'Turun',
        deselectAllRows: 'Batal pilih semua baris',
        document: 'Dokumen',
        documentIsTrashed: '{{label}} ini ada di tempat sampah dan bersifat hanya-baca.',
        documentLocked: 'Dokumen terkunci',
        documents: 'Dokumen',
        duplicate: 'Duplikat',
        duplicateWithoutSaving: 'Duplikat tanpa menyimpan perubahan',
        edit: 'Edit',
        editAll: 'Edit semua',
        editedSince: 'Diedit sejak',
        editing: 'Mengedit',
        editingLabel_many: 'Mengedit {{count}} {{label}}',
        editingLabel_one: 'Mengedit {{count}} {{label}}',
        editingLabel_other: 'Mengedit {{count}} {{label}}',
        editingTakenOver: 'Pengeditan diambil alih',
        editLabel: 'Edit {{label}}',
        email: 'Email',
        emailAddress: 'Alamat Email',
        emptyTrash: 'Kosongkan tempat sampah',
        emptyTrashLabel: 'Kosongkan tempat sampah {{label}}',
        enterAValue: 'Masukkan nilai',
        error: 'Kesalahan',
        errors: 'Kesalahan',
        exitLivePreview: 'Keluar dari Pratinjau Langsung',
        export: 'Ekspor',
        fallbackToDefaultLocale: 'Kembali ke lokal default',
        false: 'Salah',
        filter: 'Filter',
        filters: 'Filter',
        filterWhere: 'Filter {{label}} di mana',
        globals: 'Global',
        goBack: 'Kembali',
        groupByLabel: 'Kelompokkan berdasarkan {{label}}',
        import: 'Impor',
        isEditing: 'sedang mengedit',
        item: 'item',
        items: 'item',
        language: 'Bahasa',
        lastModified: 'Terakhir Diubah',
        leaveAnyway: 'Tetap pergi',
        leaveWithoutSaving: 'Pergi tanpa menyimpan',
        light: 'Terang',
        livePreview: 'Pratinjau Langsung',
        loading: 'Memuat',
        locale: 'Lokal',
        locales: 'Lokal',
        menu: 'Menu',
        moreOptions: 'Opsi lainnya',
        move: 'Pindah',
        moveConfirm: 'Anda akan memindahkan {{count}} {{label}} ke <1>{{destination}}</1>. Apakah Anda yakin?',
        moveCount: 'Pindahkan {{count}} {{label}}',
        moveDown: 'Pindah ke Bawah',
        moveUp: 'Pindah ke Atas',
        moving: 'Memindahkan',
        movingCount: 'Memindahkan {{count}} {{label}}',
        newPassword: 'Kata Sandi Baru',
        next: 'Berikutnya',
        no: 'Tidak',
        noDateSelected: 'Tidak ada tanggal yang dipilih',
        noFiltersSet: 'Tidak ada filter yang diatur',
        noLabel: '<Tidak ada {{label}}>',
        none: 'Tidak ada',
        noOptions: 'Tidak ada opsi',
        noResults: 'Tidak ada {{label}} yang ditemukan. Entah belum ada {{label}} atau tidak ada yang cocok dengan filter yang Anda tentukan di atas.',
        notFound: 'Tidak Ditemukan',
        nothingFound: 'Tidak ada yang ditemukan',
        noTrashResults: 'Tidak ada {{label}} di tempat sampah.',
        noUpcomingEventsScheduled: 'Tidak ada acara mendatang yang dijadwalkan.',
        noValue: 'Tidak ada nilai',
        of: 'dari',
        only: 'Hanya',
        open: 'Buka',
        or: 'Atau',
        order: 'Urutan',
        overwriteExistingData: 'Timpa data isian yang ada',
        pageNotFound: 'Halaman tidak ditemukan',
        password: 'Kata Sandi',
        pasteField: 'Tempel Isian',
        pasteRow: 'Tempel Baris',
        payloadSettings: 'Pengaturan Payload',
        permanentlyDelete: 'Hapus Secara Permanen',
        permanentlyDeletedCountSuccessfully: 'Berhasil menghapus secara permanen {{count}} {{label}}.',
        perPage: 'Per Halaman: {{limit}}',
        previous: 'Sebelumnya',
        reindex: 'Indeks Ulang',
        reindexingAll: 'Mengindeks ulang semua {{collections}}.',
        remove: 'Hapus',
        rename: 'Ganti Nama',
        reset: 'Atur Ulang',
        resetPreferences: 'Atur Ulang Preferensi',
        resetPreferencesDescription: 'Ini akan mengatur ulang semua preferensi Anda ke pengaturan default.',
        resettingPreferences: 'Mengatur Ulang Preferensi.',
        restore: 'Pulihkan',
        restoreAsPublished: 'Pulihkan sebagai versi yang diterbitkan',
        restoredCountSuccessfully: 'Berhasil memulihkan {{count}} {{label}}.',
        restoring: 'Memulihkan...',
        row: 'Baris',
        rows: 'Baris',
        save: 'Simpan',
        saving: 'Menyimpan...',
        schedulePublishFor: 'Jadwalkan publikasi untuk {{title}}',
        searchBy: 'Cari berdasarkan {{label}}',
        select: 'Pilih',
        selectAll: 'Pilih semua {{count}} {{label}}',
        selectAllRows: 'Pilih semua baris',
        selectedCount: '{{count}} {{label}} dipilih',
        selectLabel: 'Pilih {{label}}',
        selectValue: 'Pilih nilai',
        showAllLabel: 'Tampilkan semua {{label}}',
        sorryNotFound: 'Maaf—tidak ada yang sesuai dengan permintaan Anda.',
        sort: 'Urutkan',
        sortByLabelDirection: 'Urutkan berdasarkan {{label}} {{direction}}',
        stayOnThisPage: 'Tetap di halaman ini',
        submissionSuccessful: 'Pengiriman Berhasil.',
        submit: 'Kirim',
        submitting: 'Mengirim...',
        success: 'Sukses',
        successfullyCreated: '{{label}} berhasil dibuat.',
        successfullyDuplicated: '{{label}} berhasil diduplikasi.',
        successfullyReindexed: 'Berhasil mengindeks ulang {{count}} dari {{total}} dokumen dari {{collections}}',
        takeOver: 'Ambil alih',
        thisLanguage: 'Bahasa Indonesia',
        time: 'Waktu',
        timezone: 'Zona Waktu',
        titleDeleted: '{{label}} "{{title}}" berhasil dihapus.',
        titleRestored: '{{label}} "{{title}}" berhasil dipulihkan.',
        titleTrashed: '{{label}} "{{title}}" dipindahkan ke tempat sampah.',
        trash: 'Tempat Sampah',
        trashedCountSuccessfully: '{{count}} {{label}} dipindahkan ke tempat sampah.',
        true: 'Benar',
        unauthorized: 'Tidak Sah',
        unsavedChanges: 'Anda memiliki perubahan yang belum disimpan. Simpan atau buang sebelum melanjutkan.',
        unsavedChangesDuplicate: 'Anda memiliki perubahan yang belum disimpan. Apakah Anda ingin melanjutkan untuk menduplikasi?',
        untitled: 'Tanpa Judul',
        upcomingEvents: 'Acara Mendatang',
        updatedAt: 'Diperbarui Pada',
        updatedCountSuccessfully: 'Berhasil memperbarui {{count}} {{label}}.',
        updatedLabelSuccessfully: 'Berhasil memperbarui {{label}}.',
        updatedSuccessfully: 'Berhasil diperbarui.',
        updateForEveryone: 'Perbarui untuk semua orang',
        updating: 'Memperbarui',
        uploading: 'Mengunggah',
        uploadingBulk: 'Mengunggah {{current}} dari {{total}}',
        user: 'Pengguna',
        username: 'Nama Pengguna',
        users: 'Pengguna',
        value: 'Nilai',
        viewing: 'Melihat',
        viewReadOnly: 'Lihat hanya-baca',
        welcome: 'Selamat Datang',
        yes: 'Ya'
    },
    localization: {
        cannotCopySameLocale: 'Tidak dapat menyalin ke lokal yang sama',
        copyFrom: 'Salin dari',
        copyFromTo: 'Menyalin dari {{from}} ke {{to}}',
        copyTo: 'Salin ke',
        copyToLocale: 'Salin ke lokal',
        localeToPublish: 'Lokal untuk dipublikasikan',
        selectLocaleToCopy: 'Pilih lokal untuk disalin'
    },
    operators: {
        contains: 'mengandung',
        equals: 'sama dengan',
        exists: 'ada',
        intersects: 'bersinggungan',
        isGreaterThan: 'lebih besar dari',
        isGreaterThanOrEqualTo: 'lebih besar dari atau sama dengan',
        isIn: 'berada di dalam',
        isLessThan: 'lebih kecil dari',
        isLessThanOrEqualTo: 'lebih kecil dari atau sama dengan',
        isLike: 'seperti',
        isNotEqualTo: 'tidak sama dengan',
        isNotIn: 'tidak berada di dalam',
        isNotLike: 'tidak seperti',
        near: 'dekat',
        within: 'di dalam'
    },
    upload: {
        addFile: 'Tambah file',
        addFiles: 'Tambah file',
        bulkUpload: 'Unggah Massal',
        crop: 'Pangkas',
        cropToolDescription: 'Seret sudut area yang dipilih, gambar area baru atau sesuaikan nilai di bawah ini.',
        download: 'Unduh',
        dragAndDrop: 'Seret dan lepas file',
        dragAndDropHere: 'atau seret dan lepas file di sini',
        editImage: 'Edit Gambar',
        fileName: 'Nama File',
        fileSize: 'Ukuran File',
        filesToUpload: 'File untuk Diunggah',
        fileToUpload: 'File untuk Diunggah',
        focalPoint: 'Titik Fokus',
        focalPointDescription: 'Seret titik fokus langsung pada pratinjau atau sesuaikan nilai di bawah ini.',
        height: 'Tinggi',
        lessInfo: 'Info lebih sedikit',
        moreInfo: 'Info lebih lanjut',
        noFile: 'Tidak ada file',
        pasteURL: 'Tempel URL',
        previewSizes: 'Ukuran Pratinjau',
        selectCollectionToBrowse: 'Pilih Koleksi untuk Dijelajahi',
        selectFile: 'Pilih file',
        setCropArea: 'Atur area pangkas',
        setFocalPoint: 'Atur titik fokus',
        sizes: 'Ukuran',
        sizesFor: 'Ukuran untuk {{label}}',
        width: 'Lebar'
    },
    validation: {
        emailAddress: 'Harap masukkan alamat email yang valid.',
        enterNumber: 'Harap masukkan nomor yang valid.',
        fieldHasNo: 'Isian ini tidak memiliki {{label}}',
        greaterThanMax: '{{value}} lebih besar dari {{label}} maksimum yang diizinkan yaitu {{max}}.',
        invalidInput: 'Isian ini memiliki masukan yang tidak valid.',
        invalidSelection: 'Isian ini memiliki pilihan yang tidak valid.',
        invalidSelections: 'Isian ini memiliki pilihan tidak valid berikut:',
        lessThanMin: '{{value}} lebih kecil dari {{label}} minimum yang diizinkan yaitu {{min}}.',
        limitReached: 'Batas tercapai, hanya {{max}} item yang dapat ditambahkan.',
        longerThanMin: 'Nilai ini harus lebih panjang dari panjang minimum {{minLength}} karakter.',
        notValidDate: '"{{value}}" bukan tanggal yang valid.',
        required: 'Isian ini wajib diisi.',
        requiresAtLeast: 'Isian ini membutuhkan setidaknya {{count}} {{label}}.',
        requiresNoMoreThan: 'Isian ini membutuhkan tidak lebih dari {{count}} {{label}}.',
        requiresTwoNumbers: 'Isian ini membutuhkan dua angka.',
        shorterThanMax: 'Nilai ini harus lebih pendek dari panjang maksimum {{maxLength}} karakter.',
        timezoneRequired: 'Zona waktu diperlukan.',
        trueOrFalse: 'Isian ini hanya bisa sama dengan benar atau salah.',
        username: 'Harap masukkan nama pengguna yang valid. Dapat berisi huruf, angka, tanda hubung, titik, dan garis bawah.',
        validUploadID: 'Isian ini bukan ID unggahan yang valid.'
    },
    version: {
        type: 'Tipe',
        aboutToPublishSelection: 'Anda akan mempublikasikan semua {{label}} dalam pilihan. Apakah Anda yakin?',
        aboutToRestore: 'Anda akan memulihkan dokumen {{label}} ini ke keadaan pada {{versionDate}}.',
        aboutToRestoreGlobal: 'Anda akan memulihkan global {{label}} ke keadaan pada {{versionDate}}.',
        aboutToRevertToPublished: 'Anda akan mengembalikan perubahan dokumen ini ke keadaan yang dipublikasikan. Apakah Anda yakin?',
        aboutToUnpublish: 'Anda akan membatalkan publikasi dokumen ini. Apakah Anda yakin?',
        aboutToUnpublishSelection: 'Anda akan membatalkan publikasi semua {{label}} dalam pilihan. Apakah Anda yakin?',
        autosave: 'Simpan Otomatis',
        autosavedSuccessfully: 'Berhasil disimpan otomatis.',
        autosavedVersion: 'Versi simpan otomatis',
        changed: 'Berubah',
        changedFieldsCount_one: '{{count}} Isian berubah',
        changedFieldsCount_other: '{{count}} Isian berubah',
        compareVersion: 'Bandingkan versi dengan:',
        compareVersions: 'Bandingkan Versi',
        comparingAgainst: 'Membandingkan dengan',
        confirmPublish: 'Konfirmasi publikasi',
        confirmRevertToSaved: 'Konfirmasi kembali ke yang tersimpan',
        confirmUnpublish: 'Konfirmasi pembatalan publikasi',
        confirmVersionRestoration: 'Konfirmasi Pemulihan Versi',
        currentDocumentStatus: 'Dokumen {{docStatus}} saat ini',
        currentDraft: 'Draf Saat Ini',
        currentlyPublished: 'Sedang Dipublikasikan',
        currentlyViewing: 'Sedang melihat',
        currentPublishedVersion: 'Versi Terbitan Saat Ini',
        draft: 'Draf',
        draftSavedSuccessfully: 'Draf berhasil disimpan.',
        lastSavedAgo: 'Terakhir disimpan {{distance}} yang lalu',
        modifiedOnly: 'Hanya yang diubah',
        moreVersions: 'Versi lainnya...',
        noFurtherVersionsFound: 'Tidak ada versi lebih lanjut yang ditemukan',
        noRowsFound: 'Tidak ada {{label}} yang ditemukan',
        noRowsSelected: 'Tidak ada {{label}} yang dipilih',
        preview: 'Pratinjau',
        previouslyDraft: 'Sebelumnya Draf',
        previouslyPublished: 'Sebelumnya Dipublikasikan',
        previousVersion: 'Versi Sebelumnya',
        problemRestoringVersion: 'Terjadi masalah saat memulihkan versi ini',
        publish: 'Publikasikan',
        publishAllLocales: 'Publikasikan semua lokal',
        publishChanges: 'Publikasikan perubahan',
        published: 'Diterbitkan',
        publishIn: 'Publikasikan di {{locale}}',
        publishing: 'Mempublikasikan',
        restoreAsDraft: 'Pulihkan sebagai draf',
        restoredSuccessfully: 'Berhasil dipulihkan.',
        restoreThisVersion: 'Pulihkan versi ini',
        restoring: 'Memulihkan...',
        reverting: 'Mengembalikan...',
        revertToPublished: 'Kembali ke yang dipublikasikan',
        saveDraft: 'Simpan Draf',
        scheduledSuccessfully: 'Berhasil dijadwalkan.',
        schedulePublish: 'Jadwalkan Publikasi',
        selectLocales: 'Pilih lokal untuk ditampilkan',
        selectVersionToCompare: 'Pilih versi untuk dibandingkan',
        showingVersionsFor: 'Menampilkan versi untuk:',
        showLocales: 'Tampilkan lokal:',
        specificVersion: 'Versi Spesifik',
        status: 'Status',
        unpublish: 'Batalkan Publikasi',
        unpublishing: 'Membatalkan publikasi...',
        version: 'Versi',
        versionAgo: '{{distance}} yang lalu',
        versionCount_many: '{{count}} versi ditemukan',
        versionCount_none: 'Tidak ada versi yang ditemukan',
        versionCount_one: '{{count}} versi ditemukan',
        versionCount_other: '{{count}} versi ditemukan',
        versionCreatedOn: '{{version}} dibuat pada:',
        versionID: 'ID Versi',
        versions: 'Versi',
        viewingVersion: 'Melihat versi untuk {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Melihat versi untuk global {{entityLabel}}',
        viewingVersions: 'Melihat versi untuk {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Melihat versi untuk global {{entityLabel}}'
    }
};
export const id = {
    dateFNSKey: 'id',
    translations: idTranslations
};

//# sourceMappingURL=id.js.map