{"version": 3, "sources": ["../../src/languages/pt.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const ptTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Conta',\n    accountOfCurrentUser: 'Conta do usuário ativo',\n    accountVerified: 'Conta verificada com sucesso.',\n    alreadyActivated: 'Conta já ativada',\n    alreadyLoggedIn: 'Login já realizado',\n    apiKey: 'Chave da API',\n    authenticated: 'Autenticado',\n    backToLogin: 'Voltar para login',\n    beginCreateFirstUser: 'Para começar, crie seu primeiro usuário.',\n    changePassword: 'Mudar senha',\n    checkYourEmailForPasswordReset:\n      'Se o endereço de email estiver associado a uma conta, você receberá instruções para redefinir sua senha em breve. Por favor, verifique sua pasta de spam ou lixo eletrônico se você não vir o email na sua caixa de entrada.',\n    confirmGeneration: 'Confirma<PERSON>',\n    confirmPassword: 'Confirma<PERSON>',\n    createFirstUser: 'Criar primeiro usuário',\n    emailNotValid: 'O email fornecido não é válido',\n    emailOrUsername: 'Email ou Nome de Usuário',\n    emailSent: 'Email Enviado',\n    emailVerified: 'Email verificado com sucesso.',\n    enableAPIKey: 'Habilitar Chave API',\n    failedToUnlock: 'Falha ao desbloquear',\n    forceUnlock: 'Forçar Desbloqueio',\n    forgotPassword: 'Esqueci a senha',\n    forgotPasswordEmailInstructions:\n      'Por favor, preencha seu email abaixo. Você receberá um email com instruções para gerar uma nova senha',\n    forgotPasswordQuestion: 'Esqueceu a senha?',\n    forgotPasswordUsernameInstructions:\n      'Digite seu nome de usuário abaixo. Instruções sobre como redefinir sua senha serão enviadas para o endereço de e-mail associado ao seu nome de usuário.',\n    generate: 'Gerar',\n    generateNewAPIKey: 'Gerar nova chave API',\n    generatingNewAPIKeyWillInvalidate:\n      'Gerar uma nova chave API <1>invalidará</1> a chave anterior. Você tem certeza que deseja prosseguir?',\n    lockUntil: 'Bloquear Até',\n    logBackIn: 'Fazer login novamente',\n    loggedIn: 'Para fazer login como outro usuário, você deve fazer o <0>log out</0> antes.',\n    loggedInChangePassword:\n      'Para mudar a sua senha, acesse a sua <0>conta</0> e edite sua senha lá.',\n    loggedOutInactivity: 'Você foi desconectado devido a inatividade.',\n    loggedOutSuccessfully: 'Log out efetuado com sucesso.',\n    loggingOut: 'Saindo...',\n    login: 'Login',\n    loginAttempts: 'Tentativas de Login',\n    loginUser: 'Iniciar sessão',\n    loginWithAnotherUser:\n      'Para fazer login como outro usuário, você deve fazer o <0>log out</0> antes.',\n    logOut: 'Log out',\n    logout: 'Logout',\n    logoutSuccessful: 'Logout bem sucedido.',\n    logoutUser: 'Encerrar sessão',\n    newAccountCreated:\n      'Uma nova conta acaba de ser criada para que você possa acessar <a href=\"{{serverURL}}\">{{serverURL}}</a> Por favor, clique no link a seguir ou cole a URL abaixo no seu navegador para verificar seu email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Após a verificação de email, você será capaz de fazer o login.',\n    newAPIKeyGenerated: 'Nova Chave API Gerada.',\n    newPassword: 'Nova Senha',\n    passed: 'Autenticação Aprovada',\n    passwordResetSuccessfully: 'Redefinição de senha realizada com sucesso.',\n    resetPassword: 'Redefinir Senha',\n    resetPasswordExpiration: 'Tempo Limite para Redefinição de Senha',\n    resetPasswordToken: 'Token para Redefinição de Senha',\n    resetYourPassword: 'Redefinir Sua Senha',\n    stayLoggedIn: 'Manter sessão ativa',\n    successfullyRegisteredFirstUser: 'Primeiro usuário registrado com sucesso.',\n    successfullyUnlocked: 'Desbloqueado com sucesso',\n    tokenRefreshSuccessful: 'Atualização do token bem-sucedida.',\n    unableToVerify: 'Não foi possível verificar',\n    username: 'Nome de usuário',\n    usernameNotValid: 'O nome de usuário fornecido não é válido',\n    verified: 'Verificado',\n    verifiedSuccessfully: 'Verificado com Sucesso',\n    verify: 'Verificar',\n    verifyUser: 'Verificar Usuário',\n    verifyYourEmail: 'Verifique seu email',\n    youAreInactive:\n      'Você não está ativo há algum tempo e sua sessão será automaticamente finalizada em breve, para sua própria segurança. Você gostaria de manter a sessão ativa?',\n    youAreReceivingResetPassword:\n      'Você está recebendo essa mensagem porque você (ou outra pessoa) requisitou a redefinição de senha da sua conta. Por favor, clique no link a seguir ou cole no seu navegador para completar o processo:',\n    youDidNotRequestPassword:\n      'Se você não fez essa requisição, por favor ignore esse email e sua senha permanecerá igual.',\n  },\n  error: {\n    accountAlreadyActivated: 'Essa conta já foi ativada.',\n    autosaving: 'Ocorreu um problema ao salvar automaticamente esse documento.',\n    correctInvalidFields: 'Por favor, corrija os campos inválidos.',\n    deletingFile: 'Ocorreu um erro ao excluir o arquivo.',\n    deletingTitle:\n      'Ocorreu um erro ao excluir {{title}}. Por favor, verifique sua conexão e tente novamente.',\n    documentNotFound:\n      'O documento com o ID {{id}} não pôde ser encontrado. Ele pode ter sido deletado ou nunca ter existido, ou você pode não ter acesso a ele.',\n    emailOrPasswordIncorrect: 'O email ou senha fornecido está incorreto.',\n    followingFieldsInvalid_one: 'O campo a seguir está inválido:',\n    followingFieldsInvalid_other: 'Os campos a seguir estão inválidos:',\n    incorrectCollection: 'Coleção Incorreta',\n    insufficientClipboardPermissions:\n      'Acesso à área de transferência negado. Verifique suas permissões da área de transferência.',\n    invalidClipboardData: 'Dados inválidos na área de transferência.',\n    invalidFileType: 'Tipo de arquivo inválido',\n    invalidFileTypeValue: 'Tipo de arquivo inválido: {{value}}',\n    invalidRequestArgs: 'Argumentos inválidos passados na solicitação: {{args}}',\n    loadingDocument: 'Ocorreu um problema ao carregar o documento com ID {{id}}.',\n    localesNotSaved_one: 'A seguinte configuração regional não pôde ser salva:',\n    localesNotSaved_other: 'As seguintes configurações regionais não puderam ser salvas:',\n    logoutFailed: 'Falha ao sair.',\n    missingEmail: 'Email ausente.',\n    missingIDOfDocument: 'ID do documento a ser atualizado ausente.',\n    missingIDOfVersion: 'ID da versão ausente.',\n    missingRequiredData: 'Dados requeridos ausentes.',\n    noFilesUploaded: 'Nenhum arquivo foi carregado.',\n    noMatchedField: 'Não foi encontrado nenhum campo correspondente a \"{{label}}\"',\n    notAllowedToAccessPage: 'Você não tem permissão para acessar essa página.',\n    notAllowedToPerformAction: 'Você não tem permissão para realizar essa ação.',\n    notFound: 'O recurso requisitado não foi encontrado.',\n    noUser: 'Nenhum Usuário',\n    previewing: 'Ocorreu um problema ao visualizar esse documento.',\n    problemUploadingFile: 'Ocorreu um problema ao carregar o arquivo.',\n    restoringTitle:\n      'Ocorreu um erro ao restaurar {{title}}. Por favor, verifique sua conexão e tente novamente.',\n    tokenInvalidOrExpired: 'Token expirado ou inválido.',\n    tokenNotProvided: 'Token não fornecido.',\n    unableToCopy: 'Não é possível copiar.',\n    unableToDeleteCount: 'Não é possível excluir {{count}} de {{total}} {{label}}.',\n    unableToReindexCollection: 'Erro ao reindexar a coleção {{collection}}. Operação abortada.',\n    unableToUpdateCount: 'Não foi possível atualizar {{count}} de {{total}} {{label}}.',\n    unauthorized: 'Não autorizado. Você deve estar logado para fazer essa requisição',\n    unauthorizedAdmin: 'Não autorizado, esse usuário não tem acesso ao painel de administração.',\n    unknown: 'Ocorreu um erro desconhecido.',\n    unPublishingDocument: 'Ocorreu um problema ao despublicar esse documento',\n    unspecific: 'Ocorreu um erro.',\n    unverifiedEmail: 'Por favor, verifique seu e-mail antes de fazer login.',\n    userEmailAlreadyRegistered: 'Um usuário com o email fornecido já está registrado.',\n    userLocked: 'Esse usuário está bloqueado devido a muitas tentativas de login malsucedidas.',\n    usernameAlreadyRegistered: 'Um usuário com o nome de usuário fornecido já está registrado.',\n    usernameOrPasswordIncorrect: 'O nome de usuário ou senha fornecidos estão incorretos.',\n    valueMustBeUnique: 'Valor deve ser único',\n    verificationTokenInvalid: 'Token de verificação inválido.',\n  },\n  fields: {\n    addLabel: 'Adicionar {{label}}',\n    addLink: 'Adicionar Link',\n    addNew: 'Adicionar novo',\n    addNewLabel: 'Adicionar novo {{label}}',\n    addRelationship: 'Adicionar Relação',\n    addUpload: 'Adicionar Upload',\n    block: 'bloco',\n    blocks: 'blocos',\n    blockType: 'Tipo de bloco',\n    chooseBetweenCustomTextOrDocument:\n      'Escolha entre inserir um URL de texto personalizado ou vincular a outro documento.',\n    chooseDocumentToLink: 'Escolha um documento para vincular',\n    chooseFromExisting: 'Escolher entre os existentes',\n    chooseLabel: 'Escolher {{label}}',\n    collapseAll: 'Recolher todos',\n    customURL: 'URL personalizado',\n    editLabelData: 'Editar dados de {{label}}',\n    editLink: 'Editar Link',\n    editRelationship: 'Editar Relacionamento',\n    enterURL: 'Insira um URL',\n    internalLink: 'Link Interno',\n    itemsAndMore: '{{items}} e mais {{count}}',\n    labelRelationship: 'Relacionado a {{label}}',\n    latitude: 'Latitude',\n    linkedTo: 'Ligado a <0>{{label}}</0>',\n    linkType: 'Tipo de link',\n    longitude: 'Longitude',\n    newLabel: 'Novo(a) {{label}}',\n    openInNewTab: 'Abrir em nova aba',\n    passwordsDoNotMatch: 'Senhas não coincidem.',\n    relatedDocument: 'Documento Relacionado',\n    relationTo: 'Relacionado a',\n    removeRelationship: 'Remover Relacionamento',\n    removeUpload: 'Remover Upload',\n    saveChanges: 'Salvar alterações',\n    searchForBlock: 'Procurar bloco',\n    selectExistingLabel: 'Selecionar {{label}} existente',\n    selectFieldsToEdit: 'Selecione os campos para editar',\n    showAll: 'Mostrar Tudo',\n    swapRelationship: 'Relação de Troca',\n    swapUpload: 'Substituir Upload',\n    textToDisplay: 'Texto a ser exibido',\n    toggleBlock: 'Alternar bloco',\n    uploadNewLabel: 'Carregar novo(a) {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Navegar por Pasta',\n    byFolder: 'Por Pasta',\n    deleteFolder: 'Apagar Pasta',\n    folderName: 'Nome da Pasta',\n    folders: 'Pastas',\n    folderTypeDescription:\n      'Selecione qual tipo de documentos da coleção devem ser permitidos nesta pasta.',\n    itemHasBeenMoved: '{{title}} foi movido para {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} foi movido para a pasta raiz',\n    itemsMovedToFolder: '{{title}} movido para {{folderName}}',\n    itemsMovedToRoot: '{{title}} foi movido para a pasta raiz',\n    moveFolder: 'Mover Pasta',\n    moveItemsToFolderConfirmation:\n      'Você está prestes a mover <1>{{count}} {{label}}</1> para <2>{{toFolder}}</2>. Tem certeza?',\n    moveItemsToRootConfirmation:\n      'Você está prestes a mover <1>{{count}} {{label}}</1> para a pasta raiz. Tem certeza?',\n    moveItemToFolderConfirmation:\n      'Você está prestes a mover <1>{{title}}</1> para <2>{{toFolder}}</2>. Tem certeza?',\n    moveItemToRootConfirmation:\n      'Você está prestes a mover <1>{{title}}</1> para a pasta raiz. Tem certeza disso?',\n    movingFromFolder: 'Movendo {{title}} de {{fromFolder}}',\n    newFolder: 'Nova Pasta',\n    noFolder: 'Sem Pasta',\n    renameFolder: 'Renomear Pasta',\n    searchByNameInFolder: 'Pesquisar por Nome em {{folderName}}',\n    selectFolderForItem: 'Selecione a pasta para {{title}}',\n  },\n  general: {\n    name: 'Nome',\n    aboutToDelete: 'Você está prestes a excluir o/a {{label}} <1>{{title}}</1>. Tem certeza?',\n    aboutToDeleteCount_many: 'Você está prestes a deletar {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Você está prestes a deletar {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Você está prestes a deletar {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Está prestes a apagar permanentemente o {{label}} <1>{{title}}</1>. Tem certeza?',\n    aboutToPermanentlyDeleteTrash:\n      'Você está prestes a excluir permanentemente <0>{{count}}</0> <1>{{label}}</1> da lixeira. Você tem certeza?',\n    aboutToRestore: 'Está prestes a restaurar o {{label}} <1>{{title}}</1>. Tem certeza?',\n    aboutToRestoreAsDraft:\n      'Está prestes a restaurar o {{label}} <1>{{title}}</1> como um rascunho. Tem certeza?',\n    aboutToRestoreAsDraftCount: 'Está prestes a restaurar {{count}} {{label}} como rascunho',\n    aboutToRestoreCount: 'Você está prestes a restaurar {{count}} {{label}}',\n    aboutToTrash:\n      'Você está prestes a mover o {{label}} <1>{{title}}</1> para a lixeira. Tem certeza?',\n    aboutToTrashCount: 'Estás prestes a mover {{count}} {{label}} para o lixo',\n    addBelow: 'Adicionar abaixo',\n    addFilter: 'Adicionar Filtro',\n    adminTheme: 'Tema do Admin',\n    all: 'Todos',\n    allCollections: 'Todas as Coleções',\n    allLocales: 'Todos os locais',\n    and: 'E',\n    anotherUser: 'Outro usuário',\n    anotherUserTakenOver: 'Outro usuário assumiu a edição deste documento.',\n    applyChanges: 'Aplicar alterações',\n    ascending: 'Ascendente',\n    automatic: 'Automático',\n    backToDashboard: 'Voltar para Painel de Controle',\n    cancel: 'Cancelar',\n    changesNotSaved:\n      'Suas alterações não foram salvas. Se você sair agora, essas alterações serão perdidas.',\n    clear: 'Claro',\n    clearAll: 'Limpar Tudo',\n    close: 'Fechar',\n    collapse: 'Recolher',\n    collections: 'Coleções',\n    columns: 'Colunas',\n    columnToSort: 'Coluna para Ordenar',\n    confirm: 'Confirmar',\n    confirmCopy: 'Confirme cópia',\n    confirmDeletion: 'Confirmar exclusão',\n    confirmDuplication: 'Confirmar duplicação',\n    confirmMove: 'Confirme a movimentação',\n    confirmReindex: 'Reindexar todas as {{collections}}?',\n    confirmReindexAll: 'Reindexar todas as coleções?',\n    confirmReindexDescription:\n      'Isso removerá os índices existentes e reindexará os documentos nas coleções {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Isso removerá os índices existentes e reindexará os documentos em todas as coleções.',\n    confirmRestoration: 'Confirme a restauração',\n    copied: 'Copiado',\n    copy: 'Copiar',\n    copyField: 'Copiar campo',\n    copying: 'Copiando',\n    copyRow: 'Copiar linha',\n    copyWarning:\n      'Você está prestes a sobrescrever {{to}} com {{from}} para {{label}} {{title}}. Tem certeza?',\n    create: 'Criar',\n    created: 'Criado',\n    createdAt: 'Criado Em',\n    createNew: 'Criar Novo',\n    createNewLabel: 'Criar novo(a) {{label}}',\n    creating: 'Criando',\n    creatingNewLabel: 'Criando novo(a) {{label}}',\n    currentlyEditing:\n      'está editando este documento no momento. Se você assumir, eles serão impedidos de continuar editando e poderão perder alterações não salvas.',\n    custom: 'Personalizado',\n    dark: 'Escuro',\n    dashboard: 'Painel de Controle',\n    delete: 'Excluir',\n    deleted: 'Excluído',\n    deletedAt: 'Excluído Em',\n    deletedCountSuccessfully: 'Excluído {{count}} {{label}} com sucesso.',\n    deletedSuccessfully: 'Apagado com sucesso.',\n    deletePermanently: 'Pular lixeira e excluir permanentemente',\n    deleting: 'Excluindo...',\n    depth: 'Profundidade',\n    descending: 'Decrescente',\n    deselectAllRows: 'Desmarcar todas as linhas',\n    document: 'Documento',\n    documentIsTrashed: 'Este {{label}} está na lixeira e é somente para leitura.',\n    documentLocked: 'Documento bloqueado',\n    documents: 'Documentos',\n    duplicate: 'Duplicar',\n    duplicateWithoutSaving: 'Duplicar sem salvar alterações',\n    edit: 'Editar',\n    editAll: 'Editar todos',\n    editedSince: 'Editado desde',\n    editing: 'Editando',\n    editingLabel_many: 'Editando {{count}} {{label}}',\n    editingLabel_one: 'Editando {{count}} {{label}}',\n    editingLabel_other: 'Editando {{count}} {{label}}',\n    editingTakenOver: 'Edição assumida',\n    editLabel: 'Editar {{label}}',\n    email: 'Email',\n    emailAddress: 'Endereço de Email',\n    emptyTrash: 'Esvaziar lixo',\n    emptyTrashLabel: 'Esvazie o lixo {{label}}',\n    enterAValue: 'Insira um valor',\n    error: 'Erro',\n    errors: 'Erros',\n    exitLivePreview: 'Sair da Visualização ao Vivo',\n    export: 'Exportação',\n    fallbackToDefaultLocale: 'Recuo para o local padrão',\n    false: 'Falso',\n    filter: 'Filtro',\n    filters: 'Filtros',\n    filterWhere: 'Filtrar {{label}} em que',\n    globals: 'Globais',\n    goBack: 'Voltar',\n    groupByLabel: 'Agrupar por {{label}}',\n    import: 'Importar',\n    isEditing: 'está editando',\n    item: 'item',\n    items: 'itens',\n    language: 'Idioma',\n    lastModified: 'Última modificação',\n    leaveAnyway: 'Sair mesmo assim',\n    leaveWithoutSaving: 'Sair sem salvar',\n    light: 'Claro',\n    livePreview: 'Pré-visualização',\n    loading: 'Carregando',\n    locale: 'Local',\n    locales: 'Localizações',\n    menu: 'Cardápio',\n    moreOptions: 'Mais opções',\n    move: 'Mova',\n    moveConfirm:\n      'Você está prestes a mover {{count}} {{label}} para <1>{{destination}}</1>. Tem certeza?',\n    moveCount: 'Mova {{count}} {{label}}',\n    moveDown: 'Mover para Baixo',\n    moveUp: 'Mover para Cima',\n    moving: 'Mudando',\n    movingCount: 'Movendo {{count}} {{label}}',\n    newPassword: 'Nova Senha',\n    next: 'Próximo',\n    no: 'Não',\n    noDateSelected: 'Nenhuma data selecionada',\n    noFiltersSet: 'Nenhum filtro definido',\n    noLabel: '<Nenhum(a) {{label}}>',\n    none: 'Nenhum',\n    noOptions: 'Sem opções',\n    noResults:\n      'Nenhum {{label}} encontrado. Ou nenhum(a) {{label}} existe ainda, ou nenhum(a) corresponde aos filtros que você especificou acima.',\n    notFound: 'Não Encontrado',\n    nothingFound: 'Nada encontrado',\n    noTrashResults: 'Não há {{label}} no lixo.',\n    noUpcomingEventsScheduled: 'Não há eventos futuros agendados.',\n    noValue: 'Nenhum valor',\n    of: 'de',\n    only: 'Apenas',\n    open: 'Abrir',\n    or: 'Ou',\n    order: 'Ordem',\n    overwriteExistingData: 'Sobrescrever dados de campo existentes',\n    pageNotFound: 'Página não encontrada',\n    password: 'Senha',\n    pasteField: 'Colar campo',\n    pasteRow: 'Colar linha',\n    payloadSettings: 'Configurações do Payload',\n    permanentlyDelete: 'Excluir Permanentemente',\n    permanentlyDeletedCountSuccessfully: 'Apagou permanentemente {{count}} {{label}} com sucesso.',\n    perPage: 'Itens por Página: {{limit}}',\n    previous: 'Anterior',\n    reindex: 'Reindexar',\n    reindexingAll: 'Reindexando todas as {{collections}}.',\n    remove: 'Remover',\n    rename: 'Renomear',\n    reset: 'Redefinir',\n    resetPreferences: 'Redefinir preferências',\n    resetPreferencesDescription:\n      'Isso redefinirá todas as suas preferências para as configurações padrão.',\n    resettingPreferences: 'Redefinindo preferências.',\n    restore: 'Restaurar',\n    restoreAsPublished: 'Restaurar como versão publicada',\n    restoredCountSuccessfully: 'Restaurado {{count}} {{label}} com sucesso.',\n    restoring:\n      'Respeite o significado do texto original dentro do contexto do Payload. Aqui está uma lista de termos comuns do Payload que possuem significados muito específicos:\\n    - Collection: Uma coleção é um grupo de documentos que compartilham uma estrutura e propósito comuns. As coleções são usadas para organizar e gerenciar conteúdo no Payload.\\n    - Field: Um campo é uma peça específica de dados dentro de um documento em uma coleção. Os campos definem a estrutura e o tipo de dados que podem ser armazenados em um documento.\\n    - Document: Um documento é um registro individual dentro de uma coleção. Ele contém dados estruturados de acordo',\n    row: 'Linha',\n    rows: 'Linhas',\n    save: 'Salvar',\n    saving: 'Salvando...',\n    schedulePublishFor: 'Agendar publicação para {{title}}',\n    searchBy: 'Buscar por {{label}}',\n    select: 'Selecionar',\n    selectAll: 'Selecione tudo {{count}} {{label}}',\n    selectAllRows: 'Selecione todas as linhas',\n    selectedCount: '{{count}} {{label}} selecionado',\n    selectLabel: 'Selecione {{label}}',\n    selectValue: 'Selecione um valor',\n    showAllLabel: 'Mostre todos {{label}}',\n    sorryNotFound: 'Desculpe—não há nada que corresponda à sua requisição.',\n    sort: 'Ordenar',\n    sortByLabelDirection: 'Ordenar por {{label}} {{direction}}',\n    stayOnThisPage: 'Permanecer nessa página',\n    submissionSuccessful: 'Envio bem-sucedido.',\n    submit: 'Enviar',\n    submitting: 'Enviando...',\n    success: 'Sucesso',\n    successfullyCreated: '{{label}} criado com sucesso.',\n    successfullyDuplicated: '{{label}} duplicado com sucesso.',\n    successfullyReindexed:\n      'Reindexação concluída com sucesso de {{count}} de {{total}} documentos das coleções {{collections}}.',\n    takeOver: 'Assumir',\n    thisLanguage: 'Português',\n    time: 'Tempo',\n    timezone: 'Fuso horário',\n    titleDeleted: '{{label}} {{title}} excluído com sucesso.',\n    titleRestored: '{{label}} \"{{title}}\" restaurado com sucesso.',\n    titleTrashed: '{{label}} \"{{title}}\" movido para a lixeira.',\n    trash: 'Lixo',\n    trashedCountSuccessfully: '{{count}} {{label}} movido para o lixo.',\n    true: 'Verdadeiro',\n    unauthorized: 'Não autorizado',\n    unsavedChanges: 'Você tem alterações não salvas. Salve ou descarte antes de continuar.',\n    unsavedChangesDuplicate: 'Você tem mudanças não salvas. Você gostaria de continuar a duplicar?',\n    untitled: 'Sem título',\n    upcomingEvents: 'Próximos Eventos',\n    updatedAt: 'Atualizado Em',\n    updatedCountSuccessfully: 'Atualizado {{count}} {{label}} com sucesso.',\n    updatedLabelSuccessfully: '{{label}} atualizado com sucesso.',\n    updatedSuccessfully: 'Atualizado com sucesso.',\n    updateForEveryone: 'Atualização para todos',\n    updating: 'Atualizando',\n    uploading: 'Fazendo upload',\n    uploadingBulk: 'Carregando {{current}} de {{total}}',\n    user: 'usuário',\n    username: 'Nome de usuário',\n    users: 'usuários',\n    value: 'Valor',\n    viewing: 'Visualização',\n    viewReadOnly: 'Visualizar somente leitura',\n    welcome: 'Boas vindas',\n    yes: 'Sim',\n  },\n  localization: {\n    cannotCopySameLocale: 'Não é possível copiar para o mesmo local',\n    copyFrom: 'Copiar de',\n    copyFromTo: 'Copiando de {{from}} para {{to}}',\n    copyTo: 'Copiar para',\n    copyToLocale: 'Copiar para localidade',\n    localeToPublish: 'Local para publicar',\n    selectLocaleToCopy: 'Selecione o local para copiar',\n  },\n  operators: {\n    contains: 'contém',\n    equals: 'igual',\n    exists: 'existe',\n    intersects: 'intersecciona',\n    isGreaterThan: 'é maior que',\n    isGreaterThanOrEqualTo: 'é maior ou igual a',\n    isIn: 'está em',\n    isLessThan: 'é menor que',\n    isLessThanOrEqualTo: 'é menor ou igual a',\n    isLike: 'é como',\n    isNotEqualTo: 'não é igual a',\n    isNotIn: 'não está em',\n    isNotLike: 'não é como',\n    near: 'perto',\n    within: 'dentro',\n  },\n  upload: {\n    addFile: 'Adicionar arquivo',\n    addFiles: 'Adicionar Arquivos',\n    bulkUpload: 'Upload em Massa',\n    crop: 'Cultura',\n    cropToolDescription:\n      'Arraste as bordas da área selecionada, desenhe uma nova área ou ajuste os valores abaixo.',\n    download: 'Baixar',\n    dragAndDrop: 'Arraste e solte um arquivo',\n    dragAndDropHere: 'ou arraste um arquivo aqui',\n    editImage: 'Editar imagem',\n    fileName: 'Nome do Arquivo',\n    fileSize: 'Tamanho do Arquivo',\n    filesToUpload: 'Arquivos para Carregar',\n    fileToUpload: 'Arquivo para upload',\n    focalPoint: 'Ponto Focal',\n    focalPointDescription:\n      'Arraste o ponto focal diretamente na pré-visualização ou ajuste os valores abaixo.',\n    height: 'Altura',\n    lessInfo: 'Ver menos',\n    moreInfo: 'Ver mais',\n    noFile: 'Sem arquivo',\n    pasteURL: 'Colar URL',\n    previewSizes: 'Tamanhos de Pré-visualização',\n    selectCollectionToBrowse: 'Selecione uma Coleção para Navegar',\n    selectFile: 'Selecione um arquivo',\n    setCropArea: 'Definir área de corte',\n    setFocalPoint: 'Definir ponto focal',\n    sizes: 'Tamanhos',\n    sizesFor: 'Tamanhos para {{label}}',\n    width: 'Largura',\n  },\n  validation: {\n    emailAddress: 'Por favor, insira um endereço de email válido.',\n    enterNumber: 'Por favor, insira um número válido.',\n    fieldHasNo: 'Esse campo não contém {{label}}',\n    greaterThanMax: '{{value}} é maior que o máximo permitido de {{label}} que é {{max}}.',\n    invalidInput: 'Esse campo tem um conteúdo inválido.',\n    invalidSelection: 'Esse campo tem uma seleção inválida.',\n    invalidSelections: \"'Esse campo tem as seguintes seleções inválidas:'\",\n    lessThanMin: '{{value}} é menor que o mínimo permitido de {{label}} que é {{min}}.',\n    limitReached: 'Limite atingido, apenas {{max}} itens podem ser adicionados.',\n    longerThanMin: 'Esse valor deve ser maior do que o mínimo de {{minLength}} characters.',\n    notValidDate: '\"{{value}}\" não é uma data válida.',\n    required: 'Esse campo é obrigatório.',\n    requiresAtLeast: 'Esse campo requer no máximo {{count}} {{label}}.',\n    requiresNoMoreThan: 'Esse campo requer pelo menos {{count}} {{label}}.',\n    requiresTwoNumbers: 'Esse campo requer dois números.',\n    shorterThanMax: 'Esse valor deve ser menor do que o máximo de {{maxLength}} caracteres.',\n    timezoneRequired: 'É necessário um fuso horário.',\n    trueOrFalse: 'Esse campo pode ser apenas verdadeiro (true) ou falso (false)',\n    username:\n      'Por favor, insira um nome de usuário válido. Pode conter letras, números, hifens, pontos e sublinhados.',\n    validUploadID: \"'Esse campo não é um ID de upload válido.'\",\n  },\n  version: {\n    type: 'Tipo',\n    aboutToPublishSelection:\n      'Você está prestes a publicar todos os {{label}} da seleção. Tem certeza?',\n    aboutToRestore:\n      'Você está prestes a restaurar o documento {{label}} para o estado em que ele se encontrava em {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Você está prestes a restaurar o Global {{label}} para o estado em que ele se encontrava em {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Você está prestes a reverter as alterações desse documento para seu estado de publicação. Tem certeza?',\n    aboutToUnpublish: 'Você está prestes a despublicar esse documento. Tem certeza?',\n    aboutToUnpublishSelection:\n      'Você está prestes a cancelar a publicação de todos os {{label}} na seleção. Tem certeza?',\n    autosave: 'Salvamento automático',\n    autosavedSuccessfully: 'Salvamento automático com sucesso.',\n    autosavedVersion: 'Versão de salvamento automático',\n    changed: 'Alterado',\n    changedFieldsCount_one: '{{count}} campo alterado',\n    changedFieldsCount_other: '{{count}} campos alterados',\n    compareVersion: 'Comparar versão com:',\n    compareVersions: 'Comparar Versões',\n    comparingAgainst: 'Comparando com',\n    confirmPublish: 'Confirmar publicação',\n    confirmRevertToSaved: 'Confirmar a reversão para o salvo',\n    confirmUnpublish: 'Confirmar despublicação',\n    confirmVersionRestoration: 'Confirmar Restauração de versão',\n    currentDocumentStatus: 'Documento {{docStatus}} atual',\n    currentDraft: 'Rascunho Atual',\n    currentlyPublished: 'Atualmente Publicado',\n    currentlyViewing: 'Atualmente visualizando',\n    currentPublishedVersion: 'Versão Publicada Atual',\n    draft: 'Rascunho',\n    draftSavedSuccessfully: 'Rascunho salvo com sucesso.',\n    lastSavedAgo: 'Última gravação há {{distance}}',\n    modifiedOnly: 'Modificado apenas',\n    moreVersions: 'Mais versões...',\n    noFurtherVersionsFound: 'Nenhuma outra versão encontrada',\n    noRowsFound: 'Nenhum(a) {{label}} encontrado(a)',\n    noRowsSelected: 'Nenhum {{rótulo}} selecionado',\n    preview: 'Pré-visualização',\n    previouslyDraft: 'Anteriormente um Rascunho',\n    previouslyPublished: 'Publicado Anteriormente',\n    previousVersion: 'Versão Anterior',\n    problemRestoringVersion: 'Ocorreu um problema ao restaurar essa versão',\n    publish: 'Publicar',\n    publishAllLocales: 'Publicar todas as localidades',\n    publishChanges: 'Publicar alterações',\n    published: 'Publicado',\n    publishIn: 'Publicar em {{locale}}',\n    publishing: 'Publicação',\n    restoreAsDraft: 'Restaurar como rascunho',\n    restoredSuccessfully: 'Restaurado com sucesso.',\n    restoreThisVersion: 'Restaurar essa versão',\n    restoring: 'Restaurando...',\n    reverting: 'Revertendo...',\n    revertToPublished: 'Reverter para publicado',\n    saveDraft: 'Salvar rascunho',\n    scheduledSuccessfully: 'Agendado com sucesso.',\n    schedulePublish: 'Agendar Publicação',\n    selectLocales: 'Selecione as localizações para exibir',\n    selectVersionToCompare: 'Selecione uma versão para comparar',\n    showingVersionsFor: 'Mostrando versões para:',\n    showLocales: 'Exibir localizações:',\n    specificVersion: 'Versão Específica',\n    status: 'Status',\n    unpublish: 'Despublicar',\n    unpublishing: 'Despublicando...',\n    version: 'Versão',\n    versionAgo: 'há {{distance}}',\n    versionCount_many: '{{count}} versões encontradas',\n    versionCount_none: 'Nenhuma versão encontrada',\n    versionCount_one: '{{count}} versão encontrada',\n    versionCount_other: '{{count}} versões encontradas',\n    versionCreatedOn: '{{version}} criada em:',\n    versionID: 'ID da versão',\n    versions: 'Versões',\n    viewingVersion: 'Visualizando versão para o/a {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: '`Visualizando versão para o global {{entityLabel}}',\n    viewingVersions: 'Visualizando versões para o/a {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: '`Visualizando versões para o global {{entityLabel}}',\n  },\n}\n\nexport const pt: Language = {\n  dateFNSKey: 'pt',\n  translations: ptTranslations,\n}\n"], "names": ["ptTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "pt", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BAA2B;QAC3BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}