{"version": 3, "sources": ["../../src/languages/ca.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const caTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Compte',\n    accountOfCurrentUser: 'Usuari actual',\n    accountVerified: 'Compte verificat',\n    alreadyActivated: 'Ja activat',\n    alreadyLoggedIn: '<PERSON><PERSON> has iniciat sessió',\n    apiKey: 'Clau API',\n    authenticated: 'Autenticat',\n    backToLogin: \"Tornar a l'inici de sessió\",\n    beginCreateFirstUser: 'Comença a crear el primer usuari',\n    changePassword: 'Canviar contrasenya',\n    checkYourEmailForPasswordReset:\n      \"Si l'adreça de correu electrònic està associada amb un compte, rebràs instruccions per restablir la teva contrasenya aviat. Si no trobes el correu electrònic a la safata d'entrada, revisa la carpeta de correu brossa o no desitjat.\",\n    confirmGeneration: 'Confirmar generació',\n    confirmPassword: 'Confirma la contrasenya',\n    createFirstUser: 'Crea el primer usuari',\n    emailNotValid: 'El correu electrònic proporcionat no és vàlid',\n    emailOrUsername: \"Correu electrònic o nom d'usuari\",\n    emailSent: 'Correu electrònic enviat',\n    emailVerified: 'Correu electrònic verificat amb èxit.',\n    enableAPIKey: 'Habilitar clau API',\n    failedToUnlock: \"No s'ha pogut desbloquejar\",\n    forceUnlock: 'Forçar desbloqueig',\n    forgotPassword: 'Has oblidat la contrasenya',\n    forgotPasswordEmailInstructions:\n      'Si us plau, introdueix el teu correu electrònic a continuació. Rebràs un correu electrònic amb instruccions sobre com restablir la teva contrasenya.',\n    forgotPasswordQuestion: 'Has oblidat la contrasenya?',\n    forgotPasswordUsernameInstructions:\n      \"Si us plau, introdueix el teu nom d'usuari a continuació. Les instruccions per restablir la contrasenya s'enviaran al correu electrònic associat amb el teu nom d'usuari.\",\n    generate: 'Generar',\n    generateNewAPIKey: 'Generar una nova clau API',\n    generatingNewAPIKeyWillInvalidate:\n      'Generar una nova clau API <1>invalidarà</1> la clau anterior. Estàs segur que vols continuar?',\n    lockUntil: 'Bloqueja fins',\n    logBackIn: 'Tornar a iniciar sessió',\n    loggedIn: 'Per iniciar sessió amb un altre usuari, primer <0>tanca la sessió</0>.',\n    loggedInChangePassword:\n      'Per canviar la teva contrasenya, ves al teu <0>compte</0> i edita la contrasenya allà.',\n    loggedOutInactivity: 'Has estat tancat la sessió per inactivitat.',\n    loggedOutSuccessfully: 'Has tancat la sessió amb èxit.',\n    loggingOut: 'Tancant la sessió...',\n    login: 'Inicia sessió',\n    loginAttempts: \"Intents d'inici de sessió\",\n    loginUser: 'Inicia sessió amb un usuari',\n    loginWithAnotherUser: 'Per iniciar sessió amb un altre usuari, primer <0>tanca la sessió</0>.',\n    logOut: 'Tanca la sessió',\n    logout: 'Tancar sessió',\n    logoutSuccessful: 'Sessió tancada amb èxit.',\n    logoutUser: \"Tanca la sessió de l'usuari\",\n    newAccountCreated:\n      'S\\'ha creat un nou compte per a tu per accedir a <a href=\"{{serverURL}}\">{{serverURL}}</a>. Si us plau, fes clic en el següent enllaç o enganxa l\\'URL a continuació al teu navegador per verificar el teu correu electrònic: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Després de verificar el teu correu electrònic, podràs iniciar sessió amb èxit.',\n    newAPIKeyGenerated: \"S'ha generat una nova clau API.\",\n    newPassword: 'Nova contrasenya',\n    passed: 'Autenticació superada',\n    passwordResetSuccessfully: 'Contrasenya restablerta amb èxit.',\n    resetPassword: 'Restablir contrasenya',\n    resetPasswordExpiration: 'Caducitat del restabliment de contrasenya',\n    resetPasswordToken: 'Token de restabliment de contrasenya',\n    resetYourPassword: 'Restableix la teva contrasenya',\n    stayLoggedIn: 'Roman connectat',\n    successfullyRegisteredFirstUser: 'Primer usuari registrat amb èxit.',\n    successfullyUnlocked: 'Desbloquejat amb èxit',\n    tokenRefreshSuccessful: 'Actualització del token amb èxit.',\n    unableToVerify: \"No s'ha pogut verificar\",\n    username: \"Nom d'usuari\",\n    usernameNotValid: \"El nom d'usuari proporcionat no és vàlid\",\n    verified: 'Verificat',\n    verifiedSuccessfully: 'Verificat amb èxit',\n    verify: 'Verificar',\n    verifyUser: 'Verificar usuari',\n    verifyYourEmail: 'Verifica el teu correu electrònic',\n    youAreInactive:\n      \"Fa una estona que no estàs actiu i aviat se't tancarà la sessió automàticament per la teva pròpia seguretat. Vols romandre connectat?\",\n    youAreReceivingResetPassword:\n      \"Estàs rebent aquest correu perquè tu (o algú altre) has sol·licitat el restabliment de la contrasenya del teu compte. Si us plau, fes clic en el següent enllaç o enganxa'l al teu navegador per completar el procés:\",\n    youDidNotRequestPassword:\n      'Si no has sol·licitat això, ignora aquest correu i la teva contrasenya romandrà inalterada.',\n  },\n  error: {\n    accountAlreadyActivated: 'Aquest compte ja ha estat activat.',\n    autosaving: \"Hi ha hagut un problema mentre s'estava desant automàticament aquest document.\",\n    correctInvalidFields: 'Si us plau, corregeix els camps no vàlids.',\n    deletingFile: \"Hi ha hagut un error en eliminar l'arxiu.\",\n    deletingTitle:\n      \"Hi ha hagut un error mentre s'eliminava {{title}}. Si us plau, comprova la teva connexió i torna-ho a intentar.\",\n    documentNotFound:\n      \"El document amb ID {{id}} no s'ha pogut trobar. Pot haver estat esborrat o mai haver existit, o potser no tens accés a aquest.\",\n    emailOrPasswordIncorrect:\n      'El correu electrònic o la contrasenya proporcionats no són correctes.',\n    followingFieldsInvalid_one: 'El següent camp no és vàlid:',\n    followingFieldsInvalid_other: 'Els següents camps no són vàlids:',\n    incorrectCollection: 'Col·lecció incorrecta',\n    insufficientClipboardPermissions:\n      'Accés al porta-retalls denegat. Comproveu els permisos del porta-retalls.',\n    invalidClipboardData: 'Dades del porta-retalls no vàlides.',\n    invalidFileType: \"Tipus d'arxiu no vàlid\",\n    invalidFileTypeValue: \"Tipus d'arxiu no vàlid: {{value}}\",\n    invalidRequestArgs: 'Arguments no vàlids en la sol·licitud: {{args}}',\n    loadingDocument: \"Hi ha hagut un problema carregant el document amb l'ID {{id}}.\",\n    localesNotSaved_one: \"No s'ha pogut desar el següent idioma:\",\n    localesNotSaved_other: \"No s'han pogut desar els següents idiomes:\",\n    logoutFailed: 'La desconnexió ha fallat.',\n    missingEmail: 'Falta el correu electrònic.',\n    missingIDOfDocument: \"Falta l'ID del document a actualitzar.\",\n    missingIDOfVersion: \"Falta l'ID de la versió.\",\n    missingRequiredData: 'Falten dades necessàries.',\n    noFilesUploaded: \"No s'ha carregat cap arxiu.\",\n    noMatchedField: 'No s\\'ha trobat cap camp coincident per a \"{{label}}\"',\n    notAllowedToAccessPage: 'No tens permís per accedir a aquesta pàgina.',\n    notAllowedToPerformAction: 'No tens permís per dur a terme aquesta acció.',\n    notFound: \"El recurs sol·licitat no s'ha trobat.\",\n    noUser: 'Cap usuari',\n    previewing: 'Hi ha hagut un problema en previsualitzar aquest document.',\n    problemUploadingFile: \"Hi ha hagut un problema mentre es carregava l'arxiu.\",\n    restoringTitle:\n      'Hi ha hagut un error en restaurar {{title}}. Si us plau, comproveu la vostra connexió i torneu-ho a provar.',\n    tokenInvalidOrExpired: 'El token és invàlid o ha caducat.',\n    tokenNotProvided: \"No s'ha proporcionat cap token.\",\n    unableToCopy: 'No es pot copiar.',\n    unableToDeleteCount: \"No s'han pogut eliminar {{count}} de {{total}} {{label}}.\",\n    unableToReindexCollection:\n      'Error al reindexar la col·lecció {{collection}}. Operació cancel·lada.',\n    unableToUpdateCount: \"No s'han pogut actualitzar {{count}} de {{total}} {{label}}.\",\n    unauthorized: \"No autoritzat, has d'iniciar sessió per fer aquesta sol·licitud.\",\n    unauthorizedAdmin: \"No autoritzat, aquest usuari no té accés al panell d'administració.\",\n    unknown: \"S'ha produït un error desconegut.\",\n    unPublishingDocument: 'Hi ha hagut un problema mentre es despublicava aquest document.',\n    unspecific: \"S'ha produït un error.\",\n    unverifiedEmail: 'Si us plau, verifica el teu correu electrònic abans d’iniciar sessió.',\n    userEmailAlreadyRegistered: 'Ja hi ha un usuari registrat amb aquest correu electrònic.',\n    userLocked: \"Aquest usuari està bloquejat per massa intents fallits d'inici de sessió.\",\n    usernameAlreadyRegistered: \"Ja hi ha un usuari registrat amb aquest nom d'usuari.\",\n    usernameOrPasswordIncorrect: \"El nom d'usuari o la contrasenya proporcionats no són correctes.\",\n    valueMustBeUnique: 'El valor ha de ser únic.',\n    verificationTokenInvalid: 'El token de verificació és invàlid.',\n  },\n  fields: {\n    addLabel: 'Afegeix {{label}}',\n    addLink: 'Afegeix enllaç',\n    addNew: 'Afegeix nou',\n    addNewLabel: 'Afegeix nou {{label}}',\n    addRelationship: 'Afegeix relació',\n    addUpload: 'Afegeix pujada',\n    block: 'bloc',\n    blocks: 'blocs',\n    blockType: 'Tipus de bloc',\n    chooseBetweenCustomTextOrDocument:\n      'Tria entre introduir una URL de text personalitzada o enllaçar a un altre document.',\n    chooseDocumentToLink: 'Tria un document per enllaçar',\n    chooseFromExisting: 'Tria d’entre els existents',\n    chooseLabel: 'Tria {{label}}',\n    collapseAll: 'Col·lapsa-ho tot',\n    customURL: 'URL personalitzada',\n    editLabelData: 'Edita les dades de {{label}}',\n    editLink: 'Edita l’enllaç',\n    editRelationship: 'Edita la relació',\n    enterURL: 'Introdueix una URL',\n    internalLink: 'Enllaç intern',\n    itemsAndMore: '{{items}} i {{count}} més',\n    labelRelationship: 'Relació de {{label}}',\n    latitude: 'Latitud',\n    linkedTo: 'Enllaçat a <0>{{label}}</0>',\n    linkType: 'Tipus d’enllaç',\n    longitude: 'Longitud',\n    newLabel: 'Nou {{label}}',\n    openInNewTab: 'Obre en una nova pestanya',\n    passwordsDoNotMatch: 'Les contrasenyes no coincideixen.',\n    relatedDocument: 'Document relacionat',\n    relationTo: 'Relació amb',\n    removeRelationship: 'Elimina la relació',\n    removeUpload: 'Elimina la pujada',\n    saveChanges: 'Desa els canvis',\n    searchForBlock: 'Cerca un bloc',\n    selectExistingLabel: 'Selecciona un {{label}} existent',\n    selectFieldsToEdit: 'Selecciona camps per editar',\n    showAll: 'Mostra-ho tot',\n    swapRelationship: 'Intercanvia la relació',\n    swapUpload: 'Intercanvia la pujada',\n    textToDisplay: 'Text a mostrar',\n    toggleBlock: 'Alterna el bloc',\n    uploadNewLabel: 'Puja un nou {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Navega per carpeta',\n    byFolder: 'Per Carpeta',\n    deleteFolder: 'Esborra la carpeta',\n    folderName: 'Nom de la Carpeta',\n    folders: 'Carpetes',\n    folderTypeDescription:\n      'Seleccioneu quin tipus de documents de la col·lecció haurien de ser permesos en aquesta carpeta.',\n    itemHasBeenMoved: \"{{title}} s'ha traslladat a {{folderName}}\",\n    itemHasBeenMovedToRoot: \"{{title}} s'ha mogut a la carpeta arrel\",\n    itemsMovedToFolder: \"{{title}} s'ha traslladat a {{folderName}}\",\n    itemsMovedToRoot: \"{{title}} s'ha traslladat a la carpeta arrel\",\n    moveFolder: 'Mou la carpeta',\n    moveItemsToFolderConfirmation:\n      \"Estàs a punt de moure <1>{{count}} {{label}}</1> a <2>{{toFolder}}</2>. N'estàs segur?\",\n    moveItemsToRootConfirmation:\n      'Estàs a punt de moure <1>{{count}} {{label}}</1> a la carpeta arrel. Estàs segur?',\n    moveItemToFolderConfirmation:\n      \"Estàs a punt de moure <1>{{title}}</1> a <2>{{toFolder}}</2>. N'estàs segur?\",\n    moveItemToRootConfirmation:\n      \"Estàs a punt de moure <1>{{title}}</1> a la carpeta arrel. N'estàs segur?\",\n    movingFromFolder: 'Movent {{title}} de {{fromFolder}}',\n    newFolder: 'Nova carpeta',\n    noFolder: 'No hi ha carpeta',\n    renameFolder: 'Anomena carpeta',\n    searchByNameInFolder: 'Cerca per Nom en {{folderName}}',\n    selectFolderForItem: 'Selecciona la carpeta per a {{title}}',\n  },\n  general: {\n    name: 'Nom',\n    aboutToDelete: 'Estas apunt de eliminar {{label}} <1>{{title}}</1>. Estas segur?',\n    aboutToDeleteCount_many: 'Estas apunt de eliminar {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Estas apunt de eliminar {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Estas apunt de eliminar {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      \"Estàs a punt d'esborrar permanentment l'{{etiqueta}} <1>{{títol}}</1>. N'estàs segur?\",\n    aboutToPermanentlyDeleteTrash:\n      \"Estàs a punt de suprimir permanentment <0>{{count}}</0> <1>{{label}}</1> de la paperera. N'estàs segur?\",\n    aboutToRestore: \"Estàs a punt de restaurar l'{{label}} <1>{{title}}</1>. N'estàs segur?\",\n    aboutToRestoreAsDraft:\n      \"Estàs a punt de restaurar l'etiqueta {{label}} <1>{{title}}</1> com a esborrany. N'estàs segur?\",\n    aboutToRestoreAsDraftCount: 'Està a punt de restaurar {{count}} {{label}} com a esborrany',\n    aboutToRestoreCount: 'Està a punt de restaurar {{count}} {{label}}',\n    aboutToTrash:\n      \"Estàs a punt de moure l'{{label}} <1>{{title}}</1> a la paperera. N'estàs segur?\",\n    aboutToTrashCount: 'Estàs a punt de moure {{count}} {{label}} a la paperera',\n    addBelow: 'Afegeix a sota',\n    addFilter: 'Afegeix filtre',\n    adminTheme: \"Tema d'administració\",\n    all: 'Tots',\n    allCollections: 'Totes les col·leccions',\n    allLocales: 'Totes les localitats',\n    and: 'i',\n    anotherUser: 'Altre usuari',\n    anotherUserTakenOver: \"Un altre usuari ha pres la edició d'aquest document.\",\n    applyChanges: 'Apica els canvis',\n    ascending: 'Ascendent',\n    automatic: 'Automàtic',\n    backToDashboard: 'Torna al tauler',\n    cancel: 'Cancel·la',\n    changesNotSaved: 'El teu document té canvis no desats. Si continues, els canvis es perdran.',\n    clear: 'Clar',\n    clearAll: 'Esborra-ho tot',\n    close: 'Tanca',\n    collapse: 'Replegar',\n    collections: 'Col·leccions',\n    columns: 'Columnes',\n    columnToSort: 'Columna per ordenar',\n    confirm: 'Confirma',\n    confirmCopy: 'Confirmar còpia',\n    confirmDeletion: \"Confirma l'eliminació\",\n    confirmDuplication: 'Confirma duplicacat',\n    confirmMove: 'Confirmar moviment',\n    confirmReindex: 'Reindexa {{collections}}?',\n    confirmReindexAll: 'Reindexa totes les col·leccions?',\n    confirmReindexDescription:\n      'Aixo eliminarà els índexs existents i reindexarà els documents de les col·leccions {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Aixo eliminarà els índexs existents i reindexarà els documents de totes les col·leccions.',\n    confirmRestoration: 'Confirmeu la restauració',\n    copied: 'Copiat',\n    copy: 'Copiar',\n    copyField: 'Copiar camp',\n    copying: 'Copiant',\n    copyRow: 'Copiar fila',\n    copyWarning:\n      'Estas a punt de sobreescriure {{to}} amb {{from}} per {{label}} {{title}}. Estas segur?',\n    create: 'Crear',\n    created: 'Creat',\n    createdAt: 'Creat el',\n    createNew: 'Crear nou',\n    createNewLabel: 'Crea nou {{label}}',\n    creating: 'Creant',\n    creatingNewLabel: 'Creant nou {{label}}',\n    currentlyEditing:\n      'esta editant actualment aquest document. Si prens el control, es bloquejarà per continuar editant i potser perdrà els canvis no desats.',\n    custom: 'Personalitzat',\n    dark: 'Fosc',\n    dashboard: 'Tauler',\n    delete: 'Eliminar',\n    deleted: 'Eliminat',\n    deletedAt: 'Eliminat en',\n    deletedCountSuccessfully: 'Eliminat {{count}} {{label}} correctament.',\n    deletedSuccessfully: 'Eliminat correntament.',\n    deletePermanently: 'Omet la paperera i elimina permanentment',\n    deleting: 'Eliminant...',\n    depth: 'Profunditat',\n    descending: 'Descendent',\n    deselectAllRows: 'Deselecciona totes les files',\n    document: 'Document',\n    documentIsTrashed: \"Aquesta {{label}} s'ha eliminat i és de només lectura.\",\n    documentLocked: 'Document bloquejat',\n    documents: 'Documents',\n    duplicate: 'Duplicar',\n    duplicateWithoutSaving: 'Duplica sense desar',\n    edit: 'Edita',\n    editAll: 'Edita-ho tot',\n    editedSince: 'Editat des de',\n    editing: 'Editant',\n    editingLabel_many: 'Editent {{count}} {{label}}',\n    editingLabel_one: 'Editent {{count}} {{label}}',\n    editingLabel_other: 'Editant {{count}} {{label}}',\n    editingTakenOver: 'Edició presa',\n    editLabel: 'Edita {{label}}',\n    email: 'correu electrònic',\n    emailAddress: 'Addressa de correu electrònic',\n    emptyTrash: 'Buida la paperera',\n    emptyTrashLabel: 'Buideu la paperera {{label}}',\n    enterAValue: 'Introdueix un valor',\n    error: 'Error',\n    errors: 'Errors',\n    exitLivePreview: 'Sortir de la Vista Previa en Directe',\n    export: 'Exportació',\n    fallbackToDefaultLocale: 'Torna al idioma per defecte',\n    false: 'Fals',\n    filter: 'Filtra',\n    filters: 'Filtres',\n    filterWhere: 'Filtra {{label}} on',\n    globals: 'Globals',\n    goBack: 'Torna enrere',\n    groupByLabel: 'Agrupa per {{label}}',\n    import: 'Importar',\n    isEditing: 'esta editant',\n    item: 'element',\n    items: 'articles',\n    language: 'Idioma',\n    lastModified: 'Última modificació',\n    leaveAnyway: 'Deixa-ho de totes maneres',\n    leaveWithoutSaving: 'Deixa sense desar',\n    light: 'Clar',\n    livePreview: 'Previsualització en viu',\n    loading: 'Carregant',\n    locale: 'Idioma',\n    locales: 'Idiomes',\n    menu: 'Menu',\n    moreOptions: 'Més opcions',\n    move: 'Mou-te',\n    moveConfirm:\n      \"Està a punt de moure {{count}} {{label}} a <1>{{destination}}</1>. N'estàs segur?\",\n    moveCount: 'Mou {{count}} {{label}}',\n    moveDown: 'Mou avall',\n    moveUp: 'Move amunt',\n    moving: 'En moviment',\n    movingCount: 'Moure {{count}} {{label}}',\n    newPassword: 'Nova contrasenya',\n    next: 'Seguent',\n    no: 'No',\n    noDateSelected: 'Data not seleccionada',\n    noFiltersSet: 'Sense filtres',\n    noLabel: '<No {{label}}>',\n    none: 'Cap',\n    noOptions: 'Sense opcions',\n    noResults:\n      \"No s'ha trobat cap {{label}}. O no n'hi ha cap encara o cap coincideix amb els filtres que has especificat anteriorment.\",\n    notFound: 'No trobat',\n    nothingFound: 'Res trobat',\n    noTrashResults: 'No hi ha cap {{label}} a la paperera.',\n    noUpcomingEventsScheduled: 'No hi ha esdeveniments programats.',\n    noValue: 'No hi ha cap valor',\n    of: 'de',\n    only: 'Nomes',\n    open: 'Obert',\n    or: 'O',\n    order: 'Ordre',\n    overwriteExistingData: 'Sobreescriu les dades existents',\n    pageNotFound: 'Pàgina no trobada',\n    password: 'Contrasenya',\n    pasteField: 'Enganxar camp',\n    pasteRow: 'Enganxar fila',\n    payloadSettings: 'configuracio Payload',\n    permanentlyDelete: 'Esborrar permanentment',\n    permanentlyDeletedCountSuccessfully:\n      \"S'ha eliminat permanentment {{count}} {{label}} amb èxit.\",\n    perPage: 'Per pagian: {{limit}}',\n    previous: 'Previ',\n    reindex: 'Reindexa',\n    reindexingAll: 'Reindexa tots el {{collections}}.',\n    remove: 'Elimina',\n    rename: 'Canvia el nom',\n    reset: 'Restableix',\n    resetPreferences: 'Restablir les preferències',\n    resetPreferencesDescription:\n      'Això restablirà totes les teves preferències a les configuracions per defecte.',\n    resettingPreferences: 'Restablint les preferències.',\n    restore: 'Restaura',\n    restoreAsPublished: 'Restaura com a versió publicada',\n    restoredCountSuccessfully: \"S'ha restaurat {{count}} {{label}} correctament.\",\n    restoring: 'Restauració...',\n    row: 'Fila',\n    rows: 'Files',\n    save: 'Desa',\n    saving: 'Desant...',\n    schedulePublishFor: 'Programa la publicacio {{title}}',\n    searchBy: 'Cerca per {{label}}',\n    select: 'Selecciona',\n    selectAll: 'Selecciona totes les {{count}} {{label}}',\n    selectAllRows: 'Selecciona totes les files',\n    selectedCount: '{{count}} {{label}} seleccionats',\n    selectLabel: 'Selecciona {{label}}',\n    selectValue: 'Selecciona un valor',\n    showAllLabel: 'Mostra totes {{label}}',\n    sorryNotFound: \"Ho sento, no s'ha trobat la pàgina que busques.\",\n    sort: 'Ordena',\n    sortByLabelDirection: 'Ordena per {{label}} {{direction}}',\n    stayOnThisPage: 'Permaneix en aquesta pàgina',\n    submissionSuccessful: 'Enviament exitós',\n    submit: 'Envia',\n    submitting: 'Enviant...',\n    success: 'Èxit',\n    successfullyCreated: '{{label}} creada correctament.',\n    successfullyDuplicated: '{{label}} duplicada correctament.',\n    successfullyReindexed:\n      'Reindexació correcta de {{count}} de {{total}} documents de {{collections}}',\n    takeOver: 'Prendre el control',\n    thisLanguage: 'Catala',\n    time: 'Temps',\n    timezone: 'Fus horari',\n    titleDeleted: '{{label}} \"{{title}}\" eliminat correctament.',\n    titleRestored: '{{label}} \"{{title}}\" s\\'ha restaurat correctament.',\n    titleTrashed: '{{label}} \"{{title}}\" s\\'ha traslladat a la paperera.',\n    trash: 'Brossa',\n    trashedCountSuccessfully: \"{{count}} {{label}} s'ha mogut a la paperera.\",\n    true: 'Veritat',\n    unauthorized: 'No autoritzat',\n    unsavedChanges: 'Tens canvis no desats. Vols continuar sense desar?',\n    unsavedChangesDuplicate: 'Tens canvis no desats. Vols duplicar sense desar?',\n    untitled: 'Sense titol',\n    upcomingEvents: 'Esdeveniments programats',\n    updatedAt: 'Actualitzat el',\n    updatedCountSuccessfully: 'Actualitzat {{count}} {{label}} correctament.',\n    updatedLabelSuccessfully: 'Actualitzat {{label}} amb èxit.',\n    updatedSuccessfully: 'Actualitzat amb exit.',\n    updateForEveryone: 'Actualització per a tothom',\n    updating: 'Actualitzant',\n    uploading: 'Pujant',\n    uploadingBulk: 'Pujant {{current}} de {{total}}',\n    user: 'Usuari',\n    username: \"Nom d'usuari\",\n    users: 'Usuaris',\n    value: 'Valor',\n    viewing: 'Visualització',\n    viewReadOnly: 'Veure només de lectura',\n    welcome: 'Benvingut',\n    yes: 'Sí',\n  },\n  localization: {\n    cannotCopySameLocale: 'No es pot copiar al mateix idioma',\n    copyFrom: 'Copiar de',\n    copyFromTo: 'Copiant de {{from}} a {{to}}',\n    copyTo: 'Copiar a',\n    copyToLocale: 'Copiar a idioma',\n    localeToPublish: 'Idioma per publicar',\n    selectLocaleToCopy: \"Selecciona l'idioma per copiar\",\n  },\n  operators: {\n    contains: 'conté',\n    equals: 'és igual a',\n    exists: 'existeix',\n    intersects: 'interseca',\n    isGreaterThan: 'és més gran que',\n    isGreaterThanOrEqualTo: 'és més gran o igual a',\n    isIn: 'està en',\n    isLessThan: 'és menor que',\n    isLessThanOrEqualTo: 'és menor o igual a',\n    isLike: 'és semblant a',\n    isNotEqualTo: 'no és igual a',\n    isNotIn: 'no està en',\n    isNotLike: 'no és com',\n    near: 'a prop de',\n    within: 'dins de',\n  },\n  upload: {\n    addFile: 'Afegir fitxer',\n    addFiles: 'Afegir fitxers',\n    bulkUpload: 'Carregar arxius massius',\n    crop: 'Retallar',\n    cropToolDescription:\n      'Arrossega les cantonades de l’àrea seleccionada, dibuixa una nova àrea o ajusta els valors a continuació.',\n    download: 'Descarrega',\n    dragAndDrop: 'Arrossega i deixa anar un fitxer',\n    dragAndDropHere: 'o arrossega i deixa anar un fitxer aquí',\n    editImage: 'Editar imatge',\n    fileName: 'Nom del fitxer',\n    fileSize: 'Mida del fitxer',\n    filesToUpload: 'Fitxers a carregar',\n    fileToUpload: 'Fitxer a carregar',\n    focalPoint: 'Punt focal',\n    focalPointDescription:\n      'Arrossega el punt focal directament sobre la vista prèvia o ajusta els valors a continuació.',\n    height: 'Alçada',\n    lessInfo: 'Menys informació',\n    moreInfo: 'Més informació',\n    noFile: 'No hi ha cap fitxer',\n    pasteURL: \"Enganxa l'URL\",\n    previewSizes: 'Mides de la vista prèvia',\n    selectCollectionToBrowse: 'Selecciona una col·lecció per explorar',\n    selectFile: 'Selecciona un fitxer',\n    setCropArea: \"Estableix l'àrea de retall\",\n    setFocalPoint: 'Estableix el punt focal',\n    sizes: 'Mides',\n    sizesFor: 'Mides per a {{label}}',\n    width: 'Amplada',\n  },\n  validation: {\n    emailAddress: 'Si us plau, introdueix una adreça de correu electrònic vàlida.',\n    enterNumber: 'Si us plau, introdueix un número vàlid.',\n    fieldHasNo: 'Aquest camp no té {{label}}',\n    greaterThanMax: '{{value}} és més gran que el màxim permès {{label}} de {{max}}.',\n    invalidInput: 'Aquest camp té una entrada invàlida.',\n    invalidSelection: 'Aquest camp té una selecció invàlida.',\n    invalidSelections: 'Aquest camp té les següents seleccions invàlides:',\n    lessThanMin: '{{value}} és menor que el mínim permès {{label}} de {{min}}.',\n    limitReached: \"S'ha arribat al límit, només es poden afegir {{max}} elements.\",\n    longerThanMin:\n      'Aquest valor ha de ser més llarg que la longitud mínima de {{minLength}} caràcters.',\n    notValidDate: '\"{{value}}\" no és una data vàlida.',\n    required: 'Aquest camp és obligatori.',\n    requiresAtLeast: 'Aquest camp requereix almenys {{count}} {{label}}.',\n    requiresNoMoreThan: 'Aquest camp requereix com a màxim {{count}} {{label}}.',\n    requiresTwoNumbers: 'Aquest camp requereix dos números.',\n    shorterThanMax:\n      'Aquest valor ha de ser més curt que la longitud màxima de {{maxLength}} caràcters.',\n    timezoneRequired: 'Es requereix una zona horària.',\n    trueOrFalse: 'Aquest camp només pot ser igual a true o false.',\n    username:\n      \"Si us plau, introdueix un nom d'usuari vàlid. Pot contenir lletres, números, guions, punts i guions baixos.\",\n    validUploadID: 'Aquest camp no és un ID de càrrega vàlid.',\n  },\n  version: {\n    type: 'Tipus',\n    aboutToPublishSelection:\n      'Estàs a punt de publicar tots els {{label}} de la selecció. Estàs segur?',\n    aboutToRestore:\n      \"Estàs a punt de restaurar aquest document {{label}} a l'estat en què es trobava el {{versionDate}}.\",\n    aboutToRestoreGlobal:\n      \"Estàs a punt de restaurar el {{label}} global a l'estat en què es trobava el {{versionDate}}.\",\n    aboutToRevertToPublished:\n      \"Estàs a punt de revertir els canvis d'aquest document a l'estat publicat. Estàs segur?\",\n    aboutToUnpublish: 'Estàs a punt de despublicar aquest document. Estàs segur?',\n    aboutToUnpublishSelection:\n      'Estàs a punt de despublicar tots els {{label}} de la selecció. Estàs segur?',\n    autosave: 'Desa automàticament',\n    autosavedSuccessfully: 'Desat automàticament amb èxit.',\n    autosavedVersion: 'Versió desada automàticament',\n    changed: 'Canviat',\n    changedFieldsCount_one: '{{count}} camp canviat',\n    changedFieldsCount_other: '{{count}} camps modificats',\n    compareVersion: 'Comparar versió amb:',\n    compareVersions: 'Compara Versions',\n    comparingAgainst: 'Comparant amb',\n    confirmPublish: 'Confirmar publicació',\n    confirmRevertToSaved: 'Confirmar revertir a desat',\n    confirmUnpublish: 'Confirmar despublicació',\n    confirmVersionRestoration: 'Confirmar restauració de versió',\n    currentDocumentStatus: 'Estat actual del document {{docStatus}}',\n    currentDraft: 'Borrador actual',\n    currentlyPublished: 'Actualment publicat',\n    currentlyViewing: 'Actualment veient',\n    currentPublishedVersion: 'Versió publicada actual',\n    draft: 'Borrador',\n    draftSavedSuccessfully: 'Borrador desat amb èxit.',\n    lastSavedAgo: 'Últim desament fa {{distance}}',\n    modifiedOnly: 'Només modificat',\n    moreVersions: 'Més versions...',\n    noFurtherVersionsFound: \"No s'han trobat més versions\",\n    noRowsFound: \"No s'han trobat {{label}}\",\n    noRowsSelected: \"No s'han seleccionat {{label}}\",\n    preview: 'Vista prèvia',\n    previouslyDraft: 'Anteriorment un Esborrany',\n    previouslyPublished: 'Publicat anteriorment',\n    previousVersion: 'Versió anterior',\n    problemRestoringVersion: 'Hi ha hagut un problema en restaurar aquesta versió',\n    publish: 'Publicar',\n    publishAllLocales: 'Publica totes les configuracions regionals',\n    publishChanges: 'Publicar canvis',\n    published: 'Publicat',\n    publishIn: 'Publicar en {{locale}}',\n    publishing: 'Publicant',\n    restoreAsDraft: 'Restaurar com a borrador',\n    restoredSuccessfully: 'Restaurat amb èxit.',\n    restoreThisVersion: 'Restaurar aquesta versió',\n    restoring: 'Restaurant...',\n    reverting: 'Revertint...',\n    revertToPublished: 'Revertir a publicat',\n    saveDraft: 'Desar borrador',\n    scheduledSuccessfully: 'Programat amb èxit.',\n    schedulePublish: 'Programar publicació',\n    selectLocales: 'Selecciona els idiomes per mostrar',\n    selectVersionToCompare: 'Selecciona una versió per comparar',\n    showingVersionsFor: 'Mostrant versions per a:',\n    showLocales: 'Mostrar idiomes:',\n    specificVersion: 'Versió Específica',\n    status: 'Estat',\n    unpublish: 'Despublicar',\n    unpublishing: 'Despublicant...',\n    version: 'Versió',\n    versionAgo: 'fa {{distance}}',\n    versionCount_many: '{{count}} versions trobades',\n    versionCount_none: \"No s'han trobat versions\",\n    versionCount_one: '{{count}} versió trobada',\n    versionCount_other: '{{count}} versions trobades',\n    versionCreatedOn: '{{version}} creada el:',\n    versionID: 'ID de versió',\n    versions: 'Versions',\n    viewingVersion: 'Veient versió per al {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Veient versió per al {{entityLabel}} global',\n    viewingVersions: 'Veient versions per al {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Veient versions per al {{entityLabel}} global',\n  },\n}\n\nexport const ca: Language = {\n  dateFNSKey: 'ca',\n  translations: caTranslations,\n}\n"], "names": ["caTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "ca", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BACE;QACFC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCACE;QACFC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBACE;QACFC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}