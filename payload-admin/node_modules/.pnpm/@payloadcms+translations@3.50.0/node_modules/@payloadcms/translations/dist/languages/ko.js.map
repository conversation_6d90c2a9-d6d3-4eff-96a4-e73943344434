{"version": 3, "sources": ["../../src/languages/ko.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const koTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: '계정',\n    accountOfCurrentUser: '현재 사용자의 계정',\n    accountVerified: '계정이 성공적으로 인증되었습니다.',\n    alreadyActivated: '이미 활성화됨',\n    alreadyLoggedIn: '이미 로그인됨',\n    apiKey: 'API 키',\n    authenticated: '인증됨',\n    backToLogin: '로그인 화면으로 돌아가기',\n    beginCreateFirstUser: '시작하려면 첫 번째 사용자를 생성하세요.',\n    changePassword: '비밀번호 변경',\n    checkYourEmailForPasswordReset:\n      '이메일 주소가 계정과 연결되어 있다면, 곧 비밀번호를 재설정하는 방법에 대한 지시를 받게 될 것입니다. 인박스에서 이메일을 찾을 수 없다면 스팸 또는 정크 메일 폴더를 확인해 주시기 바랍니다.',\n    confirmGeneration: '생성 확인',\n    confirmPassword: '비밀번호 확인',\n    createFirstUser: '첫 번째 사용자 생성',\n    emailNotValid: '입력한 이메일은 유효하지 않습니다.',\n    emailOrUsername: '이메일 또는 사용자 이름',\n    emailSent: '이메일 전송됨',\n    emailVerified: '이메일이 성공적으로 인증되었습니다.',\n    enableAPIKey: 'API 키 활성화',\n    failedToUnlock: '잠금 해제 실패',\n    forceUnlock: '강제 잠금 해제',\n    forgotPassword: '비밀번호를 잊으셨나요?',\n    forgotPasswordEmailInstructions:\n      '아래에 이메일을 입력하세요. 비밀번호를 재설정하는 방법에 대한 안내가 포함된 이메일 메시지를 받게 될 것입니다.',\n    forgotPasswordQuestion: '비밀번호를 잊으셨나요?',\n    forgotPasswordUsernameInstructions:\n      '아래에 사용자 이름을 입력해 주세요. 암호를 재설정하는 방법에 대한 지침은 사용자 이름과 관련된 이메일 주소로 발송됩니다.',\n    generate: '생성',\n    generateNewAPIKey: '새로운 API 키 생성',\n    generatingNewAPIKeyWillInvalidate:\n      '새로운 API 키를 생성하면 이전 키가 무효화됩니다. 계속하시겠습니까?',\n    lockUntil: '잠금 시간',\n    logBackIn: '다시 로그인',\n    loggedIn: '다른 사용자로 로그인하려면 먼저 <0>로그아웃</0>해야 합니다.',\n    loggedInChangePassword:\n      '비밀번호를 변경하려면 <0>계정 화면</0>으로 이동하여 비밀번호를 편집하세요.',\n    loggedOutInactivity: '보안을 위해 일정 시간 동안 활동하지 않아 로그아웃되었습니다.',\n    loggedOutSuccessfully: '로그아웃되었습니다.',\n    loggingOut: '로그아웃 중...',\n    login: '로그인',\n    loginAttempts: '로그인 시도',\n    loginUser: '현재 사용자 로그인',\n    loginWithAnotherUser: '다른 사용자로 로그인하려면 먼저 <0>로그아웃</0>해야 합니다.',\n    logOut: '로그아웃',\n    logout: '로그아웃',\n    logoutSuccessful: '로그아웃 성공.',\n    logoutUser: '현재 사용자 로그아웃',\n    newAccountCreated:\n      '<a href=\"{{serverURL}}\">{{serverURL}}</a>에 접근할 수 있는 새로운 계정이 생성되었습니다. 다음 링크를 클릭하거나 브라우저에 URL을 붙여넣으세요: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> 이메일을 확인한 후에 로그인할 수 있습니다.',\n    newAPIKeyGenerated: '새로운 API 키가 생성되었습니다.',\n    newPassword: '새로운 비밀번호',\n    passed: '인증 통과',\n    passwordResetSuccessfully: '비밀번호가 성공적으로 재설정되었습니다.',\n    resetPassword: '비밀번호 재설정',\n    resetPasswordExpiration: '비밀번호 재설정 만료',\n    resetPasswordToken: '비밀번호 재설정 토큰',\n    resetYourPassword: '비밀번호 재설정',\n    stayLoggedIn: '로그인 상태 유지',\n    successfullyRegisteredFirstUser: '첫 번째 사용자를 성공적으로 등록했습니다.',\n    successfullyUnlocked: '잠금 해제 성공',\n    tokenRefreshSuccessful: '토큰 새로 고침이 성공했습니다.',\n    unableToVerify: '확인할 수 없음',\n    username: '사용자 이름',\n    usernameNotValid: '제공된 사용자 이름이 유효하지 않습니다.',\n    verified: '확인됨',\n    verifiedSuccessfully: '성공적으로 확인됨',\n    verify: '확인',\n    verifyUser: '현재 사용자 확인',\n    verifyYourEmail: '이메일을 확인해주세요',\n    youAreInactive:\n      '잠시 활동하지 않았으며 보안을 위해 곧 자동 로그아웃됩니다. 로그인 상태를 유지하시겠습니까?',\n    youAreReceivingResetPassword:\n      '당신(혹은 다른 사람)이 계정의 비밀번호 초기화를 요청했기 때문에 이 이메일을 받았습니다. 다음 링크를 클릭하거나 브라우저에 붙여넣어 비밀번호를 초기화하세요:',\n    youDidNotRequestPassword:\n      '비밀번호 초기화를 요청하지 않았다면 이 이메일을 무시하시고 비밀번호를 변경하지 마세요.',\n  },\n  error: {\n    accountAlreadyActivated: '이 계정은 이미 활성화되었습니다.',\n    autosaving: '이 문서를 자동 저장하는 중에 문제가 발생했습니다.',\n    correctInvalidFields: '입력하신 내용을 확인해주세요.',\n    deletingFile: '파일을 삭제하는 중에 오류가 발생했습니다.',\n    deletingTitle:\n      '{{title}} 삭제하는 중에 오류가 발생했습니다. 인터넷 연결을 확인하고 다시 시도하세요.',\n    documentNotFound:\n      'ID가 {{id}}인 문서를 찾을 수 없습니다. 이 문서는 삭제되었거나 존재하지 않았거나, 당신이 접근 권한이 없을 수 있습니다.',\n    emailOrPasswordIncorrect: '입력한 이메일 또는 비밀번호가 올바르지 않습니다.',\n    followingFieldsInvalid_one: '다음 입력란이 유효하지 않습니다:',\n    followingFieldsInvalid_other: '다음 입력란이 유효하지 않습니다:',\n    incorrectCollection: '잘못된 컬렉션',\n    insufficientClipboardPermissions:\n      '클립보드 접근이 거부되었습니다. 클립보드 권한을 확인하십시오.',\n    invalidClipboardData: '유효하지 않은 클립보드 데이터입니다.',\n    invalidFileType: '잘못된 파일 형식',\n    invalidFileTypeValue: '잘못된 파일 형식: {{value}}',\n    invalidRequestArgs: '요청에 잘못된 인수가 전달되었습니다: {{args}}',\n    loadingDocument: 'ID가 {{id}}인 문서를 불러오는 중에 문제가 발생했습니다.',\n    localesNotSaved_one: '다음 로케일을 저장할 수 없습니다:',\n    localesNotSaved_other: '다음 로케일들을 저장할 수 없습니다:',\n    logoutFailed: '로그아웃 실패했습니다.',\n    missingEmail: '이메일이 누락되었습니다.',\n    missingIDOfDocument: '업데이트할 문서의 ID가 누락되었습니다.',\n    missingIDOfVersion: '버전의 ID가 누락되었습니다.',\n    missingRequiredData: '필수 데이터가 누락되었습니다.',\n    noFilesUploaded: '파일이 업로드되지 않았습니다.',\n    noMatchedField: '\"{{label}}\"에 대한 일치하는 입력란이 없습니다.',\n    notAllowedToAccessPage: '이 페이지에 접근할 권한이 없습니다.',\n    notAllowedToPerformAction: '이 작업을 수행할 권한이 없습니다.',\n    notFound: '요청한 리소스를 찾을 수 없습니다.',\n    noUser: '사용자가 없습니다.',\n    previewing: '이 문서를 미리보는 중에 문제가 발생했습니다.',\n    problemUploadingFile: '파일 업로드 중에 문제가 발생했습니다.',\n    restoringTitle:\n      '{{title}} 복원 중 오류가 발생했습니다. 연결 상태를 확인하고 다시 시도해 주세요.',\n    tokenInvalidOrExpired: '토큰이 유효하지 않거나 만료되었습니다.',\n    tokenNotProvided: '토큰이 제공되지 않았습니다.',\n    unableToCopy: '복사할 수 없습니다.',\n    unableToDeleteCount: '총 {{total}}개 중 {{count}}개의 {{label}}을(를) 삭제할 수 없습니다.',\n    unableToReindexCollection:\n      '{{collection}} 컬렉션의 재인덱싱 중 오류가 발생했습니다. 작업이 중단되었습니다.',\n    unableToUpdateCount: '총 {{total}}개 중 {{count}}개의 {{label}}을(를) 업데이트할 수 없습니다.',\n    unauthorized: '권한 없음, 이 요청을 수행하려면 로그인해야 합니다.',\n    unauthorizedAdmin: '관리자 패널에 액세스할 수 없습니다.',\n    unknown: '알 수 없는 오류가 발생했습니다.',\n    unPublishingDocument: '이 문서의 게시 취소 중에 문제가 발생했습니다.',\n    unspecific: '오류가 발생했습니다.',\n    unverifiedEmail: '로그인하기 전에 이메일을 확인하세요.',\n    userEmailAlreadyRegistered: '주어진 이메일로 이미 등록된 사용자가 있습니다.',\n    userLocked: '이 사용자는 로그인 실패 횟수가 너무 많아 잠겼습니다.',\n    usernameAlreadyRegistered: '주어진 사용자 이름을 가진 사용자가 이미 등록되어 있습니다.',\n    usernameOrPasswordIncorrect: '제공된 사용자 이름 또는 비밀번호가 잘못되었습니다.',\n    valueMustBeUnique: '값은 고유해야 합니다.',\n    verificationTokenInvalid: '확인 토큰이 유효하지 않습니다.',\n  },\n  fields: {\n    addLabel: '{{label}} 추가',\n    addLink: '링크 추가',\n    addNew: '새로 추가',\n    addNewLabel: '새로운 {{label}} 추가',\n    addRelationship: '관계 추가',\n    addUpload: '업로드 추가',\n    block: '블록',\n    blocks: '블록',\n    blockType: '블록 유형',\n    chooseBetweenCustomTextOrDocument:\n      '사용자 지정 텍스트 URL 또는 다른 문서에 링크 중 선택하세요.',\n    chooseDocumentToLink: '연결할 문서 선택',\n    chooseFromExisting: '기존 항목 중 선택',\n    chooseLabel: '{{label}} 선택',\n    collapseAll: '모두 접기',\n    customURL: '사용자 지정 URL',\n    editLabelData: '{{label}} 데이터 수정',\n    editLink: '링크 수정',\n    editRelationship: '관계 수정',\n    enterURL: 'URL 입력',\n    internalLink: '내부 링크',\n    itemsAndMore: '{{items}} 및 {{count}}개 더',\n    labelRelationship: '{{label}} 관계',\n    latitude: '위도',\n    linkedTo: '<0>{{label}}</0>에 연결됨',\n    linkType: '링크 유형',\n    longitude: '경도',\n    newLabel: '새로운 {{label}}',\n    openInNewTab: '새 탭에서 열기',\n    passwordsDoNotMatch: '비밀번호가 일치하지 않습니다.',\n    relatedDocument: '관련 문서',\n    relationTo: '관계',\n    removeRelationship: '관계 제거',\n    removeUpload: '제거',\n    saveChanges: '변경 사항 저장',\n    searchForBlock: '블록 검색',\n    selectExistingLabel: '기존 {{label}} 선택',\n    selectFieldsToEdit: '수정할 입력란 선택',\n    showAll: '모두 표시',\n    swapRelationship: '관계 교체',\n    swapUpload: '업로드 교체',\n    textToDisplay: '표시할 텍스트',\n    toggleBlock: '블록 토글',\n    uploadNewLabel: '새로운 {{label}} 업로드',\n  },\n  folder: {\n    browseByFolder: '폴더별 브라우징',\n    byFolder: '폴더별로',\n    deleteFolder: '폴더 삭제',\n    folderName: '폴더 이름',\n    folders: '폴더들',\n    folderTypeDescription: '이 폴더에서 어떤 유형의 컬렉션 문서가 허용되어야 하는지 선택하세요.',\n    itemHasBeenMoved: '{{title}}는 {{folderName}}로 이동되었습니다.',\n    itemHasBeenMovedToRoot: '{{title}}이(가) 루트 폴더로 이동되었습니다.',\n    itemsMovedToFolder: '{{title}}이(가) {{folderName}}로 이동되었습니다.',\n    itemsMovedToRoot: '{{title}}이(가) 루트 폴더로 이동되었습니다.',\n    moveFolder: '폴더 이동',\n    moveItemsToFolderConfirmation:\n      '<1>{{count}} {{label}}</1>을(를) <2>{{toFolder}}</2>(으)로 이동하려 합니다. 확실합니까?',\n    moveItemsToRootConfirmation:\n      '당신은 <1>{{count}} {{label}}</1>을 최상위 폴더로 이동하려고 합니다. 확실합니까?',\n    moveItemToFolderConfirmation:\n      '<1>{{title}}</1>을(를) <2>{{toFolder}}</2>(으)로 이동하려고 합니다. 확실하신가요?',\n    moveItemToRootConfirmation: '<1>{{title}}</1>을 루트 폴더로 이동하려고 합니다. 확실합니까?',\n    movingFromFolder: '{{title}}를 {{fromFolder}}에서 이동합니다',\n    newFolder: '새 폴더',\n    noFolder: '폴더 없음',\n    renameFolder: '폴더 이름 변경',\n    searchByNameInFolder: '{{folderName}}에서 이름으로 검색하세요.',\n    selectFolderForItem: '{{title}}에 대한 폴더 선택',\n  },\n  general: {\n    name: '이름',\n    aboutToDelete: '{{label}} <1>{{title}}</1>를 삭제하려고 합니다. 계속하시겠습니까?',\n    aboutToDeleteCount_many: '{{label}}를 {{count}}개 삭제하려고 합니다.',\n    aboutToDeleteCount_one: '{{label}}를 {{count}}개 삭제하려고 합니다.',\n    aboutToDeleteCount_other: '{{label}}를 {{count}}개 삭제하려고 합니다.',\n    aboutToPermanentlyDelete:\n      '당신은 {{label}} <1>{{title}}</1>을 영구적으로 삭제하려고 합니다. 확실합니까?',\n    aboutToPermanentlyDeleteTrash:\n      '휴지통에서 <0>{{count}}</0> <1>{{label}}</1>을(를) 영구적으로 삭제하려고 합니다. 확실합니까?',\n    aboutToRestore: '당신은 {{label}} <1>{{title}}</1>을 복원하려고 합니다. 확실합니까?',\n    aboutToRestoreAsDraft:\n      '당신은 {{label}} <1>{{title}}</1>을 초안으로 복원하려고 합니다. 확실합니까?',\n    aboutToRestoreAsDraftCount: '당신은 {{count}}개의 {{label}}을 초안으로 복원하려고 합니다.',\n    aboutToRestoreCount: '당신은 {{count}} {{label}}을 복원하려고 합니다.',\n    aboutToTrash: '{{label}} <1>{{title}}</1>을 휴지통으로 이동하려고 합니다. 확실합니까?',\n    aboutToTrashCount: '당신은 곧 {{count}} {{label}}을(를) 휴지통으로 이동하려고 합니다.',\n    addBelow: '아래에 추가',\n    addFilter: '필터 추가',\n    adminTheme: '관리자 테마',\n    all: '모두',\n    allCollections: '모든 컬렉션',\n    allLocales: '모든 지역 설정',\n    and: '및',\n    anotherUser: '다른 사용자',\n    anotherUserTakenOver: '다른 사용자가 이 문서의 편집을 인수했습니다.',\n    applyChanges: '변경 사항 적용',\n    ascending: '오름차순',\n    automatic: '자동 설정',\n    backToDashboard: '대시보드로 돌아가기',\n    cancel: '취소',\n    changesNotSaved: '변경 사항이 저장되지 않았습니다. 지금 떠나면 변경 사항을 잃게 됩니다.',\n    clear:\n      '페이로드의 맥락 내에서 원문의 의미를 존중하십시오. 다음은 페이로드에서 사용되는 특정 의미를 내포하는 일반적인 페이로드 용어 목록입니다: \\n- Collection: 컬렉션은 공통의 구조와 목적을 공유하는 문서의 그룹입니다. 컬렉션은 페이로드에서 콘텐츠를 정리하고 관리하는 데 사용됩니다.\\n- Field: 필드는 컬렉',\n    clearAll: '모두 지우기',\n    close: '닫기',\n    collapse: '접기',\n    collections: '컬렉션',\n    columns: '열',\n    columnToSort: '정렬할 열',\n    confirm: '확인',\n    confirmCopy: '복사 확인',\n    confirmDeletion: '삭제하시겠습니까?',\n    confirmDuplication: '복제하시겠습니까?',\n    confirmMove: '이동 확인',\n    confirmReindex: '모든 {{collections}}를 다시 인덱싱하시겠습니까?',\n    confirmReindexAll: '모든 컬렉션을 다시 인덱싱하시겠습니까?',\n    confirmReindexDescription:\n      '이 작업은 기존 인덱스를 삭제하고 {{collections}} 컬렉션 내의 문서를 다시 인덱싱합니다.',\n    confirmReindexDescriptionAll:\n      '이 작업은 기존 인덱스를 삭제하고 모든 컬렉션 내의 문서를 다시 인덱싱합니다.',\n    confirmRestoration: '복구를 확인하십시오',\n    copied: '복사됨',\n    copy: '복사',\n    copyField: '필드 복사',\n    copying: '복사하기',\n    copyRow: '행 복사',\n    copyWarning: '{{label}} {{title}}에 대해 {{from}}으로 {{to}}를 덮어쓰려고 합니다. 확실합니까?',\n    create: '생성',\n    created: '생성됨',\n    createdAt: '생성 일시',\n    createNew: '새로 생성',\n    createNewLabel: '새로운 {{label}} 생성',\n    creating: '생성 중',\n    creatingNewLabel: '{{label}} 생성 중',\n    currentlyEditing:\n      '현재 이 문서를 편집 중입니다. 당신이 인수하면, 편집을 계속할 수 없게 되고, 저장되지 않은 변경 사항이 손실될 수 있습니다.',\n    custom: '사용자 정의',\n    dark: '다크',\n    dashboard: '대시보드',\n    delete: '삭제',\n    deleted: '삭제됨',\n    deletedAt: '삭제된 시간',\n    deletedCountSuccessfully: '{{count}}개의 {{label}}를 삭제했습니다.',\n    deletedSuccessfully: '삭제되었습니다.',\n    deletePermanently: '휴지통 건너뛰고 영구적으로 삭제하세요',\n    deleting: '삭제 중...',\n    depth: '깊이',\n    descending: '내림차순',\n    deselectAllRows: '모든 행 선택 해제',\n    document: '문서',\n    documentIsTrashed: '이 {{label}}은 휴지통에 있으며 읽기 전용입니다.',\n    documentLocked: '문서가 잠겼습니다',\n    documents: '문서들',\n    duplicate: '복제',\n    duplicateWithoutSaving: '변경 사항 저장 없이 복제',\n    edit: '수정',\n    editAll: '모두 수정',\n    editedSince: '편집됨',\n    editing: '수정 중',\n    editingLabel_many: '{{count}}개의 {{label}} 수정 중',\n    editingLabel_one: '{{count}}개의 {{label}} 수정 중',\n    editingLabel_other: '{{count}}개의 {{label}} 수정 중',\n    editingTakenOver: '편집이 인수되었습니다',\n    editLabel: '{{label}} 수정',\n    email: '이메일',\n    emailAddress: '이메일 주소',\n    emptyTrash: '휴지통 비우기',\n    emptyTrashLabel: '{{label}} 휴지통 비우기',\n    enterAValue: '값을 입력하세요',\n    error: '오류',\n    errors: '오류',\n    exitLivePreview: '실시간 미리보기 종료',\n    export: '수출',\n    fallbackToDefaultLocale: '기본 locale로 대체',\n    false: '거짓',\n    filter: '필터',\n    filters: '필터',\n    filterWhere: '{{label}} 필터링 조건',\n    globals: '글로벌',\n    goBack: '돌아가기',\n    groupByLabel: '{{label}}로 그룹화',\n    import: '수입',\n    isEditing: '편집 중',\n    item: '항목',\n    items: '항목들',\n    language: '언어',\n    lastModified: '마지막 수정 일시',\n    leaveAnyway: '그래도 나가시겠습니까?',\n    leaveWithoutSaving: '저장하지 않고 나가기',\n    light: '라이트',\n    livePreview: '실시간 미리보기',\n    loading: '불러오는 중',\n    locale: 'locale',\n    locales: 'locale',\n    menu: '메뉴',\n    moreOptions: '더 많은 옵션',\n    move: '움직이세요',\n    moveConfirm:\n      '당신은 <1>{{destination}}</1>로 {{count}}개의 {{label}}을(를) 이동하려고 합니다. 확실합니까?',\n    moveCount: '{{count}} {{label}} 이동',\n    moveDown: '아래로 이동',\n    moveUp: '위로 이동',\n    moving: '이동하는',\n    movingCount: '{{count}} {{label}}을(를) 이동시킵니다.',\n    newPassword: '새 비밀번호',\n    next: '다음',\n    no: '아니요',\n    noDateSelected: '선택된 날짜가 없습니다.',\n    noFiltersSet: '설정된 필터 없음',\n    noLabel: '<{{label}} 없음>',\n    none: '없음',\n    noOptions: '옵션 없음',\n    noResults:\n      '{{label}}를 찾을 수 없습니다. 아직 {{label}}이 없거나 설정한 필터와 일치하는 것이 없습니다.',\n    notFound: '찾을 수 없음',\n    nothingFound: '찾을 수 없습니다',\n    noTrashResults: '휴지통에 {{label}}이 없습니다.',\n    noUpcomingEventsScheduled: '예정된 행사가 없습니다.',\n    noValue: '값 없음',\n    of: '의',\n    only: '오직',\n    open: '열기',\n    or: '또는',\n    order: '순서',\n    overwriteExistingData: '기존 필드 데이터 덮어쓰기',\n    pageNotFound: '페이지를 찾을 수 없음',\n    password: '비밀번호',\n    pasteField: '필드 붙여넣기',\n    pasteRow: '행 붙여넣기',\n    payloadSettings: 'Payload 설정',\n    permanentlyDelete: '영구적으로 삭제',\n    permanentlyDeletedCountSuccessfully:\n      '영구적으로 {{count}} {{label}}가 성공적으로 삭제되었습니다.',\n    perPage: '페이지당 개수: {{limit}}',\n    previous: '이전',\n    reindex: '재인덱싱',\n    reindexingAll: '모든 {{collections}}를 다시 인덱싱하는 중입니다.',\n    remove: '제거',\n    rename: '이름 변경',\n    reset: '초기화',\n    resetPreferences: '기본 설정으로 재설정',\n    resetPreferencesDescription: '이렇게 하면 모든 기본 설정이 기본값으로 재설정됩니다.',\n    resettingPreferences: '기본 설정을 재설정하는 중.',\n    restore: '복원',\n    restoreAsPublished: '게시된 버전으로 복원하다',\n    restoredCountSuccessfully: '성공적으로 {{count}} {{label}}를 복원했습니다.',\n    restoring:\n      '원래 텍스트의 의미를 Payload 문맥 내에서 존중하십시오. 여기에는 매우 특정한 의미를 가진 일반 Payload 용어 목록이 있습니다:\\n    - Collection: 컬렉션은 공통 구조와 목적을 공유하는 문서의 그룹입니다. 컬렉션은 Payload에서 컨텐츠를 구성하고 관리하는 데 사용됩니다.\\n    - Field: 필드는 컬렉션 내의 문서에 있는 특정 데이터 조각입니다.',\n    row: '행',\n    rows: '행',\n    save: '저장',\n    saving: '저장 중...',\n    schedulePublishFor: '{{title}}에 대한 게시 일정 설정',\n    searchBy: '{{label}}로 검색',\n    select: '선택하십시오',\n    selectAll: '{{count}}개 {{label}} 모두 선택',\n    selectAllRows: '모든 행 선택',\n    selectedCount: '{{count}}개의 {{label}} 선택됨',\n    selectLabel: '{{label}}을 선택하십시오.',\n    selectValue: '값 선택',\n    showAllLabel: '{{label}} 모두 표시',\n    sorryNotFound: '죄송합니다. 요청과 일치하는 항목이 없습니다.',\n    sort: '정렬',\n    sortByLabelDirection: '{{label}} {{direction}}으로 정렬',\n    stayOnThisPage: '이 페이지에 머무르기',\n    submissionSuccessful: '제출이 완료되었습니다.',\n    submit: '제출',\n    submitting: '제출 중...',\n    success: '성공',\n    successfullyCreated: '{{label}}이(가) 생성되었습니다.',\n    successfullyDuplicated: '{{label}}이(가) 복제되었습니다.',\n    successfullyReindexed:\n      '{{collections}} 컬렉션에서 {{total}} 문서 중 {{count}} 문서가 성공적으로 재인덱싱되었습니다.',\n    takeOver: '인수하기',\n    thisLanguage: '한국어',\n    time: '시간',\n    timezone: '시간대',\n    titleDeleted: '{{label}} \"{{title}}\"을(를) 삭제했습니다.',\n    titleRestored: '\"{{label}}\" \"{{title}}\"이(가) 성공적으로 복원되었습니다.',\n    titleTrashed: '\"{{label}}\" \"{{title}}\"이(가) 휴지통으로 이동되었습니다.',\n    trash: '휴지통',\n    trashedCountSuccessfully: '{{count}} {{label}}가 휴지통으로 이동했습니다.',\n    true: '참',\n    unauthorized: '권한 없음',\n    unsavedChanges: '저장되지 않은 변경 사항이 있습니다. 계속하기 전에 저장하거나 무시하십시오.',\n    unsavedChangesDuplicate: '저장되지 않은 변경 사항이 있습니다. 복제를 계속하시겠습니까?',\n    untitled: '제목 없음',\n    upcomingEvents: '다가오는 이벤트',\n    updatedAt: '업데이트 일시',\n    updatedCountSuccessfully: '{{count}}개의 {{label}}을(를) 업데이트했습니다.',\n    updatedLabelSuccessfully: '{{label}}이(가) 성공적으로 업데이트되었습니다.',\n    updatedSuccessfully: '성공적으로 업데이트되었습니다.',\n    updateForEveryone: '모두를 위한 업데이트',\n    updating: '업데이트 중',\n    uploading: '업로드 중',\n    uploadingBulk: '{{current}} / {{total}} 업로드 중',\n    user: '사용자',\n    username: '사용자 이름',\n    users: '사용자',\n    value: '값',\n    viewing: '열람',\n    viewReadOnly: '읽기 전용으로 보기',\n    welcome: '환영합니다',\n    yes: '네',\n  },\n  localization: {\n    cannotCopySameLocale: '동일한 로캘에 복사할 수 없습니다.',\n    copyFrom: '에서 복사하십시오.',\n    copyFromTo: '{{from}}에서 {{to}}로 복사하기',\n    copyTo: '복사하기',\n    copyToLocale: '로케일로 복사',\n    localeToPublish: '발행할 장소',\n    selectLocaleToCopy: '복사할 지역을 선택하십시오.',\n  },\n  operators: {\n    contains: '포함',\n    equals: '같음',\n    exists: '존재',\n    intersects: '교차합니다',\n    isGreaterThan: '보다 큼',\n    isGreaterThanOrEqualTo: '보다 크거나 같음',\n    isIn: '포함됨',\n    isLessThan: '보다 작음',\n    isLessThanOrEqualTo: '보다 작거나 같음',\n    isLike: '유사',\n    isNotEqualTo: '같지 않음',\n    isNotIn: '포함되지 않음',\n    isNotLike: '같지 않다',\n    near: '근처',\n    within: '내에서',\n  },\n  upload: {\n    addFile: '파일 추가',\n    addFiles: '파일 추가',\n    bulkUpload: '일괄 업로드',\n    crop: '자르기',\n    cropToolDescription:\n      '선택한 영역의 모퉁이를 드래그하거나 새로운 영역을 그리거나 아래의 값을 조정하세요.',\n    download: '다운로드',\n    dragAndDrop: '파일을 끌어다 놓으세요',\n    dragAndDropHere: '또는 여기로 파일을 끌어다 놓으세요',\n    editImage: '이미지 수정',\n    fileName: '파일 이름',\n    fileSize: '파일 크기',\n    filesToUpload: '업로드할 파일들',\n    fileToUpload: '업로드할 파일',\n    focalPoint: '초점',\n    focalPointDescription: '미리보기에서 초점을 직접 드래그하거나 아래의 값을 조정하세요.',\n    height: '높이',\n    lessInfo: '정보 숨기기',\n    moreInfo: '정보 더보기',\n    noFile: '파일 없음',\n    pasteURL: 'URL 붙여넣기',\n    previewSizes: '미리보기 크기',\n    selectCollectionToBrowse: '찾을 컬렉션 선택',\n    selectFile: '파일 선택',\n    setCropArea: '자르기 영역 설정',\n    setFocalPoint: '초점 설정',\n    sizes: '크기',\n    sizesFor: '{{label}} 크기',\n    width: '너비',\n  },\n  validation: {\n    emailAddress: '유효한 이메일 주소를 입력하세요.',\n    enterNumber: '유효한 숫자를 입력하세요.',\n    fieldHasNo: '이 입력란에는 {{label}}이(가) 없습니다.',\n    greaterThanMax: '{{value}}은(는) 최대 허용된 {{label}}인 {{max}}개보다 큽니다.',\n    invalidInput: '이 입력란에는 유효하지 않은 입력이 있습니다.',\n    invalidSelection: '이 입력란에는 유효하지 않은 선택이 있습니다.',\n    invalidSelections: '이 입력란에는 다음과 같은 유효하지 않은 선택 사항이 있습니다:',\n    lessThanMin: '{{value}}은(는) 최소 허용된 {{label}}인 {{min}}개보다 작습니다.',\n    limitReached: '제한에 도달했습니다. {{max}}개 항목만 추가할 수 있습니다.',\n    longerThanMin: '이 값은 최소 길이인 {{minLength}}자보다 길어야 합니다.',\n    notValidDate: '\"{{value}}\"은(는) 유효한 날짜가 아닙니다.',\n    required: '이 입력란은 필수입니다.',\n    requiresAtLeast: '이 입력란운 최소한 {{count}} {{label}}이 필요합니다.',\n    requiresNoMoreThan: '이 입력란은 최대 {{count}} {{label}} 이하이어야 합니다.',\n    requiresTwoNumbers: '이 입력란은 두 개의 숫자가 필요합니다.',\n    shorterThanMax: '이 값은 최대 길이인 {{maxLength}}자보다 짧아야 합니다.',\n    timezoneRequired: '시간대가 필요합니다.',\n    trueOrFalse: '이 입력란은 true 또는 false만 가능합니다.',\n    username:\n      '유효한 사용자 이름을 입력해 주세요. 글자, 숫자, 하이픈, 마침표, 및 밑줄을 사용할 수 있습니다.',\n    validUploadID: '이 입력란은 유효한 업로드 ID가 아닙니다.',\n  },\n  version: {\n    type: '유형',\n    aboutToPublishSelection: '선택한 {{label}}을(를) 게시하려고 합니다. 계속하시겠습니까?',\n    aboutToRestore: '이 {{label}} 문서를 {{versionDate}}기준 버전으로 복원하려고 합니다.',\n    aboutToRestoreGlobal: '글로벌 {{label}}을(를) {{versionDate}}기준 버전으로 복원하려고 합니다.',\n    aboutToRevertToPublished:\n      '이 문서의 변경 사항을 게시된 상태로 되돌리려고 합니다. 계속하시겠습니까?',\n    aboutToUnpublish: '이 문서를 게시 해제하려고 합니다. 계속하시겠습니까?',\n    aboutToUnpublishSelection: '선택한 {{label}}을(를) 게시 해제하려고 합니다. 계속하시겠습니까?',\n    autosave: '자동 저장',\n    autosavedSuccessfully: '자동 저장이 완료되었습니다.',\n    autosavedVersion: '자동 저장된 버전',\n    changed: '변경됨',\n    changedFieldsCount_one: '{{count}} 변경된 필드',\n    changedFieldsCount_other: '{{count}}개의 변경된 필드',\n    compareVersion: '비교할 버전 선택:',\n    compareVersions: '버전 비교',\n    comparingAgainst: '비교 대상으로',\n    confirmPublish: '게시하기',\n    confirmRevertToSaved: '저장된 상태로 되돌리기',\n    confirmUnpublish: '게시 해제하기',\n    confirmVersionRestoration: '버전 복원하기',\n    currentDocumentStatus: '현재 {{docStatus}} 문서',\n    currentDraft: '현재 초안',\n    currentlyPublished: '현재 게시됨',\n    currentlyViewing: '현재 보고 있습니다',\n    currentPublishedVersion: '현재 게시된 버전',\n    draft: '초안',\n    draftSavedSuccessfully: '초안이 저장되었습니다.',\n    lastSavedAgo: '마지막으로 저장한지 {{distance}} 전',\n    modifiedOnly: '수정된 것만',\n    moreVersions: '더 많은 버전...',\n    noFurtherVersionsFound: '더 이상의 버전을 찾을 수 없습니다.',\n    noRowsFound: '{{label}}을(를) 찾을 수 없음',\n    noRowsSelected: '선택된 {{label}} 없음',\n    preview: '미리보기',\n    previouslyDraft: '이전에는 초안',\n    previouslyPublished: '이전에 발표된',\n    previousVersion: '이전 버전',\n    problemRestoringVersion: '이 버전을 복원하는 중 문제가 발생했습니다.',\n    publish: '게시',\n    publishAllLocales: '모든 로케일을 게시하십시오',\n    publishChanges: '변경 사항 게시',\n    published: '게시됨',\n    publishIn: '{{locale}}에서 게시하십시오.',\n    publishing: '게시',\n    restoreAsDraft: '임시 저장으로 복원',\n    restoredSuccessfully: '복원이 완료되었습니다.',\n    restoreThisVersion: '이 버전 복원',\n    restoring: '복원 중...',\n    reverting: '되돌리는 중...',\n    revertToPublished: '게시된 상태로 되돌리기',\n    saveDraft: '초안 저장',\n    scheduledSuccessfully: '성공적으로 예약되었습니다.',\n    schedulePublish: '발행 일정',\n    selectLocales: '표시할 locale 선택',\n    selectVersionToCompare: '비교할 버전 선택',\n    showingVersionsFor: '다음 버전 표시 중:',\n    showLocales: 'locale 표시:',\n    specificVersion: '특정 버전',\n    status: '상태',\n    unpublish: '게시 해제',\n    unpublishing: '게시 해제 중...',\n    version: '버전',\n    versionAgo: '{{distance}} 전',\n    versionCount_many: '{{count}}개의 버전을 찾았습니다',\n    versionCount_none: '버전을 찾을 수 없습니다',\n    versionCount_one: '{{count}}개의 버전을 찾았습니다',\n    versionCount_other: '{{count}}개의 버전을 찾았습니다',\n    versionCreatedOn: '{{version}} 생성 날짜:',\n    versionID: '버전 ID',\n    versions: '버전',\n    viewingVersion: '{{entityLabel}} {{documentTitle}}의 버전 보기',\n    viewingVersionGlobal: '글로벌 {{entityLabel}}의 버전 보기',\n    viewingVersions: '{{entityLabel}} {{documentTitle}}에 대한 버전 보기',\n    viewingVersionsGlobal: '글로벌 {{entityLabel}}에 대한 버전 보기',\n  },\n}\n\nexport const ko: Language = {\n  dateFNSKey: 'ko',\n  translations: koTranslations,\n}\n"], "names": ["koTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "ko", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OACE;QACFC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCACE;QACFC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BACE;QACFC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}