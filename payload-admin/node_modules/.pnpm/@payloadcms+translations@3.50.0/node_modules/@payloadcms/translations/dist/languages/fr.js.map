{"version": 3, "sources": ["../../src/languages/fr.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const frTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Compte',\n    accountOfCurrentUser: 'Compte de l’utilisateur actuel',\n    accountVerified: 'Compte vérifié avec succès.',\n    alreadyActivated: 'Déjà activé',\n    alreadyLoggedIn: 'Déjà connecté',\n    apiKey: 'Clé API',\n    authenticated: 'Authentifié',\n    backToLogin: 'Retour à la connexion',\n    beginCreateFirstUser: 'Pour commencer, créez votre premier utilisateur.',\n    changePassword: 'Changer le mot de passe',\n    checkYourEmailForPasswordReset:\n      \"Si l'adresse e-mail est associée à un compte, vous recevrez sous peu des instructions pour réinitialiser votre mot de passe. Veuillez vérifier votre dossier de courrier indésirable ou de spam si vous ne voyez pas l'e-mail dans votre boîte de réception.\",\n    confirmGeneration: 'Confirmer la génération',\n    confirmPassword: 'Confirmez le mot de passe',\n    createFirstUser: 'Créer le premier utilisateur',\n    emailNotValid: 'L’adresse e-mail fournie n’est pas valide',\n    emailOrUsername: \"Email ou Nom d'utilisateur\",\n    emailSent: 'E-mail envoyé',\n    emailVerified: 'E-mail vérifié avec succès.',\n    enableAPIKey: 'Activer la clé API',\n    failedToUnlock: 'Déverrouillage échoué',\n    forceUnlock: 'Forcer le déverrouillage',\n    forgotPassword: 'Mot de passe oublié',\n    forgotPasswordEmailInstructions:\n      'Veuillez saisir votre e-mail ci-dessous. Vous recevrez un e-mail avec des instructions concernant comment réinitialiser votre mot de passe.',\n    forgotPasswordQuestion: 'Mot de passe oublié ?',\n    forgotPasswordUsernameInstructions:\n      \"Veuillez entrer votre nom d'utilisateur ci-dessous. Les instructions sur comment réinitialiser votre mot de passe seront envoyées à l'adresse e-mail associée à votre nom d'utilisateur.\",\n    generate: 'Générer',\n    generateNewAPIKey: 'Générer une nouvelle clé API',\n    generatingNewAPIKeyWillInvalidate:\n      'La génération d’une nouvelle clé API <1>invalidera</1> la clé précédente. Êtes-vous sûr de vouloir continuer ?',\n    lockUntil: 'Verrouiller jusqu’à',\n    logBackIn: 'Se reconnecter',\n    loggedIn:\n      'Pour vous connecter en tant qu’un autre utilisateur, vous devez d’abord vous <0>déconnecter</0>.',\n    loggedInChangePassword:\n      'Pour changer votre mot de passe, rendez-vous sur votre <0>compte</0> puis modifiez-y votre mot de passe.',\n    loggedOutInactivity: 'Vous avez été déconnecté pour cause d’inactivité.',\n    loggedOutSuccessfully: 'Vous avez été déconnecté avec succès.',\n    loggingOut: 'Déconnexion...',\n    login: 'Se connecter',\n    loginAttempts: 'Tentatives de connexion',\n    loginUser: 'Connecter l’utilisateur',\n    loginWithAnotherUser:\n      'Pour vous connecter en tant qu’un autre utilisateur, vous devez d’abord vous <0>déconnecter</0>.',\n    logOut: 'Se déconnecter',\n    logout: 'Se déconnecter',\n    logoutSuccessful: 'Déconnexion réussie.',\n    logoutUser: 'Déconnecter l’utilisateur',\n    newAccountCreated:\n      'Un nouveau compte vient d’être créé pour vous permettre d’accéder <a href=\"{{serverURL}}\">{{serverURL}}</a>. Veuillez cliquer sur le lien suivant ou collez l’URL ci-dessous dans votre navigateur pour vérifier votre adresse e-mail: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br>. Après avoir vérifié votre adresse e-mail, vous pourrez vous connecter avec succès.',\n    newAPIKeyGenerated: 'Nouvelle clé API générée.',\n    newPassword: 'Nouveau mot de passe',\n    passed: 'Authentification réussie',\n    passwordResetSuccessfully: 'Réinitialisation du mot de passe réussie.',\n    resetPassword: 'Réinitialiser le mot de passe',\n    resetPasswordExpiration: 'Réinitialiser l’expiration du mot de passe',\n    resetPasswordToken: 'Réinitialiser le jeton de mot de passe',\n    resetYourPassword: 'Réinitialisez votre mot de passe',\n    stayLoggedIn: 'Rester connecté',\n    successfullyRegisteredFirstUser: 'Premier utilisateur enregistré avec succès.',\n    successfullyUnlocked: 'Déverrouillé avec succès',\n    tokenRefreshSuccessful: 'Actualisation du jeton réussie.',\n    unableToVerify: 'Vérification échouée',\n    username: \"Nom d'utilisateur\",\n    usernameNotValid: \"Le nom d'utilisateur fourni n'est pas valide\",\n    verified: 'Vérifié',\n    verifiedSuccessfully: 'Vérifié avec succès',\n    verify: 'Vérifier',\n    verifyUser: 'Vérifier l’utilisateur',\n    verifyYourEmail: 'Vérifiez votre e-mail',\n    youAreInactive:\n      'Vous n’avez pas été actif depuis un moment alors vous serez bientôt automatiquement déconnecté pour votre propre sécurité. Souhaitez-vous rester connecté ?',\n    youAreReceivingResetPassword:\n      'Vous recevez ceci parce que vous (ou quelqu’un d’autre) avez demandé la réinitialisation du mot de passe de votre compte. Veuillez cliquer sur le lien suivant ou le coller dans votre navigateur pour terminer le processus :',\n    youDidNotRequestPassword:\n      'Si vous ne l’avez pas demandé, veuillez ignorer cet e-mail et votre mot de passe restera inchangé.',\n  },\n  error: {\n    accountAlreadyActivated: 'Ce compte a déjà été activé.',\n    autosaving: 'Un problème est survenu lors de l’enregistrement automatique de ce document.',\n    correctInvalidFields: 'Veuillez corriger les champs invalides.',\n    deletingFile: 'Une erreur s’est produite lors de la suppression du fichier.',\n    deletingTitle:\n      'Une erreur s’est produite lors de la suppression de {{title}}. Veuillez vérifier votre connexion puis réessayer.',\n    documentNotFound:\n      \"Le document avec l'ID {{id}} n'a pas pu être trouvé. Il a peut-être été supprimé ou n'a jamais existé, ou vous n'avez peut-être pas accès à celui-ci.\",\n    emailOrPasswordIncorrect: 'L’adresse e-mail ou le mot de passe fourni est incorrect.',\n    followingFieldsInvalid_one: 'Le champ suivant n’est pas valide :',\n    followingFieldsInvalid_other: 'Les champs suivants ne sont pas valides :',\n    incorrectCollection: 'Collection incorrecte',\n    insufficientClipboardPermissions:\n      'Accès au presse-papiers refusé. Veuillez vérifier vos autorisations pour le presse-papiers.',\n    invalidClipboardData: 'Données invalides dans le presse-papiers.',\n    invalidFileType: 'Type de fichier invalide',\n    invalidFileTypeValue: 'Type de fichier invalide : {{value}}',\n    invalidRequestArgs: 'Arguments non valides dans la requête : {{args}}',\n    loadingDocument:\n      'Un problème est survenu lors du chargement du document qui a pour identifiant {{id}}.',\n    localesNotSaved_one: 'Le paramètre régional suivant n’a pas pu être enregistré :',\n    localesNotSaved_other: 'Les paramètres régionaux suivants n’ont pas pu être enregistrés :',\n    logoutFailed: 'La déconnexion a échouée.',\n    missingEmail: 'E-mail manquant.',\n    missingIDOfDocument: 'Il manque l’identifiant du document à mettre à jour.',\n    missingIDOfVersion: 'Il manque l’identifiant de la version.',\n    missingRequiredData: 'Données requises manquantes.',\n    noFilesUploaded: 'Aucun fichier n’a été téléversé.',\n    noMatchedField: 'Aucun champ correspondant n’a été trouvé pour \"{{label}}\"',\n    notAllowedToAccessPage: 'Vous n’êtes pas autorisé à accéder à cette page.',\n    notAllowedToPerformAction: 'Vous n’êtes pas autorisé à effectuer cette action.',\n    notFound: 'La ressource demandée n’a pas été trouvée.',\n    noUser: 'Aucun utilisateur',\n    previewing: 'Un problème est survenu lors de l’aperçu de ce document.',\n    problemUploadingFile: 'Il y a eu un problème lors du téléversement du fichier.',\n    restoringTitle:\n      'Il y a eu une erreur lors de la restauration de {{title}}. Veuillez vérifier votre connexion et réessayer.',\n    tokenInvalidOrExpired: 'Le jeton n’est soit pas valide ou a expiré.',\n    tokenNotProvided: 'Jeton non fourni.',\n    unableToCopy: 'Impossible de copier.',\n    unableToDeleteCount: 'Impossible de supprimer {{count}} sur {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Erreur lors de la réindexation de la collection {{collection}}. Opération annulée.',\n    unableToUpdateCount: 'Impossible de mettre à jour {{count}} sur {{total}} {{label}}.',\n    unauthorized: 'Non autorisé, vous devez être connecté pour effectuer cette demande.',\n    unauthorizedAdmin: 'Non autorisé, cet utilisateur n’a pas accès au panneau d’administration.',\n    unknown: 'Une erreur inconnue s’est produite.',\n    unPublishingDocument:\n      'Un problème est survenu lors de l’annulation de la publication de ce document.',\n    unspecific: 'Une erreur est survenue.',\n    unverifiedEmail: 'Veuillez vérifier votre e-mail avant de vous connecter.',\n    userEmailAlreadyRegistered: \"Un utilisateur avec l'email donné est déjà enregistré.\",\n    userLocked:\n      'Cet utilisateur est verrouillé en raison d’un trop grand nombre de tentatives de connexion infructueuses.',\n    usernameAlreadyRegistered:\n      \"Un utilisateur avec le nom d'utilisateur donné est déjà enregistré.\",\n    usernameOrPasswordIncorrect: \"Le nom d'utilisateur ou le mot de passe fourni est incorrect.\",\n    valueMustBeUnique: 'La valeur doit être unique',\n    verificationTokenInvalid: 'Le jeton de vérification n’est pas valide.',\n  },\n  fields: {\n    addLabel: 'Ajouter {{label}}',\n    addLink: 'Ajouter un Lien',\n    addNew: 'Ajouter nouveau ou nouvelle',\n    addNewLabel: 'Ajouter nouveau ou nouvelle {{label}}',\n    addRelationship: 'Ajouter une relation',\n    addUpload: 'Ajouter le téléchargement',\n    block: 'bloc',\n    blocks: 'blocs',\n    blockType: 'Type de bloc',\n    chooseBetweenCustomTextOrDocument:\n      'Choisissez entre saisir une URL personnalisée ou créer un lien vers un autre document.',\n    chooseDocumentToLink: 'Choisissez un document vers lequel établir un lien',\n    chooseFromExisting: 'Choisir parmi les existant(e)s',\n    chooseLabel: 'Choisir un(e) {{label}}',\n    collapseAll: 'Tout réduire',\n    customURL: 'URL personnalisée',\n    editLabelData: 'Modifier les données de ou du {{label}}',\n    editLink: 'Modifier le lien',\n    editRelationship: 'Modifier la relation',\n    enterURL: 'Entrez une URL',\n    internalLink: 'Lien interne',\n    itemsAndMore: '{{items}} et {{count}} de plus',\n    labelRelationship: 'Relation de ou du {{label}} ',\n    latitude: 'Latitude',\n    linkedTo: 'Lié à <0>{{label}}</0>',\n    linkType: 'Type de lien',\n    longitude: 'Longitude',\n    newLabel: 'Nouveau ou nouvelle {{label}}',\n    openInNewTab: 'Ouvrir dans un nouvel onglet',\n    passwordsDoNotMatch: 'Les mots de passe ne correspondent pas.',\n    relatedDocument: 'Document connexe',\n    relationTo: 'Lié à',\n    removeRelationship: 'Supprimer la relation',\n    removeUpload: 'Supprimer le téléversement',\n    saveChanges: 'Sauvegarder les modifications',\n    searchForBlock: 'Rechercher un bloc',\n    selectExistingLabel: 'Sélectionnez {{label}} existant',\n    selectFieldsToEdit: 'Sélectionnez les champs à modifier',\n    showAll: 'Afficher tout',\n    swapRelationship: 'Changer de relation',\n    swapUpload: 'Changer de Fichier',\n    textToDisplay: 'Texte à afficher',\n    toggleBlock: 'Bloc bascule',\n    uploadNewLabel: 'Téléverser un(e) nouveau ou nouvelle {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Parcourir par Dossier',\n    byFolder: 'Par Dossier',\n    deleteFolder: 'Supprimer le dossier',\n    folderName: 'Nom du dossier',\n    folders: 'Dossiers',\n    folderTypeDescription:\n      'Sélectionnez le type de documents de collection qui devraient être autorisés dans ce dossier.',\n    itemHasBeenMoved: '{{title}} a été déplacé vers {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} a été déplacé dans le dossier racine',\n    itemsMovedToFolder: '{{title}} déplacé vers {{folderName}}',\n    itemsMovedToRoot: '{{title}} déplacé vers le dossier racine',\n    moveFolder: 'Déplacer le dossier',\n    moveItemsToFolderConfirmation:\n      'Vous êtes sur le point de déplacer <1>{{count}} {{label}}</1> vers <2>{{toFolder}}</2>. Êtes-vous sûr ?',\n    moveItemsToRootConfirmation:\n      'Vous êtes sur le point de déplacer <1>{{count}} {{label}}</1> vers le dossier racine. Êtes-vous sûr ?',\n    moveItemToFolderConfirmation:\n      'Vous êtes sur le point de déplacer <1>{{title}}</1> dans <2>{{toFolder}}</2>. Êtes-vous sûr ?',\n    moveItemToRootConfirmation:\n      'Vous êtes sur le point de déplacer <1>{{title}}</1> vers le dossier racine. Êtes-vous sûr ?',\n    movingFromFolder: 'Déplacement de {{title}} de {{fromFolder}}',\n    newFolder: 'Nouveau Dossier',\n    noFolder: 'Pas de dossier',\n    renameFolder: 'Renommer le dossier',\n    searchByNameInFolder: 'Recherche par nom dans {{folderName}}',\n    selectFolderForItem: 'Sélectionnez le dossier pour {{title}}',\n  },\n  general: {\n    name: 'Nom',\n    aboutToDelete:\n      'Vous êtes sur le point de supprimer ce ou cette {{label}} <1>{{title}}</1>. Êtes-vous sûr ?',\n    aboutToDeleteCount_many: 'Vous êtes sur le point de supprimer {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Vous êtes sur le point de supprimer {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Vous êtes sur le point de supprimer {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Vous êtes sur le point de supprimer définitivement le {{label}} <1>{{title}}</1>. Êtes-vous sûr ?',\n    aboutToPermanentlyDeleteTrash:\n      'Vous êtes sur le point de supprimer définitivement <0>{{count}}</0> <1>{{label}}</1> de la corbeille. Êtes-vous sûr ?',\n    aboutToRestore:\n      'Vous êtes sur le point de restaurer le {{label}} <1>{{title}}</1>. Êtes-vous sûr ?',\n    aboutToRestoreAsDraft:\n      'Vous êtes sur le point de restaurer le {{label}} <1>{{title}}</1> en tant que brouillon. Êtes-vous sûr?',\n    aboutToRestoreAsDraftCount:\n      'Vous êtes sur le point de restaurer {{count}} {{label}} en tant que brouillon',\n    aboutToRestoreCount: 'Vous êtes sur le point de restaurer {{count}} {{label}}',\n    aboutToTrash:\n      'Vous êtes sur le point de déplacer le {{label}} <1>{{title}}</1> dans la corbeille. Êtes-vous sûr ?',\n    aboutToTrashCount: 'Vous êtes sur le point de déplacer {{count}} {{label}} à la corbeille',\n    addBelow: 'Ajoutez ci-dessous',\n    addFilter: 'Ajouter un filtre',\n    adminTheme: 'Thème d’administration',\n    all: 'Tout',\n    allCollections: 'Toutes les collections',\n    allLocales: 'Tous les paramètres régionaux',\n    and: 'Et',\n    anotherUser: 'Un autre utilisateur',\n    anotherUserTakenOver: 'Un autre utilisateur a pris en charge la modification de ce document.',\n    applyChanges: 'Appliquer les modifications',\n    ascending: 'Ascendant',\n    automatic: 'Automatique',\n    backToDashboard: 'Retour au tableau de bord',\n    cancel: 'Annuler',\n    changesNotSaved:\n      'Vos modifications n’ont pas été enregistrées. Vous perdrez vos modifications si vous quittez maintenant.',\n    clear: 'Clair',\n    clearAll: 'Tout effacer',\n    close: 'Fermer',\n    collapse: 'Réduire',\n    collections: 'Collections',\n    columns: 'Colonnes',\n    columnToSort: 'Colonne à trier',\n    confirm: 'Confirmer',\n    confirmCopy: 'Confirmer la copie',\n    confirmDeletion: 'Confirmer la suppression',\n    confirmDuplication: 'Confirmer la duplication',\n    confirmMove: 'Confirmez le déplacement',\n    confirmReindex: 'Réindexer toutes les {{collections}} ?',\n    confirmReindexAll: 'Réindexer toutes les collections ?',\n    confirmReindexDescription:\n      'Cela supprimera les index existants et réindexera les documents dans les collections {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Cela supprimera les index existants et réindexera les documents dans toutes les collections.',\n    confirmRestoration: 'Confirmer la restauration',\n    copied: 'Copié',\n    copy: 'Copie',\n    copyField: 'Copier le champ',\n    copying: 'Copie',\n    copyRow: 'Copier la ligne',\n    copyWarning:\n      \"Vous êtes sur le point d'écraser {{to}} avec {{from}} pour {{label}} {{title}}. Êtes-vous sûr ?\",\n    create: 'Créer',\n    created: 'Créé(e)',\n    createdAt: 'Créé(e) à',\n    createNew: 'Créer un(e) nouveau ou nouvelle',\n    createNewLabel: 'Créer un(e) nouveau ou nouvelle {{label}}',\n    creating: 'création en cours',\n    creatingNewLabel: 'Création d’un(e) nouveau ou nouvelle {{label}}',\n    currentlyEditing:\n      'est en train de modifier ce document. Si vous prenez le contrôle, ils seront bloqués pour continuer à modifier et pourraient également perdre les modifications non enregistrées.',\n    custom: 'Personnalisé',\n    dark: 'Sombre',\n    dashboard: 'Tableau de bord',\n    delete: 'Supprimer',\n    deleted: 'Supprimé',\n    deletedAt: 'Supprimé à',\n    deletedCountSuccessfully: '{{count}} {{label}} supprimé avec succès.',\n    deletedSuccessfully: 'Supprimé(e) avec succès.',\n    deletePermanently: 'Ignorer la corbeille et supprimer définitivement',\n    deleting: 'Suppression en cours...',\n    depth: 'Profondeur',\n    descending: 'Descendant(e)',\n    deselectAllRows: 'Désélectionner toutes les lignes',\n    document: 'Document',\n    documentIsTrashed: 'Ce {{label}} est mis à la corbeille et est en lecture seule.',\n    documentLocked: 'Document verrouillé',\n    documents: 'Documents',\n    duplicate: 'Dupliquer',\n    duplicateWithoutSaving: 'Dupliquer sans enregistrer les modifications',\n    edit: 'Éditer',\n    editAll: 'Modifier tout',\n    editedSince: 'Modifié depuis',\n    editing: 'Modification en cours',\n    editingLabel_many: 'Modification des {{count}} {{label}}',\n    editingLabel_one: 'Modification de {{count}} {{label}}',\n    editingLabel_other: 'Modification des {{count}} {{label}}',\n    editingTakenOver: 'Modification prise en charge',\n    editLabel: 'Modifier {{label}}',\n    email: 'E-mail',\n    emailAddress: 'Adresse e-mail',\n    emptyTrash: 'Vider la corbeille',\n    emptyTrashLabel: 'Vider la corbeille {{label}}',\n    enterAValue: 'Entrez une valeur',\n    error: 'Erreur',\n    errors: 'Erreurs',\n    exitLivePreview: \"Quittez l'aperçu en direct\",\n    export: 'Exportation',\n    fallbackToDefaultLocale: 'Retour à la locale par défaut',\n    false: 'Faux',\n    filter: 'Filtrer',\n    filters: 'Filtres',\n    filterWhere: 'Filtrer {{label}} où',\n    globals: 'Globals(es)',\n    goBack: 'Retourner',\n    groupByLabel: 'Regrouper par {{label}}',\n    import: 'Importation',\n    isEditing: 'est en train de modifier',\n    item: 'article',\n    items: 'articles',\n    language: 'Langue',\n    lastModified: 'Dernière modification',\n    leaveAnyway: 'Quitter quand même',\n    leaveWithoutSaving: 'Quitter sans sauvegarder',\n    light: 'Clair',\n    livePreview: 'Aperçu',\n    loading: 'Chargement en cours',\n    locale: 'Paramètres régionaux',\n    locales: 'Paramètres régionaux',\n    menu: 'Menu',\n    moreOptions: \"Plus d'options\",\n    move: 'Déplacez-vous',\n    moveConfirm:\n      'Vous êtes sur le point de déplacer {{count}} {{label}} vers <1>{{destination}}</1>. Êtes-vous sûr ?',\n    moveCount: 'Déplacez {{count}} {{label}}',\n    moveDown: 'Déplacer vers le bas',\n    moveUp: 'Déplacer vers le haut',\n    moving: 'Déménagement',\n    movingCount: 'Déplacement de {{count}} {{label}}',\n    newPassword: 'Nouveau mot de passe',\n    next: 'Prochain',\n    no: 'Non',\n    noDateSelected: 'Aucune date sélectionnée',\n    noFiltersSet: 'Aucun filtre défini',\n    noLabel: '<Pas de {{label}}>',\n    none: 'Aucun(e)',\n    noOptions: 'Aucune option',\n    noResults:\n      'Aucun(e) {{label}} trouvé(e). Soit aucun(e) {{label}} n’existe encore, soit aucun(e) ne correspond aux filtres que vous avez spécifiés ci-dessus',\n    notFound: 'Pas trouvé',\n    nothingFound: 'Rien n’a été trouvé',\n    noTrashResults: 'Aucun {{label}} dans la corbeille.',\n    noUpcomingEventsScheduled: 'Aucun événement à venir prévu.',\n    noValue: 'Aucune valeur',\n    of: 'de',\n    only: 'Seulement',\n    open: 'Ouvrir',\n    or: 'ou',\n    order: 'Ordre',\n    overwriteExistingData: 'Écraser les données existantes du champ',\n    pageNotFound: 'Page non trouvée',\n    password: 'Mot de passe',\n    pasteField: 'Coller le champ',\n    pasteRow: 'Coller la ligne',\n    payloadSettings: 'Paramètres de Payload',\n    permanentlyDelete: 'Supprimer définitivement',\n    permanentlyDeletedCountSuccessfully: 'Supprimé définitivement {{count}} {{label}} avec succès.',\n    perPage: 'Par Page: {{limit}}',\n    previous: 'Précédent',\n    reindex: 'Réindexer',\n    reindexingAll: 'Réindexation de toutes les {{collections}}.',\n    remove: 'Retirer',\n    rename: 'Renommer',\n    reset: 'Réinitialiser',\n    resetPreferences: 'Réinitialiser les préférences',\n    resetPreferencesDescription:\n      'Cela réinitialisera toutes vos préférences aux paramètres par défaut.',\n    resettingPreferences: 'Réinitialisation des préférences.',\n    restore: 'Restaurer',\n    restoreAsPublished: 'Restaurer en tant que version publiée',\n    restoredCountSuccessfully: '{{count}} {{label}} restauré avec succès.',\n    restoring: 'Restauration...',\n    row: 'Ligne',\n    rows: 'Lignes',\n    save: 'Sauvegarder',\n    saving: 'Sauvegarde en cours...',\n    schedulePublishFor: 'Programmer la publication pour {{titre}}',\n    searchBy: 'Rechercher par {{label}}',\n    select: 'Sélectionner',\n    selectAll: 'Tout sélectionner {{count}} {{label}}',\n    selectAllRows: 'Sélectionnez toutes les lignes',\n    selectedCount: '{{count}} {{label}} sélectionné',\n    selectLabel: 'Sélectionnez {{label}}',\n    selectValue: 'Sélectionnez une valeur',\n    showAllLabel: 'Afficher tous les {{label}}',\n    sorryNotFound: 'Désolé, rien ne correspond à votre demande.',\n    sort: 'Trier',\n    sortByLabelDirection: 'Trier par {{label}} {{direction}}',\n    stayOnThisPage: 'Rester sur cette page',\n    submissionSuccessful: 'Soumission réussie.',\n    submit: 'Soumettre',\n    submitting: 'Soumission...',\n    success: 'Succès',\n    successfullyCreated: '{{label}} créé(e) avec succès.',\n    successfullyDuplicated: '{{label}} dupliqué(e) avec succès.',\n    successfullyReindexed:\n      '{{count}} des {{total}} documents des collections {{collections}} ont été réindexés avec succès.',\n    takeOver: 'Prendre en charge',\n    thisLanguage: 'Français',\n    time: 'Temps',\n    timezone: 'Fuseau horaire',\n    titleDeleted: '{{label}} \"{{title}}\" supprimé(e) avec succès.',\n    titleRestored: '{{label}} \"{{title}}\" restauré avec succès.',\n    titleTrashed: '{{label}} \"{{title}}\" déplacé vers la corbeille.',\n    trash: 'Corbeille',\n    trashedCountSuccessfully: '{{count}} {{label}} déplacé à la corbeille.',\n    true: 'Vrai',\n    unauthorized: 'Non autorisé',\n    unsavedChanges:\n      'Vous avez des modifications non enregistrées. Enregistrez ou supprimez avant de continuer.',\n    unsavedChangesDuplicate:\n      'Vous avez des changements non enregistrés. Souhaitez-vous continuer la duplication ?',\n    untitled: 'Sans titre',\n    upcomingEvents: 'Événements à venir',\n    updatedAt: 'Modifié le',\n    updatedCountSuccessfully: '{{count}} {{label}} mis à jour avec succès.',\n    updatedLabelSuccessfully: '{{label}} mis à jour avec succès.',\n    updatedSuccessfully: 'Mis à jour avec succès.',\n    updateForEveryone: 'Mise à jour pour tout le monde',\n    updating: 'Mise à jour',\n    uploading: 'Téléchargement',\n    uploadingBulk: 'Téléchargement de {{current}} sur {{total}}',\n    user: 'Utilisateur',\n    username: \"Nom d'utilisateur\",\n    users: 'Utilisateurs',\n    value: 'Valeur',\n    viewing: 'Visualisation',\n    viewReadOnly: 'Afficher en lecture seule',\n    welcome: 'Bienvenue',\n    yes: 'Oui',\n  },\n  localization: {\n    cannotCopySameLocale: 'Impossible de copier dans le même endroit',\n    copyFrom: 'Copier de',\n    copyFromTo: 'Copier de {{from}} à {{to}}',\n    copyTo: 'Copier à',\n    copyToLocale: 'Copier vers le lieu',\n    localeToPublish: 'Locale à publier',\n    selectLocaleToCopy: 'Sélectionnez la locale à copier',\n  },\n  operators: {\n    contains: 'contient',\n    equals: 'est égal à',\n    exists: 'existe',\n    intersects: 'intersecte',\n    isGreaterThan: 'est supérieur à',\n    isGreaterThanOrEqualTo: 'est supérieur ou égal à',\n    isIn: 'est dans',\n    isLessThan: 'est inférieur à',\n    isLessThanOrEqualTo: 'est inférieur ou égal à',\n    isLike: 'est comme',\n    isNotEqualTo: 'n’est pas égal à',\n    isNotIn: 'n’est pas dans',\n    isNotLike: \"n'est pas comme\",\n    near: 'proche',\n    within: 'dans',\n  },\n  upload: {\n    addFile: 'Ajouter un fichier',\n    addFiles: 'Ajouter des fichiers',\n    bulkUpload: 'Téléchargement en masse',\n    crop: 'Recadrer',\n    cropToolDescription:\n      'Faites glisser les coins de la zone sélectionnée, dessinez une nouvelle zone ou ajustez les valeurs ci-dessous.',\n    download: 'Télécharger',\n    dragAndDrop: 'Glisser-déposer un fichier',\n    dragAndDropHere: 'ou glissez-déposez un fichier ici',\n    editImage: 'Modifier l’image',\n    fileName: 'Nom du fichier',\n    fileSize: 'Taille du fichier',\n    filesToUpload: 'Fichiers à télécharger',\n    fileToUpload: 'Fichier à télécharger',\n    focalPoint: 'Point focal',\n    focalPointDescription:\n      'Faites glisser le point focal directement sur l’aperçu ou ajustez les valeurs ci-dessous.',\n    height: 'Hauteur',\n    lessInfo: 'Moins d’infos',\n    moreInfo: 'Plus d’infos',\n    noFile: 'Aucun fichier',\n    pasteURL: \"Coller l'URL\",\n    previewSizes: 'Tailles d’aperçu',\n    selectCollectionToBrowse: 'Sélectionnez une collection à parcourir',\n    selectFile: 'Sélectionnez un fichier',\n    setCropArea: 'Définir la zone de recadrage',\n    setFocalPoint: 'Définir le point focal',\n    sizes: 'Tailles',\n    sizesFor: 'Tailles pour {{label}}',\n    width: 'Largeur',\n  },\n  validation: {\n    emailAddress: 'S’il vous plaît, veuillez entrer une adresse e-mail valide.',\n    enterNumber: 'S’il vous plait, veuillez entrer un nombre valide.',\n    fieldHasNo: 'Ce champ n’a pas de {{label}}',\n    greaterThanMax: '{{value}} est supérieur au max autorisé {{label}} de {{max}}.',\n    invalidInput: 'Ce champ a une entrée invalide.',\n    invalidSelection: 'Ce champ a une sélection invalide.',\n    invalidSelections: 'Ce champ contient les sélections invalides suivantes :',\n    lessThanMin: '{{value}} est inférieur au min autorisé {{label}} de {{min}}.',\n    limitReached: 'Limite atteinte, seulement {{max}} éléments peuvent être ajoutés.',\n    longerThanMin:\n      'Cette valeur doit être supérieure à la longueur minimale de {{minLength}} caractères.',\n    notValidDate: '\"{{value}}\" n’est pas une date valide.',\n    required: 'Ce champ est requis.',\n    requiresAtLeast: 'Ce champ doit avoir au moins {{count}} {{label}}.',\n    requiresNoMoreThan: 'Ce champ ne doit pas avoir plus de {{count}} {{label}}.',\n    requiresTwoNumbers: 'Ce champ doit avoir deux chiffres.',\n    shorterThanMax:\n      'Cette valeur doit être inférieure à la longueur maximale de {{maxLength}} caractères.',\n    timezoneRequired: 'Un fuseau horaire est requis.',\n    trueOrFalse: 'Ce champ ne peut être égal qu’à vrai ou faux.',\n    username:\n      \"Veuillez entrer un nom d'utilisateur valide. Il peut contenir des lettres, des chiffres, des tirets, des points et des tirets bas.\",\n    validUploadID: 'Ce champ n’est pas un valide identifiant de fichier.',\n  },\n  version: {\n    type: 'Type',\n    aboutToPublishSelection:\n      'Vous êtes sur le point de publier tous les {{label}} de la sélection. Êtes-vous sûr ?',\n    aboutToRestore:\n      'Vous êtes sur le point de restaurer le document {{label}} à l’état où il se trouvait le {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Vous êtes sur le point de restaurer le ou la {{label}} global(e) à l’état où il ou elle se trouvait le {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Vous êtes sur le point de rétablir les modifications apportées à ce document à la version publiée. Êtes-vous sûr ?',\n    aboutToUnpublish:\n      'Vous êtes sur le point d’annuler la publication de ce document. Êtes-vous sûr ?',\n    aboutToUnpublishSelection:\n      'Vous êtes sur le point de dépublier tous les {{label}} de la sélection. Êtes-vous sûr ?',\n    autosave: 'Enregistrement automatique',\n    autosavedSuccessfully: 'Enregistrement automatique réussi.',\n    autosavedVersion: 'Version enregistrée automatiquement',\n    changed: 'Modifié',\n    changedFieldsCount_one: '{{count}} champ modifié',\n    changedFieldsCount_other: '{{count}} champs modifiés',\n    compareVersion: 'Comparez cette version à :',\n    compareVersions: 'Comparer les versions',\n    comparingAgainst: 'En comparaison avec',\n    confirmPublish: 'Confirmer la publication',\n    confirmRevertToSaved: 'Confirmer la restauration',\n    confirmUnpublish: 'Confirmer l’annulation',\n    confirmVersionRestoration: 'Confirmer la restauration de la version',\n    currentDocumentStatus: 'Document {{docStatus}} actuel',\n    currentDraft: 'Projet actuel',\n    currentlyPublished: 'Actuellement publié',\n    currentlyViewing: 'Actuellement en train de regarder',\n    currentPublishedVersion: 'Version Publiée Actuelle',\n    draft: 'Brouillon',\n    draftSavedSuccessfully: 'Brouillon enregistré avec succès.',\n    lastSavedAgo: 'Dernière sauvegarde il y a {{distance}}',\n    modifiedOnly: 'Modifié uniquement',\n    moreVersions: 'Plus de versions...',\n    noFurtherVersionsFound: 'Aucune autre version trouvée',\n    noRowsFound: 'Aucun(e) {{label}} trouvé(e)',\n    noRowsSelected: 'Aucune {{étiquette}} sélectionnée',\n    preview: 'Aperçu',\n    previouslyDraft: 'Précédemment un Brouillon',\n    previouslyPublished: 'Précédemment publié',\n    previousVersion: 'Version Précédente',\n    problemRestoringVersion: 'Un problème est survenu lors de la restauration de cette version',\n    publish: 'Publier',\n    publishAllLocales: 'Publier toutes les localités',\n    publishChanges: 'Publier les modifications',\n    published: 'Publié',\n    publishIn: 'Publier en {{locale}}',\n    publishing: 'Publication',\n    restoreAsDraft: 'Restaurer comme brouillon',\n    restoredSuccessfully: 'Restauré(e) avec succès.',\n    restoreThisVersion: 'Restaurer cette version',\n    restoring: 'Restauration en cours...',\n    reverting: 'Republication en cours...',\n    revertToPublished: 'Republier',\n    saveDraft: 'Enregistrer le brouillon',\n    scheduledSuccessfully: 'Programmé avec succès.',\n    schedulePublish: 'Programmer la publication',\n    selectLocales: 'Sélectionnez les paramètres régionaux à afficher',\n    selectVersionToCompare: 'Sélectionnez une version à comparer',\n    showingVersionsFor: 'Affichage des versions pour :',\n    showLocales: 'Afficher les paramètres régionaux :',\n    specificVersion: 'Version spécifique',\n    status: 'Statut',\n    unpublish: 'Annuler la publication',\n    unpublishing: 'Annulation en cours...',\n    version: 'Version',\n    versionAgo: 'il y a {{distance}}',\n    versionCount_many: '{{count}} versions trouvées',\n    versionCount_none: 'Aucune version trouvée',\n    versionCount_one: '{{count}} version trouvée',\n    versionCount_other: '{{count}} versions trouvées',\n    versionCreatedOn: '{{version}} créé(e) le :',\n    versionID: 'Identifiant de la version',\n    versions: 'Versions',\n    viewingVersion: 'Affichage de la version de ou du {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Affichage de la version globale de ou du {{entityLabel}}',\n    viewingVersions: 'Affichage des versions de ou du {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Affichage des versions globales de ou du {{entityLabel}}',\n  },\n}\n\nexport const fr: Language = {\n  dateFNSKey: 'fr',\n  translations: frTranslations,\n}\n"], "names": ["frTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "fr", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UACE;QACFC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBACE;QACFC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YACE;QACFC,2BACE;QACFC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eACE;QACFC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBACE;QACFC,uBACE;QACFC,4BACE;QACFC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBACE;QACFC,yBACE;QACFC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBACE;QACFC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBACE;QACFC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}