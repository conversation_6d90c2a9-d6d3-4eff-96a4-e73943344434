export const frTranslations = {
    authentication: {
        account: 'Compte',
        accountOfCurrentUser: 'Compte de l’utilisateur actuel',
        accountVerified: 'Compte vérifié avec succès.',
        alreadyActivated: 'Déjà activé',
        alreadyLoggedIn: 'Déjà connecté',
        api<PERSON>ey: 'Clé API',
        authenticated: 'Authentifié',
        backToLogin: 'Retour à la connexion',
        beginCreateFirstUser: 'Pour commencer, créez votre premier utilisateur.',
        changePassword: 'Changer le mot de passe',
        checkYourEmailForPasswordReset: "Si l'adresse e-mail est associée à un compte, vous recevrez sous peu des instructions pour réinitialiser votre mot de passe. Veuillez vérifier votre dossier de courrier indésirable ou de spam si vous ne voyez pas l'e-mail dans votre boîte de réception.",
        confirmGeneration: 'Confirmer la génération',
        confirmPassword: 'Confirmez le mot de passe',
        createFirstUser: 'Créer le premier utilisateur',
        emailNotValid: 'L’adresse e-mail fournie n’est pas valide',
        emailOrUsername: "Email ou Nom d'utilisateur",
        emailSent: 'E-mail envoyé',
        emailVerified: 'E-mail vérifié avec succès.',
        enableAPIKey: 'Activer la clé API',
        failedToUnlock: 'Déverrouillage échoué',
        forceUnlock: 'Forcer le déverrouillage',
        forgotPassword: 'Mot de passe oublié',
        forgotPasswordEmailInstructions: 'Veuillez saisir votre e-mail ci-dessous. Vous recevrez un e-mail avec des instructions concernant comment réinitialiser votre mot de passe.',
        forgotPasswordQuestion: 'Mot de passe oublié ?',
        forgotPasswordUsernameInstructions: "Veuillez entrer votre nom d'utilisateur ci-dessous. Les instructions sur comment réinitialiser votre mot de passe seront envoyées à l'adresse e-mail associée à votre nom d'utilisateur.",
        generate: 'Générer',
        generateNewAPIKey: 'Générer une nouvelle clé API',
        generatingNewAPIKeyWillInvalidate: 'La génération d’une nouvelle clé API <1>invalidera</1> la clé précédente. Êtes-vous sûr de vouloir continuer ?',
        lockUntil: 'Verrouiller jusqu’à',
        logBackIn: 'Se reconnecter',
        loggedIn: 'Pour vous connecter en tant qu’un autre utilisateur, vous devez d’abord vous <0>déconnecter</0>.',
        loggedInChangePassword: 'Pour changer votre mot de passe, rendez-vous sur votre <0>compte</0> puis modifiez-y votre mot de passe.',
        loggedOutInactivity: 'Vous avez été déconnecté pour cause d’inactivité.',
        loggedOutSuccessfully: 'Vous avez été déconnecté avec succès.',
        loggingOut: 'Déconnexion...',
        login: 'Se connecter',
        loginAttempts: 'Tentatives de connexion',
        loginUser: 'Connecter l’utilisateur',
        loginWithAnotherUser: 'Pour vous connecter en tant qu’un autre utilisateur, vous devez d’abord vous <0>déconnecter</0>.',
        logOut: 'Se déconnecter',
        logout: 'Se déconnecter',
        logoutSuccessful: 'Déconnexion réussie.',
        logoutUser: 'Déconnecter l’utilisateur',
        newAccountCreated: 'Un nouveau compte vient d’être créé pour vous permettre d’accéder <a href="{{serverURL}}">{{serverURL}}</a>. Veuillez cliquer sur le lien suivant ou collez l’URL ci-dessous dans votre navigateur pour vérifier votre adresse e-mail: <a href="{{verificationURL}}">{{verificationURL}}</a><br>. Après avoir vérifié votre adresse e-mail, vous pourrez vous connecter avec succès.',
        newAPIKeyGenerated: 'Nouvelle clé API générée.',
        newPassword: 'Nouveau mot de passe',
        passed: 'Authentification réussie',
        passwordResetSuccessfully: 'Réinitialisation du mot de passe réussie.',
        resetPassword: 'Réinitialiser le mot de passe',
        resetPasswordExpiration: 'Réinitialiser l’expiration du mot de passe',
        resetPasswordToken: 'Réinitialiser le jeton de mot de passe',
        resetYourPassword: 'Réinitialisez votre mot de passe',
        stayLoggedIn: 'Rester connecté',
        successfullyRegisteredFirstUser: 'Premier utilisateur enregistré avec succès.',
        successfullyUnlocked: 'Déverrouillé avec succès',
        tokenRefreshSuccessful: 'Actualisation du jeton réussie.',
        unableToVerify: 'Vérification échouée',
        username: "Nom d'utilisateur",
        usernameNotValid: "Le nom d'utilisateur fourni n'est pas valide",
        verified: 'Vérifié',
        verifiedSuccessfully: 'Vérifié avec succès',
        verify: 'Vérifier',
        verifyUser: 'Vérifier l’utilisateur',
        verifyYourEmail: 'Vérifiez votre e-mail',
        youAreInactive: 'Vous n’avez pas été actif depuis un moment alors vous serez bientôt automatiquement déconnecté pour votre propre sécurité. Souhaitez-vous rester connecté ?',
        youAreReceivingResetPassword: 'Vous recevez ceci parce que vous (ou quelqu’un d’autre) avez demandé la réinitialisation du mot de passe de votre compte. Veuillez cliquer sur le lien suivant ou le coller dans votre navigateur pour terminer le processus :',
        youDidNotRequestPassword: 'Si vous ne l’avez pas demandé, veuillez ignorer cet e-mail et votre mot de passe restera inchangé.'
    },
    error: {
        accountAlreadyActivated: 'Ce compte a déjà été activé.',
        autosaving: 'Un problème est survenu lors de l’enregistrement automatique de ce document.',
        correctInvalidFields: 'Veuillez corriger les champs invalides.',
        deletingFile: 'Une erreur s’est produite lors de la suppression du fichier.',
        deletingTitle: 'Une erreur s’est produite lors de la suppression de {{title}}. Veuillez vérifier votre connexion puis réessayer.',
        documentNotFound: "Le document avec l'ID {{id}} n'a pas pu être trouvé. Il a peut-être été supprimé ou n'a jamais existé, ou vous n'avez peut-être pas accès à celui-ci.",
        emailOrPasswordIncorrect: 'L’adresse e-mail ou le mot de passe fourni est incorrect.',
        followingFieldsInvalid_one: 'Le champ suivant n’est pas valide :',
        followingFieldsInvalid_other: 'Les champs suivants ne sont pas valides :',
        incorrectCollection: 'Collection incorrecte',
        insufficientClipboardPermissions: 'Accès au presse-papiers refusé. Veuillez vérifier vos autorisations pour le presse-papiers.',
        invalidClipboardData: 'Données invalides dans le presse-papiers.',
        invalidFileType: 'Type de fichier invalide',
        invalidFileTypeValue: 'Type de fichier invalide : {{value}}',
        invalidRequestArgs: 'Arguments non valides dans la requête : {{args}}',
        loadingDocument: 'Un problème est survenu lors du chargement du document qui a pour identifiant {{id}}.',
        localesNotSaved_one: 'Le paramètre régional suivant n’a pas pu être enregistré :',
        localesNotSaved_other: 'Les paramètres régionaux suivants n’ont pas pu être enregistrés :',
        logoutFailed: 'La déconnexion a échouée.',
        missingEmail: 'E-mail manquant.',
        missingIDOfDocument: 'Il manque l’identifiant du document à mettre à jour.',
        missingIDOfVersion: 'Il manque l’identifiant de la version.',
        missingRequiredData: 'Données requises manquantes.',
        noFilesUploaded: 'Aucun fichier n’a été téléversé.',
        noMatchedField: 'Aucun champ correspondant n’a été trouvé pour "{{label}}"',
        notAllowedToAccessPage: 'Vous n’êtes pas autorisé à accéder à cette page.',
        notAllowedToPerformAction: 'Vous n’êtes pas autorisé à effectuer cette action.',
        notFound: 'La ressource demandée n’a pas été trouvée.',
        noUser: 'Aucun utilisateur',
        previewing: 'Un problème est survenu lors de l’aperçu de ce document.',
        problemUploadingFile: 'Il y a eu un problème lors du téléversement du fichier.',
        restoringTitle: 'Il y a eu une erreur lors de la restauration de {{title}}. Veuillez vérifier votre connexion et réessayer.',
        tokenInvalidOrExpired: 'Le jeton n’est soit pas valide ou a expiré.',
        tokenNotProvided: 'Jeton non fourni.',
        unableToCopy: 'Impossible de copier.',
        unableToDeleteCount: 'Impossible de supprimer {{count}} sur {{total}} {{label}}.',
        unableToReindexCollection: 'Erreur lors de la réindexation de la collection {{collection}}. Opération annulée.',
        unableToUpdateCount: 'Impossible de mettre à jour {{count}} sur {{total}} {{label}}.',
        unauthorized: 'Non autorisé, vous devez être connecté pour effectuer cette demande.',
        unauthorizedAdmin: 'Non autorisé, cet utilisateur n’a pas accès au panneau d’administration.',
        unknown: 'Une erreur inconnue s’est produite.',
        unPublishingDocument: 'Un problème est survenu lors de l’annulation de la publication de ce document.',
        unspecific: 'Une erreur est survenue.',
        unverifiedEmail: 'Veuillez vérifier votre e-mail avant de vous connecter.',
        userEmailAlreadyRegistered: "Un utilisateur avec l'email donné est déjà enregistré.",
        userLocked: 'Cet utilisateur est verrouillé en raison d’un trop grand nombre de tentatives de connexion infructueuses.',
        usernameAlreadyRegistered: "Un utilisateur avec le nom d'utilisateur donné est déjà enregistré.",
        usernameOrPasswordIncorrect: "Le nom d'utilisateur ou le mot de passe fourni est incorrect.",
        valueMustBeUnique: 'La valeur doit être unique',
        verificationTokenInvalid: 'Le jeton de vérification n’est pas valide.'
    },
    fields: {
        addLabel: 'Ajouter {{label}}',
        addLink: 'Ajouter un Lien',
        addNew: 'Ajouter nouveau ou nouvelle',
        addNewLabel: 'Ajouter nouveau ou nouvelle {{label}}',
        addRelationship: 'Ajouter une relation',
        addUpload: 'Ajouter le téléchargement',
        block: 'bloc',
        blocks: 'blocs',
        blockType: 'Type de bloc',
        chooseBetweenCustomTextOrDocument: 'Choisissez entre saisir une URL personnalisée ou créer un lien vers un autre document.',
        chooseDocumentToLink: 'Choisissez un document vers lequel établir un lien',
        chooseFromExisting: 'Choisir parmi les existant(e)s',
        chooseLabel: 'Choisir un(e) {{label}}',
        collapseAll: 'Tout réduire',
        customURL: 'URL personnalisée',
        editLabelData: 'Modifier les données de ou du {{label}}',
        editLink: 'Modifier le lien',
        editRelationship: 'Modifier la relation',
        enterURL: 'Entrez une URL',
        internalLink: 'Lien interne',
        itemsAndMore: '{{items}} et {{count}} de plus',
        labelRelationship: 'Relation de ou du {{label}} ',
        latitude: 'Latitude',
        linkedTo: 'Lié à <0>{{label}}</0>',
        linkType: 'Type de lien',
        longitude: 'Longitude',
        newLabel: 'Nouveau ou nouvelle {{label}}',
        openInNewTab: 'Ouvrir dans un nouvel onglet',
        passwordsDoNotMatch: 'Les mots de passe ne correspondent pas.',
        relatedDocument: 'Document connexe',
        relationTo: 'Lié à',
        removeRelationship: 'Supprimer la relation',
        removeUpload: 'Supprimer le téléversement',
        saveChanges: 'Sauvegarder les modifications',
        searchForBlock: 'Rechercher un bloc',
        selectExistingLabel: 'Sélectionnez {{label}} existant',
        selectFieldsToEdit: 'Sélectionnez les champs à modifier',
        showAll: 'Afficher tout',
        swapRelationship: 'Changer de relation',
        swapUpload: 'Changer de Fichier',
        textToDisplay: 'Texte à afficher',
        toggleBlock: 'Bloc bascule',
        uploadNewLabel: 'Téléverser un(e) nouveau ou nouvelle {{label}}'
    },
    folder: {
        browseByFolder: 'Parcourir par Dossier',
        byFolder: 'Par Dossier',
        deleteFolder: 'Supprimer le dossier',
        folderName: 'Nom du dossier',
        folders: 'Dossiers',
        folderTypeDescription: 'Sélectionnez le type de documents de collection qui devraient être autorisés dans ce dossier.',
        itemHasBeenMoved: '{{title}} a été déplacé vers {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} a été déplacé dans le dossier racine',
        itemsMovedToFolder: '{{title}} déplacé vers {{folderName}}',
        itemsMovedToRoot: '{{title}} déplacé vers le dossier racine',
        moveFolder: 'Déplacer le dossier',
        moveItemsToFolderConfirmation: 'Vous êtes sur le point de déplacer <1>{{count}} {{label}}</1> vers <2>{{toFolder}}</2>. Êtes-vous sûr ?',
        moveItemsToRootConfirmation: 'Vous êtes sur le point de déplacer <1>{{count}} {{label}}</1> vers le dossier racine. Êtes-vous sûr ?',
        moveItemToFolderConfirmation: 'Vous êtes sur le point de déplacer <1>{{title}}</1> dans <2>{{toFolder}}</2>. Êtes-vous sûr ?',
        moveItemToRootConfirmation: 'Vous êtes sur le point de déplacer <1>{{title}}</1> vers le dossier racine. Êtes-vous sûr ?',
        movingFromFolder: 'Déplacement de {{title}} de {{fromFolder}}',
        newFolder: 'Nouveau Dossier',
        noFolder: 'Pas de dossier',
        renameFolder: 'Renommer le dossier',
        searchByNameInFolder: 'Recherche par nom dans {{folderName}}',
        selectFolderForItem: 'Sélectionnez le dossier pour {{title}}'
    },
    general: {
        name: 'Nom',
        aboutToDelete: 'Vous êtes sur le point de supprimer ce ou cette {{label}} <1>{{title}}</1>. Êtes-vous sûr ?',
        aboutToDeleteCount_many: 'Vous êtes sur le point de supprimer {{count}} {{label}}',
        aboutToDeleteCount_one: 'Vous êtes sur le point de supprimer {{count}} {{label}}',
        aboutToDeleteCount_other: 'Vous êtes sur le point de supprimer {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Vous êtes sur le point de supprimer définitivement le {{label}} <1>{{title}}</1>. Êtes-vous sûr ?',
        aboutToPermanentlyDeleteTrash: 'Vous êtes sur le point de supprimer définitivement <0>{{count}}</0> <1>{{label}}</1> de la corbeille. Êtes-vous sûr ?',
        aboutToRestore: 'Vous êtes sur le point de restaurer le {{label}} <1>{{title}}</1>. Êtes-vous sûr ?',
        aboutToRestoreAsDraft: 'Vous êtes sur le point de restaurer le {{label}} <1>{{title}}</1> en tant que brouillon. Êtes-vous sûr?',
        aboutToRestoreAsDraftCount: 'Vous êtes sur le point de restaurer {{count}} {{label}} en tant que brouillon',
        aboutToRestoreCount: 'Vous êtes sur le point de restaurer {{count}} {{label}}',
        aboutToTrash: 'Vous êtes sur le point de déplacer le {{label}} <1>{{title}}</1> dans la corbeille. Êtes-vous sûr ?',
        aboutToTrashCount: 'Vous êtes sur le point de déplacer {{count}} {{label}} à la corbeille',
        addBelow: 'Ajoutez ci-dessous',
        addFilter: 'Ajouter un filtre',
        adminTheme: 'Thème d’administration',
        all: 'Tout',
        allCollections: 'Toutes les collections',
        allLocales: 'Tous les paramètres régionaux',
        and: 'Et',
        anotherUser: 'Un autre utilisateur',
        anotherUserTakenOver: 'Un autre utilisateur a pris en charge la modification de ce document.',
        applyChanges: 'Appliquer les modifications',
        ascending: 'Ascendant',
        automatic: 'Automatique',
        backToDashboard: 'Retour au tableau de bord',
        cancel: 'Annuler',
        changesNotSaved: 'Vos modifications n’ont pas été enregistrées. Vous perdrez vos modifications si vous quittez maintenant.',
        clear: 'Clair',
        clearAll: 'Tout effacer',
        close: 'Fermer',
        collapse: 'Réduire',
        collections: 'Collections',
        columns: 'Colonnes',
        columnToSort: 'Colonne à trier',
        confirm: 'Confirmer',
        confirmCopy: 'Confirmer la copie',
        confirmDeletion: 'Confirmer la suppression',
        confirmDuplication: 'Confirmer la duplication',
        confirmMove: 'Confirmez le déplacement',
        confirmReindex: 'Réindexer toutes les {{collections}} ?',
        confirmReindexAll: 'Réindexer toutes les collections ?',
        confirmReindexDescription: 'Cela supprimera les index existants et réindexera les documents dans les collections {{collections}}.',
        confirmReindexDescriptionAll: 'Cela supprimera les index existants et réindexera les documents dans toutes les collections.',
        confirmRestoration: 'Confirmer la restauration',
        copied: 'Copié',
        copy: 'Copie',
        copyField: 'Copier le champ',
        copying: 'Copie',
        copyRow: 'Copier la ligne',
        copyWarning: "Vous êtes sur le point d'écraser {{to}} avec {{from}} pour {{label}} {{title}}. Êtes-vous sûr ?",
        create: 'Créer',
        created: 'Créé(e)',
        createdAt: 'Créé(e) à',
        createNew: 'Créer un(e) nouveau ou nouvelle',
        createNewLabel: 'Créer un(e) nouveau ou nouvelle {{label}}',
        creating: 'création en cours',
        creatingNewLabel: 'Création d’un(e) nouveau ou nouvelle {{label}}',
        currentlyEditing: 'est en train de modifier ce document. Si vous prenez le contrôle, ils seront bloqués pour continuer à modifier et pourraient également perdre les modifications non enregistrées.',
        custom: 'Personnalisé',
        dark: 'Sombre',
        dashboard: 'Tableau de bord',
        delete: 'Supprimer',
        deleted: 'Supprimé',
        deletedAt: 'Supprimé à',
        deletedCountSuccessfully: '{{count}} {{label}} supprimé avec succès.',
        deletedSuccessfully: 'Supprimé(e) avec succès.',
        deletePermanently: 'Ignorer la corbeille et supprimer définitivement',
        deleting: 'Suppression en cours...',
        depth: 'Profondeur',
        descending: 'Descendant(e)',
        deselectAllRows: 'Désélectionner toutes les lignes',
        document: 'Document',
        documentIsTrashed: 'Ce {{label}} est mis à la corbeille et est en lecture seule.',
        documentLocked: 'Document verrouillé',
        documents: 'Documents',
        duplicate: 'Dupliquer',
        duplicateWithoutSaving: 'Dupliquer sans enregistrer les modifications',
        edit: 'Éditer',
        editAll: 'Modifier tout',
        editedSince: 'Modifié depuis',
        editing: 'Modification en cours',
        editingLabel_many: 'Modification des {{count}} {{label}}',
        editingLabel_one: 'Modification de {{count}} {{label}}',
        editingLabel_other: 'Modification des {{count}} {{label}}',
        editingTakenOver: 'Modification prise en charge',
        editLabel: 'Modifier {{label}}',
        email: 'E-mail',
        emailAddress: 'Adresse e-mail',
        emptyTrash: 'Vider la corbeille',
        emptyTrashLabel: 'Vider la corbeille {{label}}',
        enterAValue: 'Entrez une valeur',
        error: 'Erreur',
        errors: 'Erreurs',
        exitLivePreview: "Quittez l'aperçu en direct",
        export: 'Exportation',
        fallbackToDefaultLocale: 'Retour à la locale par défaut',
        false: 'Faux',
        filter: 'Filtrer',
        filters: 'Filtres',
        filterWhere: 'Filtrer {{label}} où',
        globals: 'Globals(es)',
        goBack: 'Retourner',
        groupByLabel: 'Regrouper par {{label}}',
        import: 'Importation',
        isEditing: 'est en train de modifier',
        item: 'article',
        items: 'articles',
        language: 'Langue',
        lastModified: 'Dernière modification',
        leaveAnyway: 'Quitter quand même',
        leaveWithoutSaving: 'Quitter sans sauvegarder',
        light: 'Clair',
        livePreview: 'Aperçu',
        loading: 'Chargement en cours',
        locale: 'Paramètres régionaux',
        locales: 'Paramètres régionaux',
        menu: 'Menu',
        moreOptions: "Plus d'options",
        move: 'Déplacez-vous',
        moveConfirm: 'Vous êtes sur le point de déplacer {{count}} {{label}} vers <1>{{destination}}</1>. Êtes-vous sûr ?',
        moveCount: 'Déplacez {{count}} {{label}}',
        moveDown: 'Déplacer vers le bas',
        moveUp: 'Déplacer vers le haut',
        moving: 'Déménagement',
        movingCount: 'Déplacement de {{count}} {{label}}',
        newPassword: 'Nouveau mot de passe',
        next: 'Prochain',
        no: 'Non',
        noDateSelected: 'Aucune date sélectionnée',
        noFiltersSet: 'Aucun filtre défini',
        noLabel: '<Pas de {{label}}>',
        none: 'Aucun(e)',
        noOptions: 'Aucune option',
        noResults: 'Aucun(e) {{label}} trouvé(e). Soit aucun(e) {{label}} n’existe encore, soit aucun(e) ne correspond aux filtres que vous avez spécifiés ci-dessus',
        notFound: 'Pas trouvé',
        nothingFound: 'Rien n’a été trouvé',
        noTrashResults: 'Aucun {{label}} dans la corbeille.',
        noUpcomingEventsScheduled: 'Aucun événement à venir prévu.',
        noValue: 'Aucune valeur',
        of: 'de',
        only: 'Seulement',
        open: 'Ouvrir',
        or: 'ou',
        order: 'Ordre',
        overwriteExistingData: 'Écraser les données existantes du champ',
        pageNotFound: 'Page non trouvée',
        password: 'Mot de passe',
        pasteField: 'Coller le champ',
        pasteRow: 'Coller la ligne',
        payloadSettings: 'Paramètres de Payload',
        permanentlyDelete: 'Supprimer définitivement',
        permanentlyDeletedCountSuccessfully: 'Supprimé définitivement {{count}} {{label}} avec succès.',
        perPage: 'Par Page: {{limit}}',
        previous: 'Précédent',
        reindex: 'Réindexer',
        reindexingAll: 'Réindexation de toutes les {{collections}}.',
        remove: 'Retirer',
        rename: 'Renommer',
        reset: 'Réinitialiser',
        resetPreferences: 'Réinitialiser les préférences',
        resetPreferencesDescription: 'Cela réinitialisera toutes vos préférences aux paramètres par défaut.',
        resettingPreferences: 'Réinitialisation des préférences.',
        restore: 'Restaurer',
        restoreAsPublished: 'Restaurer en tant que version publiée',
        restoredCountSuccessfully: '{{count}} {{label}} restauré avec succès.',
        restoring: 'Restauration...',
        row: 'Ligne',
        rows: 'Lignes',
        save: 'Sauvegarder',
        saving: 'Sauvegarde en cours...',
        schedulePublishFor: 'Programmer la publication pour {{titre}}',
        searchBy: 'Rechercher par {{label}}',
        select: 'Sélectionner',
        selectAll: 'Tout sélectionner {{count}} {{label}}',
        selectAllRows: 'Sélectionnez toutes les lignes',
        selectedCount: '{{count}} {{label}} sélectionné',
        selectLabel: 'Sélectionnez {{label}}',
        selectValue: 'Sélectionnez une valeur',
        showAllLabel: 'Afficher tous les {{label}}',
        sorryNotFound: 'Désolé, rien ne correspond à votre demande.',
        sort: 'Trier',
        sortByLabelDirection: 'Trier par {{label}} {{direction}}',
        stayOnThisPage: 'Rester sur cette page',
        submissionSuccessful: 'Soumission réussie.',
        submit: 'Soumettre',
        submitting: 'Soumission...',
        success: 'Succès',
        successfullyCreated: '{{label}} créé(e) avec succès.',
        successfullyDuplicated: '{{label}} dupliqué(e) avec succès.',
        successfullyReindexed: '{{count}} des {{total}} documents des collections {{collections}} ont été réindexés avec succès.',
        takeOver: 'Prendre en charge',
        thisLanguage: 'Français',
        time: 'Temps',
        timezone: 'Fuseau horaire',
        titleDeleted: '{{label}} "{{title}}" supprimé(e) avec succès.',
        titleRestored: '{{label}} "{{title}}" restauré avec succès.',
        titleTrashed: '{{label}} "{{title}}" déplacé vers la corbeille.',
        trash: 'Corbeille',
        trashedCountSuccessfully: '{{count}} {{label}} déplacé à la corbeille.',
        true: 'Vrai',
        unauthorized: 'Non autorisé',
        unsavedChanges: 'Vous avez des modifications non enregistrées. Enregistrez ou supprimez avant de continuer.',
        unsavedChangesDuplicate: 'Vous avez des changements non enregistrés. Souhaitez-vous continuer la duplication ?',
        untitled: 'Sans titre',
        upcomingEvents: 'Événements à venir',
        updatedAt: 'Modifié le',
        updatedCountSuccessfully: '{{count}} {{label}} mis à jour avec succès.',
        updatedLabelSuccessfully: '{{label}} mis à jour avec succès.',
        updatedSuccessfully: 'Mis à jour avec succès.',
        updateForEveryone: 'Mise à jour pour tout le monde',
        updating: 'Mise à jour',
        uploading: 'Téléchargement',
        uploadingBulk: 'Téléchargement de {{current}} sur {{total}}',
        user: 'Utilisateur',
        username: "Nom d'utilisateur",
        users: 'Utilisateurs',
        value: 'Valeur',
        viewing: 'Visualisation',
        viewReadOnly: 'Afficher en lecture seule',
        welcome: 'Bienvenue',
        yes: 'Oui'
    },
    localization: {
        cannotCopySameLocale: 'Impossible de copier dans le même endroit',
        copyFrom: 'Copier de',
        copyFromTo: 'Copier de {{from}} à {{to}}',
        copyTo: 'Copier à',
        copyToLocale: 'Copier vers le lieu',
        localeToPublish: 'Locale à publier',
        selectLocaleToCopy: 'Sélectionnez la locale à copier'
    },
    operators: {
        contains: 'contient',
        equals: 'est égal à',
        exists: 'existe',
        intersects: 'intersecte',
        isGreaterThan: 'est supérieur à',
        isGreaterThanOrEqualTo: 'est supérieur ou égal à',
        isIn: 'est dans',
        isLessThan: 'est inférieur à',
        isLessThanOrEqualTo: 'est inférieur ou égal à',
        isLike: 'est comme',
        isNotEqualTo: 'n’est pas égal à',
        isNotIn: 'n’est pas dans',
        isNotLike: "n'est pas comme",
        near: 'proche',
        within: 'dans'
    },
    upload: {
        addFile: 'Ajouter un fichier',
        addFiles: 'Ajouter des fichiers',
        bulkUpload: 'Téléchargement en masse',
        crop: 'Recadrer',
        cropToolDescription: 'Faites glisser les coins de la zone sélectionnée, dessinez une nouvelle zone ou ajustez les valeurs ci-dessous.',
        download: 'Télécharger',
        dragAndDrop: 'Glisser-déposer un fichier',
        dragAndDropHere: 'ou glissez-déposez un fichier ici',
        editImage: 'Modifier l’image',
        fileName: 'Nom du fichier',
        fileSize: 'Taille du fichier',
        filesToUpload: 'Fichiers à télécharger',
        fileToUpload: 'Fichier à télécharger',
        focalPoint: 'Point focal',
        focalPointDescription: 'Faites glisser le point focal directement sur l’aperçu ou ajustez les valeurs ci-dessous.',
        height: 'Hauteur',
        lessInfo: 'Moins d’infos',
        moreInfo: 'Plus d’infos',
        noFile: 'Aucun fichier',
        pasteURL: "Coller l'URL",
        previewSizes: 'Tailles d’aperçu',
        selectCollectionToBrowse: 'Sélectionnez une collection à parcourir',
        selectFile: 'Sélectionnez un fichier',
        setCropArea: 'Définir la zone de recadrage',
        setFocalPoint: 'Définir le point focal',
        sizes: 'Tailles',
        sizesFor: 'Tailles pour {{label}}',
        width: 'Largeur'
    },
    validation: {
        emailAddress: 'S’il vous plaît, veuillez entrer une adresse e-mail valide.',
        enterNumber: 'S’il vous plait, veuillez entrer un nombre valide.',
        fieldHasNo: 'Ce champ n’a pas de {{label}}',
        greaterThanMax: '{{value}} est supérieur au max autorisé {{label}} de {{max}}.',
        invalidInput: 'Ce champ a une entrée invalide.',
        invalidSelection: 'Ce champ a une sélection invalide.',
        invalidSelections: 'Ce champ contient les sélections invalides suivantes :',
        lessThanMin: '{{value}} est inférieur au min autorisé {{label}} de {{min}}.',
        limitReached: 'Limite atteinte, seulement {{max}} éléments peuvent être ajoutés.',
        longerThanMin: 'Cette valeur doit être supérieure à la longueur minimale de {{minLength}} caractères.',
        notValidDate: '"{{value}}" n’est pas une date valide.',
        required: 'Ce champ est requis.',
        requiresAtLeast: 'Ce champ doit avoir au moins {{count}} {{label}}.',
        requiresNoMoreThan: 'Ce champ ne doit pas avoir plus de {{count}} {{label}}.',
        requiresTwoNumbers: 'Ce champ doit avoir deux chiffres.',
        shorterThanMax: 'Cette valeur doit être inférieure à la longueur maximale de {{maxLength}} caractères.',
        timezoneRequired: 'Un fuseau horaire est requis.',
        trueOrFalse: 'Ce champ ne peut être égal qu’à vrai ou faux.',
        username: "Veuillez entrer un nom d'utilisateur valide. Il peut contenir des lettres, des chiffres, des tirets, des points et des tirets bas.",
        validUploadID: 'Ce champ n’est pas un valide identifiant de fichier.'
    },
    version: {
        type: 'Type',
        aboutToPublishSelection: 'Vous êtes sur le point de publier tous les {{label}} de la sélection. Êtes-vous sûr ?',
        aboutToRestore: 'Vous êtes sur le point de restaurer le document {{label}} à l’état où il se trouvait le {{versionDate}}.',
        aboutToRestoreGlobal: 'Vous êtes sur le point de restaurer le ou la {{label}} global(e) à l’état où il ou elle se trouvait le {{versionDate}}.',
        aboutToRevertToPublished: 'Vous êtes sur le point de rétablir les modifications apportées à ce document à la version publiée. Êtes-vous sûr ?',
        aboutToUnpublish: 'Vous êtes sur le point d’annuler la publication de ce document. Êtes-vous sûr ?',
        aboutToUnpublishSelection: 'Vous êtes sur le point de dépublier tous les {{label}} de la sélection. Êtes-vous sûr ?',
        autosave: 'Enregistrement automatique',
        autosavedSuccessfully: 'Enregistrement automatique réussi.',
        autosavedVersion: 'Version enregistrée automatiquement',
        changed: 'Modifié',
        changedFieldsCount_one: '{{count}} champ modifié',
        changedFieldsCount_other: '{{count}} champs modifiés',
        compareVersion: 'Comparez cette version à :',
        compareVersions: 'Comparer les versions',
        comparingAgainst: 'En comparaison avec',
        confirmPublish: 'Confirmer la publication',
        confirmRevertToSaved: 'Confirmer la restauration',
        confirmUnpublish: 'Confirmer l’annulation',
        confirmVersionRestoration: 'Confirmer la restauration de la version',
        currentDocumentStatus: 'Document {{docStatus}} actuel',
        currentDraft: 'Projet actuel',
        currentlyPublished: 'Actuellement publié',
        currentlyViewing: 'Actuellement en train de regarder',
        currentPublishedVersion: 'Version Publiée Actuelle',
        draft: 'Brouillon',
        draftSavedSuccessfully: 'Brouillon enregistré avec succès.',
        lastSavedAgo: 'Dernière sauvegarde il y a {{distance}}',
        modifiedOnly: 'Modifié uniquement',
        moreVersions: 'Plus de versions...',
        noFurtherVersionsFound: 'Aucune autre version trouvée',
        noRowsFound: 'Aucun(e) {{label}} trouvé(e)',
        noRowsSelected: 'Aucune {{étiquette}} sélectionnée',
        preview: 'Aperçu',
        previouslyDraft: 'Précédemment un Brouillon',
        previouslyPublished: 'Précédemment publié',
        previousVersion: 'Version Précédente',
        problemRestoringVersion: 'Un problème est survenu lors de la restauration de cette version',
        publish: 'Publier',
        publishAllLocales: 'Publier toutes les localités',
        publishChanges: 'Publier les modifications',
        published: 'Publié',
        publishIn: 'Publier en {{locale}}',
        publishing: 'Publication',
        restoreAsDraft: 'Restaurer comme brouillon',
        restoredSuccessfully: 'Restauré(e) avec succès.',
        restoreThisVersion: 'Restaurer cette version',
        restoring: 'Restauration en cours...',
        reverting: 'Republication en cours...',
        revertToPublished: 'Republier',
        saveDraft: 'Enregistrer le brouillon',
        scheduledSuccessfully: 'Programmé avec succès.',
        schedulePublish: 'Programmer la publication',
        selectLocales: 'Sélectionnez les paramètres régionaux à afficher',
        selectVersionToCompare: 'Sélectionnez une version à comparer',
        showingVersionsFor: 'Affichage des versions pour :',
        showLocales: 'Afficher les paramètres régionaux :',
        specificVersion: 'Version spécifique',
        status: 'Statut',
        unpublish: 'Annuler la publication',
        unpublishing: 'Annulation en cours...',
        version: 'Version',
        versionAgo: 'il y a {{distance}}',
        versionCount_many: '{{count}} versions trouvées',
        versionCount_none: 'Aucune version trouvée',
        versionCount_one: '{{count}} version trouvée',
        versionCount_other: '{{count}} versions trouvées',
        versionCreatedOn: '{{version}} créé(e) le :',
        versionID: 'Identifiant de la version',
        versions: 'Versions',
        viewingVersion: 'Affichage de la version de ou du {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Affichage de la version globale de ou du {{entityLabel}}',
        viewingVersions: 'Affichage des versions de ou du {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Affichage des versions globales de ou du {{entityLabel}}'
    }
};
export const fr = {
    dateFNSKey: 'fr',
    translations: frTranslations
};

//# sourceMappingURL=fr.js.map