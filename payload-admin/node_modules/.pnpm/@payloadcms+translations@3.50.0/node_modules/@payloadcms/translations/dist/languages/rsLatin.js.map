{"version": 3, "sources": ["../../src/languages/rsLatin.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const rsLatinTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Nalog',\n    accountOfCurrentUser: 'Nalog trenutnog korisnika',\n    accountVerified: 'Nalog je uspešno verifikovan.',\n    alreadyActivated: 'Već aktivirano',\n    alreadyLoggedIn: 'Već prijavljen',\n    apiKey: 'API ključ',\n    authenticated: 'Autentifikovan',\n    backToLogin: 'Nazad na prijavu',\n    beginCreateFirstUser: 'Na samom početku kreiraj svog prvog korisnika',\n    changePassword: 'Promeni lozinku',\n    checkYourEmailForPasswordReset:\n      'Ako je e-mail adresa povezana sa nekim nalozima, uskoro ćete dobiti uputstva za resetovanje lozinke. Ako ne vidite e-mail u svojoj prijemnoj pošti, proverite spam ili folder za neželjenu poštu.',\n    confirmGeneration: 'Potvrdi kreiranje',\n    confirmPassword: 'Potvrdi lozinku',\n    createFirstUser: '<PERSON>reiraj prvog korisnika',\n    emailNotValid: 'Adresa e-pošte nije validna',\n    emailOrUsername: 'Email ili Korisničko ime',\n    emailSent: 'Poruka e-pošte prosleđena',\n    emailVerified: 'E-pošta je uspešno verifikovana.',\n    enableAPIKey: 'Omogući API ključ',\n    failedToUnlock: 'Neuspešno otključavanje.',\n    forceUnlock: 'Prinudno otključaj',\n    forgotPassword: 'Zaboravljena lozinka',\n    forgotPasswordEmailInstructions:\n      'Molimo Vas da unesete svoj adresu e-pošte. Primićete poruku sa uputstvom za ponovno postavljanje lozinke.',\n    forgotPasswordQuestion: 'Zaboravljena lozinka?',\n    forgotPasswordUsernameInstructions:\n      'Molimo unesite vaše korisničko ime ispod. Instrukcije za resetovanje vaše lozinke biće poslate na email adresu povezanu sa vašim korisničkim imenom.',\n    generate: 'Generiši',\n    generateNewAPIKey: 'Generiši novi API ključ',\n    generatingNewAPIKeyWillInvalidate:\n      'Generisanje novog API ključa će <1>poništiti</1> prethodni ključ. Da li ste sigurni da želite nastaviti?',\n    lockUntil: 'Zaključaj dok',\n    logBackIn: 'Ponovna prijava',\n    loggedIn: 'Za prijavu sa drugim korisničkim nalogom potrebno je prvo <0>odjaviti se</0>',\n    loggedInChangePassword:\n      'Da biste promenili lozinku, otvorite svoj <0>nalog</0> i promenite lozinku.',\n    loggedOutInactivity: 'Odjavljeni se zbog neaktivnosti.',\n    loggedOutSuccessfully: 'Uspešno ste odjavljeni',\n    loggingOut: 'Odjavljivanje...',\n    login: 'Prijava',\n    loginAttempts: 'Pokušaji prijave',\n    loginUser: 'Prijava korisnika',\n    loginWithAnotherUser:\n      'Za prijavu sa drugim korisničkim nalogom potrebno je prvo <0>odjaviti se</0>',\n    logOut: 'Odjava',\n    logout: 'Odjava',\n    logoutSuccessful: 'Uspešno ste se odjavili.',\n    logoutUser: 'Odjava korisnika',\n    newAccountCreated:\n      'Novi nalog je kreiran. Pristupite nalogu klikom na <a href=\"{{serverURL}}\">{{serverURL}}</a>. Molimo Vas kliknite na sledeći link ili zalepite URL koji se nalazi ispod u pretraživač da biste potvrdili adresu e-pošte: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Nakon što potvrdite adresu e-pošte možete se ulogovati.',\n    newAPIKeyGenerated: 'Novi API ključ generisan.',\n    newPassword: 'Nova lozinka',\n    passed: 'Autentifikacija prošla',\n    passwordResetSuccessfully: 'Lozinka uspešno resetovana.',\n    resetPassword: 'Promena lozinke',\n    resetPasswordExpiration: 'Promena roka trajanja lozinke',\n    resetPasswordToken: 'Promena lozinke tokena',\n    resetYourPassword: 'Promeni svoju lozinku',\n    stayLoggedIn: 'Ostani prijavljen',\n    successfullyRegisteredFirstUser: 'Uspešno registrovan prvi korisnik.',\n    successfullyUnlocked: 'Uspešno otključano',\n    tokenRefreshSuccessful: 'Osvežavanje tokena je uspelo.',\n    unableToVerify: 'Nije moguće potvrditi',\n    username: 'Korisničko ime',\n    usernameNotValid: 'Uneseno korisničko ime nije validno.',\n    verified: 'Potvrđeno',\n    verifiedSuccessfully: 'Uspešno potvrđeno',\n    verify: 'Potvrdi',\n    verifyUser: 'Potvrdi korisnika',\n    verifyYourEmail: 'Potvrdi svoju adresu e-pošte',\n    youAreInactive:\n      'Neaktivni ste već neko vreme i uskoro ćete biti automatski odjavljeni zbog sigurnosti. Da li želite ostati prijavljeni?',\n    youAreReceivingResetPassword:\n      'Primili ste ovo pošto ste Vi (ili neko u vaše ime) zatražili promenu lozinke naloga. Molimo Vas kliknite na link ili zalepite URL u svoj pretraživač da biste završili proces:',\n    youDidNotRequestPassword:\n      'Ako niste zatražili promenu lozinke ignorišite ovu poruku i lozinka će ostati nepromenjena.',\n  },\n  error: {\n    accountAlreadyActivated: 'Ovaj nalog je već aktiviran.',\n    autosaving: 'Nastao je problem pri automatskom čuvanju ovog dokumenta.',\n    correctInvalidFields: 'Molimo ispravite nevalidna polja.',\n    deletingFile: 'Dogodila se greška pri brisanju datoteke.',\n    deletingTitle:\n      'Dogodila se greška pri brisanju {{title}}. Proverite internet konekciju i pokušajte ponovo.',\n    documentNotFound:\n      'Dokument sa ID {{id}} nije mogao biti pronađen. Moguće je da je obrisan ili nikad nije postojao, ili možda nemate pristup njemu.',\n    emailOrPasswordIncorrect: 'Adresa e-pošte ili lozinka su neispravni.',\n    followingFieldsInvalid_one: 'Ovo polje je nevalidno:',\n    followingFieldsInvalid_other: 'Ova polja su nevalidna:',\n    incorrectCollection: 'Nevalidna kolekcija',\n    insufficientClipboardPermissions:\n      'Pristup clipboard-u odbijen. Proverite svoja dopuštenja za clipboard.',\n    invalidClipboardData: 'Nevažeći podaci u clipboard-u.',\n    invalidFileType: 'Nevalidan tip datoteke',\n    invalidFileTypeValue: 'Nevalidan tip datoteke: {{value}}',\n    invalidRequestArgs: 'Nevažeći argumenti prosleđeni u zahtevu: {{args}}',\n    loadingDocument: 'Postoji problem pri učitavanju dokumenta čiji je ID {{id}}.',\n    localesNotSaved_one: 'Nije moglo da se sačuva sledeće lokalno podešavanje:',\n    localesNotSaved_other: 'Nisu mogla da se sačuvaju sledeća lokalna podešavanja:',\n    logoutFailed: 'Odjava nije uspela.',\n    missingEmail: 'Nedostaje adresa e-pošte.',\n    missingIDOfDocument: 'Nedostaje ID dokumenta da bi se ažurirao.',\n    missingIDOfVersion: 'Nedostaje ID verzije.',\n    missingRequiredData: 'Nedostaju obavezni podaci.',\n    noFilesUploaded: 'Nijedna datoteka nije učitana.',\n    noMatchedField: 'Nema podudarajućih polja za \"{{label}}\"',\n    notAllowedToAccessPage: 'Nemate dozvolu za pristup ovoj stranici.',\n    notAllowedToPerformAction: 'Nemate dozvolu za izvršenje ove radnje.',\n    notFound: 'Traženi resurs nije pronađen.',\n    noUser: 'Nema korisnika',\n    previewing: 'Postoji problem pri pregledu ovog dokumenta.',\n    problemUploadingFile: 'Postoji problem pri učitavanju datoteke.',\n    restoringTitle:\n      'Došlo je do greške prilikom vraćanja {{title}}. Molimo vas da proverite svoju vezu i pokušate ponovo.',\n    tokenInvalidOrExpired: 'Token je nevalidan ili je istekao.',\n    tokenNotProvided: 'Token nije obezbeđen.',\n    unableToCopy: 'Kopiranje nije moguće.',\n    unableToDeleteCount: 'Nije moguće izbrisati {{count}} od {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Greška pri reindeksiranju kolekcije {{collection}}. Operacija je prekinuta.',\n    unableToUpdateCount: 'Nije moguće ažurirati {{count}} od {{total}} {{label}}.',\n    unauthorized: 'Niste autorizovani da biste uputili ovaj zahtev.',\n    unauthorizedAdmin: 'Nemate pristup administratorskom panelu.',\n    unknown: 'Došlo je do nepoznate greške.',\n    unPublishingDocument: 'Postoji problem pri poništavanju objave ovog dokumenta.',\n    unspecific: 'Došlo je do greške.',\n    unverifiedEmail: 'Molimo vas da verifikujete svoju e-poštu pre prijave.',\n    userEmailAlreadyRegistered: 'Korisnik sa datom imejl adresom je već registrovan.',\n    userLocked: 'Ovaj korisnik je zaključan zbog prevelikog broja neuspešnih pokušaja prijave.',\n    usernameAlreadyRegistered: 'Korisnik sa datim korisničkim imenom je već registrovan.',\n    usernameOrPasswordIncorrect: 'Korisničko ime ili lozinka koju ste uneli su netačni.',\n    valueMustBeUnique: 'Vrednost mora biti jedinstvena.',\n    verificationTokenInvalid: 'Verifikacioni token je nevalidan.',\n  },\n  fields: {\n    addLabel: 'Dodaj {{label}}',\n    addLink: 'Dodaj link',\n    addNew: 'Dodaj novi',\n    addNewLabel: 'Dodaj novi {{label}}',\n    addRelationship: 'Dodaj relaciju',\n    addUpload: 'Dodaj učitavanje',\n    block: 'blokiranje',\n    blocks: 'blokiranja',\n    blockType: 'Vrsta blokiranja',\n    chooseBetweenCustomTextOrDocument:\n      'Izaberite između unosa prilagođenog teksta URL ili linka na drugi dokument.',\n    chooseDocumentToLink: 'Odaberite dokument koji želite linkovati.',\n    chooseFromExisting: 'Odaberite iz postojećih.',\n    chooseLabel: 'Odaberite {{label}}',\n    collapseAll: 'Skupi sve',\n    customURL: 'Prilagođeni URL',\n    editLabelData: 'Izmeni {{label}} podatke',\n    editLink: 'Izmeni link',\n    editRelationship: 'Izmeni odnos',\n    enterURL: 'Unesi URL',\n    internalLink: 'Interni link',\n    itemsAndMore: '{{items}} i {{count}} više',\n    labelRelationship: '{{label}} veza',\n    latitude: 'Geografska širina',\n    linkedTo: 'Povezani sa <0>{{label}}</0>',\n    linkType: 'Tip linka',\n    longitude: 'Geografska dužina',\n    newLabel: 'Novo {{label}}',\n    openInNewTab: 'Otvori u novoj kartici.',\n    passwordsDoNotMatch: 'Lozinke nisu iste.',\n    relatedDocument: 'Povezani dokument',\n    relationTo: 'Veza sa',\n    removeRelationship: 'Ukloni vezu',\n    removeUpload: 'Ukloni prenos',\n    saveChanges: 'Sačuvaj promene',\n    searchForBlock: 'Pretraži blok',\n    selectExistingLabel: 'Odaberi postojeću {{label}}',\n    selectFieldsToEdit: 'Odaberite polja za promenu',\n    showAll: 'Pokaži sve',\n    swapRelationship: 'Zameni vezu',\n    swapUpload: 'Zameni prenos',\n    textToDisplay: 'Tekst za prikaz',\n    toggleBlock: 'Prebaci blok',\n    uploadNewLabel: 'Učitaj novi {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Pregledaj po Folderu',\n    byFolder: 'Po fascikli',\n    deleteFolder: 'Obriši mapu',\n    folderName: 'Naziv fascikle',\n    folders: 'Fascikle',\n    folderTypeDescription:\n      'Odaberite koja vrsta dokumenta iz kolekcije bi trebala biti dozvoljena u ovoj fascikli.',\n    itemHasBeenMoved: '{{title}} je premesten u {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} je premešten u osnovnu fasciklu',\n    itemsMovedToFolder: '{{title}} premešteno u {{folderName}}',\n    itemsMovedToRoot: '{{title}} je premješten u glavnu fasciklu',\n    moveFolder: 'Premesti folder',\n    moveItemsToFolderConfirmation:\n      'Na korak ste da premestite <1>{{count}} {{label}}</1> u <2>{{toFolder}}</2>. Da li ste sigurni?',\n    moveItemsToRootConfirmation:\n      'Uskoro ćete premestiti <1>{{count}} {{label}}</1> u koreni direktorijum. Da li ste sigurni?',\n    moveItemToFolderConfirmation:\n      'Upravo ćete premestiti <1>{{title}}</1> u <2>{{toFolder}}</2>. Da li ste sigurni?',\n    moveItemToRootConfirmation:\n      'Upravo ćete premestiti <1>{{title}}</1> u glavnu fasciklu. Da li ste sigurni?',\n    movingFromFolder: 'Premestanje {{title}} iz {{fromFolder}}',\n    newFolder: 'Novi Folder',\n    noFolder: 'Nema foldera',\n    renameFolder: 'Preimenuj folder',\n    searchByNameInFolder: 'Pretraga po imenu u {{folderName}}',\n    selectFolderForItem: 'Izaberite fasciklu za {{title}}',\n  },\n  general: {\n    name: 'Ime',\n    aboutToDelete: 'Izbrisaćete {{label}} <1>{{title}}</1>. Da li ste sigurni?',\n    aboutToDeleteCount_many: 'Izbrisaćete {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Izbrisaćete {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Izbrisaćete {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Na korak ste da trajno izbrišete {{label}} <1>{{title}}</1>. Da li ste sigurni?',\n    aboutToPermanentlyDeleteTrash:\n      'Na korak ste da trajno obrišete <0>{{count}}</0> <1>{{label}}</1> iz otpada. Da li ste sigurni?',\n    aboutToRestore: 'Na korak ste da vratite {{label}} <1>{{title}}</1>. Da li ste sigurni?',\n    aboutToRestoreAsDraft:\n      'Uskoro ćete obnoviti {{label}} <1>{{title}}</1> kao skicu. Da li ste sigurni?',\n    aboutToRestoreAsDraftCount: 'Uskoro ćete vratiti {{count}} {{label}} kao nacrt',\n    aboutToRestoreCount: 'Uskoro ćete obnoviti {{count}} {{label}}',\n    aboutToTrash:\n      'Na korak ste da premestite {{label}} <1>{{title}}</1> u otpad. Da li ste sigurni?',\n    aboutToTrashCount: 'Upravo ćete prebaciti {{count}} {{label}} u smeće',\n    addBelow: 'Dodaj ispod',\n    addFilter: 'Dodaj filter',\n    adminTheme: 'Administratorska tema',\n    all: 'Svi',\n    allCollections: 'Sve Kolekcije',\n    allLocales: 'Sve lokacije',\n    and: 'I',\n    anotherUser: 'Drugi korisnik',\n    anotherUserTakenOver: 'Drugi korisnik je preuzeo uređivanje ovog dokumenta.',\n    applyChanges: 'Primeni promene',\n    ascending: 'Uzlazno',\n    automatic: 'Automatsko',\n    backToDashboard: 'Nazad na kontrolni panel',\n    cancel: 'Otkaži',\n    changesNotSaved: 'Vaše promene nisu sačuvane. Ako izađete sada, izgubićete promene.',\n    clear: 'Jasno',\n    clearAll: 'Očisti sve',\n    close: 'Zatvori',\n    collapse: 'Skupi',\n    collections: 'Kolekcije',\n    columns: 'Kolone',\n    columnToSort: 'Kolona za sortiranje',\n    confirm: 'Potvrdi',\n    confirmCopy: 'Potvrdi kopiju',\n    confirmDeletion: 'Potvrdi brisanje',\n    confirmDuplication: 'Potvrdi duplikaciju',\n    confirmMove: 'Potvrdi pomeranje',\n    confirmReindex: 'Ponovo indeksirati sve {{collections}}?',\n    confirmReindexAll: 'Ponovo indeksirati sve kolekcije?',\n    confirmReindexDescription:\n      'Ovo će ukloniti postojeće indekse i ponovo indeksirati dokumente u kolekcijama {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Ovo će ukloniti postojeće indekse i ponovo indeksirati dokumente u svim kolekcijama.',\n    confirmRestoration: 'Potvrdite obnovu',\n    copied: 'Kopirano',\n    copy: 'Kopiraj',\n    copyField: 'Kopiraj polje',\n    copying: 'Kopiranje',\n    copyRow: 'Kopiraj red',\n    copyWarning:\n      'Na korak ste da prepišete {{to}} sa {{from}} za {{label}} {{title}}. Da li ste sigurni?',\n    create: 'Kreiraj',\n    created: 'Kreirano',\n    createdAt: 'Kreirano u',\n    createNew: 'Kreiraj novo',\n    createNewLabel: 'Kreiraj novo {{label}}',\n    creating: 'Kreira se',\n    creatingNewLabel: 'Kreiranje novog {{label}}',\n    currentlyEditing:\n      'trenutno uređuje ovaj dokument. Ako preuzmete kontrolu, biće blokirani da nastave sa uređivanjem i mogu izgubiti nesačuvane izmene.',\n    custom: 'Prilagođen',\n    dark: 'Tamno',\n    dashboard: 'Kontrolni panel',\n    delete: 'Obriši',\n    deleted: 'Obrisano',\n    deletedAt: 'Obrisano U',\n    deletedCountSuccessfully: 'Uspešno izbrisano {{count}} {{label}}.',\n    deletedSuccessfully: 'Uspešno izbrisano.',\n    deletePermanently: 'Preskoči kantu za smeće i trajno izbriši',\n    deleting: 'Brisanje...',\n    depth: 'Dubina',\n    descending: 'Opadajuće',\n    deselectAllRows: 'Deselektujte sve redove',\n    document: 'Dokument',\n    documentIsTrashed: 'Ova {{label}} je odbačena i može se samo čitati.',\n    documentLocked: 'Dokument je zaključan',\n    documents: 'Dokumenti',\n    duplicate: 'Duplikat',\n    duplicateWithoutSaving: 'Ponovi bez čuvanja promena',\n    edit: 'Uredi',\n    editAll: 'Uredi sve',\n    editedSince: 'Izmenjeno od',\n    editing: 'Uređivanje',\n    editingLabel_many: 'Uređivanje {{count}} {{label}}',\n    editingLabel_one: 'Uređivanje {{count}} {{label}}',\n    editingLabel_other: 'Uređivanje {{count}} {{label}}',\n    editingTakenOver: 'Uređivanje preuzeto',\n    editLabel: 'Uredi {{label}}',\n    email: 'E-pošta',\n    emailAddress: 'Аdresa e-pošte',\n    emptyTrash: 'Isprazni otpad',\n    emptyTrashLabel: 'Isprazni {{label}} korpu za smeće',\n    enterAValue: 'Unesi vrednost',\n    error: 'Greška',\n    errors: 'Greške',\n    exitLivePreview: 'Izađite iz Live pregleda',\n    export: 'Izvoz',\n    fallbackToDefaultLocale: 'Vraćanje na zadati jezik',\n    false: 'Lažno',\n    filter: 'Filter',\n    filters: 'Filteri',\n    filterWhere: 'Filter {{label}} gde',\n    globals: 'Globali',\n    goBack: 'Vrati se',\n    groupByLabel: 'Grupiši po {{label}}',\n    import: 'Uvoz',\n    isEditing: 'uređuje',\n    item: 'stavka',\n    items: 'stavke',\n    language: 'Jezik',\n    lastModified: 'Zadnja promena',\n    leaveAnyway: 'Svejedno napusti',\n    leaveWithoutSaving: 'Napusti bez čuvanja',\n    light: 'Svetlo',\n    livePreview: 'Pregled',\n    loading: 'Učitavanje',\n    locale: 'Jezik',\n    locales: 'Prevodi',\n    menu: 'Meni',\n    moreOptions: 'Više opcija',\n    move: 'Pomeri',\n    moveConfirm:\n      'Uskoro ćete premestiti {{count}} {{label}} u <1>{{destination}}</1>. Da li ste sigurni?',\n    moveCount: 'Pomeri {{count}} {{label}}',\n    moveDown: 'Pomeri dole',\n    moveUp: 'Pomeri gore',\n    moving: 'Pomeranje',\n    movingCount: 'Pomeranje {{count}} {{label}}',\n    newPassword: 'Nova lozinka',\n    next: 'Sledeći',\n    no: 'Ne',\n    noDateSelected: 'Nijedan datum nije odabran',\n    noFiltersSet: 'Nema postavljenih filtera',\n    noLabel: '<Nema {{label}}>',\n    none: 'Nijedan',\n    noOptions: 'Nema opcija',\n    noResults:\n      'Nema pronađenih {{label}}. Moguće da {{label}} još uvek ne postoji ili nema rezultata u skladu sa postavljenim filterima.',\n    notFound: 'Nije pronađeno',\n    nothingFound: 'Ništa nije pronađeno',\n    noTrashResults: 'Nema {{label}} u otpadu.',\n    noUpcomingEventsScheduled: 'Nema zakazanih predstojećih događaja.',\n    noValue: 'Bez vrednosti',\n    of: 'Od',\n    only: 'Samo',\n    open: 'Otvori',\n    or: 'Ili',\n    order: 'Redosled',\n    overwriteExistingData: 'Prepiši postojeće podatke iz polja',\n    pageNotFound: 'Stranica nije pronađena',\n    password: 'Lozinka',\n    pasteField: 'Zalepi polje',\n    pasteRow: 'Zalepi red',\n    payloadSettings: 'Payload postavke',\n    permanentlyDelete: 'Trajno Obriši',\n    permanentlyDeletedCountSuccessfully: 'Trajno obrisano {{count}} {{label}} uspešno.',\n    perPage: 'Po stranici: {{limit}}',\n    previous: 'Prethodni',\n    reindex: 'Reindeksiraj',\n    reindexingAll: 'Ponovno indeksiranje svih {{collections}}.',\n    remove: 'Ukloni',\n    rename: 'Preimenuj',\n    reset: 'Ponovo postavi',\n    resetPreferences: 'Poništi podešavanja',\n    resetPreferencesDescription:\n      'Ovo će poništiti sva vaša podešavanja na podrazumevane vrednosti.',\n    resettingPreferences: 'Poništavanje podešavanja.',\n    restore: 'Vrati',\n    restoreAsPublished: 'Vrati kao objavljenu verziju',\n    restoredCountSuccessfully: 'Uspešno obnovljeno {{count}} {{label}}.',\n    restoring: 'Vraćanje na prethodno stanje...',\n    row: 'Red',\n    rows: 'Redovi',\n    save: 'Sačuvaj',\n    saving: 'Čuvanje u toku...',\n    schedulePublishFor: 'Zakaži objavljivanje za {{title}}',\n    searchBy: 'Traži po {{label}}',\n    select: 'Izaberite',\n    selectAll: 'Odaberite sve {{count}} {{label}}',\n    selectAllRows: 'Odaberite sve redove',\n    selectedCount: '{{count}} {{label}} odabrano',\n    selectLabel: 'Izaberite {{label}}',\n    selectValue: 'Odaberi vrednost',\n    showAllLabel: 'Prikaži sve {{label}}',\n    sorryNotFound: 'Nažalost, ne postoji ništa što odgovara vašem zahtevu.',\n    sort: 'Sortiraj',\n    sortByLabelDirection: 'Sortiraj prema {{label}} {{direction}}',\n    stayOnThisPage: 'Ostani na ovoj stranici',\n    submissionSuccessful: 'Uspešno slanje',\n    submit: 'Potvrdi',\n    submitting: 'Podnošenje...',\n    success: 'Uspeh',\n    successfullyCreated: '{{label}} uspešno kreirano.',\n    successfullyDuplicated: '{{label}} uspešno duplicirano.',\n    successfullyReindexed:\n      'Uspešno je reindeksirano {{count}} od {{total}} dokumenata iz {{collections}} kolekcija.',\n    takeOver: 'Preuzeti',\n    thisLanguage: 'Srpski (latinica)',\n    time: 'Vreme',\n    timezone: 'Vremenska zona',\n    titleDeleted: '{{label}} \"{{title}}\" uspešno obrisano.',\n    titleRestored: 'Oznaka \"{{title}}\" uspešno obnovljena.',\n    titleTrashed: '{{label}} \"{{title}}\" premešteno u smeće.',\n    trash: 'Otpad',\n    trashedCountSuccessfully: '{{count}} {{label}} premešteno u kantu za smeće.',\n    true: 'Istinito',\n    unauthorized: 'Niste autorizovani',\n    unsavedChanges: 'Imate nesačuvane promene. Sačuvajte ili odbacite pre nego što nastavite.',\n    unsavedChangesDuplicate: 'Imate nesačuvane promene. Da li želite nastaviti sa dupliciranjem?',\n    untitled: 'Bez naslova',\n    upcomingEvents: 'Predstojeći događaji',\n    updatedAt: 'Ažurirano u',\n    updatedCountSuccessfully: 'Uspešno ažurirano {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Uspešno ažurirano {{label}}.',\n    updatedSuccessfully: 'Uspešno ažurirano.',\n    updateForEveryone: 'Ažuriranje za sve',\n    updating: 'Ažuriranje',\n    uploading: 'Prenos',\n    uploadingBulk: 'Otpremanje {{current}} od {{total}}',\n    user: 'Korisnik',\n    username: 'Korisničko ime',\n    users: 'Korisnici',\n    value: 'Vrednost',\n    viewing: 'Pregled',\n    viewReadOnly: 'Pregledaj samo za čitanje',\n    welcome: 'Dobrodošli',\n    yes: 'Da',\n  },\n  localization: {\n    cannotCopySameLocale: 'Ne može se kopirati na istu lokaciju',\n    copyFrom: 'Kopiraj iz',\n    copyFromTo: 'Kopiranje iz {{from}} u {{to}}',\n    copyTo: 'Kopiraj u',\n    copyToLocale: 'Kopiraj na lokaciju',\n    localeToPublish: 'Lokal za objavljivanje',\n    selectLocaleToCopy: 'Izaberite lokalitet za kopiranje',\n  },\n  operators: {\n    contains: 'sadrži',\n    equals: 'jednako',\n    exists: 'postoji',\n    intersects: 'seče',\n    isGreaterThan: 'je veće od',\n    isGreaterThanOrEqualTo: 'je veće od ili jednako',\n    isIn: 'je u',\n    isLessThan: 'manje je od',\n    isLessThanOrEqualTo: 'manje je ili jednako',\n    isLike: 'je kao',\n    isNotEqualTo: 'nije jednako',\n    isNotIn: 'nije unutra',\n    isNotLike: 'nije kao',\n    near: 'blizu',\n    within: 'unutar',\n  },\n  upload: {\n    addFile: 'Dodaj datoteku',\n    addFiles: 'Dodaj Datoteke',\n    bulkUpload: 'Masovno otpremanje',\n    crop: 'Isecite sliku',\n    cropToolDescription:\n      'Prevucite uglove izabranog područja, nacrtajte novo područje ili prilagodite vrednosti ispod.',\n    download: 'Preuzmi',\n    dragAndDrop: 'Prevucite i ispustite datoteku',\n    dragAndDropHere: 'ili povucite i ispustite datoteku ovde',\n    editImage: 'Uredi sliku',\n    fileName: 'Ime datoteke',\n    fileSize: 'Veličina datoteke',\n    filesToUpload: 'Fajlovi za Slanje',\n    fileToUpload: 'Datoteka za otpremanje',\n    focalPoint: 'Centralna tačka',\n    focalPointDescription:\n      'Prevucite središnju tačku direktno na pregled ili prilagodite vrednosti ispod.',\n    height: 'Visina',\n    lessInfo: 'Manje informacija',\n    moreInfo: 'Više informacija',\n    noFile: 'Nema datoteke',\n    pasteURL: 'Nalepi URL',\n    previewSizes: 'Veličine pregleda',\n    selectCollectionToBrowse: 'Odaberite kolekciju za pregled',\n    selectFile: 'Odaberite datoteku',\n    setCropArea: 'Postavite područje za isečenu sliku',\n    setFocalPoint: 'Postavite centralnu tačku',\n    sizes: 'Veličine',\n    sizesFor: 'Veličine za {{label}}',\n    width: 'Širina',\n  },\n  validation: {\n    emailAddress: 'Molimo Vas unesite validnu email adresu.',\n    enterNumber: 'Molimo Vas unesite validan broj.',\n    fieldHasNo: 'Ovo polje nema {{label}}',\n    greaterThanMax: '{{value}} prekoračuje maksimalan dozvoljeni {{label}} limit od {{max}}.',\n    invalidInput: 'Ovo polje sadrži nevalidan unos.',\n    invalidSelection: 'Ovo polje sadrži nevalidan odabir.',\n    invalidSelections: 'Ovo polje ima sledeće nevalidne odabire:',\n    lessThanMin: '{{value}} je ispod dozvoljenog minimuma za {{label}} (donji limit je {{min}}).',\n    limitReached: 'Dosegnut je limit, može se dodati samo {{max}} stavki.',\n    longerThanMin: 'Ova vrednost mora biti duža od minimalne dužine od {{minLength}} karaktera',\n    notValidDate: '\"{{value}}\" nije validan datum.',\n    required: 'Ovo polje je obavezno.',\n    requiresAtLeast: 'Ovo polje zahteva minimalno {{count}} {{label}}.',\n    requiresNoMoreThan: 'Ovo polje zahteva ne više od {{count}} {{label}}.',\n    requiresTwoNumbers: 'Ovo polje zahteva dva broja.',\n    shorterThanMax: 'Ova vrednost mora biti kraća od maksimalne dužine od {{maxLength}} karaktera',\n    timezoneRequired: 'Potrebna je vremenska zona.',\n    trueOrFalse: 'Ovo polje može biti samo tačno ili netačno',\n    username:\n      'Molimo unesite važeće korisničko ime. Može sadržavati slova, brojeve, crtice, tačke i donje crte.',\n    validUploadID: 'Ovo polje ne sadrži validan ID prenosa.',\n  },\n  version: {\n    type: 'Tip',\n    aboutToPublishSelection: 'Upravo ćete objaviti sve {{label}} u izboru. Da li ste sigurni?',\n    aboutToRestore: 'Vratićete {{label}} dokument u stanje u kojem je bio {{versionDate}}',\n    aboutToRestoreGlobal: 'Vratićete globalni {{label}} u stanje u kojem je bio {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Vratićete promene u dokumentu u objavljeno stanje. Da li ste sigurni?',\n    aboutToUnpublish: 'Poništićete objavu ovog dokumenta. Da li ste sigurni?',\n    aboutToUnpublishSelection:\n      'Upravo ćete poništiti objavu svih {{label}} u odabiru. Da li ste sigurni?',\n    autosave: 'Automatsko čuvanje',\n    autosavedSuccessfully: 'Automatsko čuvanje uspešno.',\n    autosavedVersion: 'Verzija automatski sačuvanog dokumenta',\n    changed: 'Promenjeno',\n    changedFieldsCount_one: '{{count}} promenjeno polje',\n    changedFieldsCount_other: '{{count}} promenjenih polja',\n    compareVersion: 'Uporedi verziju sa:',\n    compareVersions: 'Uporedite verzije',\n    comparingAgainst: 'Upoređivanje sa',\n    confirmPublish: 'Potvrdi objavu',\n    confirmRevertToSaved: 'Potvrdite vraćanje na sačuvano',\n    confirmUnpublish: 'Potvrdite poništavanje objave',\n    confirmVersionRestoration: 'Potvrdite vraćanje verzije',\n    currentDocumentStatus: 'Trenutni {{docStatus}} dokumenta',\n    currentDraft: 'Trenutni nacrt',\n    currentlyPublished: 'Trenutno Objavljeno',\n    currentlyViewing: 'Trenutno gledate',\n    currentPublishedVersion: 'Trenutna Objavljena Verzija',\n    draft: 'Nacrt',\n    draftSavedSuccessfully: 'Nacrt uspešno sačuvan.',\n    lastSavedAgo: 'Zadnji put sačuvano pre {{distance}}',\n    modifiedOnly: 'Samo izmenjen',\n    moreVersions: 'Više verzija...',\n    noFurtherVersionsFound: 'Nisu pronađene naredne verzije',\n    noRowsFound: '{{label}} nije pronađeno',\n    noRowsSelected: 'Nije odabrana {{label}}',\n    preview: 'Pregled',\n    previouslyDraft: 'Prethodno Nacrt',\n    previouslyPublished: 'Prethodno objavljeno',\n    previousVersion: 'Prethodna Verzija',\n    problemRestoringVersion: 'Nastao je problem pri vraćanju ove verzije',\n    publish: 'Objaviti',\n    publishAllLocales: 'Objavi sve lokalne postavke',\n    publishChanges: 'Objavljivanje',\n    published: 'Objavljeno',\n    publishIn: 'Objavite na {{locale}}',\n    publishing: 'Objavljivanje',\n    restoreAsDraft: 'Vrati kao nacrt',\n    restoredSuccessfully: 'Uspešno vraćeno.',\n    restoreThisVersion: 'Vrati ovu verziju',\n    restoring: 'Vraćanje...',\n    reverting: 'Vraćanje...',\n    revertToPublished: 'Vrati na objavljeno',\n    saveDraft: 'Sačuvaj nacrt',\n    scheduledSuccessfully: 'Uspešno zakazano.',\n    schedulePublish: 'Zakaži objavljivanje',\n    selectLocales: 'Odaberite jezike',\n    selectVersionToCompare: 'Odaberite verziju za upoređivanje',\n    showingVersionsFor: 'Pokazujem verzije za:',\n    showLocales: 'Prikaži jezike:',\n    specificVersion: 'Specifična verzija',\n    status: 'Status',\n    unpublish: 'Poništi objavu',\n    unpublishing: 'Poništavanje objave...',\n    version: 'Verzija',\n    versionAgo: 'pre {{distance}}',\n    versionCount_many: '{{count}} pronađenih verzija',\n    versionCount_none: 'Nema pronađenih verzija',\n    versionCount_one: '{{count}} pronađena verzija',\n    versionCount_other: '{{count}} pronađenih verzija',\n    versionCreatedOn: '{{version}} kreiranih:',\n    versionID: 'ID verzije',\n    versions: 'Verzije',\n    viewingVersion: 'Pregled verzije za {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Pregled verzije za globalni {{entityLabel}}',\n    viewingVersions: 'Pregled verzija za {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Pregled verzije za globalni {{entityLabel}}',\n  },\n}\n\nexport const rsLatin: Language = {\n  dateFNSKey: 'rs-Latin',\n  translations: rsLatinTranslations,\n}\n"], "names": ["rsLatinTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "rsLatin", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,sBAAiD;IAC5DC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,UAAoB;IAC/BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}