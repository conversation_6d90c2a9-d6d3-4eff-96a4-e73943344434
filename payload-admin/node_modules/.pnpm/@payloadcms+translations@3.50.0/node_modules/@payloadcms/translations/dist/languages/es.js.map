{"version": 3, "sources": ["../../src/languages/es.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const esTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Cuenta',\n    accountOfCurrentUser: 'Cuenta del usuario actual',\n    accountVerified: 'Cuenta verificada con éxito.',\n    alreadyActivated: 'Ya Activado',\n    alreadyLoggedIn: 'Ya has iniciado sesión',\n    apiKey: 'Clave API',\n    authenticated: 'Autenticado',\n    backToLogin: 'Regresar al inicio de sesión',\n    beginCreateFirstUser: 'Para empezar, crea tu primer usuario.',\n    changePassword: 'Cambiar contraseña',\n    checkYourEmailForPasswordReset:\n      'Si la dirección de correo electrónico está asociada a una cuenta, recibirás instrucciones para restablecer tu contraseña en breve. Por favor, revisa tu carpeta de spam o correo no deseado si no ves el correo en tu bandeja de entrada.',\n    confirmGeneration: 'Confirmar Generación',\n    confirmPassword: 'Confirmar <PERSON>tras<PERSON>',\n    createFirstUser: 'Crear el primer usuario',\n    emailNotValid: 'El correo proporcionado es inválido',\n    emailOrUsername: 'Correo electrónico o nombre de usuario',\n    emailSent: 'Correo Enviado',\n    emailVerified: 'Correo electrónico verificado con éxito.',\n    enableAPIKey: 'Habilitar Clave API',\n    failedToUnlock: 'Desbloqueo Fallido',\n    forceUnlock: 'Forzar Desbloqueo',\n    forgotPassword: 'Olvidé mi contraseña',\n    forgotPasswordEmailInstructions:\n      'Por favor introduce tu correo electrónico. Recibirás un mensaje con las instrucciones para restablecer tu contraseña.',\n    forgotPasswordQuestion: '¿Olvidaste tu contraseña?',\n    forgotPasswordUsernameInstructions:\n      'Por favor, introduce tu nombre de usuario a continuación. Se enviarán instrucciones sobre cómo restablecer tu contraseña a la dirección de correo electrónico asociada a tu nombre de usuario.',\n    generate: 'Generar',\n    generateNewAPIKey: 'Generar Nueva Clave API',\n    generatingNewAPIKeyWillInvalidate:\n      'Generar una nueva clave API <1>invalidará</1> la clave anterior. ¿Deseas continuar?',\n    lockUntil: 'Bloquear Hasta',\n    logBackIn: 'Volver a iniciar sesión',\n    loggedIn: 'Para iniciar sesión con otro usuario, primero <0>cierra tu sesión</0>.',\n    loggedInChangePassword:\n      'Para cambiar tu contraseña, entra a <0>tu cuenta</0> y edita la contraseña desde ahí.',\n    loggedOutInactivity: 'Tú sesión se cerró debido a inactividad.',\n    loggedOutSuccessfully: 'Tú sesión se cerró correctamente.',\n    loggingOut: 'Cerrando sesión...',\n    login: 'Iniciar sesión',\n    loginAttempts: 'Intentos de inicio de sesión',\n    loginUser: 'Iniciar sesión',\n    loginWithAnotherUser: 'Para iniciar sesión con otro usuario, primero <0>cierra tu sesión</0>.',\n    logOut: 'Cerrar sesión',\n    logout: 'Cerrar sesión',\n    logoutSuccessful: 'Cierre de sesión exitoso.',\n    logoutUser: 'Cerrar sesión',\n    newAccountCreated:\n      'Se ha creado una nueva cuenta para que puedas acceder a <a href=\"{{serverURL}}\">{{serverURL}}</a>. Por favor, haz click o copia el siguiente enlace a tu navegador para verificar tu correo: <a href=\"{{verificationURL}}\">{{verificationURL}}</a>.<br> Una vez hayas verificado tu correo, podrás iniciar sesión.',\n    newAPIKeyGenerated: 'Nueva Clave de API Generada.',\n    newPassword: 'Nueva Contraseña',\n    passed: 'Autenticación Exitosa',\n    passwordResetSuccessfully: 'Contraseña restablecida con éxito.',\n    resetPassword: 'Restablecer Contraseña',\n    resetPasswordExpiration: 'Restablecer Caducidad de la Contraseña',\n    resetPasswordToken: 'Restablecer Token de la Contraseña',\n    resetYourPassword: 'Restablecer tu Contraseña',\n    stayLoggedIn: 'Mantener sesión abierta',\n    successfullyRegisteredFirstUser: 'Primer usuario registrado exitosamente.',\n    successfullyUnlocked: 'Desbloqueado exitosamente',\n    tokenRefreshSuccessful: 'Token actualizado con éxito.',\n    unableToVerify: 'No se pudo Verificar',\n    username: 'Nombre de usuario',\n    usernameNotValid: 'El nombre de usuario proporcionado no es válido.',\n    verified: 'Verificado',\n    verifiedSuccessfully: 'Verificado Correctamente',\n    verify: 'Verificar',\n    verifyUser: 'Verificar Usuario',\n    verifyYourEmail: 'Verifica tu correo',\n    youAreInactive:\n      'Has estado inactivo por un tiempo y por tu seguridad se cerrará tu sesión automáticamente en breve. ¿Deseas mantener tu sesión abierta?',\n    youAreReceivingResetPassword:\n      'Estás recibiendo este correo porque tú (o alguien más) ha solicitado restablecer la contraseña de tu cuenta. Por favor haz clic en el siguiente enlace o pégalo en tu navegador para completar el proceso:',\n    youDidNotRequestPassword:\n      'Si no solicitaste esto, por favor ignora este correo y tu contraseña permanecerá sin cambios.',\n  },\n  error: {\n    accountAlreadyActivated: 'Esta cuenta ya fue activada.',\n    autosaving: 'Hubo un problema al guardar automáticamente este documento.',\n    correctInvalidFields: 'Por favor, corrige los campos inválidos.',\n    deletingFile: 'Ocurrió un error al eliminar el archivo.',\n    deletingTitle:\n      'Ocurrió un error al eliminar {{title}}. Por favor, revisa tu conexión y vuelve a intentarlo.',\n    documentNotFound:\n      'No se pudo encontrar el documento con ID {{id}}. Puede haber sido eliminado o nunca existió, o puede que no tenga acceso a él.',\n    emailOrPasswordIncorrect: 'El correo o la contraseña son incorrectos.',\n    followingFieldsInvalid_one: 'El siguiente campo es inválido:',\n    followingFieldsInvalid_other: 'Los siguientes campos son inválidos:',\n    incorrectCollection: 'Colección Incorrecta',\n    insufficientClipboardPermissions:\n      'Acceso al portapapeles denegado. Verifique los permisos del portapapeles.',\n    invalidClipboardData: 'Datos del portapapeles no válidos.',\n    invalidFileType: 'Tipo de archivo inválido',\n    invalidFileTypeValue: 'Tipo de archivo inválido: {{value}}',\n    invalidRequestArgs: 'Argumentos inválidos en la solicitud: {{args}}',\n    loadingDocument: 'Ocurrió un problema al cargar el documento con ID {{id}}.',\n    localesNotSaved_one: 'No se pudo guardar el siguiente idioma:',\n    localesNotSaved_other: 'No se pudieron guardar los siguientes idiomas:',\n    logoutFailed: 'El cierre de sesión falló.',\n    missingEmail: 'Falta el correo electrónico.',\n    missingIDOfDocument: 'Falta el ID del documento a actualizar.',\n    missingIDOfVersion: 'Falta el ID de la versión.',\n    missingRequiredData: 'Falta información obligatoria.',\n    noFilesUploaded: 'No se subieron archivos.',\n    noMatchedField: 'No se encontró un campo para \"{{label}}\"',\n    notAllowedToAccessPage: 'No tienes permiso para acceder a esta página.',\n    notAllowedToPerformAction: 'No tienes permiso para realizar esta acción.',\n    notFound: 'No se encontró el recurso solicitado.',\n    noUser: 'Sin usuario',\n    previewing: 'Ocurrió un problema al previsualizar este documento.',\n    problemUploadingFile: 'Ocurrió un problema al subir el archivo.',\n    restoringTitle:\n      'Hubo un error al restaurar {{title}}. Por favor, verifique su conexión e intente nuevamente.',\n    tokenInvalidOrExpired: 'El token es inválido o ya expiró.',\n    tokenNotProvided: 'Token no proporcionado.',\n    unableToCopy: 'No se puede copiar.',\n    unableToDeleteCount: 'No se pudo eliminar {{count}} de {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Error al reindexar la colección {{collection}}. Operación abortada.',\n    unableToUpdateCount: 'No se puede actualizar {{count}} de {{total}} {{label}}.',\n    unauthorized: 'No autorizado, debes iniciar sesión para realizar esta solicitud.',\n    unauthorizedAdmin: 'No autorizado, este usuario no tiene acceso al panel de administración.',\n    unknown: 'Ocurrió un error desconocido.',\n    unPublishingDocument: 'Ocurrió un error al despublicar este documento.',\n    unspecific: 'Ocurrió un error.',\n    unverifiedEmail: 'Por favor, verifica tu correo electrónico antes de iniciar sesión.',\n    userEmailAlreadyRegistered:\n      'Ya existe un usuario registrado con el correo electrónico proporcionado.',\n    userLocked:\n      'Este usuario ha sido bloqueado debido a demasiados intentos fallidos de inicio de sesión.',\n    usernameAlreadyRegistered:\n      'Ya existe un usuario registrado con el nombre de usuario proporcionado.',\n    usernameOrPasswordIncorrect:\n      'El nombre de usuario o la contraseña proporcionados son incorrectos.',\n    valueMustBeUnique: 'El valor debe ser único',\n    verificationTokenInvalid: 'Token de verificación inválido.',\n  },\n  fields: {\n    addLabel: 'Añadir {{label}}',\n    addLink: 'Añadir Enlace',\n    addNew: 'Añadir nuevo',\n    addNewLabel: 'Añadir {{label}}',\n    addRelationship: 'Añadir Relación',\n    addUpload: 'Añadir documento',\n    block: 'bloque',\n    blocks: 'bloques',\n    blockType: 'Tipo de bloque',\n    chooseBetweenCustomTextOrDocument:\n      'Elige entre ingresar una URL personalizada o enlazar a otro documento.',\n    chooseDocumentToLink: 'Elige un documento a enlazar',\n    chooseFromExisting: 'Elegir de los existentes',\n    chooseLabel: 'Elegir {{label}}',\n    collapseAll: 'Contraer Todo',\n    customURL: 'URL Personalizada',\n    editLabelData: 'Editar datos de {{label}}',\n    editLink: 'Editar Enlace',\n    editRelationship: 'Editar Relación',\n    enterURL: 'Introduce una URL',\n    internalLink: 'Enlace Interno',\n    itemsAndMore: '{{items}} y {{count}} más',\n    labelRelationship: 'Relación de {{label}}',\n    latitude: 'Latitud',\n    linkedTo: 'Enlazado a <0>{{label}}</0>',\n    linkType: 'Tipo de enlace',\n    longitude: 'Longitud',\n    newLabel: 'Nuevo {{label}}',\n    openInNewTab: 'Abrir en nueva pestaña',\n    passwordsDoNotMatch: 'Las contraseñas no coinciden.',\n    relatedDocument: 'Documento Relacionado',\n    relationTo: 'Relación con',\n    removeRelationship: 'Eliminar relación',\n    removeUpload: 'Eliminar documento',\n    saveChanges: 'Guardar cambios',\n    searchForBlock: 'Buscar bloque',\n    selectExistingLabel: 'Seleccionar {{label}} existente',\n    selectFieldsToEdit: 'Seleccionar campos para editar',\n    showAll: 'Mostrar Todo',\n    swapRelationship: 'Cambiar Relación',\n    swapUpload: 'Cambiar carga',\n    textToDisplay: 'Texto a mostrar',\n    toggleBlock: 'Alternar bloque',\n    uploadNewLabel: 'Subir nuevo {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Explorar por Carpeta',\n    byFolder: 'Por Carpeta',\n    deleteFolder: 'Eliminar Carpeta',\n    folderName: 'Nombre de la Carpeta',\n    folders: 'Carpetas',\n    folderTypeDescription:\n      'Seleccione qué tipo de documentos de la colección se deben permitir en esta carpeta.',\n    itemHasBeenMoved: '{{title}} se ha movido a {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} se ha movido a la carpeta raíz',\n    itemsMovedToFolder: '{{title}} movido a {{folderName}}',\n    itemsMovedToRoot: '{{title}} movido a la carpeta raíz',\n    moveFolder: 'Mover Carpeta',\n    moveItemsToFolderConfirmation:\n      'Estás a punto de mover <1>{{count}} {{label}}</1> a <2>{{toFolder}}</2>. ¿Estás seguro?',\n    moveItemsToRootConfirmation:\n      'Estás a punto de mover <1>{{count}} {{label}}</1> a la carpeta raíz. ¿Estás seguro?',\n    moveItemToFolderConfirmation:\n      'Estás a punto de mover <1>{{title}}</1> a <2>{{toFolder}}</2>. ¿Estás seguro?',\n    moveItemToRootConfirmation:\n      'Estás a punto de mover <1>{{title}}</1> a la carpeta raíz. ¿Estás seguro?',\n    movingFromFolder: 'Moviendo {{title}} desde {{fromFolder}}',\n    newFolder: 'Nueva Carpeta',\n    noFolder: 'Sin Carpeta',\n    renameFolder: 'Renombrar carpeta',\n    searchByNameInFolder: 'Buscar por nombre en {{folderName}}',\n    selectFolderForItem: 'Seleccione la carpeta para {{title}}',\n  },\n  general: {\n    name: 'Nombre',\n    aboutToDelete: 'Estás por eliminar el {{label}} <1>{{title}}</1>. ¿Estás seguro?',\n    aboutToDeleteCount_many: 'Estás a punto de eliminar {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Estás a punto de eliminar {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Estás a punto de eliminar {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Está a punto de eliminar permanentemente la {{label}} <1>{{title}}</1>. ¿Está seguro?',\n    aboutToPermanentlyDeleteTrash:\n      'Está a punto de eliminar permanentemente <0>{{count}}</0> <1>{{label}}</1> de la basura. ¿Está seguro?',\n    aboutToRestore: 'Está a punto de restaurar la {{label}} <1>{{title}}</1>. ¿Está seguro?',\n    aboutToRestoreAsDraft:\n      'Está a punto de restaurar la {{label}} <1>{{title}}</1> como borrador. ¿Está seguro?',\n    aboutToRestoreAsDraftCount: 'Estás a punto de restaurar {{count}} {{label}} como borrador',\n    aboutToRestoreCount: 'Estás a punto de restaurar {{count}} {{label}}',\n    aboutToTrash:\n      'Estás a punto de mover la {{label}} <1>{{title}}</1> a la papelera. ¿Estás seguro?',\n    aboutToTrashCount: 'Estás a punto de mover {{count}} {{label}} a la papelera',\n    addBelow: 'Añadir abajo',\n    addFilter: 'Añadir filtro',\n    adminTheme: 'Tema del admin',\n    all: 'Todo',\n    allCollections: 'Todas las colecciones',\n    allLocales: 'Todos los idiomas',\n    and: 'Y',\n    anotherUser: 'Otro usuario',\n    anotherUserTakenOver: 'Otro usuario ha tomado el control de la edición de este documento.',\n    applyChanges: 'Aplicar Cambios',\n    ascending: 'Ascendente',\n    automatic: 'Automático',\n    backToDashboard: 'Volver al Panel de Control',\n    cancel: 'Cancelar',\n    changesNotSaved:\n      'Tus cambios no han sido guardados. Si te sales ahora, se perderán tus cambios.',\n    clear: 'Claro',\n    clearAll: 'Limpiar todo',\n    close: 'Cerrar',\n    collapse: 'Contraer',\n    collections: 'Colecciones',\n    columns: 'Columnas',\n    columnToSort: 'Columna de ordenado',\n    confirm: 'Confirmar',\n    confirmCopy: 'Confirmar copia',\n    confirmDeletion: 'Confirmar eliminación',\n    confirmDuplication: 'Confirmar duplicado',\n    confirmMove: 'Confirmar movimiento',\n    confirmReindex: '¿Reindexar todas las {{collections}}?',\n    confirmReindexAll: '¿Reindexar todas las colecciones?',\n    confirmReindexDescription:\n      'Esto eliminará los índices existentes y volverá a indexar los documentos en las colecciones {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Esto eliminará los índices existentes y volverá a indexar los documentos en todas las colecciones.',\n    confirmRestoration: 'Confirme la restauración',\n    copied: 'Copiado',\n    copy: 'Copiar',\n    copyField: 'Copiar campo',\n    copying: 'Copiando',\n    copyRow: 'Copiar fila',\n    copyWarning:\n      'Estás a punto de sobrescribir {{to}} con {{from}} para {{label}} {{title}}. ¿Estás seguro?',\n    create: 'Crear',\n    created: 'Creado',\n    createdAt: 'Fecha de creación',\n    createNew: 'Crear nuevo',\n    createNewLabel: 'Crear nuevo {{label}}',\n    creating: 'Creando',\n    creatingNewLabel: 'Creando nuevo {{label}}',\n    currentlyEditing:\n      'está editando este documento. Si tomas el control, se le impedirá continuar editando y podría perder los cambios no guardados.',\n    custom: 'Personalizado',\n    dark: 'Oscuro',\n    dashboard: 'Panel de Control',\n    delete: 'Eliminar',\n    deleted: 'Eliminado',\n    deletedAt: 'Eliminado En',\n    deletedCountSuccessfully: 'Se eliminaron {{count}} {{label}} correctamente.',\n    deletedSuccessfully: 'Eliminado correctamente.',\n    deletePermanently: 'Omitir la papelera y eliminar permanentemente',\n    deleting: 'Eliminando...',\n    depth: 'Profundidad',\n    descending: 'Descendente',\n    deselectAllRows: 'Deseleccionar todas las filas',\n    document: 'Documento',\n    documentIsTrashed: 'Esta {{label}} está en la papelera y es de solo lectura.',\n    documentLocked: 'Documento bloqueado',\n    documents: 'Documentos',\n    duplicate: 'Duplicar',\n    duplicateWithoutSaving: 'Duplicar sin guardar cambios',\n    edit: 'Editar',\n    editAll: 'Editar Todo',\n    editedSince: 'Editado desde',\n    editing: 'Editando',\n    editingLabel_many: 'Editando {{count}} {{label}}',\n    editingLabel_one: 'Editando {{count}} {{label}}',\n    editingLabel_other: 'Editando {{count}} {{label}}',\n    editingTakenOver: 'Edición tomada',\n    editLabel: 'Editar {{label}}',\n    email: 'Correo electrónico',\n    emailAddress: 'Dirección de Correo Electrónico',\n    emptyTrash: 'Vaciar la papelera',\n    emptyTrashLabel: 'Vaciar la basura {{label}}',\n    enterAValue: 'Introduce un valor',\n    error: 'Error',\n    errors: 'Errores',\n    exitLivePreview: 'Salir de la vista previa en vivo',\n    export: 'Exportar',\n    fallbackToDefaultLocale: 'Volver al idioma predeterminado',\n    false: 'Falso',\n    filter: 'Filtro',\n    filters: 'Filtros',\n    filterWhere: 'Filtrar {{label}} donde',\n    globals: 'Globales',\n    goBack: 'Volver',\n    groupByLabel: 'Agrupar por {{label}}',\n    import: 'Importar',\n    isEditing: 'está editando',\n    item: 'artículo',\n    items: 'artículos',\n    language: 'Idioma',\n    lastModified: 'Última modificación',\n    leaveAnyway: 'Salir de todos modos',\n    leaveWithoutSaving: 'Salir sin guardar',\n    light: 'Claro',\n    livePreview: 'Previsualizar',\n    loading: 'Cargando',\n    locale: 'Idioma',\n    locales: 'Idiomas',\n    menu: 'Menú',\n    moreOptions: 'Más opciones',\n    move: 'Mover',\n    moveConfirm:\n      'Estás a punto de mover {{count}} {{label}} a <1>{{destination}}</1>. ¿Estás seguro?',\n    moveCount: 'Mover {{count}} {{label}}',\n    moveDown: 'Mover abajo',\n    moveUp: 'Mover arriba',\n    moving: 'Moviendo',\n    movingCount: 'Moviendo {{count}} {{label}}',\n    newPassword: 'Nueva contraseña',\n    next: 'Siguiente',\n    no: 'No',\n    noDateSelected: 'No se seleccionó ninguna fecha',\n    noFiltersSet: 'No hay filtros establecidos',\n    noLabel: '<Sin {{label}}>',\n    none: 'Ninguna',\n    noOptions: 'Sin opciones',\n    noResults:\n      'No se encontró ningún {{label}}. Puede que aún no existan o que no coincidan con los filtros aplicados.',\n    notFound: 'No encontrado',\n    nothingFound: 'No se encontró nada',\n    noTrashResults: 'No hay {{label}} en la papelera.',\n    noUpcomingEventsScheduled: 'No hay eventos próximos programados.',\n    noValue: 'Sin valor',\n    of: 'de',\n    only: 'Solo',\n    open: 'Abrir',\n    or: 'O',\n    order: 'Orden',\n    overwriteExistingData: 'Sobrescribir los datos existentes del campo',\n    pageNotFound: 'Página no encontrada',\n    password: 'Contraseña',\n    pasteField: 'Pegar campo',\n    pasteRow: 'Pegar fila',\n    payloadSettings: 'Configuración de Payload',\n    permanentlyDelete: 'Eliminar Permanentemente',\n    permanentlyDeletedCountSuccessfully:\n      'Se ha eliminado permanentemente {{count}} {{label}} con éxito.',\n    perPage: 'Por página: {{limit}}',\n    previous: 'Anterior',\n    reindex: 'Reindexar',\n    reindexingAll: 'Reindexando todas las {{collections}}.',\n    remove: 'Eliminar',\n    rename: 'Renombrar',\n    reset: 'Restablecer',\n    resetPreferences: 'Restablecer preferencias',\n    resetPreferencesDescription:\n      'Esto restablecerá todas tus preferencias a los valores predeterminados.',\n    resettingPreferences: 'Restableciendo preferencias...',\n    restore: 'Restaurar',\n    restoreAsPublished: 'Restaurar como versión publicada',\n    restoredCountSuccessfully: 'Restaurado {{count}} {{label}} con éxito.',\n    restoring: 'Restaurando...',\n    row: 'Fila',\n    rows: 'Filas',\n    save: 'Guardar',\n    saving: 'Guardando...',\n    schedulePublishFor: 'Programar publicación para {{title}}',\n    searchBy: 'Buscar por {{label}}',\n    select: 'Seleccionar',\n    selectAll: 'Seleccionar los {{count}} {{label}}',\n    selectAllRows: 'Seleccionar todas las filas',\n    selectedCount: '{{count}} {{label}} seleccionados',\n    selectLabel: 'Seleccionar {{label}}',\n    selectValue: 'Seleccionar un valor',\n    showAllLabel: 'Mostrar todos los {{label}}',\n    sorryNotFound: 'Lo sentimos, no hay nada que coincida con tu solicitud.',\n    sort: 'Ordenar',\n    sortByLabelDirection: 'Ordenar por {{label}} {{direction}}',\n    stayOnThisPage: 'Permanecer en esta página',\n    submissionSuccessful: 'Envío realizado con éxito.',\n    submit: 'Enviar',\n    submitting: 'Enviando...',\n    success: 'Éxito',\n    successfullyCreated: '{{label}} creado con éxito.',\n    successfullyDuplicated: '{{label}} duplicado con éxito.',\n    successfullyReindexed:\n      '{{count}} de {{total}} documentos de {{collections}} reindexados con éxito.',\n    takeOver: 'Tomar el control',\n    thisLanguage: 'Español',\n    time: 'Hora',\n    timezone: 'Zona horaria',\n    titleDeleted: '{{label}} \"{{title}}\" eliminado con éxito.',\n    titleRestored: '{{label}} \"{{title}}\" restaurado con éxito.',\n    titleTrashed: '{{label}} \"{{title}}\" movido a la papelera.',\n    trash: 'Basura',\n    trashedCountSuccessfully: '{{count}} {{label}} movido a la papelera.',\n    true: 'Verdadero',\n    unauthorized: 'No autorizado',\n    unsavedChanges: 'Tienes cambios sin guardar. Guarda o descarta antes de continuar.',\n    unsavedChangesDuplicate: 'Tienes cambios sin guardar. ¿Deseas continuar con la duplicación?',\n    untitled: 'Sin título',\n    upcomingEvents: 'Próximos eventos',\n    updatedAt: 'Última modificación',\n    updatedCountSuccessfully: '{{count}} {{label}} actualizados con éxito.',\n    updatedLabelSuccessfully: '{{label}} actualizado con éxito.',\n    updatedSuccessfully: 'Actualizado con éxito.',\n    updateForEveryone: 'Actualizar para todos',\n    updating: 'Actualizando',\n    uploading: 'Subiendo',\n    uploadingBulk: 'Subiendo {{current}} de {{total}}',\n    user: 'Usuario',\n    username: 'Nombre de usuario',\n    users: 'Usuarios',\n    value: 'Valor',\n    viewing: 'Visualización',\n    viewReadOnly: 'Ver solo lectura',\n    welcome: 'Bienvenido',\n    yes: 'Sí',\n  },\n  localization: {\n    cannotCopySameLocale: 'No se puede copiar al mismo idioma',\n    copyFrom: 'Copiar desde',\n    copyFromTo: 'Copiando de {{from}} a {{to}}',\n    copyTo: 'Copiar a',\n    copyToLocale: 'Copiar a idioma',\n    localeToPublish: 'Idioma para publicar',\n    selectLocaleToCopy: 'Selecciona el idioma a copiar',\n  },\n  operators: {\n    contains: 'contiene',\n    equals: 'igual',\n    exists: 'existe',\n    intersects: 'interseca',\n    isGreaterThan: 'es mayor que',\n    isGreaterThanOrEqualTo: 'es mayor o igual que',\n    isIn: 'está en',\n    isLessThan: 'es menor que',\n    isLessThanOrEqualTo: 'es menor o igual que',\n    isLike: 'es similar a',\n    isNotEqualTo: 'no es igual a',\n    isNotIn: 'no está en',\n    isNotLike: 'no es similar a',\n    near: 'cerca',\n    within: 'dentro de',\n  },\n  upload: {\n    addFile: 'Añadir archivo',\n    addFiles: 'Añadir archivos',\n    bulkUpload: 'Subida en lotes',\n    crop: 'Recortar',\n    cropToolDescription:\n      'Arrastra las esquinas del área seleccionada, dibuja un nuevo área o ajusta los valores a continuación.',\n    download: 'Descargar',\n    dragAndDrop: 'Arrastra y suelta un archivo',\n    dragAndDropHere: 'o arrastra un archivo aquí',\n    editImage: 'Editar imagen',\n    fileName: 'Nombre del archivo',\n    fileSize: 'Tamaño del archivo',\n    filesToUpload: 'Archivos para subir',\n    fileToUpload: 'Archivo para subir',\n    focalPoint: 'Punto Focal',\n    focalPointDescription:\n      'Arrastra el punto focal directamente en la vista previa o ajusta los valores a continuación.',\n    height: 'Alto',\n    lessInfo: 'Menos info',\n    moreInfo: 'Más info',\n    noFile: 'Ningún archivo',\n    pasteURL: 'Pegar URL',\n    previewSizes: 'Tamaños de Vista Previa',\n    selectCollectionToBrowse: 'Selecciona una Colección para navegar',\n    selectFile: 'Selecciona un archivo',\n    setCropArea: 'Establecer área de recorte',\n    setFocalPoint: 'Establecer punto focal',\n    sizes: 'Tamaños',\n    sizesFor: 'Tamaños para {{label}}',\n    width: 'Ancho',\n  },\n  validation: {\n    emailAddress: 'Por favor, introduce un correo electrónico válido.',\n    enterNumber: 'Por favor, introduce un número válido.',\n    fieldHasNo: 'Este campo no tiene {{label}}',\n    greaterThanMax: '{{value}} es mayor que el máximo permitido de {{max}} en {{label}}.',\n    invalidInput: 'La información en este campo es inválida.',\n    invalidSelection: 'La selección en este campo es inválida.',\n    invalidSelections: 'Este campo tiene las siguientes selecciones inválidas:',\n    lessThanMin: '{{value}} es menor que el mínimo permitido de {{min}} en {{label}}.',\n    limitReached: 'Se ha alcanzado el límite. Solo se pueden agregar {{max}} elementos.',\n    longerThanMin: 'Este valor debe tener una longitud mínima de {{minLength}} caracteres.',\n    notValidDate: '\"{{value}}\" es una fecha inválida.',\n    required: 'Este campo es obligatorio.',\n    requiresAtLeast: 'Este campo requiere al menos {{count}} {{label}}.',\n    requiresNoMoreThan: 'Este campo require no más de {{count}} {{label}}',\n    requiresTwoNumbers: 'Este campo requiere dos números.',\n    shorterThanMax: 'Este valor debe tener una longitud máxima de {{maxLength}} caracteres.',\n    timezoneRequired: 'Se requiere una zona horaria.',\n    trueOrFalse: 'Este campo solo puede ser verdadero o falso.',\n    username:\n      'Por favor, introduce un nombre de usuario válido. Puede contener letras, números, guiones, puntos y guiones bajos.',\n    validUploadID: 'Este campo no es un ID de subida válido.',\n  },\n  version: {\n    type: 'Tipo',\n    aboutToPublishSelection:\n      'Estás a punto de publicar todos los {{label}} seleccionados. ¿Estás seguro?',\n    aboutToRestore:\n      'Estás a punto de restaurar este documento de {{label}} al estado en el que se encontraba el {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Estás a punto de restaurar el {{label}} global al estado en el que se encontraba el {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Estás a punto de revertir los cambios de este documento a su estado publicado. ¿Estás seguro?',\n    aboutToUnpublish: 'Estás a punto de despublicar este documento. ¿Estás seguro?',\n    aboutToUnpublishSelection:\n      'Estás a punto de despublicar todos los {{label}} seleccionados. ¿Estás seguro?',\n    autosave: 'Autoguardado',\n    autosavedSuccessfully: 'Guardado automáticamente con éxito.',\n    autosavedVersion: 'Versión Autoguardada',\n    changed: 'Modificado',\n    changedFieldsCount_one: '{{count}} campo modificado',\n    changedFieldsCount_other: '{{count}} campos modificados',\n    compareVersion: 'Comparar versión con:',\n    compareVersions: 'Comparar Versiones',\n    comparingAgainst: 'Comparando contra',\n    confirmPublish: 'Confirmar publicación',\n    confirmRevertToSaved: 'Confirmar revertir a guardado',\n    confirmUnpublish: 'Confirmar despublicación',\n    confirmVersionRestoration: 'Confirmar restauración de versión',\n    currentDocumentStatus: 'Documento actual: {{docStatus}}',\n    currentDraft: 'Borrador actual',\n    currentlyPublished: 'Actualmente Publicado',\n    currentlyViewing: 'Actualmente viendo',\n    currentPublishedVersion: 'Versión publicada actual',\n    draft: 'Borrador',\n    draftSavedSuccessfully: 'Borrador guardado con éxito.',\n    lastSavedAgo: 'Guardado por última vez hace {{distance}}',\n    modifiedOnly: 'Modificado solamente',\n    moreVersions: 'Más versiones...',\n    noFurtherVersionsFound: 'No se encontraron más versiones',\n    noRowsFound: 'No se encontraron {{label}}.',\n    noRowsSelected: 'No se ha seleccionado ningún {{label}}.',\n    preview: 'Vista previa',\n    previouslyDraft: 'Previamente un Borrador',\n    previouslyPublished: 'Publicado anteriormente',\n    previousVersion: 'Versión Anterior',\n    problemRestoringVersion: 'Hubo un problema al restaurar esta versión',\n    publish: 'Publicar',\n    publishAllLocales: 'Publicar en todos los idiomas',\n    publishChanges: 'Publicar cambios',\n    published: 'Publicado',\n    publishIn: 'Publicar en {{locale}}',\n    publishing: 'Publicando',\n    restoreAsDraft: 'Restaurar como borrador',\n    restoredSuccessfully: 'Restaurado con éxito.',\n    restoreThisVersion: 'Restaurar esta versión',\n    restoring: 'Restaurando...',\n    reverting: 'Revirtiendo...',\n    revertToPublished: 'Revertir a la versión publicada',\n    saveDraft: 'Guardar borrador',\n    scheduledSuccessfully: 'Programado con éxito.',\n    schedulePublish: 'Programar publicación',\n    selectLocales: 'Seleccionar idiomas a mostrar',\n    selectVersionToCompare: 'Seleccionar una versión para comparar',\n    showingVersionsFor: 'Mostrando versiones para:',\n    showLocales: 'Mostrar idiomas:',\n    specificVersion: 'Versión Específica',\n    status: 'Estado',\n    unpublish: 'Despublicar',\n    unpublishing: 'Despublicando...',\n    version: 'Versión',\n    versionAgo: 'hace {{distance}}',\n    versionCount_many: '{{count}} versiones encontradas',\n    versionCount_none: 'No se encontraron versiones',\n    versionCount_one: '{{count}} versión encontrada',\n    versionCount_other: '{{count}} versiones encontradas',\n    versionCreatedOn: '{{version}} creada el:',\n    versionID: 'ID de la versión',\n    versions: 'Versiones',\n    viewingVersion: 'Viendo versión para {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Viendo versión para el global {{entityLabel}}',\n    viewingVersions: 'Viendo versiones para {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Viendo versiones para el global {{entityLabel}}',\n  },\n}\n\nexport const es: Language = {\n  dateFNSKey: 'es',\n  translations: esTranslations,\n}\n"], "names": ["esTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "es", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BACE;QACFC,YACE;QACFC,2BACE;QACFC,6BACE;QACFC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCACE;QACFC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}