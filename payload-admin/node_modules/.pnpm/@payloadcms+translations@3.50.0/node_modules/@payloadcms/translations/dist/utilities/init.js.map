{"version": 3, "sources": ["../../src/utilities/init.ts"], "sourcesContent": ["import type {\n  DefaultTranslationKeys,\n  DefaultTranslationsObject,\n  I18n,\n  InitI18n,\n  InitTFunction,\n  Language,\n} from '../types.js'\n\nimport { importDateFNSLocale } from '../importDateFNSLocale.js'\nimport { deepMergeSimple } from './deepMergeSimple.js'\nimport { getTranslationsByContext } from './getTranslationsByContext.js'\n\n/**\n * @function getTranslationString\n *\n * Gets a translation string from a translations object\n *\n * @returns string\n */\nexport const getTranslationString = <\n  TTranslations = DefaultTranslationsObject,\n  TTranslationKeys = DefaultTranslationKeys,\n>({\n  count,\n  key,\n  translations,\n}: {\n  count?: number\n  key: TTranslationKeys\n  translations: Language<TTranslations>['translations']\n}): string => {\n  const keys = (key as DefaultTranslationKeys).split(':')\n  let keySuffix = ''\n\n  const translation: string = keys.reduce((acc: any, key, index) => {\n    if (typeof acc === 'string') {\n      return acc\n    }\n\n    if (typeof count === 'number') {\n      if (count === 0 && `${key}_zero` in acc) {\n        keySuffix = '_zero'\n      } else if (count === 1 && `${key}_one` in acc) {\n        keySuffix = '_one'\n      } else if (count === 2 && `${key}_two` in acc) {\n        keySuffix = '_two'\n      } else if (count > 5 && `${key}_many` in acc) {\n        keySuffix = '_many'\n      } else if (count > 2 && count <= 5 && `${key}_few` in acc) {\n        keySuffix = '_few'\n      } else if (`${key}_other` in acc) {\n        keySuffix = '_other'\n      }\n    }\n    let keyToUse = key\n    if (index === keys.length - 1 && keySuffix) {\n      keyToUse = `${key}${keySuffix}`\n    }\n\n    if (acc && keyToUse in acc) {\n      return acc[keyToUse]\n    }\n\n    return undefined\n  }, translations)\n\n  if (!translation) {\n    console.log('key not found:', key)\n  }\n\n  return translation || (key as string)\n}\n\n/**\n * @function replaceVars\n *\n * Replaces variables in a translation string with values from an object\n *\n * @returns string\n */\nconst replaceVars = ({\n  translationString,\n  vars,\n}: {\n  translationString: string\n  vars: {\n    [key: string]: any\n  }\n}) => {\n  const parts = translationString.split(/(\\{\\{.*?\\}\\})/)\n\n  return parts\n    .map((part) => {\n      if (part.startsWith('{{') && part.endsWith('}}')) {\n        const placeholder = part.substring(2, part.length - 2).trim()\n        const value = vars[placeholder]\n        return value !== undefined && value !== null ? value : part\n      } else {\n        return part\n      }\n    })\n    .join('')\n}\n\n/**\n * @function t\n *\n * Merges config defined translations with translations passed in as an argument\n * returns a function that can be used to translate a string\n *\n * @returns string\n */\nexport function t<\n  TTranslations = DefaultTranslationsObject,\n  TTranslationKeys = DefaultTranslationKeys,\n>({\n  key,\n  translations,\n  vars,\n}: {\n  key: TTranslationKeys\n  translations?: Language<TTranslations>['translations']\n  vars?: Record<string, any>\n}): string {\n  let translationString = getTranslationString({\n    count: typeof vars?.count === 'number' ? vars.count : undefined,\n    key,\n    translations,\n  })\n\n  if (vars) {\n    translationString = replaceVars({\n      translationString,\n      vars,\n    })\n  }\n\n  if (!translationString) {\n    translationString = key as string\n  }\n\n  return translationString\n}\n\nconst initTFunction: InitTFunction = (args) => {\n  const { config, language, translations } = args\n  const mergedTranslations =\n    language && config?.translations?.[language as keyof typeof config.translations]\n      ? deepMergeSimple<DefaultTranslationsObject>(\n          translations,\n          config.translations[language as keyof typeof config.translations]!,\n        )\n      : translations\n\n  return {\n    t: (key, vars) => {\n      return t({\n        key,\n        translations: mergedTranslations,\n        vars,\n      })\n    },\n    translations: mergedTranslations,\n  }\n}\n\nfunction memoize<T extends Parameters<InitI18n>[0], K extends keyof T>(\n  fn: (args: T) => Promise<I18n>,\n  keys: K[],\n): (args: T) => Promise<I18n> {\n  const cacheMap = new Map<string, I18n>()\n\n  const memoized = async (args: T) => {\n    const cacheKey = keys.reduce((acc, key) => acc + String(args[key]), '')\n\n    if (!cacheMap.has(cacheKey)) {\n      const result = await fn(args)\n      cacheMap.set(cacheKey, result)\n    }\n\n    return cacheMap.get(cacheKey)!\n  }\n\n  return memoized\n}\n\nexport const initI18n = memoize(\n  async ({ config, context, language = config.fallbackLanguage }) => {\n    if (!language || !config.supportedLanguages?.[language]) {\n      throw new Error(`Language ${language} not supported`)\n    }\n\n    const translations = getTranslationsByContext(config.supportedLanguages?.[language], context)\n\n    const { t, translations: mergedTranslations } = initTFunction({\n      config: config as any,\n      language: language || config.fallbackLanguage,\n      translations: translations as any,\n    })\n\n    const dateFNSKey = config.supportedLanguages[language]?.dateFNSKey || 'en-US'\n\n    const dateFNS = await importDateFNSLocale(dateFNSKey)\n\n    const i18n: I18n = {\n      dateFNS,\n      dateFNSKey,\n      fallbackLanguage: config.fallbackLanguage!,\n      language: language || config.fallbackLanguage,\n      t,\n      translations: mergedTranslations,\n    }\n\n    return i18n\n  },\n  ['language', 'context'] satisfies Array<keyof Parameters<InitI18n>[0]>,\n)\n"], "names": ["importDateFNSLocale", "deepMergeSimple", "getTranslationsByContext", "getTranslationString", "count", "key", "translations", "keys", "split", "keySuffix", "translation", "reduce", "acc", "index", "keyToUse", "length", "undefined", "console", "log", "replaceVars", "translationString", "vars", "parts", "map", "part", "startsWith", "endsWith", "placeholder", "substring", "trim", "value", "join", "t", "initTFunction", "args", "config", "language", "mergedTranslations", "memoize", "fn", "cacheMap", "Map", "memoized", "cache<PERSON>ey", "String", "has", "result", "set", "get", "initI18n", "context", "fallbackLanguage", "supportedLanguages", "Error", "dateFNS<PERSON>ey", "dateFNS", "i18n"], "mappings": "AASA,SAASA,mBAAmB,QAAQ,4BAA2B;AAC/D,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,wBAAwB,QAAQ,gCAA+B;AAExE;;;;;;CAMC,GACD,OAAO,MAAMC,uBAAuB,CAGlC,EACAC,KAAK,EACLC,GAAG,EACHC,YAAY,EAKb;IACC,MAAMC,OAAO,AAACF,IAA+BG,KAAK,CAAC;IACnD,IAAIC,YAAY;IAEhB,MAAMC,cAAsBH,KAAKI,MAAM,CAAC,CAACC,KAAUP,KAAKQ;QACtD,IAAI,OAAOD,QAAQ,UAAU;YAC3B,OAAOA;QACT;QAEA,IAAI,OAAOR,UAAU,UAAU;YAC7B,IAAIA,UAAU,KAAK,GAAGC,IAAI,KAAK,CAAC,IAAIO,KAAK;gBACvCH,YAAY;YACd,OAAO,IAAIL,UAAU,KAAK,GAAGC,IAAI,IAAI,CAAC,IAAIO,KAAK;gBAC7CH,YAAY;YACd,OAAO,IAAIL,UAAU,KAAK,GAAGC,IAAI,IAAI,CAAC,IAAIO,KAAK;gBAC7CH,YAAY;YACd,OAAO,IAAIL,QAAQ,KAAK,GAAGC,IAAI,KAAK,CAAC,IAAIO,KAAK;gBAC5CH,YAAY;YACd,OAAO,IAAIL,QAAQ,KAAKA,SAAS,KAAK,GAAGC,IAAI,IAAI,CAAC,IAAIO,KAAK;gBACzDH,YAAY;YACd,OAAO,IAAI,GAAGJ,IAAI,MAAM,CAAC,IAAIO,KAAK;gBAChCH,YAAY;YACd;QACF;QACA,IAAIK,WAAWT;QACf,IAAIQ,UAAUN,KAAKQ,MAAM,GAAG,KAAKN,WAAW;YAC1CK,WAAW,GAAGT,MAAMI,WAAW;QACjC;QAEA,IAAIG,OAAOE,YAAYF,KAAK;YAC1B,OAAOA,GAAG,CAACE,SAAS;QACtB;QAEA,OAAOE;IACT,GAAGV;IAEH,IAAI,CAACI,aAAa;QAChBO,QAAQC,GAAG,CAAC,kBAAkBb;IAChC;IAEA,OAAOK,eAAgBL;AACzB,EAAC;AAED;;;;;;CAMC,GACD,MAAMc,cAAc,CAAC,EACnBC,iBAAiB,EACjBC,IAAI,EAML;IACC,MAAMC,QAAQF,kBAAkBZ,KAAK,CAAC;IAEtC,OAAOc,MACJC,GAAG,CAAC,CAACC;QACJ,IAAIA,KAAKC,UAAU,CAAC,SAASD,KAAKE,QAAQ,CAAC,OAAO;YAChD,MAAMC,cAAcH,KAAKI,SAAS,CAAC,GAAGJ,KAAKT,MAAM,GAAG,GAAGc,IAAI;YAC3D,MAAMC,QAAQT,IAAI,CAACM,YAAY;YAC/B,OAAOG,UAAUd,aAAac,UAAU,OAAOA,QAAQN;QACzD,OAAO;YACL,OAAOA;QACT;IACF,GACCO,IAAI,CAAC;AACV;AAEA;;;;;;;CAOC,GACD,OAAO,SAASC,EAGd,EACA3B,GAAG,EACHC,YAAY,EACZe,IAAI,EAKL;IACC,IAAID,oBAAoBjB,qBAAqB;QAC3CC,OAAO,OAAOiB,MAAMjB,UAAU,WAAWiB,KAAKjB,KAAK,GAAGY;QACtDX;QACAC;IACF;IAEA,IAAIe,MAAM;QACRD,oBAAoBD,YAAY;YAC9BC;YACAC;QACF;IACF;IAEA,IAAI,CAACD,mBAAmB;QACtBA,oBAAoBf;IACtB;IAEA,OAAOe;AACT;AAEA,MAAMa,gBAA+B,CAACC;IACpC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE9B,YAAY,EAAE,GAAG4B;IAC3C,MAAMG,qBACJD,YAAYD,QAAQ7B,cAAc,CAAC8B,SAA6C,GAC5EnC,gBACEK,cACA6B,OAAO7B,YAAY,CAAC8B,SAA6C,IAEnE9B;IAEN,OAAO;QACL0B,GAAG,CAAC3B,KAAKgB;YACP,OAAOW,EAAE;gBACP3B;gBACAC,cAAc+B;gBACdhB;YACF;QACF;QACAf,cAAc+B;IAChB;AACF;AAEA,SAASC,QACPC,EAA8B,EAC9BhC,IAAS;IAET,MAAMiC,WAAW,IAAIC;IAErB,MAAMC,WAAW,OAAOR;QACtB,MAAMS,WAAWpC,KAAKI,MAAM,CAAC,CAACC,KAAKP,MAAQO,MAAMgC,OAAOV,IAAI,CAAC7B,IAAI,GAAG;QAEpE,IAAI,CAACmC,SAASK,GAAG,CAACF,WAAW;YAC3B,MAAMG,SAAS,MAAMP,GAAGL;YACxBM,SAASO,GAAG,CAACJ,UAAUG;QACzB;QAEA,OAAON,SAASQ,GAAG,CAACL;IACtB;IAEA,OAAOD;AACT;AAEA,OAAO,MAAMO,WAAWX,QACtB,OAAO,EAAEH,MAAM,EAAEe,OAAO,EAAEd,WAAWD,OAAOgB,gBAAgB,EAAE;IAC5D,IAAI,CAACf,YAAY,CAACD,OAAOiB,kBAAkB,EAAE,CAAChB,SAAS,EAAE;QACvD,MAAM,IAAIiB,MAAM,CAAC,SAAS,EAAEjB,SAAS,cAAc,CAAC;IACtD;IAEA,MAAM9B,eAAeJ,yBAAyBiC,OAAOiB,kBAAkB,EAAE,CAAChB,SAAS,EAAEc;IAErF,MAAM,EAAElB,CAAC,EAAE1B,cAAc+B,kBAAkB,EAAE,GAAGJ,cAAc;QAC5DE,QAAQA;QACRC,UAAUA,YAAYD,OAAOgB,gBAAgB;QAC7C7C,cAAcA;IAChB;IAEA,MAAMgD,aAAanB,OAAOiB,kBAAkB,CAAChB,SAAS,EAAEkB,cAAc;IAEtE,MAAMC,UAAU,MAAMvD,oBAAoBsD;IAE1C,MAAME,OAAa;QACjBD;QACAD;QACAH,kBAAkBhB,OAAOgB,gBAAgB;QACzCf,UAAUA,YAAYD,OAAOgB,gBAAgB;QAC7CnB;QACA1B,cAAc+B;IAChB;IAEA,OAAOmB;AACT,GACA;IAAC;IAAY;CAAU,EACxB"}