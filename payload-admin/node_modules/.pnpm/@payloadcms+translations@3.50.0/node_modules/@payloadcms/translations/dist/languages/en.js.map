{"version": 3, "sources": ["../../src/languages/en.ts"], "sourcesContent": ["import { title } from 'process'\n\nimport type { Language } from '../types.js'\n\nexport const enTranslations = {\n  authentication: {\n    account: 'Account',\n    accountOfCurrentUser: 'Account of current user',\n    accountVerified: 'Account verified successfully.',\n    alreadyActivated: 'Already Activated',\n    alreadyLoggedIn: 'Already logged in',\n    api<PERSON>ey: 'API Key',\n    authenticated: 'Authenticated',\n    backToLogin: 'Back to login',\n    beginCreateFirstUser: 'To begin, create your first user.',\n    changePassword: 'Change Password',\n    checkYourEmailForPasswordReset:\n      \"If the email address is associated with an account, you will receive instructions to reset your password shortly. Please check your spam or junk mail folder if you don't see the email in your inbox.\",\n    confirmGeneration: 'Confirm Generation',\n    confirmPassword: 'Confirm Password',\n    createFirstUser: 'Create first user',\n    emailNotValid: 'The email provided is not valid',\n    emailOrUsername: 'Email or Username',\n    emailSent: 'Email Sent',\n    emailVerified: 'Email verified successfully.',\n    enableAPI<PERSON>ey: 'Enable API Key',\n    failedToUnlock: 'Failed to unlock',\n    forceUnlock: 'Force Unlock',\n    forgotPassword: 'Forgot Password',\n    forgotPasswordEmailInstructions:\n      'Please enter your email below. You will receive an email message with instructions on how to reset your password.',\n    forgotPasswordUsernameInstructions:\n      'Please enter your username below. Instructions on how to reset your password will be sent to email address associated with your username.',\n    usernameNotValid: 'The username provided is not valid',\n\n    forgotPasswordQuestion: 'Forgot password?',\n    generate: 'Generate',\n    generateNewAPIKey: 'Generate new API key',\n    generatingNewAPIKeyWillInvalidate:\n      'Generating a new API key will <1>invalidate</1> the previous key. Are you sure you wish to continue?',\n    lockUntil: 'Lock Until',\n    logBackIn: 'Log back in',\n    loggedIn: 'To log in with another user, you should <0>log out</0> first.',\n    loggedInChangePassword:\n      'To change your password, go to your <0>account</0> and edit your password there.',\n    loggedOutInactivity: 'You have been logged out due to inactivity.',\n    loggedOutSuccessfully: 'You have been logged out successfully.',\n    loggingOut: 'Logging out...',\n    login: 'Login',\n    loginAttempts: 'Login Attempts',\n    loginUser: 'Login user',\n    loginWithAnotherUser: 'To log in with another user, you should <0>log out</0> first.',\n    logOut: 'Log out',\n    logout: 'Logout',\n    logoutSuccessful: 'Logout successful.',\n    logoutUser: 'Logout user',\n    newAccountCreated:\n      'A new account has just been created for you to access <a href=\"{{serverURL}}\">{{serverURL}}</a> Please click on the following link or paste the URL below into your browser to verify your email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> After verifying your email, you will be able to log in successfully.',\n    newAPIKeyGenerated: 'New API Key Generated.',\n    newPassword: 'New Password',\n    passed: 'Authentication Passed',\n    passwordResetSuccessfully: 'Password reset successfully.',\n    resetPassword: 'Reset Password',\n    resetPasswordExpiration: 'Reset Password Expiration',\n    resetPasswordToken: 'Reset Password Token',\n    resetYourPassword: 'Reset Your Password',\n    stayLoggedIn: 'Stay logged in',\n    successfullyRegisteredFirstUser: 'Successfully registered first user.',\n    successfullyUnlocked: 'Successfully unlocked',\n    tokenRefreshSuccessful: 'Token refresh successful.',\n    unableToVerify: 'Unable to Verify',\n    username: 'Username',\n    verified: 'Verified',\n    verifiedSuccessfully: 'Verified Successfully',\n    verify: 'Verify',\n    verifyUser: 'Verify User',\n    verifyYourEmail: 'Verify your email',\n    youAreInactive:\n      \"You haven't been active in a little while and will shortly be automatically logged out for your own security. Would you like to stay logged in?\",\n    youAreReceivingResetPassword:\n      'You are receiving this because you (or someone else) have requested the reset of the password for your account. Please click on the following link, or paste this into your browser to complete the process:',\n    youDidNotRequestPassword:\n      'If you did not request this, please ignore this email and your password will remain unchanged.',\n  },\n  error: {\n    accountAlreadyActivated: 'This account has already been activated.',\n    autosaving: 'There was a problem while autosaving this document.',\n    correctInvalidFields: 'Please correct invalid fields.',\n    deletingFile: 'There was an error deleting file.',\n    deletingTitle:\n      'There was an error while deleting {{title}}. Please check your connection and try again.',\n    documentNotFound:\n      'The document with ID {{id}} could not be found. It may have been deleted or never existed, or you may not have access to it.',\n    emailOrPasswordIncorrect: 'The email or password provided is incorrect.',\n    followingFieldsInvalid_one: 'The following field is invalid:',\n    followingFieldsInvalid_other: 'The following fields are invalid:',\n    incorrectCollection: 'Incorrect Collection',\n    insufficientClipboardPermissions:\n      'Clipboard access denied. Please check your clipboard permissions.',\n    invalidClipboardData: 'Invalid clipboard data.',\n    invalidFileType: 'Invalid file type',\n    invalidFileTypeValue: 'Invalid file type: {{value}}',\n    invalidRequestArgs: 'Invalid arguments passed in request: {{args}}',\n    loadingDocument: 'There was a problem loading the document with ID of {{id}}.',\n    localesNotSaved_one: 'The following locale could not be saved:',\n    localesNotSaved_other: 'The following locales could not be saved:',\n    logoutFailed: 'Logout failed.',\n    missingEmail: 'Missing email.',\n    missingIDOfDocument: 'Missing ID of document to update.',\n    missingIDOfVersion: 'Missing ID of version.',\n    missingRequiredData: 'Missing required data.',\n    noFilesUploaded: 'No files were uploaded.',\n    noMatchedField: 'No matched field found for \"{{label}}\"',\n    notAllowedToAccessPage: 'You are not allowed to access this page.',\n    notAllowedToPerformAction: 'You are not allowed to perform this action.',\n    notFound: 'The requested resource was not found.',\n    noUser: 'No User',\n    previewing: 'There was a problem previewing this document.',\n    problemUploadingFile: 'There was a problem while uploading the file.',\n    restoringTitle:\n      'There was an error while restoring {{title}}. Please check your connection and try again.',\n    tokenInvalidOrExpired: 'Token is either invalid or has expired.',\n    tokenNotProvided: 'Token not provided.',\n    unableToCopy: 'Unable to copy.',\n    unableToDeleteCount: 'Unable to delete {{count}} out of {{total}} {{label}}.',\n    unableToReindexCollection: 'Error reindexing collection {{collection}}. Operation aborted.',\n    unableToUpdateCount: 'Unable to update {{count}} out of {{total}} {{label}}.',\n    unauthorized: 'Unauthorized, you must be logged in to make this request.',\n    unauthorizedAdmin: 'Unauthorized, this user does not have access to the admin panel.',\n    unknown: 'An unknown error has occurred.',\n    unPublishingDocument: 'There was a problem while un-publishing this document.',\n    unspecific: 'An error has occurred.',\n    unverifiedEmail: 'Please verify your email before logging in.',\n    userEmailAlreadyRegistered: 'A user with the given email is already registered.',\n    userLocked: 'This user is locked due to having too many failed login attempts.',\n    usernameAlreadyRegistered: 'A user with the given username is already registered.',\n    usernameOrPasswordIncorrect: 'The username or password provided is incorrect.',\n    valueMustBeUnique: 'Value must be unique',\n    verificationTokenInvalid: 'Verification token is invalid.',\n  },\n  fields: {\n    addLabel: 'Add {{label}}',\n    addLink: 'Add Link',\n    addNew: 'Add new',\n    addNewLabel: 'Add new {{label}}',\n    addRelationship: 'Add Relationship',\n    addUpload: 'Add Upload',\n    block: 'block',\n    blocks: 'blocks',\n    blockType: 'Block Type',\n    chooseBetweenCustomTextOrDocument:\n      'Choose between entering a custom text URL or linking to another document.',\n    chooseDocumentToLink: 'Choose a document to link to',\n    chooseFromExisting: 'Choose from existing',\n    chooseLabel: 'Choose {{label}}',\n    collapseAll: 'Collapse All',\n    customURL: 'Custom URL',\n    editLabelData: 'Edit {{label}} data',\n    editLink: 'Edit Link',\n    editRelationship: 'Edit Relationship',\n    enterURL: 'Enter a URL',\n    internalLink: 'Internal Link',\n    itemsAndMore: '{{items}} and {{count}} more',\n    labelRelationship: '{{label}} Relationship',\n    latitude: 'Latitude',\n    linkedTo: 'Linked to <0>{{label}}</0>',\n    linkType: 'Link Type',\n    longitude: 'Longitude',\n    newLabel: 'New {{label}}',\n    openInNewTab: 'Open in new tab',\n    passwordsDoNotMatch: 'Passwords do not match.',\n    relatedDocument: 'Related Document',\n    relationTo: 'Relation To',\n    removeRelationship: 'Remove Relationship',\n    removeUpload: 'Remove Upload',\n    saveChanges: 'Save changes',\n    searchForBlock: 'Search for a block',\n    selectExistingLabel: 'Select existing {{label}}',\n    selectFieldsToEdit: 'Select fields to edit',\n    showAll: 'Show All',\n    swapRelationship: 'Swap Relationship',\n    swapUpload: 'Swap Upload',\n    textToDisplay: 'Text to display',\n    toggleBlock: 'Toggle block',\n    uploadNewLabel: 'Upload new {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Browse by Folder',\n    byFolder: 'By Folder',\n    deleteFolder: 'Delete Folder',\n    folderName: 'Folder Name',\n    folders: 'Folders',\n    folderTypeDescription:\n      'Select which type of collection documents should be allowed in this folder.',\n    itemHasBeenMoved: '{{title}} has been moved to {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} has been moved to the root folder',\n    itemsMovedToFolder: '{{title}} moved to {{folderName}}',\n    itemsMovedToRoot: '{{title}} moved to the root folder',\n    moveFolder: 'Move Folder',\n    moveItemsToFolderConfirmation:\n      'You are about to move <1>{{count}} {{label}}</1> to <2>{{toFolder}}</2>. Are you sure?',\n    moveItemsToRootConfirmation:\n      'You are about to move <1>{{count}} {{label}}</1> to the root folder. Are you sure?',\n    moveItemToFolderConfirmation:\n      'You are about to move <1>{{title}}</1> to <2>{{toFolder}}</2>. Are you sure?',\n    moveItemToRootConfirmation:\n      'You are about to move <1>{{title}}</1> to the root folder. Are you sure?',\n    movingFromFolder: 'Moving {{title}} from {{fromFolder}}',\n    newFolder: 'New Folder',\n    noFolder: 'No Folder',\n    renameFolder: 'Rename Folder',\n    searchByNameInFolder: 'Search by Name in {{folderName}}',\n    selectFolderForItem: 'Select folder for {{title}}',\n  },\n  general: {\n    name: 'Name',\n    aboutToDelete: 'You are about to delete the {{label}} <1>{{title}}</1>. Are you sure?',\n    aboutToDeleteCount_many: 'You are about to delete {{count}} {{label}}',\n    aboutToDeleteCount_one: 'You are about to delete {{count}} {{label}}',\n    aboutToDeleteCount_other: 'You are about to delete {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'You are about to permanently delete the {{label}} <1>{{title}}</1>. Are you sure?',\n    aboutToPermanentlyDeleteTrash:\n      'You are about to permanently delete <0>{{count}}</0> <1>{{label}}</1> from the trash. Are you sure?',\n    aboutToRestore: 'You are about to restore the {{label}} <1>{{title}}</1>. Are you sure?',\n    aboutToRestoreAsDraft:\n      'You are about to restore the {{label}} <1>{{title}}</1> as a draft. Are you sure?',\n    aboutToRestoreAsDraftCount: 'You are about to restore {{count}} {{label}} as draft',\n    aboutToRestoreCount: 'You are about to restore {{count}} {{label}}',\n    aboutToTrash:\n      'You are about to move the {{label}} <1>{{title}}</1> to the trash. Are you sure?',\n    aboutToTrashCount: 'You are about to move {{count}} {{label}} to the trash',\n    addBelow: 'Add Below',\n    addFilter: 'Add Filter',\n    adminTheme: 'Admin Theme',\n    all: 'All',\n    allCollections: 'All Collections',\n    allLocales: 'All locales',\n    and: 'And',\n    anotherUser: 'Another user',\n    anotherUserTakenOver: 'Another user has taken over editing this document.',\n    applyChanges: 'Apply Changes',\n    ascending: 'Ascending',\n    automatic: 'Automatic',\n    backToDashboard: 'Back to Dashboard',\n    cancel: 'Cancel',\n    changesNotSaved:\n      'Your changes have not been saved. If you leave now, you will lose your changes.',\n    clear: 'Clear',\n    clearAll: 'Clear All',\n    close: 'Close',\n    collapse: 'Collapse',\n    collections: 'Collections',\n    columns: 'Columns',\n    columnToSort: 'Column to Sort',\n    confirm: 'Confirm',\n    confirmCopy: 'Confirm copy',\n    confirmDeletion: 'Confirm deletion',\n    confirmDuplication: 'Confirm duplication',\n    confirmMove: 'Confirm move',\n    confirmReindex: 'Reindex all {{collections}}?',\n    confirmReindexAll: 'Reindex all collections?',\n    confirmReindexDescription:\n      'This will remove existing indexes and reindex documents in the {{collections}} collections.',\n    confirmReindexDescriptionAll:\n      'This will remove existing indexes and reindex documents in all collections.',\n    confirmRestoration: 'Confirm restoration',\n    copied: 'Copied',\n    copy: 'Copy',\n    copyField: 'Copy Field',\n    copying: 'Copying',\n    copyRow: 'Copy Row',\n    copyWarning:\n      'You are about to overwrite {{to}} with {{from}} for {{label}} {{title}}. Are you sure?',\n    create: 'Create',\n    created: 'Created',\n    createdAt: 'Created At',\n    createNew: 'Create New',\n    createNewLabel: 'Create new {{label}}',\n    creating: 'Creating',\n    creatingNewLabel: 'Creating new {{label}}',\n    currentlyEditing:\n      'is currently editing this document. If you take over, they will be blocked from continuing to edit, and may also lose unsaved changes.',\n    custom: 'Custom',\n    dark: 'Dark',\n    dashboard: 'Dashboard',\n    delete: 'Delete',\n    deleted: 'Deleted',\n    deletedAt: 'Deleted At',\n    deletedCountSuccessfully: 'Deleted {{count}} {{label}} successfully.',\n    deletedSuccessfully: 'Deleted successfully.',\n    deletePermanently: 'Skip trash and delete permanently',\n    deleting: 'Deleting...',\n    depth: 'Depth',\n    descending: 'Descending',\n    deselectAllRows: 'Deselect all rows',\n    document: 'Document',\n    documentIsTrashed: 'This {{label}} is trashed and is read-only.',\n    documentLocked: 'Document locked',\n    documents: 'Documents',\n    duplicate: 'Duplicate',\n    duplicateWithoutSaving: 'Duplicate without saving changes',\n    edit: 'Edit',\n    editAll: 'Edit all',\n    editedSince: 'Edited since',\n    editing: 'Editing',\n    editingLabel_many: 'Editing {{count}} {{label}}',\n    editingLabel_one: 'Editing {{count}} {{label}}',\n    editingLabel_other: 'Editing {{count}} {{label}}',\n    editingTakenOver: 'Editing taken over',\n    editLabel: 'Edit {{label}}',\n    email: 'Email',\n    emailAddress: 'Email Address',\n    emptyTrash: 'Empty trash',\n    emptyTrashLabel: 'Empty {{label}} trash',\n    enterAValue: 'Enter a value',\n    error: 'Error',\n    errors: 'Errors',\n    exitLivePreview: 'Exit Live Preview',\n    export: 'Export',\n    fallbackToDefaultLocale: 'Fallback to default locale',\n    false: 'False',\n    filter: 'Filter',\n    filters: 'Filters',\n    filterWhere: 'Filter {{label}} where',\n    globals: 'Globals',\n    goBack: 'Go back',\n    groupByLabel: 'Group by {{label}}',\n    import: 'Import',\n    isEditing: 'is editing',\n    item: 'item',\n    items: 'items',\n    language: 'Language',\n    lastModified: 'Last Modified',\n    leaveAnyway: 'Leave anyway',\n    leaveWithoutSaving: 'Leave without saving',\n    light: 'Light',\n    livePreview: 'Live Preview',\n    loading: 'Loading',\n    locale: 'Locale',\n    locales: 'Locales',\n    menu: 'Menu',\n    moreOptions: 'More options',\n    move: 'Move',\n    moveConfirm:\n      'You are about to move {{count}} {{label}} to <1>{{destination}}</1>. Are you sure?',\n    moveCount: 'Move {{count}} {{label}}',\n    moveDown: 'Move Down',\n    moveUp: 'Move Up',\n    moving: 'Moving',\n    movingCount: 'Moving {{count}} {{label}}',\n    newPassword: 'New Password',\n    next: 'Next',\n    no: 'No',\n    noDateSelected: 'No date selected',\n    noFiltersSet: 'No filters set',\n    noLabel: '<No {{label}}>',\n    none: 'None',\n    noOptions: 'No options',\n    noResults:\n      \"No {{label}} found. Either no {{label}} exist yet or none match the filters you've specified above.\",\n    notFound: 'Not Found',\n    nothingFound: 'Nothing found',\n    noTrashResults: 'No {{label}} in trash.',\n    noUpcomingEventsScheduled: 'No upcoming events scheduled.',\n    noValue: 'No value',\n    of: 'of',\n    only: 'Only',\n    open: 'Open',\n    or: 'Or',\n    order: 'Order',\n    overwriteExistingData: 'Overwrite existing field data',\n    pageNotFound: 'Page not found',\n    password: 'Password',\n    pasteField: 'Paste Field',\n    pasteRow: 'Paste Row',\n    payloadSettings: 'Payload Settings',\n    permanentlyDelete: 'Permanently Delete',\n    permanentlyDeletedCountSuccessfully: 'Permanently deleted {{count}} {{label}} successfully.',\n    perPage: 'Per Page: {{limit}}',\n    previous: 'Previous',\n    reindex: 'Reindex',\n    reindexingAll: 'Reindexing all {{collections}}.',\n    remove: 'Remove',\n    rename: 'Rename',\n    reset: 'Reset',\n    resetPreferences: 'Reset Preferences',\n    resetPreferencesDescription:\n      'This will reset all of your preferences to their default settings.',\n    resettingPreferences: 'Resetting Preferences.',\n    restore: 'Restore',\n    restoreAsPublished: 'Restore as published version',\n    restoredCountSuccessfully: 'Restored {{count}} {{label}} successfully.',\n    restoring: 'Restoring...',\n    row: 'Row',\n    rows: 'Rows',\n    save: 'Save',\n    saving: 'Saving...',\n    schedulePublishFor: 'Schedule publish for {{title}}',\n    searchBy: 'Search by {{label}}',\n    select: 'Select',\n    selectAll: 'Select all {{count}} {{label}}',\n    selectAllRows: 'Select all rows',\n    selectedCount: '{{count}} {{label}} selected',\n    selectLabel: 'Select {{label}}',\n    selectValue: 'Select a value',\n    showAllLabel: 'Show all {{label}}',\n    sorryNotFound: 'Sorry—there is nothing to correspond with your request.',\n    sort: 'Sort',\n    sortByLabelDirection: 'Sort by {{label}} {{direction}}',\n    stayOnThisPage: 'Stay on this page',\n    submissionSuccessful: 'Submission Successful.',\n    submit: 'Submit',\n    submitting: 'Submitting...',\n    success: 'Success',\n    successfullyCreated: '{{label}} successfully created.',\n    successfullyDuplicated: '{{label}} successfully duplicated.',\n    successfullyReindexed:\n      'Successfully reindexed {{count}} of {{total}} documents from {{collections}}',\n    takeOver: 'Take over',\n    thisLanguage: 'English',\n    time: 'Time',\n    timezone: 'Timezone',\n    titleDeleted: '{{label}} \"{{title}}\" successfully deleted.',\n    titleRestored: '{{label}} \"{{title}}\" successfully restored.',\n    titleTrashed: '{{label}} \"{{title}}\" moved to trash.',\n    trash: 'Trash',\n    trashedCountSuccessfully: '{{count}} {{label}} moved to trash.',\n    true: 'True',\n    unauthorized: 'Unauthorized',\n    unsavedChanges: 'You have unsaved changes. Save or discard before continuing.',\n    unsavedChangesDuplicate: 'You have unsaved changes. Would you like to continue to duplicate?',\n    untitled: 'Untitled',\n    upcomingEvents: 'Upcoming Events',\n    updatedAt: 'Updated At',\n    updatedCountSuccessfully: 'Updated {{count}} {{label}} successfully.',\n    updatedLabelSuccessfully: 'Updated {{label}} successfully.',\n    updatedSuccessfully: 'Updated successfully.',\n    updateForEveryone: 'Update for everyone',\n    updating: 'Updating',\n    uploading: 'Uploading',\n    uploadingBulk: 'Uploading {{current}} of {{total}}',\n    user: 'User',\n    username: 'Username',\n    users: 'Users',\n    value: 'Value',\n    viewing: 'Viewing',\n    viewReadOnly: 'View read-only',\n    welcome: 'Welcome',\n    yes: 'Yes',\n  },\n  localization: {\n    cannotCopySameLocale: 'Cannot copy to the same locale',\n    copyFrom: 'Copy from',\n    copyFromTo: 'Copying from {{from}} to {{to}}',\n    copyTo: 'Copy to',\n    copyToLocale: 'Copy to locale',\n    localeToPublish: 'Locale to publish',\n    selectLocaleToCopy: 'Select locale to copy',\n  },\n  operators: {\n    contains: 'contains',\n    equals: 'equals',\n    exists: 'exists',\n    intersects: 'intersects',\n    isGreaterThan: 'is greater than',\n    isGreaterThanOrEqualTo: 'is greater than or equal to',\n    isIn: 'is in',\n    isLessThan: 'is less than',\n    isLessThanOrEqualTo: 'is less than or equal to',\n    isLike: 'is like',\n    isNotEqualTo: 'is not equal to',\n    isNotIn: 'is not in',\n    isNotLike: 'is not like',\n    near: 'near',\n    within: 'within',\n  },\n  upload: {\n    addFile: 'Add file',\n    addFiles: 'Add files',\n    bulkUpload: 'Bulk Upload',\n    crop: 'Crop',\n    cropToolDescription:\n      'Drag the corners of the selected area, draw a new area or adjust the values below.',\n    download: 'Download',\n    dragAndDrop: 'Drag and drop a file',\n    dragAndDropHere: 'or drag and drop a file here',\n    editImage: 'Edit Image',\n    fileName: 'File Name',\n    fileSize: 'File Size',\n    filesToUpload: 'Files to Upload',\n    fileToUpload: 'File to Upload',\n    focalPoint: 'Focal Point',\n    focalPointDescription:\n      'Drag the focal point directly on the preview or adjust the values below.',\n    height: 'Height',\n    lessInfo: 'Less info',\n    moreInfo: 'More info',\n    noFile: 'No file',\n    pasteURL: 'Paste URL',\n    previewSizes: 'Preview Sizes',\n    selectCollectionToBrowse: 'Select a Collection to Browse',\n    selectFile: 'Select a file',\n    setCropArea: 'Set crop area',\n    setFocalPoint: 'Set focal point',\n    sizes: 'Sizes',\n    sizesFor: 'Sizes for {{label}}',\n    width: 'Width',\n  },\n  validation: {\n    emailAddress: 'Please enter a valid email address.',\n    enterNumber: 'Please enter a valid number.',\n    fieldHasNo: 'This field has no {{label}}',\n    greaterThanMax: '{{value}} is greater than the max allowed {{label}} of {{max}}.',\n    invalidInput: 'This field has an invalid input.',\n    invalidSelection: 'This field has an invalid selection.',\n    invalidSelections: 'This field has the following invalid selections:',\n    lessThanMin: '{{value}} is less than the min allowed {{label}} of {{min}}.',\n    limitReached: 'Limit reached, only {{max}} items can be added.',\n    longerThanMin: 'This value must be longer than the minimum length of {{minLength}} characters.',\n    notValidDate: '\"{{value}}\" is not a valid date.',\n    required: 'This field is required.',\n    requiresAtLeast: 'This field requires at least {{count}} {{label}}.',\n    requiresNoMoreThan: 'This field requires no more than {{count}} {{label}}.',\n    requiresTwoNumbers: 'This field requires two numbers.',\n    shorterThanMax: 'This value must be shorter than the max length of {{maxLength}} characters.',\n    timezoneRequired: 'A timezone is required.',\n    trueOrFalse: 'This field can only be equal to true or false.',\n    username:\n      'Please enter a valid username. Can contain letters, numbers, hyphens, periods and underscores.',\n    validUploadID: 'This field is not a valid upload ID.',\n  },\n  version: {\n    type: 'Type',\n    aboutToPublishSelection:\n      'You are about to publish all {{label}} in the selection. Are you sure?',\n    aboutToRestore:\n      'You are about to restore this {{label}} document to the state that it was in on {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'You are about to restore the global {{label}} to the state that it was in on {{versionDate}}.',\n    aboutToRevertToPublished:\n      \"You are about to revert this document's changes to its published state. Are you sure?\",\n    aboutToUnpublish: 'You are about to unpublish this document. Are you sure?',\n    aboutToUnpublishSelection:\n      'You are about to unpublish all {{label}} in the selection. Are you sure?',\n    autosave: 'Autosave',\n    autosavedSuccessfully: 'Autosaved successfully.',\n    autosavedVersion: 'Autosaved version',\n    changed: 'Changed',\n    changedFieldsCount_one: '{{count}} changed field',\n    changedFieldsCount_other: '{{count}} changed fields',\n    compareVersion: 'Compare version against:',\n    compareVersions: 'Compare Versions',\n    comparingAgainst: 'Comparing against',\n    confirmPublish: 'Confirm publish',\n    confirmRevertToSaved: 'Confirm revert to saved',\n    confirmUnpublish: 'Confirm unpublish',\n    confirmVersionRestoration: 'Confirm Version Restoration',\n    currentDocumentStatus: 'Current {{docStatus}} document',\n    currentDraft: 'Current Draft',\n    currentlyPublished: 'Currently Published',\n    currentlyViewing: 'Currently viewing',\n    currentPublishedVersion: 'Current Published Version',\n    draft: 'Draft',\n    draftSavedSuccessfully: 'Draft saved successfully.',\n    lastSavedAgo: 'Last saved {{distance}} ago',\n    modifiedOnly: 'Modified only',\n    moreVersions: 'More versions...',\n    noFurtherVersionsFound: 'No further versions found',\n    noRowsFound: 'No {{label}} found',\n    noRowsSelected: 'No {{label}} selected',\n    preview: 'Preview',\n    previouslyDraft: 'Previously a Draft',\n    previouslyPublished: 'Previously Published',\n    previousVersion: 'Previous Version',\n    problemRestoringVersion: 'There was a problem restoring this version',\n    publish: 'Publish',\n    publishAllLocales: 'Publish all locales',\n    publishChanges: 'Publish changes',\n    published: 'Published',\n    publishIn: 'Publish in {{locale}}',\n    publishing: 'Publishing',\n    restoreAsDraft: 'Restore as draft',\n    restoredSuccessfully: 'Restored Successfully.',\n    restoreThisVersion: 'Restore this version',\n    restoring: 'Restoring...',\n    reverting: 'Reverting...',\n    revertToPublished: 'Revert to published',\n    saveDraft: 'Save Draft',\n    scheduledSuccessfully: 'Scheduled successfully.',\n    schedulePublish: 'Schedule Publish',\n    selectLocales: 'Select locales to display',\n    selectVersionToCompare: 'Select a version to compare',\n    showingVersionsFor: 'Showing versions for:',\n    showLocales: 'Show locales:',\n    specificVersion: 'Specific Version',\n    status: 'Status',\n    unpublish: 'Unpublish',\n    unpublishing: 'Unpublishing...',\n    version: 'Version',\n    versionAgo: '{{distance}} ago',\n    versionCount_many: '{{count}} versions found',\n    versionCount_none: 'No versions found',\n    versionCount_one: '{{count}} version found',\n    versionCount_other: '{{count}} versions found',\n    versionCreatedOn: '{{version}} created on:',\n    versionID: 'Version ID',\n    versions: 'Versions',\n    viewingVersion: 'Viewing version for the {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Viewing version for the global {{entityLabel}}',\n    viewingVersions: 'Viewing versions for the {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Viewing versions for the global {{entityLabel}}',\n  },\n}\n\nexport const en: Language = {\n  dateFNSKey: 'en-US',\n  translations: enTranslations,\n}\n"], "names": ["enTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordUsernameInstructions", "usernameNotValid", "forgotPasswordQuestion", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "en", "dateFNS<PERSON>ey", "translations"], "mappings": "AAIA,OAAO,MAAMA,iBAAiB;IAC5BC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,oCACE;QACFC,kBAAkB;QAElBC,wBAAwB;QACxBC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BAA2B;QAC3BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbvQ,aAAa;QACbwQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNjV,UAAU;QACVkV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb9Z,UACE;QACF+Z,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}