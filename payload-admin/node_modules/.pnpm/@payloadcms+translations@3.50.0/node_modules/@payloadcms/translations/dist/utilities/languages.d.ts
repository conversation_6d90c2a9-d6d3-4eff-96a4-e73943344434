import type { AcceptedLanguages } from '../types.js';
export declare const rtlLanguages: readonly ["ar", "fa", "he"];
export declare const acceptedLanguages: readonly ["ar", "az", "bg", "bn-BD", "bn-IN", "ca", "cs", "bn-BD", "bn-IN", "da", "de", "en", "es", "et", "fa", "fr", "he", "hr", "hu", "hy", "id", "it", "ja", "ko", "lt", "lv", "my", "nb", "nl", "pl", "pt", "ro", "rs", "rs-latin", "ru", "sk", "sl", "sv", "th", "tr", "uk", "vi", "zh", "zh-TW"];
export declare function extractHeaderLanguage(acceptLanguageHeader: string): AcceptedLanguages | undefined;
//# sourceMappingURL=languages.d.ts.map