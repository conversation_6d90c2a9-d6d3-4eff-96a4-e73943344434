export const esTranslations = {
    authentication: {
        account: 'Cuenta',
        accountOfCurrentUser: 'Cuenta del usuario actual',
        accountVerified: 'Cuenta verificada con éxito.',
        alreadyActivated: 'Ya Activado',
        alreadyLoggedIn: 'Ya has iniciado sesión',
        apiKey: 'Clave API',
        authenticated: 'Autenticado',
        backToLogin: 'Regresar al inicio de sesión',
        beginCreateFirstUser: 'Para empezar, crea tu primer usuario.',
        changePassword: 'Cambiar contraseña',
        checkYourEmailForPasswordReset: 'Si la dirección de correo electrónico está asociada a una cuenta, recibirás instrucciones para restablecer tu contraseña en breve. Por favor, revisa tu carpeta de spam o correo no deseado si no ves el correo en tu bandeja de entrada.',
        confirmGeneration: 'Confirmar Generación',
        confirmPassword: 'Confirmar Contrase<PERSON>',
        createFirstUser: 'Crear el primer usuario',
        emailNotValid: 'El correo proporcionado es inválido',
        emailOrUsername: 'Correo electrónico o nombre de usuario',
        emailSent: 'Correo Enviado',
        emailVerified: 'Correo electrónico verificado con éxito.',
        enableAPIKey: 'Habilitar Clave API',
        failedToUnlock: 'Desbloqueo Fallido',
        forceUnlock: 'Forzar Desbloqueo',
        forgotPassword: 'Olvidé mi contraseña',
        forgotPasswordEmailInstructions: 'Por favor introduce tu correo electrónico. Recibirás un mensaje con las instrucciones para restablecer tu contraseña.',
        forgotPasswordQuestion: '¿Olvidaste tu contraseña?',
        forgotPasswordUsernameInstructions: 'Por favor, introduce tu nombre de usuario a continuación. Se enviarán instrucciones sobre cómo restablecer tu contraseña a la dirección de correo electrónico asociada a tu nombre de usuario.',
        generate: 'Generar',
        generateNewAPIKey: 'Generar Nueva Clave API',
        generatingNewAPIKeyWillInvalidate: 'Generar una nueva clave API <1>invalidará</1> la clave anterior. ¿Deseas continuar?',
        lockUntil: 'Bloquear Hasta',
        logBackIn: 'Volver a iniciar sesión',
        loggedIn: 'Para iniciar sesión con otro usuario, primero <0>cierra tu sesión</0>.',
        loggedInChangePassword: 'Para cambiar tu contraseña, entra a <0>tu cuenta</0> y edita la contraseña desde ahí.',
        loggedOutInactivity: 'Tú sesión se cerró debido a inactividad.',
        loggedOutSuccessfully: 'Tú sesión se cerró correctamente.',
        loggingOut: 'Cerrando sesión...',
        login: 'Iniciar sesión',
        loginAttempts: 'Intentos de inicio de sesión',
        loginUser: 'Iniciar sesión',
        loginWithAnotherUser: 'Para iniciar sesión con otro usuario, primero <0>cierra tu sesión</0>.',
        logOut: 'Cerrar sesión',
        logout: 'Cerrar sesión',
        logoutSuccessful: 'Cierre de sesión exitoso.',
        logoutUser: 'Cerrar sesión',
        newAccountCreated: 'Se ha creado una nueva cuenta para que puedas acceder a <a href="{{serverURL}}">{{serverURL}}</a>. Por favor, haz click o copia el siguiente enlace a tu navegador para verificar tu correo: <a href="{{verificationURL}}">{{verificationURL}}</a>.<br> Una vez hayas verificado tu correo, podrás iniciar sesión.',
        newAPIKeyGenerated: 'Nueva Clave de API Generada.',
        newPassword: 'Nueva Contraseña',
        passed: 'Autenticación Exitosa',
        passwordResetSuccessfully: 'Contraseña restablecida con éxito.',
        resetPassword: 'Restablecer Contraseña',
        resetPasswordExpiration: 'Restablecer Caducidad de la Contraseña',
        resetPasswordToken: 'Restablecer Token de la Contraseña',
        resetYourPassword: 'Restablecer tu Contraseña',
        stayLoggedIn: 'Mantener sesión abierta',
        successfullyRegisteredFirstUser: 'Primer usuario registrado exitosamente.',
        successfullyUnlocked: 'Desbloqueado exitosamente',
        tokenRefreshSuccessful: 'Token actualizado con éxito.',
        unableToVerify: 'No se pudo Verificar',
        username: 'Nombre de usuario',
        usernameNotValid: 'El nombre de usuario proporcionado no es válido.',
        verified: 'Verificado',
        verifiedSuccessfully: 'Verificado Correctamente',
        verify: 'Verificar',
        verifyUser: 'Verificar Usuario',
        verifyYourEmail: 'Verifica tu correo',
        youAreInactive: 'Has estado inactivo por un tiempo y por tu seguridad se cerrará tu sesión automáticamente en breve. ¿Deseas mantener tu sesión abierta?',
        youAreReceivingResetPassword: 'Estás recibiendo este correo porque tú (o alguien más) ha solicitado restablecer la contraseña de tu cuenta. Por favor haz clic en el siguiente enlace o pégalo en tu navegador para completar el proceso:',
        youDidNotRequestPassword: 'Si no solicitaste esto, por favor ignora este correo y tu contraseña permanecerá sin cambios.'
    },
    error: {
        accountAlreadyActivated: 'Esta cuenta ya fue activada.',
        autosaving: 'Hubo un problema al guardar automáticamente este documento.',
        correctInvalidFields: 'Por favor, corrige los campos inválidos.',
        deletingFile: 'Ocurrió un error al eliminar el archivo.',
        deletingTitle: 'Ocurrió un error al eliminar {{title}}. Por favor, revisa tu conexión y vuelve a intentarlo.',
        documentNotFound: 'No se pudo encontrar el documento con ID {{id}}. Puede haber sido eliminado o nunca existió, o puede que no tenga acceso a él.',
        emailOrPasswordIncorrect: 'El correo o la contraseña son incorrectos.',
        followingFieldsInvalid_one: 'El siguiente campo es inválido:',
        followingFieldsInvalid_other: 'Los siguientes campos son inválidos:',
        incorrectCollection: 'Colección Incorrecta',
        insufficientClipboardPermissions: 'Acceso al portapapeles denegado. Verifique los permisos del portapapeles.',
        invalidClipboardData: 'Datos del portapapeles no válidos.',
        invalidFileType: 'Tipo de archivo inválido',
        invalidFileTypeValue: 'Tipo de archivo inválido: {{value}}',
        invalidRequestArgs: 'Argumentos inválidos en la solicitud: {{args}}',
        loadingDocument: 'Ocurrió un problema al cargar el documento con ID {{id}}.',
        localesNotSaved_one: 'No se pudo guardar el siguiente idioma:',
        localesNotSaved_other: 'No se pudieron guardar los siguientes idiomas:',
        logoutFailed: 'El cierre de sesión falló.',
        missingEmail: 'Falta el correo electrónico.',
        missingIDOfDocument: 'Falta el ID del documento a actualizar.',
        missingIDOfVersion: 'Falta el ID de la versión.',
        missingRequiredData: 'Falta información obligatoria.',
        noFilesUploaded: 'No se subieron archivos.',
        noMatchedField: 'No se encontró un campo para "{{label}}"',
        notAllowedToAccessPage: 'No tienes permiso para acceder a esta página.',
        notAllowedToPerformAction: 'No tienes permiso para realizar esta acción.',
        notFound: 'No se encontró el recurso solicitado.',
        noUser: 'Sin usuario',
        previewing: 'Ocurrió un problema al previsualizar este documento.',
        problemUploadingFile: 'Ocurrió un problema al subir el archivo.',
        restoringTitle: 'Hubo un error al restaurar {{title}}. Por favor, verifique su conexión e intente nuevamente.',
        tokenInvalidOrExpired: 'El token es inválido o ya expiró.',
        tokenNotProvided: 'Token no proporcionado.',
        unableToCopy: 'No se puede copiar.',
        unableToDeleteCount: 'No se pudo eliminar {{count}} de {{total}} {{label}}.',
        unableToReindexCollection: 'Error al reindexar la colección {{collection}}. Operación abortada.',
        unableToUpdateCount: 'No se puede actualizar {{count}} de {{total}} {{label}}.',
        unauthorized: 'No autorizado, debes iniciar sesión para realizar esta solicitud.',
        unauthorizedAdmin: 'No autorizado, este usuario no tiene acceso al panel de administración.',
        unknown: 'Ocurrió un error desconocido.',
        unPublishingDocument: 'Ocurrió un error al despublicar este documento.',
        unspecific: 'Ocurrió un error.',
        unverifiedEmail: 'Por favor, verifica tu correo electrónico antes de iniciar sesión.',
        userEmailAlreadyRegistered: 'Ya existe un usuario registrado con el correo electrónico proporcionado.',
        userLocked: 'Este usuario ha sido bloqueado debido a demasiados intentos fallidos de inicio de sesión.',
        usernameAlreadyRegistered: 'Ya existe un usuario registrado con el nombre de usuario proporcionado.',
        usernameOrPasswordIncorrect: 'El nombre de usuario o la contraseña proporcionados son incorrectos.',
        valueMustBeUnique: 'El valor debe ser único',
        verificationTokenInvalid: 'Token de verificación inválido.'
    },
    fields: {
        addLabel: 'Añadir {{label}}',
        addLink: 'Añadir Enlace',
        addNew: 'Añadir nuevo',
        addNewLabel: 'Añadir {{label}}',
        addRelationship: 'Añadir Relación',
        addUpload: 'Añadir documento',
        block: 'bloque',
        blocks: 'bloques',
        blockType: 'Tipo de bloque',
        chooseBetweenCustomTextOrDocument: 'Elige entre ingresar una URL personalizada o enlazar a otro documento.',
        chooseDocumentToLink: 'Elige un documento a enlazar',
        chooseFromExisting: 'Elegir de los existentes',
        chooseLabel: 'Elegir {{label}}',
        collapseAll: 'Contraer Todo',
        customURL: 'URL Personalizada',
        editLabelData: 'Editar datos de {{label}}',
        editLink: 'Editar Enlace',
        editRelationship: 'Editar Relación',
        enterURL: 'Introduce una URL',
        internalLink: 'Enlace Interno',
        itemsAndMore: '{{items}} y {{count}} más',
        labelRelationship: 'Relación de {{label}}',
        latitude: 'Latitud',
        linkedTo: 'Enlazado a <0>{{label}}</0>',
        linkType: 'Tipo de enlace',
        longitude: 'Longitud',
        newLabel: 'Nuevo {{label}}',
        openInNewTab: 'Abrir en nueva pestaña',
        passwordsDoNotMatch: 'Las contraseñas no coinciden.',
        relatedDocument: 'Documento Relacionado',
        relationTo: 'Relación con',
        removeRelationship: 'Eliminar relación',
        removeUpload: 'Eliminar documento',
        saveChanges: 'Guardar cambios',
        searchForBlock: 'Buscar bloque',
        selectExistingLabel: 'Seleccionar {{label}} existente',
        selectFieldsToEdit: 'Seleccionar campos para editar',
        showAll: 'Mostrar Todo',
        swapRelationship: 'Cambiar Relación',
        swapUpload: 'Cambiar carga',
        textToDisplay: 'Texto a mostrar',
        toggleBlock: 'Alternar bloque',
        uploadNewLabel: 'Subir nuevo {{label}}'
    },
    folder: {
        browseByFolder: 'Explorar por Carpeta',
        byFolder: 'Por Carpeta',
        deleteFolder: 'Eliminar Carpeta',
        folderName: 'Nombre de la Carpeta',
        folders: 'Carpetas',
        folderTypeDescription: 'Seleccione qué tipo de documentos de la colección se deben permitir en esta carpeta.',
        itemHasBeenMoved: '{{title}} se ha movido a {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} se ha movido a la carpeta raíz',
        itemsMovedToFolder: '{{title}} movido a {{folderName}}',
        itemsMovedToRoot: '{{title}} movido a la carpeta raíz',
        moveFolder: 'Mover Carpeta',
        moveItemsToFolderConfirmation: 'Estás a punto de mover <1>{{count}} {{label}}</1> a <2>{{toFolder}}</2>. ¿Estás seguro?',
        moveItemsToRootConfirmation: 'Estás a punto de mover <1>{{count}} {{label}}</1> a la carpeta raíz. ¿Estás seguro?',
        moveItemToFolderConfirmation: 'Estás a punto de mover <1>{{title}}</1> a <2>{{toFolder}}</2>. ¿Estás seguro?',
        moveItemToRootConfirmation: 'Estás a punto de mover <1>{{title}}</1> a la carpeta raíz. ¿Estás seguro?',
        movingFromFolder: 'Moviendo {{title}} desde {{fromFolder}}',
        newFolder: 'Nueva Carpeta',
        noFolder: 'Sin Carpeta',
        renameFolder: 'Renombrar carpeta',
        searchByNameInFolder: 'Buscar por nombre en {{folderName}}',
        selectFolderForItem: 'Seleccione la carpeta para {{title}}'
    },
    general: {
        name: 'Nombre',
        aboutToDelete: 'Estás por eliminar el {{label}} <1>{{title}}</1>. ¿Estás seguro?',
        aboutToDeleteCount_many: 'Estás a punto de eliminar {{count}} {{label}}',
        aboutToDeleteCount_one: 'Estás a punto de eliminar {{count}} {{label}}',
        aboutToDeleteCount_other: 'Estás a punto de eliminar {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Está a punto de eliminar permanentemente la {{label}} <1>{{title}}</1>. ¿Está seguro?',
        aboutToPermanentlyDeleteTrash: 'Está a punto de eliminar permanentemente <0>{{count}}</0> <1>{{label}}</1> de la basura. ¿Está seguro?',
        aboutToRestore: 'Está a punto de restaurar la {{label}} <1>{{title}}</1>. ¿Está seguro?',
        aboutToRestoreAsDraft: 'Está a punto de restaurar la {{label}} <1>{{title}}</1> como borrador. ¿Está seguro?',
        aboutToRestoreAsDraftCount: 'Estás a punto de restaurar {{count}} {{label}} como borrador',
        aboutToRestoreCount: 'Estás a punto de restaurar {{count}} {{label}}',
        aboutToTrash: 'Estás a punto de mover la {{label}} <1>{{title}}</1> a la papelera. ¿Estás seguro?',
        aboutToTrashCount: 'Estás a punto de mover {{count}} {{label}} a la papelera',
        addBelow: 'Añadir abajo',
        addFilter: 'Añadir filtro',
        adminTheme: 'Tema del admin',
        all: 'Todo',
        allCollections: 'Todas las colecciones',
        allLocales: 'Todos los idiomas',
        and: 'Y',
        anotherUser: 'Otro usuario',
        anotherUserTakenOver: 'Otro usuario ha tomado el control de la edición de este documento.',
        applyChanges: 'Aplicar Cambios',
        ascending: 'Ascendente',
        automatic: 'Automático',
        backToDashboard: 'Volver al Panel de Control',
        cancel: 'Cancelar',
        changesNotSaved: 'Tus cambios no han sido guardados. Si te sales ahora, se perderán tus cambios.',
        clear: 'Claro',
        clearAll: 'Limpiar todo',
        close: 'Cerrar',
        collapse: 'Contraer',
        collections: 'Colecciones',
        columns: 'Columnas',
        columnToSort: 'Columna de ordenado',
        confirm: 'Confirmar',
        confirmCopy: 'Confirmar copia',
        confirmDeletion: 'Confirmar eliminación',
        confirmDuplication: 'Confirmar duplicado',
        confirmMove: 'Confirmar movimiento',
        confirmReindex: '¿Reindexar todas las {{collections}}?',
        confirmReindexAll: '¿Reindexar todas las colecciones?',
        confirmReindexDescription: 'Esto eliminará los índices existentes y volverá a indexar los documentos en las colecciones {{collections}}.',
        confirmReindexDescriptionAll: 'Esto eliminará los índices existentes y volverá a indexar los documentos en todas las colecciones.',
        confirmRestoration: 'Confirme la restauración',
        copied: 'Copiado',
        copy: 'Copiar',
        copyField: 'Copiar campo',
        copying: 'Copiando',
        copyRow: 'Copiar fila',
        copyWarning: 'Estás a punto de sobrescribir {{to}} con {{from}} para {{label}} {{title}}. ¿Estás seguro?',
        create: 'Crear',
        created: 'Creado',
        createdAt: 'Fecha de creación',
        createNew: 'Crear nuevo',
        createNewLabel: 'Crear nuevo {{label}}',
        creating: 'Creando',
        creatingNewLabel: 'Creando nuevo {{label}}',
        currentlyEditing: 'está editando este documento. Si tomas el control, se le impedirá continuar editando y podría perder los cambios no guardados.',
        custom: 'Personalizado',
        dark: 'Oscuro',
        dashboard: 'Panel de Control',
        delete: 'Eliminar',
        deleted: 'Eliminado',
        deletedAt: 'Eliminado En',
        deletedCountSuccessfully: 'Se eliminaron {{count}} {{label}} correctamente.',
        deletedSuccessfully: 'Eliminado correctamente.',
        deletePermanently: 'Omitir la papelera y eliminar permanentemente',
        deleting: 'Eliminando...',
        depth: 'Profundidad',
        descending: 'Descendente',
        deselectAllRows: 'Deseleccionar todas las filas',
        document: 'Documento',
        documentIsTrashed: 'Esta {{label}} está en la papelera y es de solo lectura.',
        documentLocked: 'Documento bloqueado',
        documents: 'Documentos',
        duplicate: 'Duplicar',
        duplicateWithoutSaving: 'Duplicar sin guardar cambios',
        edit: 'Editar',
        editAll: 'Editar Todo',
        editedSince: 'Editado desde',
        editing: 'Editando',
        editingLabel_many: 'Editando {{count}} {{label}}',
        editingLabel_one: 'Editando {{count}} {{label}}',
        editingLabel_other: 'Editando {{count}} {{label}}',
        editingTakenOver: 'Edición tomada',
        editLabel: 'Editar {{label}}',
        email: 'Correo electrónico',
        emailAddress: 'Dirección de Correo Electrónico',
        emptyTrash: 'Vaciar la papelera',
        emptyTrashLabel: 'Vaciar la basura {{label}}',
        enterAValue: 'Introduce un valor',
        error: 'Error',
        errors: 'Errores',
        exitLivePreview: 'Salir de la vista previa en vivo',
        export: 'Exportar',
        fallbackToDefaultLocale: 'Volver al idioma predeterminado',
        false: 'Falso',
        filter: 'Filtro',
        filters: 'Filtros',
        filterWhere: 'Filtrar {{label}} donde',
        globals: 'Globales',
        goBack: 'Volver',
        groupByLabel: 'Agrupar por {{label}}',
        import: 'Importar',
        isEditing: 'está editando',
        item: 'artículo',
        items: 'artículos',
        language: 'Idioma',
        lastModified: 'Última modificación',
        leaveAnyway: 'Salir de todos modos',
        leaveWithoutSaving: 'Salir sin guardar',
        light: 'Claro',
        livePreview: 'Previsualizar',
        loading: 'Cargando',
        locale: 'Idioma',
        locales: 'Idiomas',
        menu: 'Menú',
        moreOptions: 'Más opciones',
        move: 'Mover',
        moveConfirm: 'Estás a punto de mover {{count}} {{label}} a <1>{{destination}}</1>. ¿Estás seguro?',
        moveCount: 'Mover {{count}} {{label}}',
        moveDown: 'Mover abajo',
        moveUp: 'Mover arriba',
        moving: 'Moviendo',
        movingCount: 'Moviendo {{count}} {{label}}',
        newPassword: 'Nueva contraseña',
        next: 'Siguiente',
        no: 'No',
        noDateSelected: 'No se seleccionó ninguna fecha',
        noFiltersSet: 'No hay filtros establecidos',
        noLabel: '<Sin {{label}}>',
        none: 'Ninguna',
        noOptions: 'Sin opciones',
        noResults: 'No se encontró ningún {{label}}. Puede que aún no existan o que no coincidan con los filtros aplicados.',
        notFound: 'No encontrado',
        nothingFound: 'No se encontró nada',
        noTrashResults: 'No hay {{label}} en la papelera.',
        noUpcomingEventsScheduled: 'No hay eventos próximos programados.',
        noValue: 'Sin valor',
        of: 'de',
        only: 'Solo',
        open: 'Abrir',
        or: 'O',
        order: 'Orden',
        overwriteExistingData: 'Sobrescribir los datos existentes del campo',
        pageNotFound: 'Página no encontrada',
        password: 'Contraseña',
        pasteField: 'Pegar campo',
        pasteRow: 'Pegar fila',
        payloadSettings: 'Configuración de Payload',
        permanentlyDelete: 'Eliminar Permanentemente',
        permanentlyDeletedCountSuccessfully: 'Se ha eliminado permanentemente {{count}} {{label}} con éxito.',
        perPage: 'Por página: {{limit}}',
        previous: 'Anterior',
        reindex: 'Reindexar',
        reindexingAll: 'Reindexando todas las {{collections}}.',
        remove: 'Eliminar',
        rename: 'Renombrar',
        reset: 'Restablecer',
        resetPreferences: 'Restablecer preferencias',
        resetPreferencesDescription: 'Esto restablecerá todas tus preferencias a los valores predeterminados.',
        resettingPreferences: 'Restableciendo preferencias...',
        restore: 'Restaurar',
        restoreAsPublished: 'Restaurar como versión publicada',
        restoredCountSuccessfully: 'Restaurado {{count}} {{label}} con éxito.',
        restoring: 'Restaurando...',
        row: 'Fila',
        rows: 'Filas',
        save: 'Guardar',
        saving: 'Guardando...',
        schedulePublishFor: 'Programar publicación para {{title}}',
        searchBy: 'Buscar por {{label}}',
        select: 'Seleccionar',
        selectAll: 'Seleccionar los {{count}} {{label}}',
        selectAllRows: 'Seleccionar todas las filas',
        selectedCount: '{{count}} {{label}} seleccionados',
        selectLabel: 'Seleccionar {{label}}',
        selectValue: 'Seleccionar un valor',
        showAllLabel: 'Mostrar todos los {{label}}',
        sorryNotFound: 'Lo sentimos, no hay nada que coincida con tu solicitud.',
        sort: 'Ordenar',
        sortByLabelDirection: 'Ordenar por {{label}} {{direction}}',
        stayOnThisPage: 'Permanecer en esta página',
        submissionSuccessful: 'Envío realizado con éxito.',
        submit: 'Enviar',
        submitting: 'Enviando...',
        success: 'Éxito',
        successfullyCreated: '{{label}} creado con éxito.',
        successfullyDuplicated: '{{label}} duplicado con éxito.',
        successfullyReindexed: '{{count}} de {{total}} documentos de {{collections}} reindexados con éxito.',
        takeOver: 'Tomar el control',
        thisLanguage: 'Español',
        time: 'Hora',
        timezone: 'Zona horaria',
        titleDeleted: '{{label}} "{{title}}" eliminado con éxito.',
        titleRestored: '{{label}} "{{title}}" restaurado con éxito.',
        titleTrashed: '{{label}} "{{title}}" movido a la papelera.',
        trash: 'Basura',
        trashedCountSuccessfully: '{{count}} {{label}} movido a la papelera.',
        true: 'Verdadero',
        unauthorized: 'No autorizado',
        unsavedChanges: 'Tienes cambios sin guardar. Guarda o descarta antes de continuar.',
        unsavedChangesDuplicate: 'Tienes cambios sin guardar. ¿Deseas continuar con la duplicación?',
        untitled: 'Sin título',
        upcomingEvents: 'Próximos eventos',
        updatedAt: 'Última modificación',
        updatedCountSuccessfully: '{{count}} {{label}} actualizados con éxito.',
        updatedLabelSuccessfully: '{{label}} actualizado con éxito.',
        updatedSuccessfully: 'Actualizado con éxito.',
        updateForEveryone: 'Actualizar para todos',
        updating: 'Actualizando',
        uploading: 'Subiendo',
        uploadingBulk: 'Subiendo {{current}} de {{total}}',
        user: 'Usuario',
        username: 'Nombre de usuario',
        users: 'Usuarios',
        value: 'Valor',
        viewing: 'Visualización',
        viewReadOnly: 'Ver solo lectura',
        welcome: 'Bienvenido',
        yes: 'Sí'
    },
    localization: {
        cannotCopySameLocale: 'No se puede copiar al mismo idioma',
        copyFrom: 'Copiar desde',
        copyFromTo: 'Copiando de {{from}} a {{to}}',
        copyTo: 'Copiar a',
        copyToLocale: 'Copiar a idioma',
        localeToPublish: 'Idioma para publicar',
        selectLocaleToCopy: 'Selecciona el idioma a copiar'
    },
    operators: {
        contains: 'contiene',
        equals: 'igual',
        exists: 'existe',
        intersects: 'interseca',
        isGreaterThan: 'es mayor que',
        isGreaterThanOrEqualTo: 'es mayor o igual que',
        isIn: 'está en',
        isLessThan: 'es menor que',
        isLessThanOrEqualTo: 'es menor o igual que',
        isLike: 'es similar a',
        isNotEqualTo: 'no es igual a',
        isNotIn: 'no está en',
        isNotLike: 'no es similar a',
        near: 'cerca',
        within: 'dentro de'
    },
    upload: {
        addFile: 'Añadir archivo',
        addFiles: 'Añadir archivos',
        bulkUpload: 'Subida en lotes',
        crop: 'Recortar',
        cropToolDescription: 'Arrastra las esquinas del área seleccionada, dibuja un nuevo área o ajusta los valores a continuación.',
        download: 'Descargar',
        dragAndDrop: 'Arrastra y suelta un archivo',
        dragAndDropHere: 'o arrastra un archivo aquí',
        editImage: 'Editar imagen',
        fileName: 'Nombre del archivo',
        fileSize: 'Tamaño del archivo',
        filesToUpload: 'Archivos para subir',
        fileToUpload: 'Archivo para subir',
        focalPoint: 'Punto Focal',
        focalPointDescription: 'Arrastra el punto focal directamente en la vista previa o ajusta los valores a continuación.',
        height: 'Alto',
        lessInfo: 'Menos info',
        moreInfo: 'Más info',
        noFile: 'Ningún archivo',
        pasteURL: 'Pegar URL',
        previewSizes: 'Tamaños de Vista Previa',
        selectCollectionToBrowse: 'Selecciona una Colección para navegar',
        selectFile: 'Selecciona un archivo',
        setCropArea: 'Establecer área de recorte',
        setFocalPoint: 'Establecer punto focal',
        sizes: 'Tamaños',
        sizesFor: 'Tamaños para {{label}}',
        width: 'Ancho'
    },
    validation: {
        emailAddress: 'Por favor, introduce un correo electrónico válido.',
        enterNumber: 'Por favor, introduce un número válido.',
        fieldHasNo: 'Este campo no tiene {{label}}',
        greaterThanMax: '{{value}} es mayor que el máximo permitido de {{max}} en {{label}}.',
        invalidInput: 'La información en este campo es inválida.',
        invalidSelection: 'La selección en este campo es inválida.',
        invalidSelections: 'Este campo tiene las siguientes selecciones inválidas:',
        lessThanMin: '{{value}} es menor que el mínimo permitido de {{min}} en {{label}}.',
        limitReached: 'Se ha alcanzado el límite. Solo se pueden agregar {{max}} elementos.',
        longerThanMin: 'Este valor debe tener una longitud mínima de {{minLength}} caracteres.',
        notValidDate: '"{{value}}" es una fecha inválida.',
        required: 'Este campo es obligatorio.',
        requiresAtLeast: 'Este campo requiere al menos {{count}} {{label}}.',
        requiresNoMoreThan: 'Este campo require no más de {{count}} {{label}}',
        requiresTwoNumbers: 'Este campo requiere dos números.',
        shorterThanMax: 'Este valor debe tener una longitud máxima de {{maxLength}} caracteres.',
        timezoneRequired: 'Se requiere una zona horaria.',
        trueOrFalse: 'Este campo solo puede ser verdadero o falso.',
        username: 'Por favor, introduce un nombre de usuario válido. Puede contener letras, números, guiones, puntos y guiones bajos.',
        validUploadID: 'Este campo no es un ID de subida válido.'
    },
    version: {
        type: 'Tipo',
        aboutToPublishSelection: 'Estás a punto de publicar todos los {{label}} seleccionados. ¿Estás seguro?',
        aboutToRestore: 'Estás a punto de restaurar este documento de {{label}} al estado en el que se encontraba el {{versionDate}}.',
        aboutToRestoreGlobal: 'Estás a punto de restaurar el {{label}} global al estado en el que se encontraba el {{versionDate}}.',
        aboutToRevertToPublished: 'Estás a punto de revertir los cambios de este documento a su estado publicado. ¿Estás seguro?',
        aboutToUnpublish: 'Estás a punto de despublicar este documento. ¿Estás seguro?',
        aboutToUnpublishSelection: 'Estás a punto de despublicar todos los {{label}} seleccionados. ¿Estás seguro?',
        autosave: 'Autoguardado',
        autosavedSuccessfully: 'Guardado automáticamente con éxito.',
        autosavedVersion: 'Versión Autoguardada',
        changed: 'Modificado',
        changedFieldsCount_one: '{{count}} campo modificado',
        changedFieldsCount_other: '{{count}} campos modificados',
        compareVersion: 'Comparar versión con:',
        compareVersions: 'Comparar Versiones',
        comparingAgainst: 'Comparando contra',
        confirmPublish: 'Confirmar publicación',
        confirmRevertToSaved: 'Confirmar revertir a guardado',
        confirmUnpublish: 'Confirmar despublicación',
        confirmVersionRestoration: 'Confirmar restauración de versión',
        currentDocumentStatus: 'Documento actual: {{docStatus}}',
        currentDraft: 'Borrador actual',
        currentlyPublished: 'Actualmente Publicado',
        currentlyViewing: 'Actualmente viendo',
        currentPublishedVersion: 'Versión publicada actual',
        draft: 'Borrador',
        draftSavedSuccessfully: 'Borrador guardado con éxito.',
        lastSavedAgo: 'Guardado por última vez hace {{distance}}',
        modifiedOnly: 'Modificado solamente',
        moreVersions: 'Más versiones...',
        noFurtherVersionsFound: 'No se encontraron más versiones',
        noRowsFound: 'No se encontraron {{label}}.',
        noRowsSelected: 'No se ha seleccionado ningún {{label}}.',
        preview: 'Vista previa',
        previouslyDraft: 'Previamente un Borrador',
        previouslyPublished: 'Publicado anteriormente',
        previousVersion: 'Versión Anterior',
        problemRestoringVersion: 'Hubo un problema al restaurar esta versión',
        publish: 'Publicar',
        publishAllLocales: 'Publicar en todos los idiomas',
        publishChanges: 'Publicar cambios',
        published: 'Publicado',
        publishIn: 'Publicar en {{locale}}',
        publishing: 'Publicando',
        restoreAsDraft: 'Restaurar como borrador',
        restoredSuccessfully: 'Restaurado con éxito.',
        restoreThisVersion: 'Restaurar esta versión',
        restoring: 'Restaurando...',
        reverting: 'Revirtiendo...',
        revertToPublished: 'Revertir a la versión publicada',
        saveDraft: 'Guardar borrador',
        scheduledSuccessfully: 'Programado con éxito.',
        schedulePublish: 'Programar publicación',
        selectLocales: 'Seleccionar idiomas a mostrar',
        selectVersionToCompare: 'Seleccionar una versión para comparar',
        showingVersionsFor: 'Mostrando versiones para:',
        showLocales: 'Mostrar idiomas:',
        specificVersion: 'Versión Específica',
        status: 'Estado',
        unpublish: 'Despublicar',
        unpublishing: 'Despublicando...',
        version: 'Versión',
        versionAgo: 'hace {{distance}}',
        versionCount_many: '{{count}} versiones encontradas',
        versionCount_none: 'No se encontraron versiones',
        versionCount_one: '{{count}} versión encontrada',
        versionCount_other: '{{count}} versiones encontradas',
        versionCreatedOn: '{{version}} creada el:',
        versionID: 'ID de la versión',
        versions: 'Versiones',
        viewingVersion: 'Viendo versión para {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Viendo versión para el global {{entityLabel}}',
        viewingVersions: 'Viendo versiones para {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Viendo versiones para el global {{entityLabel}}'
    }
};
export const es = {
    dateFNSKey: 'es',
    translations: esTranslations
};

//# sourceMappingURL=es.js.map