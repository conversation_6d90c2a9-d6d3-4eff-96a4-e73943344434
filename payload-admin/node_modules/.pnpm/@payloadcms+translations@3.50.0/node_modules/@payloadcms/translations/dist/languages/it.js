export const itTranslations = {
    authentication: {
        account: 'Account',
        accountOfCurrentUser: "Account dell'utente corrente",
        accountVerified: 'Account verificato con successo.',
        alreadyActivated: '<PERSON><PERSON><PERSON> Attivato',
        alreadyLoggedIn: 'Sei già loggato',
        apiKey: 'Chiave API',
        authenticated: 'Autenticato',
        backToLogin: 'Torna al login',
        beginCreateFirstUser: 'Per iniziare, crea il tuo primo utente.',
        changePassword: 'Cambia Password',
        checkYourEmailForPasswordReset: "Se l'indirizzo email è associato a un account, riceverai a breve le istruzioni per reimpostare la tua password. Si prega di controllare la cartella dello spam o della posta indesiderata se non vedi l'email nella tua casella di posta.",
        confirmGeneration: 'Conferma Generazione',
        confirmPassword: 'Conferma Password',
        createFirstUser: 'Crea il primo utente',
        emailNotValid: "L'email fornita non è valida",
        emailOrUsername: 'Email o Nome utente',
        emailSent: 'Email Inviata',
        emailVerified: 'Email verificata con successo.',
        enableAPIKey: 'Abilita la Chiave API',
        failedToUnlock: 'Lo sblocco è fallito',
        forceUnlock: 'Forza Sblocco',
        forgotPassword: 'Cambia Password',
        forgotPasswordEmailInstructions: 'Inserisci la tua mail qui sotto. Riceverai un messaggio email con le istruzioni su come cambiare la tua password.',
        forgotPasswordQuestion: 'Password dimenticata?',
        forgotPasswordUsernameInstructions: "Inserisci il tuo nome utente qui sotto. Le istruzioni su come reimpostare la tua password verranno inviate all'indirizzo email associato al tuo nome utente.",
        generate: 'Genera',
        generateNewAPIKey: 'Genera una nuova Chiave API',
        generatingNewAPIKeyWillInvalidate: 'Generando una nuova chiave API si <1>invaliderà</1> la chiave precedente. Sei sicuro di voler continuare?',
        lockUntil: 'Sblocca Fino',
        logBackIn: 'Vai al Log in',
        loggedIn: 'Per accedere con un altro utente, devi prima <0>uscire</0>.',
        loggedInChangePassword: 'Per cambiare la tua password, vai al tuo <0>account</0> e modifica la tua password lì.',
        loggedOutInactivity: 'Sei stato disconnesso automaticamente per inattività.',
        loggedOutSuccessfully: 'Sei stato disconnesso con successo.',
        loggingOut: 'Disconnessione in corso...',
        login: 'Login',
        loginAttempts: 'Tentativi di Login',
        loginUser: 'Utente Login',
        loginWithAnotherUser: 'Per accedere con un altro utente, devi prima <0>uscire</0>.',
        logOut: 'Log out',
        logout: 'Logout',
        logoutSuccessful: 'Disconnessione riuscita.',
        logoutUser: 'Logout utente',
        newAccountCreated: 'Un nuovo account è appena stato creato per te per accedere a <a href="{{serverURL}}">{{serverURL}}</a> Clicca sul seguente link o incolla l\'URL qui sotto nel browser per verificare la tua email: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Dopo aver verificato la tua email, sarai in grado di effettuare il log in con successo.',
        newAPIKeyGenerated: 'Nuova Chiave API Generata.',
        newPassword: 'Nuova Password',
        passed: 'Autenticazione Superata',
        passwordResetSuccessfully: 'Reset della password eseguito con successo.',
        resetPassword: 'Modifica Password',
        resetPasswordExpiration: 'Reimposta Scadenza Password',
        resetPasswordToken: 'Reimposta il Password Token',
        resetYourPassword: 'Modifica la tua Password',
        stayLoggedIn: 'Rimani connesso',
        successfullyRegisteredFirstUser: 'Primo utente registrato con successo.',
        successfullyUnlocked: 'Sbloccato con successo',
        tokenRefreshSuccessful: 'Aggiornamento del token riuscito.',
        unableToVerify: 'Impossibile verificare',
        username: 'Nome utente',
        usernameNotValid: 'Il nome utente fornito non è valido',
        verified: 'Verificato',
        verifiedSuccessfully: 'Verificato con successo',
        verify: 'Verifica',
        verifyUser: 'Verifica Utente',
        verifyYourEmail: 'Verifica la tua email',
        youAreInactive: "Non sei attivo da un po' di tempo e a breve verrai disconnesso automaticamente per la tua sicurezza. Vuoi rimanere connesso?",
        youAreReceivingResetPassword: 'Ricevi questo messaggio perché tu (o qualcun altro) hai richiesto la modifica della password per il tuo account. Clicca sul seguente link o incollalo nel browser per completare il processo:',
        youDidNotRequestPassword: "Se non l'hai richiesto, ignora questa email e la tua password rimarrà invariata."
    },
    error: {
        accountAlreadyActivated: 'Questo account è già stato attivato.',
        autosaving: 'Si è verificato un problema durante il salvataggio automatico di questo documento.',
        correctInvalidFields: 'Per favore correggi i campi non validi.',
        deletingFile: "Si è verificato un errore durante l'eleminazione del file.",
        deletingTitle: "Si è verificato un errore durante l'eliminazione di {{title}}. Per favore controlla la tua connessione e riprova.",
        documentNotFound: 'Il documento con ID {{id}} non è stato trovato. Potrebbe essere stato eliminato o mai esistito, oppure potresti non avere accesso ad esso.',
        emailOrPasswordIncorrect: "L'email o la password fornita non è corretta.",
        followingFieldsInvalid_one: 'Il seguente campo non è valido:',
        followingFieldsInvalid_other: 'I seguenti campi non sono validi:',
        incorrectCollection: 'Collezione non corretta',
        insufficientClipboardPermissions: 'Accesso alla clipboard negato. Verifica i permessi della clipboard.',
        invalidClipboardData: 'Dati della clipboard non validi.',
        invalidFileType: 'Tipo di file non valido',
        invalidFileTypeValue: 'Tipo di file non valido: {{value}}',
        invalidRequestArgs: 'Argomenti non validi nella richiesta: {{args}}',
        loadingDocument: 'Si è verificato un problema durante il caricamento del documento con ID {{id}}.',
        localesNotSaved_one: 'Non è stato possibile salvare la seguente impostazione locale:',
        localesNotSaved_other: 'Non è stato possibile salvare le seguenti impostazioni locali:',
        logoutFailed: 'Disconnessione non riuscita.',
        missingEmail: 'Email mancante.',
        missingIDOfDocument: 'ID del documento da aggiornare mancante.',
        missingIDOfVersion: 'ID della versione mancante.',
        missingRequiredData: 'Data mancante.',
        noFilesUploaded: 'Nessun file è stato caricato.',
        noMatchedField: 'Nessun campo corrispondente trovato per "{{label}}"',
        notAllowedToAccessPage: 'Non sei autorizzato ad accedere a questa pagina.',
        notAllowedToPerformAction: 'Non sei autorizzato a eseguire questa azione.',
        notFound: 'La risorsa richiesta non è stata trovata.',
        noUser: 'Nessun Utente',
        previewing: "Si è verificato un problema durante l'anteprima di questo documento.",
        problemUploadingFile: 'Si è verificato un problema durante il caricamento del file.',
        restoringTitle: 'Si è verificato un errore durante il ripristino di {{title}}. Si prega di controllare la connessione e riprovare.',
        tokenInvalidOrExpired: 'Il token non è valido o è scaduto.',
        tokenNotProvided: 'Token non fornito.',
        unableToCopy: 'Impossibile copiare.',
        unableToDeleteCount: 'Impossibile eliminare {{count}} su {{total}} {{label}}.',
        unableToReindexCollection: 'Errore durante la reindicizzazione della collezione {{collection}}. Operazione annullata.',
        unableToUpdateCount: 'Impossibile aggiornare {{count}} su {{total}} {{label}}.',
        unauthorized: 'Non autorizzato, devi essere loggato per effettuare questa richiesta.',
        unauthorizedAdmin: 'Non autorizzato, questo utente non ha accesso al pannello di amministrazione.',
        unknown: 'Si è verificato un errore sconosciuto.',
        unPublishingDocument: "Si è verificato un problema durante l'annullamento della pubblicazione di questo documento.",
        unspecific: 'Si è verificato un errore.',
        unverifiedEmail: 'Verifica la tua email prima di accedere.',
        userEmailAlreadyRegistered: "Un utente con l'email fornita è già registrato.",
        userLocked: 'Questo utente è bloccato a causa di troppi tentativi di accesso non riusciti.',
        usernameAlreadyRegistered: 'Un utente con il nome utente fornito è già registrato.',
        usernameOrPasswordIncorrect: 'Il nome utente o la password forniti sono incorretti.',
        valueMustBeUnique: 'Il valore deve essere univoco',
        verificationTokenInvalid: 'Il token di verifica non è valido.'
    },
    fields: {
        addLabel: 'Aggiungi {{label}}',
        addLink: 'Aggiungi Collegamento',
        addNew: 'Aggiungi nuovo',
        addNewLabel: 'Aggiungi nuovo {{label}}',
        addRelationship: 'Aggiungi Relazione',
        addUpload: 'aggiungi Carica',
        block: 'blocco',
        blocks: 'blocchi',
        blockType: 'Tipo di Blocco',
        chooseBetweenCustomTextOrDocument: "Scegli tra l'inserimento di un URL di testo personalizzato o il collegamento a un altro documento.",
        chooseDocumentToLink: 'Scegli un documento a cui collegarti',
        chooseFromExisting: 'Scegli tra esistente',
        chooseLabel: 'Scegli {{label}}',
        collapseAll: 'Comprimi tutto',
        customURL: 'URL personalizzato',
        editLabelData: 'Modifica i dati di {{label}}',
        editLink: 'Modifica Collegamento',
        editRelationship: 'Modifica Relazione',
        enterURL: 'Inserisci un URL',
        internalLink: 'Collegamento interno',
        itemsAndMore: '{{items}} e altri {{count}}',
        labelRelationship: 'Relazione {{label}}',
        latitude: 'Latitudine',
        linkedTo: 'Collegato a <0>{{label}}</0>',
        linkType: 'Tipo di collegamento',
        longitude: 'Longitudine',
        newLabel: 'Nuovo {{label}}',
        openInNewTab: 'Apri in una nuova scheda',
        passwordsDoNotMatch: 'Le password non corrispondono.',
        relatedDocument: 'Documento Correlato',
        relationTo: 'Correla a',
        removeRelationship: 'Rimuovi Relazione',
        removeUpload: 'Rimuovi Upload',
        saveChanges: 'Salva modifiche',
        searchForBlock: 'Cerca un blocco',
        selectExistingLabel: 'Seleziona {{label}} esistente',
        selectFieldsToEdit: 'Seleziona i campi da modificare',
        showAll: 'Mostra tutto',
        swapRelationship: 'Cambia Relationship',
        swapUpload: 'Cambia Upload',
        textToDisplay: 'Testo da visualizzare',
        toggleBlock: 'Apri/chiudi blocco',
        uploadNewLabel: 'Carica nuovo {{label}}'
    },
    folder: {
        browseByFolder: 'Sfoglia per Cartella',
        byFolder: 'Per Cartella',
        deleteFolder: 'Elimina cartella',
        folderName: 'Nome Cartella',
        folders: 'Cartelle',
        folderTypeDescription: 'Seleziona quale tipo di documenti della collezione dovrebbero essere consentiti in questa cartella.',
        itemHasBeenMoved: '{{title}} è stato spostato in {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} è stato spostato nella cartella principale',
        itemsMovedToFolder: '{{title}} spostato in {{folderName}}',
        itemsMovedToRoot: '{{title}} è stato spostato nella cartella principale',
        moveFolder: 'Sposta Cartella',
        moveItemsToFolderConfirmation: 'Stai per spostare <1>{{count}} {{label}}</1> in <2>{{toFolder}}</2>. Sei sicuro?',
        moveItemsToRootConfirmation: 'Stai per spostare <1>{{count}} {{label}}</1> nella cartella principale. Sei sicuro?',
        moveItemToFolderConfirmation: 'Stai per spostare <1>{{title}}</1> in <2>{{toFolder}}</2>. Sei sicuro?',
        moveItemToRootConfirmation: 'Stai per spostare <1>{{title}}</1> nella cartella principale. Sei sicuro?',
        movingFromFolder: 'Spostando {{title}} da {{fromFolder}}',
        newFolder: 'Nuova Cartella',
        noFolder: 'Nessuna cartella',
        renameFolder: 'Rinomina Cartella',
        searchByNameInFolder: 'Cerca per Nome in {{folderName}}',
        selectFolderForItem: 'Seleziona la cartella per {{title}}'
    },
    general: {
        name: 'Nome',
        aboutToDelete: 'Stai per eliminare {{label}} <1>{{title}}</1>. Sei sicuro?',
        aboutToDeleteCount_many: 'Stai per eliminare {{count}} {{label}}',
        aboutToDeleteCount_one: 'Stai per eliminare {{count}} {{label}}',
        aboutToDeleteCount_other: 'Stai per eliminare {{count}} {{label}}',
        aboutToPermanentlyDelete: "Stai per eliminare definitivamente l'{{label}} <1>{{title}}</1>. Sei sicuro?",
        aboutToPermanentlyDeleteTrash: 'Stai per eliminare definitivamente <0>{{count}}</0> <1>{{label}}</1> dal cestino. Sei sicuro?',
        aboutToRestore: 'Stai per ripristinare il {{label}} <1>{{title}}</1>. Sei sicuro?',
        aboutToRestoreAsDraft: "Stai per ripristinare l'etichetta {{label}} <1>{{title}}</1> come bozza. Sei sicuro?",
        aboutToRestoreAsDraftCount: 'Stai per ripristinare {{count}} {{label}} come bozza',
        aboutToRestoreCount: 'Stai per ripristinare {{count}} {{label}}',
        aboutToTrash: 'Stai per spostare il {{label}} <1>{{title}}</1> nel cestino. Sei sicuro?',
        aboutToTrashCount: 'Stai per spostare {{count}} {{label}} nel cestino',
        addBelow: 'Aggiungi sotto',
        addFilter: 'Aggiungi Filtro',
        adminTheme: 'Tema Admin',
        all: 'Tutto',
        allCollections: 'Tutte le collezioni',
        allLocales: 'Tutte le località',
        and: 'E',
        anotherUser: 'Un altro utente',
        anotherUserTakenOver: 'Un altro utente ha preso il controllo della modifica di questo documento.',
        applyChanges: 'Applica modifiche',
        ascending: 'Ascendente',
        automatic: 'Automatico',
        backToDashboard: 'Torna alla Dashboard',
        cancel: 'Cancella',
        changesNotSaved: 'Le tue modifiche non sono state salvate. Se esci ora, verranno perse.',
        clear: 'Chiara',
        clearAll: 'Cancella Tutto',
        close: 'Chiudere',
        collapse: 'Comprimi',
        collections: 'Collezioni',
        columns: 'Colonne',
        columnToSort: 'Colonna da Ordinare',
        confirm: 'Conferma',
        confirmCopy: 'Conferma copia',
        confirmDeletion: "Conferma l'eliminazione",
        confirmDuplication: 'Conferma la duplicazione',
        confirmMove: 'Conferma spostamento',
        confirmReindex: "Rifare l'indice di tutte le {{collections}}?",
        confirmReindexAll: "Rifare l'indice di tutte le collezioni?",
        confirmReindexDescription: "Questo rimuoverà gli indici esistenti e rifarà l'indice dei documenti nelle collezioni {{collections}}.",
        confirmReindexDescriptionAll: "Questo rimuoverà gli indici esistenti e rifarà l'indice dei documenti in tutte le collezioni.",
        confirmRestoration: 'Conferma il ripristino',
        copied: 'Copiato',
        copy: 'Copia',
        copyField: 'Copia campo',
        copying: 'Copia',
        copyRow: 'Copia riga',
        copyWarning: 'Stai per sovrascrivere {{to}} con {{from}} per {{label}} {{title}}. Sei sicuro?',
        create: 'Crea',
        created: 'Data di creazione',
        createdAt: 'Creato il',
        createNew: 'Crea Nuovo',
        createNewLabel: 'Crea nuovo {{label}}',
        creating: 'Crea nuovo',
        creatingNewLabel: 'Creazione di un nuovo {{label}}',
        currentlyEditing: 'sta attualmente modificando questo documento. Se prendi il controllo, verranno bloccati dal continuare a modificare e potrebbero anche perdere le modifiche non salvate.',
        custom: 'Personalizzato',
        dark: 'Scuro',
        dashboard: 'Dashboard',
        delete: 'Elimina',
        deleted: 'Cancellato',
        deletedAt: 'Cancellato Alle',
        deletedCountSuccessfully: '{{count}} {{label}} eliminato con successo.',
        deletedSuccessfully: 'Eliminato con successo.',
        deletePermanently: 'Salta il cestino ed elimina definitivamente',
        deleting: 'Sto eliminando...',
        depth: 'Profondità',
        descending: 'Decrescente',
        deselectAllRows: 'Deseleziona tutte le righe',
        document: 'Documento',
        documentIsTrashed: 'Questo {{label}} è stato cestinato ed è in sola lettura.',
        documentLocked: 'Documento bloccato',
        documents: 'Documenti',
        duplicate: 'Duplica',
        duplicateWithoutSaving: 'Duplica senza salvare le modifiche',
        edit: 'Modificare',
        editAll: 'Modifica Tutto',
        editedSince: 'Modificato da',
        editing: 'Modifica',
        editingLabel_many: 'Modificare {{count}} {{label}}',
        editingLabel_one: 'Modifica {{count}} {{label}}',
        editingLabel_other: 'Modificare {{count}} {{label}}',
        editingTakenOver: 'Modifica presa in carico',
        editLabel: 'Modifica {{label}}',
        email: 'Email',
        emailAddress: 'Indirizzo Email',
        emptyTrash: 'Svuota cestino',
        emptyTrashLabel: 'Svuota il cestino {{label}}',
        enterAValue: 'Inserisci un valore',
        error: 'Errore',
        errors: 'Errori',
        exitLivePreview: "Esci dall'Anteprima dal Vivo",
        export: 'Esportazione',
        fallbackToDefaultLocale: 'Fallback al locale predefinito',
        false: 'Falso',
        filter: 'Filtro',
        filters: 'Filtri',
        filterWhere: 'Filtra {{label}} se',
        globals: 'Globali',
        goBack: 'Torna indietro',
        groupByLabel: 'Raggruppa per {{label}}',
        import: 'Importare',
        isEditing: 'sta modificando',
        item: 'articolo',
        items: 'articoli',
        language: 'Lingua',
        lastModified: 'Ultima modifica',
        leaveAnyway: 'Esci comunque',
        leaveWithoutSaving: 'Esci senza salvare',
        light: 'Chiaro',
        livePreview: 'Anteprima dal vivo',
        loading: 'Caricamento',
        locale: 'Locale',
        locales: 'Localizzazioni',
        menu: 'Menù',
        moreOptions: 'Più opzioni',
        move: 'Muoviti',
        moveConfirm: 'Stai per spostare {{count}} {{label}} in <1>{{destination}}</1>. Sei sicuro?',
        moveCount: 'Sposta {{count}} {{label}}',
        moveDown: 'Sposta sotto',
        moveUp: 'Sposta sopra',
        moving: 'In movimento',
        movingCount: 'Spostando {{count}} {{label}}',
        newPassword: 'Nuova Password',
        next: 'Successivo',
        no: 'No',
        noDateSelected: 'Nessuna data selezionata',
        noFiltersSet: 'Nessun filtro impostato',
        noLabel: '<No {{label}}>',
        none: 'Nessuno',
        noOptions: 'Nessuna opzione',
        noResults: 'Nessun {{label}} trovato. Non esiste ancora nessun {{label}} oppure nessuno corrisponde ai filtri che hai specificato sopra.',
        notFound: 'Non Trovato',
        nothingFound: 'Non è stato trovato nulla',
        noTrashResults: 'Nessun {{label}} nel cestino.',
        noUpcomingEventsScheduled: 'Nessun evento in programma.',
        noValue: 'Nessun valore',
        of: 'di',
        only: 'Solo',
        open: 'Apri',
        or: 'Oppure',
        order: 'Ordine',
        overwriteExistingData: 'Sovrascrivi i dati del campo esistente',
        pageNotFound: 'Pagina non trovata',
        password: 'Password',
        pasteField: 'Incolla campo',
        pasteRow: 'Incolla riga',
        payloadSettings: 'Impostazioni di Payload',
        permanentlyDelete: 'Elimina Permanentemente',
        permanentlyDeletedCountSuccessfully: 'Eliminato definitivamente {{count}} {{label}} con successo.',
        perPage: 'Per Pagina: {{limit}}',
        previous: 'Precedente',
        reindex: 'Reindicizza',
        reindexingAll: "Rifacendo l'indice di tutte le {{collections}}.",
        remove: 'Rimuovi',
        rename: 'Rinomina',
        reset: 'Ripristina',
        resetPreferences: 'Ripristina le preferenze',
        resetPreferencesDescription: 'Questo ripristinerà tutte le tue preferenze alle impostazioni predefinite.',
        resettingPreferences: 'Ripristinando le preferenze.',
        restore: 'Ripristina',
        restoreAsPublished: 'Ripristina come versione pubblicata',
        restoredCountSuccessfully: 'Ripristinato {{count}} {{label}} con successo.',
        restoring: "Rispetta il significato del testo originale nel contesto di Payload. Ecco una lista di termini comuni di Payload che hanno significati molto specifici:\n    - Raccolta: Una raccolta è un gruppo di documenti che condividono una struttura e una finalità comuni. Le raccolte vengono utilizzate per organizzare e gestire i contenuti in Payload.\n    - Campo: Un campo è un pezzo specifico di dati all'interno di un documento in una raccolta. I campi definiscono la struttura e il tipo di dati che possono essere memorizzati in un documento.\n    - Documento: Un documento",
        row: 'Riga',
        rows: 'Righe',
        save: 'Salva',
        saving: 'Salvo...',
        schedulePublishFor: 'Pianifica la pubblicazione per {{title}}',
        searchBy: 'Cerca per {{label}}',
        select: 'Seleziona',
        selectAll: 'Seleziona tutto {{count}} {{label}}',
        selectAllRows: 'Seleziona tutte le righe',
        selectedCount: '{{count}} {{label}} selezionato',
        selectLabel: 'Seleziona {{label}}',
        selectValue: 'Seleziona un valore',
        showAllLabel: 'Mostra tutti {{label}}',
        sorryNotFound: "Siamo spiacenti, non c'è nulla che corrisponda alla tua richiesta.",
        sort: 'Ordina',
        sortByLabelDirection: 'Ordina per {{label}} {{direction}}',
        stayOnThisPage: 'Rimani su questa pagina',
        submissionSuccessful: 'Invio riuscito.',
        submit: 'Invia',
        submitting: 'Inviando...',
        success: 'Successo',
        successfullyCreated: '{{label}} creato con successo.',
        successfullyDuplicated: '{{label}} duplicato con successo.',
        successfullyReindexed: 'Reindicizzati con successo {{count}} di {{total}} documenti da {{collections}} collezioni.',
        takeOver: 'Prendi il controllo',
        thisLanguage: 'Italiano',
        time: 'Tempo',
        timezone: 'Fuso orario',
        titleDeleted: '{{label}} {{title}} eliminato con successo.',
        titleRestored: '{{label}} "{{title}}" ripristinato con successo.',
        titleTrashed: '{{label}} "{{title}}" spostato nel cestino.',
        trash: 'Cestino',
        trashedCountSuccessfully: '{{count}} {{label}} spostati nel cestino.',
        true: 'Vero',
        unauthorized: 'Non autorizzato',
        unsavedChanges: 'Hai delle modifiche non salvate. Salva o scarta prima di continuare.',
        unsavedChangesDuplicate: 'Sono presenti modifiche non salvate. Vuoi continuare a duplicare?',
        untitled: 'Senza titolo',
        upcomingEvents: 'Eventi Imminenti',
        updatedAt: 'Aggiornato il',
        updatedCountSuccessfully: '{{count}} {{label}} aggiornato con successo.',
        updatedLabelSuccessfully: '{{label}} aggiornata con successo.',
        updatedSuccessfully: 'Aggiornato con successo.',
        updateForEveryone: 'Aggiornamento per tutti',
        updating: 'Aggiornamento',
        uploading: 'Caricamento',
        uploadingBulk: 'Caricamento {{current}} di {{total}}',
        user: 'Utente',
        username: 'Nome utente',
        users: 'Utenti',
        value: 'Valore',
        viewing: 'Visualizzazione',
        viewReadOnly: 'Visualizza solo lettura',
        welcome: 'Benvenuto',
        yes: 'Sì'
    },
    localization: {
        cannotCopySameLocale: 'Non è possibile copiare nella stessa posizione',
        copyFrom: 'Copia da',
        copyFromTo: 'Copiando da {{from}} a {{to}}',
        copyTo: 'Copia per',
        copyToLocale: 'Copia in locale',
        localeToPublish: 'Località da pubblicare',
        selectLocaleToCopy: 'Seleziona la località da copiare'
    },
    operators: {
        contains: 'contiene',
        equals: 'uguale',
        exists: 'esiste',
        intersects: 'interseca',
        isGreaterThan: 'è maggiore di',
        isGreaterThanOrEqualTo: 'è maggiore o uguale a',
        isIn: 'è in',
        isLessThan: 'è minore di',
        isLessThanOrEqualTo: 'è minore o uguale a',
        isLike: 'è come',
        isNotEqualTo: 'non è uguale a',
        isNotIn: 'non è in',
        isNotLike: 'non è come',
        near: 'vicino',
        within: "all'interno"
    },
    upload: {
        addFile: 'Aggiungi file',
        addFiles: 'Aggiungi File',
        bulkUpload: 'Caricamento in Blocco',
        crop: 'Raccolto',
        cropToolDescription: "Trascina gli angoli dell'area selezionata, disegna una nuova area o regola i valori qui sotto.",
        download: 'Scarica',
        dragAndDrop: 'Trascina e rilascia un file',
        dragAndDropHere: 'oppure trascina e rilascia un file qui',
        editImage: 'Modifica immagine',
        fileName: 'Nome File',
        fileSize: 'Dimensione File',
        filesToUpload: 'File da caricare',
        fileToUpload: 'File da caricare',
        focalPoint: 'Punto Focale',
        focalPointDescription: "Trascina il punto focale direttamente sull'anteprima o regola i valori sottostanti.",
        height: 'Altezza',
        lessInfo: 'Meno info',
        moreInfo: 'Più info',
        noFile: 'Nessun file',
        pasteURL: 'Incolla URL',
        previewSizes: 'Anteprime Dimensioni',
        selectCollectionToBrowse: 'Seleziona una Collezione da Sfogliare',
        selectFile: 'Seleziona un file',
        setCropArea: 'Imposta area di ritaglio',
        setFocalPoint: 'Imposta punto focale',
        sizes: 'Formati',
        sizesFor: 'Dimensioni per {{label}}',
        width: 'Larghezza'
    },
    validation: {
        emailAddress: 'Si prega di inserire un indirizzo email valido.',
        enterNumber: 'Si prega di inserire un numero valido.',
        fieldHasNo: 'Questo campo non ha {{label}}',
        greaterThanMax: '{{value}} è superiore al massimo consentito {{label}} di {{max}}.',
        invalidInput: 'Questo campo ha un input non valido.',
        invalidSelection: 'Questo campo ha una selezione non valida.',
        invalidSelections: "'In questo campo sono presenti le seguenti selezioni non valide:'",
        lessThanMin: '{{value}} è inferiore al minimo consentito {{label}} di {{min}}.',
        limitReached: 'Raggiunto il limite, possono essere aggiunti solo {{max}} elementi.',
        longerThanMin: 'Questo valore deve essere più lungo della lunghezza minima di {{minLength}} caratteri.',
        notValidDate: '"{{value}}" non è una data valida.',
        required: 'Questo campo è obbligatorio.',
        requiresAtLeast: 'Questo campo richiede almeno {{count}} {{label}}.',
        requiresNoMoreThan: 'Questo campo richiede non più di {{count}} {{label}}.',
        requiresTwoNumbers: 'Questo campo richiede due numeri.',
        shorterThanMax: 'Questo valore deve essere inferiore alla lunghezza massima di {{maxLength}} caratteri.',
        timezoneRequired: 'È richiesto un fuso orario.',
        trueOrFalse: "Questo campo può essere solo uguale a 'true' o 'false'.",
        username: 'Inserisci un nome utente valido. Può contenere lettere, numeri, trattini, punti e underscore.',
        validUploadID: "'Questo campo non è un ID di Upload valido.'"
    },
    version: {
        type: 'Tipo',
        aboutToPublishSelection: 'Stai per pubblicare tutte le {{label}} nella selezione. Sei sicuro?',
        aboutToRestore: 'Stai per ripristinare questo documento {{label}} allo stato in cui si trovava il {{versionDate}}.',
        aboutToRestoreGlobal: 'Stai per ripristinare {{label}} allo stato in cui si trovava il {{versionDate}}.',
        aboutToRevertToPublished: 'Stai per ripristinare le modifiche di questo documento al suo stato pubblicato. Sei sicuro?',
        aboutToUnpublish: 'Stai per annullare la pubblicazione di questo documento. Sei sicuro?',
        aboutToUnpublishSelection: 'Stai per annullare la pubblicazione di tutte le {{label}} nella selezione. Sei sicuro?',
        autosave: 'Salvataggio automatico',
        autosavedSuccessfully: 'Salvataggio automatico riuscito.',
        autosavedVersion: 'Versione salvata automaticamente',
        changed: 'Modificato',
        changedFieldsCount_one: '{{count}} campo modificato',
        changedFieldsCount_other: '{{count}} campi modificati',
        compareVersion: 'Confronta versione con:',
        compareVersions: 'Confronta Versioni',
        comparingAgainst: 'Confrontando con',
        confirmPublish: 'Conferma la pubblicazione',
        confirmRevertToSaved: 'Conferma il ripristino dei salvataggi',
        confirmUnpublish: 'Conferma annullamento della pubblicazione',
        confirmVersionRestoration: 'Conferma il ripristino della versione',
        currentDocumentStatus: 'Documento {{docStatus}} corrente',
        currentDraft: 'Bozza Corrente',
        currentlyPublished: 'Attualmente Pubblicato',
        currentlyViewing: 'Visualizzazione attuale',
        currentPublishedVersion: 'Versione Pubblicata Attuale',
        draft: 'Bozza',
        draftSavedSuccessfully: 'Bozza salvata con successo.',
        lastSavedAgo: 'Ultimo salvataggio {{distance}} fa',
        modifiedOnly: 'Modificato solo',
        moreVersions: 'Altre versioni...',
        noFurtherVersionsFound: 'Non sono state trovate ulteriori versioni',
        noRowsFound: 'Nessun {{label}} trovato',
        noRowsSelected: 'Nessuna {{etichetta}} selezionata',
        preview: 'Anteprima',
        previouslyDraft: 'Precedentemente una Bozza',
        previouslyPublished: 'Precedentemente Pubblicato',
        previousVersion: 'Versione Precedente',
        problemRestoringVersion: 'Si è verificato un problema durante il ripristino di questa versione',
        publish: 'Pubblicare',
        publishAllLocales: 'Pubblica tutte le località',
        publishChanges: 'Pubblica modifiche',
        published: 'Pubblicato',
        publishIn: 'Pubblica in {{locale}}',
        publishing: 'Pubblicazione',
        restoreAsDraft: 'Ripristina come bozza',
        restoredSuccessfully: 'Ripristinato con successo.',
        restoreThisVersion: 'Ripristina questa versione',
        restoring: 'Ripristino...',
        reverting: 'Ritorno...',
        revertToPublished: 'Ritorna alla versione pubblicata',
        saveDraft: 'Salva Bozza',
        scheduledSuccessfully: 'Programmato con successo.',
        schedulePublish: 'Pubblicazione Programmata',
        selectLocales: 'Seleziona le lingue da visualizzare',
        selectVersionToCompare: 'Seleziona una versione da confrontare',
        showingVersionsFor: 'Mostra le versioni per:',
        showLocales: 'Mostra localizzazioni:',
        specificVersion: 'Versione Specifica',
        status: 'Stato',
        unpublish: 'Annulla pubblicazione',
        unpublishing: 'Annullamento pubblicazione...',
        version: 'Versione',
        versionAgo: '{{distance}} fa',
        versionCount_many: '{{count}} versioni trovate',
        versionCount_none: 'Nessuna versione trovata',
        versionCount_one: '{{count}} versione trovata',
        versionCount_other: '{{count}} versioni trovate',
        versionCreatedOn: '{{version}} creata il:',
        versionID: 'ID Versione',
        versions: 'Versioni',
        viewingVersion: 'Visualizzazione della versione per {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: '`Visualizzazione della versione per {{entityLabel}}',
        viewingVersions: 'Visualizzazione delle versioni per {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: '`Visualizzazione delle versioni per {{entityLabel}}'
    }
};
export const it = {
    dateFNSKey: 'it',
    translations: itTranslations
};

//# sourceMappingURL=it.js.map