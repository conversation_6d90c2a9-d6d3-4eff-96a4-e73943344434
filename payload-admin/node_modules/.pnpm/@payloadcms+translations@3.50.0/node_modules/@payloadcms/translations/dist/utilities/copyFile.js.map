{"version": 3, "sources": ["../../src/utilities/copyFile.ts"], "sourcesContent": ["/* eslint-disable no-console */\nimport fs from 'fs'\n\nexport function copyFile(source: string, destination: string) {\n  fs.copyFile(source, destination, (err) => {\n    if (err) {\n      // Handle error\n      console.error(`Error copying file from ${source} to ${destination}:`, err)\n      return\n    }\n    console.log(`File copied successfully from ${source} to ${destination}.`)\n  })\n}\n"], "names": ["fs", "copyFile", "source", "destination", "err", "console", "error", "log"], "mappings": "AAAA,6BAA6B,GAC7B,OAAOA,QAAQ,KAAI;AAEnB,OAAO,SAASC,SAASC,MAAc,EAAEC,WAAmB;IAC1DH,GAAGC,QAAQ,CAACC,QAAQC,aAAa,CAACC;QAChC,IAAIA,KAAK;YACP,eAAe;YACfC,QAAQC,KAAK,CAAC,CAAC,wBAAwB,EAAEJ,OAAO,IAAI,EAAEC,YAAY,CAAC,CAAC,EAAEC;YACtE;QACF;QACAC,QAAQE,GAAG,CAAC,CAAC,8BAA8B,EAAEL,OAAO,IAAI,EAAEC,YAAY,CAAC,CAAC;IAC1E;AACF"}