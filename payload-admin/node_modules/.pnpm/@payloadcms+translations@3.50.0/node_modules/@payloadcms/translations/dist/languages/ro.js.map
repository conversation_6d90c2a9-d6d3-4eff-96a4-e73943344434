{"version": 3, "sources": ["../../src/languages/ro.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const roTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Cont',\n    accountOfCurrentUser: 'Contul utilizatorului curent',\n    accountVerified: 'Contul a fost verificat cu succes.',\n    alreadyActivated: 'Deja activat',\n    alreadyLoggedIn: 'Deja autorizat',\n    apiKey: 'Cheia API',\n    authenticated: 'Autentificat',\n    backToLogin: 'Înapoi la login',\n    beginCreateFirstUser: 'Pentru a începe, creați primul utilizator.',\n    changePassword: 'Schimbați parola',\n    checkYourEmailForPasswordReset:\n      'Dacă adresa de e-mail este asociată cu un cont, veți primi în curând instrucțiuni pentru resetarea parolei voastre. Vă rugăm să verificați dosarul de spam sau de mesaje nedorite dacă nu vedeți e-mailul în inbox-ul dvs.',\n    confirmGeneration: 'Confirmați generarea',\n    confirmPassword: 'Confirmați parola',\n    createFirstUser: 'Creați primul utilizator',\n    emailNotValid: 'Emailul furnizat nu este valid',\n    emailOrUsername: 'Email sau Nume de utilizator',\n    emailSent: 'Email trimis',\n    emailVerified: 'E-mail verificat cu succes.',\n    enableAPIKey: 'Activați cheia API',\n    failedToUnlock: 'Nu s-a reușit deblocarea',\n    forceUnlock: 'Forțați deblocarea',\n    forgotPassword: 'Am uitat parola',\n    forgotPasswordEmailInstructions:\n      'Vă rugăm să introduceți emailul dumneavoastră mai jos. Veți primi un mesaj de email cu instrucțiuni despre cum să vă resetați parola.',\n    forgotPasswordQuestion: 'Ați uitat parola?',\n    forgotPasswordUsernameInstructions:\n      'Vă rugăm să introduceți numele de utilizator mai jos. Instrucțiunile despre cum să vă resetați parola vor fi trimise la adresa de e-mail asociată cu numele dvs. de utilizator.',\n    generate: 'Generează',\n    generateNewAPIKey: 'Generează o nouă cheie API',\n    generatingNewAPIKeyWillInvalidate:\n      'Generarea unei noi chei API va <1>invalida</1> cheia anterioară. Sunteți sigur că doriți să continuați?',\n    lockUntil: 'Blocați până la',\n    logBackIn: 'Autentificați-vă din nou',\n    loggedIn:\n      'Pentru a vă autentifica cu un alt utilizator, trebuie să vă <0>deconectați mai întâi</0>.',\n    loggedInChangePassword:\n      'Pentru a vă schimba parola, accesați <0>contul</0> și editați-vă parola acolo.',\n    loggedOutInactivity: 'Ați fost deconectat din cauza inactivității.',\n    loggedOutSuccessfully: 'Ați fost deconectat cu succes.',\n    loggingOut: 'Deconectare...',\n    login: 'Autentificare',\n    loginAttempts: 'Încercări de autentificare',\n    loginUser: 'Autentificare utilizator',\n    loginWithAnotherUser:\n      'Pentru a vă autentifica cu un alt utilizator, trebuie să vă <0>deconectați mai întâi</0>.',\n    logOut: 'Deconectează-te',\n    logout: 'Ieșire',\n    logoutSuccessful: 'Deconectare realizată cu succes.',\n    logoutUser: 'Deconectați utilizatorul',\n    newAccountCreated:\n      'A fost creat un nou cont pe care îl puteți accesa <a href=\"{{serverURL}}\">{{serverURL}}</a> Vă rugăm să intrați pe următorul link sau să copiați URL-ul de mai jos în browserul dvs. pentru a vă verifica emailul: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> După ce vă verificați adresa de email, vă veți putea autentifica cu succes.',\n    newAPIKeyGenerated: 'Cheie nouă API generată.',\n    newPassword: 'Parolă nouă',\n    passed: 'Autentificare reușită',\n    passwordResetSuccessfully: 'Resetarea parolei a fost realizată cu succes.',\n    resetPassword: 'Resetează parola',\n    resetPasswordExpiration: 'Resetați expirarea parolei',\n    resetPasswordToken: 'Resetați token-ul parolei',\n    resetYourPassword: 'Resetați-vă parola',\n    stayLoggedIn: 'Rămâneți conectat',\n    successfullyRegisteredFirstUser: 'Primul utilizator a fost înregistrat cu succes.',\n    successfullyUnlocked: 'Deblocat cu succes',\n    tokenRefreshSuccessful: 'Reîmprospătarea tokenului a fost efectuată cu succes.',\n    unableToVerify: 'Nu se poate verifica',\n    username: 'Nume de utilizator',\n    usernameNotValid: 'Numele de utilizator furnizat nu este valid.',\n    verified: 'Verificat',\n    verifiedSuccessfully: 'Verificat cu succes',\n    verify: 'Verifică',\n    verifyUser: 'Verifică utilizatorul',\n    verifyYourEmail: 'Verifică-ți emailul',\n    youAreInactive:\n      'Nu ați mai fost activ de ceva timp și în scurt timp veți fi deconectat automat pentru propria dvs. securitate. Doriți să rămâneți conectat(ă)?',\n    youAreReceivingResetPassword:\n      'Primiți acest mesaj deoarece dumneavoastră (sau altcineva) ați solicitat resetarea parolei pentru contul dumneavoastră. Vă rugăm să dați clic pe următorul link sau să îl copiați în browserul dvs. pentru a finaliza procesul:',\n    youDidNotRequestPassword:\n      'Dacă nu ați solicitat acest lucru, vă rugăm să ignorați acest email și parola dvs. va rămâne neschimbată.',\n  },\n  error: {\n    accountAlreadyActivated: 'Acest cont a fost deja activat.',\n    autosaving: 'A existat o problemă în timpul salvării automate a acestui document.',\n    correctInvalidFields: 'Vă rugăm să corectați datele invalide.',\n    deletingFile: 'S-a produs o eroare la ștergerea fișierului.',\n    deletingTitle:\n      'S-a produs o eroare în timpul ștergerii {{title}}. Vă rugăm să verificați conexiunea și să încercați din nou.',\n    documentNotFound:\n      'Documentul cu ID-ul {{id}} nu a putut fi găsit. S-ar putea să fi fost șters sau să nu fi existat niciodată, sau s-ar putea să nu aveți acces la acesta.',\n    emailOrPasswordIncorrect: 'Adresa de e-mail sau parola este incorectă.',\n    followingFieldsInvalid_one: 'Următorul câmp nu este valid:',\n    followingFieldsInvalid_other: 'Următoarele câmpuri nu sunt valabile:',\n    incorrectCollection: 'Colecție incorectă',\n    insufficientClipboardPermissions:\n      'Accesul la clipboard a fost refuzat. Verificați permisiunile clipboard-ului.',\n    invalidClipboardData: 'Date invalide în clipboard.',\n    invalidFileType: 'Tip de fișier invalid',\n    invalidFileTypeValue: 'Tip de fișier invalid: {{value}}',\n    invalidRequestArgs: 'Argumente invalide transmise în cerere: {{args}}',\n    loadingDocument: 'A existat o problemă la încărcarea documentului cu ID-ul de {{id}}.',\n    localesNotSaved_one: 'Următoarea localizare nu a putut fi salvată:',\n    localesNotSaved_other: 'Următoarele localizări nu au putut fi salvate:',\n    logoutFailed: 'Deconectarea a eșuat.',\n    missingEmail: 'Lipsește emailul.',\n    missingIDOfDocument: 'Lipsește ID-ul documentului care trebuie actualizat.',\n    missingIDOfVersion: 'Lipsește ID-ul versiunii.',\n    missingRequiredData: 'Lipsesc datele necesare.',\n    noFilesUploaded: 'Nu a fost încărcat niciun fișier.',\n    noMatchedField: 'Nu s-a găsit niciun câmp corespunzător pentru \"{{label}}\"',\n    notAllowedToAccessPage: 'Nu aveți voie să accesați această pagină.',\n    notAllowedToPerformAction: 'Nu aveți voie să efectuați această acțiune.',\n    notFound: 'Resursa solicitată nu a fost găsită.',\n    noUser: 'Nici un utilizator',\n    previewing: 'A existat o problemă la previzualizarea acestui document.',\n    problemUploadingFile: 'A existat o problemă în timpul încărcării fișierului.',\n    restoringTitle:\n      'A survenit o eroare în timpul restaurării {{title}}. Verificați conexiunea și încercați din nou.',\n    tokenInvalidOrExpired: 'Tokenul este invalid sau a expirat.',\n    tokenNotProvided: 'Tokenul nu a fost furnizat.',\n    unableToCopy: 'Imposibil de copiat.',\n    unableToDeleteCount: 'Nu se poate șterge {{count}} din {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Eroare la reindexarea colecției {{collection}}. Operațiune anulată.',\n    unableToUpdateCount: 'Nu se poate șterge {{count}} din {{total}} {{label}}.',\n    unauthorized: 'Neautorizat, trebuie să vă conectați pentru a face această cerere.',\n    unauthorizedAdmin: 'Neautorizat, acest utilizator nu are acces la panoul de administrare.',\n    unknown: 'S-a produs o eroare necunoscută.',\n    unPublishingDocument: 'A existat o problemă în timpul nepublicării acestui document.',\n    unspecific: 'S-a produs o eroare.',\n    unverifiedEmail: 'Vă rugăm să vă verificați e-mailul înainte de a vă autentifica.',\n    userEmailAlreadyRegistered: 'Un utilizator cu emailul dat este deja înregistrat.',\n    userLocked:\n      'Acest utilizator este blocat din cauza unui număr prea mare de încercări de autentificare eșuate.',\n    usernameAlreadyRegistered:\n      'Un utilizator cu numele de utilizator furnizat este deja înregistrat.',\n    usernameOrPasswordIncorrect: 'Numele de utilizator sau parola furnizate sunt incorecte.',\n    valueMustBeUnique: 'Valoarea trebuie să fie unică',\n    verificationTokenInvalid: 'Tokenul de verificare este invalid.',\n  },\n  fields: {\n    addLabel: 'Adăugați {{label}}',\n    addLink: 'Adăugați un link',\n    addNew: 'Adăugați un nou',\n    addNewLabel: 'Adăugați un nou {{label}}',\n    addRelationship: 'Adăugați o relație',\n    addUpload: 'Adăugați un fișier',\n    block: 'bloc',\n    blocks: 'Blocuri',\n    blockType: 'Tip de bloc',\n    chooseBetweenCustomTextOrDocument:\n      'Alegeți între a introduce un text URL personalizat sau a crea un link către un alt document.',\n    chooseDocumentToLink: 'Alegeți un document către care să creați un link',\n    chooseFromExisting: 'Alegeți dintre cele existente',\n    chooseLabel: 'Alege {{label}}',\n    collapseAll: 'Colapsează toate',\n    customURL: 'URL personalizat',\n    editLabelData: 'Editați {{label}}',\n    editLink: 'Editați Link-ul',\n    editRelationship: 'Editați relația',\n    enterURL: 'Introduceți un URL',\n    internalLink: 'Link intern',\n    itemsAndMore: '{{items}} şi {{count}} mai multe',\n    labelRelationship: 'Relația cu {{label}}',\n    latitude: 'Latitudine',\n    linkedTo: 'Legat de <0>{{label}}</0>',\n    linkType: 'Tip de link',\n    longitude: 'Longitudine',\n    newLabel: 'Nou {{label}}',\n    openInNewTab: 'Deschideți în tab nou',\n    passwordsDoNotMatch: 'Parolele nu corespund.',\n    relatedDocument: 'Document asociat',\n    relationTo: 'Relație cu',\n    removeRelationship: 'Eliminați relația',\n    removeUpload: 'Eliminați încărcarea',\n    saveChanges: 'Salvați modificările',\n    searchForBlock: 'Căutați un bloc',\n    selectExistingLabel: 'Selectați existent {{label}}',\n    selectFieldsToEdit: 'Selectați câmpurile de editat',\n    showAll: 'Afișați toate',\n    swapRelationship: 'Schimbați relația',\n    swapUpload: 'Schimbați Încărcarea',\n    textToDisplay: 'Text de afișat',\n    toggleBlock: 'Toggle bloc',\n    uploadNewLabel: 'Încărcați un nou {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Răsfoiește după Folder',\n    byFolder: 'După dosar',\n    deleteFolder: 'Ștergeți dosarul',\n    folderName: 'Nume dosar',\n    folders: 'Dosare',\n    folderTypeDescription:\n      'Selectați ce tip de documente din colecție ar trebui să fie permise în acest dosar.',\n    itemHasBeenMoved: '{{title}} a fost mutat în {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} a fost mutat în dosarul rădăcină',\n    itemsMovedToFolder: '{{title}} a fost mutat în {{folderName}}',\n    itemsMovedToRoot: '{{title}} a fost mutat în dosarul rădăcină',\n    moveFolder: 'Mutare Dosar',\n    moveItemsToFolderConfirmation:\n      'Sunteți pe cale să mutați <1>{{count}} {{label}}</1> în <2>{{toFolder}}</2>. Sunteți sigur?',\n    moveItemsToRootConfirmation:\n      'Sunteți pe cale să mutați <1>{{count}} {{label}}</1> în dosarul principal. Sunteți sigur?',\n    moveItemToFolderConfirmation:\n      'Sunteți pe cale să mutați <1>{{title}}</1> în <2>{{toFolder}}</2>. Sunteți sigur?',\n    moveItemToRootConfirmation:\n      'Sunteți pe cale să mutați <1>{{title}}</1> în dosarul rădăcină. Sigur?',\n    movingFromFolder: 'Mutarea {{title}} din {{fromFolder}}',\n    newFolder: 'Dosar nou',\n    noFolder: 'Niciun dosar',\n    renameFolder: 'Redenumiți dosarul',\n    searchByNameInFolder: 'Căutați după nume în {{folderName}}',\n    selectFolderForItem: 'Selectați dosarul pentru {{title}}',\n  },\n  general: {\n    name: 'Nume',\n    aboutToDelete: 'Sunteți pe cale să ștergeți {{label}} <1>{{title}}</1>. Sunteți sigur?',\n    aboutToDeleteCount_many: 'Sunteți pe cale să ștergeți {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Sunteți pe cale să ștergeți {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Sunteți pe cale să ștergeți {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Sunteți pe cale să ștergeți definitiv {{label}} <1>{{title}}</1>. Sunteți sigur?',\n    aboutToPermanentlyDeleteTrash:\n      'Sunteți pe cale să ștergeți definitiv <0>{{count}}</0> <1>{{label}}</1> din coșul de gunoi. Sunteți sigur?',\n    aboutToRestore: 'Sunteți pe cale să restaurați {{label}} <1>{{title}}</1>. Sunteți sigur?',\n    aboutToRestoreAsDraft:\n      'Sunteți pe cale să restaurați {{label}} <1>{{title}}</1> ca o versiune preliminară. Sunteți sigur?',\n    aboutToRestoreAsDraftCount: 'Sunteți pe cale să restaurați {{count}} {{label}} ca proiect',\n    aboutToRestoreCount: 'Sunteți pe cale să restaurați {{count}} {{label}}',\n    aboutToTrash:\n      'Sunteți pe cale să mutați {{label}} <1>{{title}}</1> în coșul de gunoi. Sunteți sigur?',\n    aboutToTrashCount: 'Sunteți pe cale să mutați {{count}} {{label}} la gunoi.',\n    addBelow: 'Adaugă mai jos',\n    addFilter: 'Adaugă filtru',\n    adminTheme: 'Tema Admin',\n    all: 'Toate',\n    allCollections: 'Toate Colecțiile',\n    allLocales: 'Toate localizările',\n    and: 'Şi',\n    anotherUser: 'Un alt utilizator',\n    anotherUserTakenOver: 'Un alt utilizator a preluat editarea acestui document.',\n    applyChanges: 'Aplicați modificările',\n    ascending: 'Ascendant',\n    automatic: 'Automat',\n    backToDashboard: 'Înapoi la panoul de bord',\n    cancel: 'Anulați',\n    changesNotSaved:\n      'Modificările dvs. nu au fost salvate. Dacă plecați acum, vă veți pierde modificările.',\n    clear: 'Clar',\n    clearAll: 'Șterge tot',\n    close: 'Închide',\n    collapse: 'Colaps',\n    collections: 'Colecții',\n    columns: 'Coloane',\n    columnToSort: 'Coloana de sortat',\n    confirm: 'Confirmați',\n    confirmCopy: 'Confirmă copierea',\n    confirmDeletion: 'Confirmați ștergerea',\n    confirmDuplication: 'Confirmați duplicarea',\n    confirmMove: 'Confirmați mutarea',\n    confirmReindex: 'Reindexați toate {{collections}}?',\n    confirmReindexAll: 'Reindexați toate colecțiile?',\n    confirmReindexDescription:\n      'Aceasta va elimina indexurile existente și va reindexa documentele din colecțiile {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Aceasta va elimina indexurile existente și va reindexa documentele din toate colecțiile.',\n    confirmRestoration: 'Confirmă restaurarea',\n    copied: 'Copiat',\n    copy: 'Copiați',\n    copyField: 'Copiază câmpul',\n    copying: 'Copiere',\n    copyRow: 'Copiază rândul',\n    copyWarning:\n      'Sunteți pe cale să suprascrieți {{to}} cu {{from}} pentru {{label}} {{title}}. Sunteți sigur?',\n    create: 'Creează',\n    created: 'Creat',\n    createdAt: 'Creat la',\n    createNew: 'Creați unul nou',\n    createNewLabel: 'Creați un nou {{label}}',\n    creating: 'Creare',\n    creatingNewLabel: 'Crearea unui nou {{label}}',\n    currentlyEditing:\n      'editează în prezent acest document. Dacă preiei controlul, vor fi blocați să continue editarea și ar putea pierde modificările nesalvate.',\n    custom: 'Personalizat',\n    dark: 'Dark',\n    dashboard: 'Panoul de bord',\n    delete: 'Șterge',\n    deleted: 'Șters',\n    deletedAt: 'Șters la',\n    deletedCountSuccessfully: 'Șterse cu succes {{count}} {{label}}.',\n    deletedSuccessfully: 'Șters cu succes.',\n    deletePermanently: 'Omite coșul și șterge definitiv',\n    deleting: 'Deleting...',\n    depth: 'Adâncime',\n    descending: 'Descendentă',\n    deselectAllRows: 'Deselectează toate rândurile',\n    document: 'Document',\n    documentIsTrashed: 'Acest {{label}} este la gunoi și poate fi doar citit.',\n    documentLocked: 'Document blocat',\n    documents: 'Documente',\n    duplicate: 'Duplicați',\n    duplicateWithoutSaving: 'Duplicați fără salvarea modificărilor',\n    edit: 'Editează',\n    editAll: 'Editează toate',\n    editedSince: 'Editat din',\n    editing: 'Editare',\n    editingLabel_many: 'Editare {{count}} {{label}}',\n    editingLabel_one: 'Editare {{count}} {{label}}',\n    editingLabel_other: 'Editare {{count}} {{label}}',\n    editingTakenOver: 'Editarea preluată',\n    editLabel: 'Editați {{label}}',\n    email: 'Email',\n    emailAddress: 'Adresa de email',\n    emptyTrash: 'Golește coșul de gunoi',\n    emptyTrashLabel: 'Goliți coșul {{label}}',\n    enterAValue: 'Introduceți o valoare',\n    error: 'Eroare',\n    errors: 'Erori',\n    exitLivePreview: 'Ieși din Previzualizarea Live',\n    export: 'Export',\n    fallbackToDefaultLocale: 'Revenire la locația implicită',\n    false: 'Fals',\n    filter: 'Filtru',\n    filters: 'Filtre',\n    filterWhere: 'Filtrează {{label}} unde',\n    globals: 'Globale',\n    goBack: 'Înapoi',\n    groupByLabel: 'Grupare după {{label}}',\n    import: 'Import',\n    isEditing: 'editează',\n    item: 'articol',\n    items: 'articole',\n    language: 'Limba',\n    lastModified: 'Ultima modificare',\n    leaveAnyway: 'Pleacă oricum',\n    leaveWithoutSaving: 'Plecare fără a salva',\n    light: 'Light',\n    livePreview: 'Previzualizare',\n    loading: 'Încărcare',\n    locale: 'Localitate',\n    locales: 'Localuri',\n    menu: 'Meniu',\n    moreOptions: 'Mai multe opțiuni',\n    move: 'Mutați',\n    moveConfirm:\n      'Sunteți pe cale să mutați {{count}} {{label}} la <1>{{destination}}</1>. Sunteți sigur?',\n    moveCount: 'Mutați {{count}} {{label}}',\n    moveDown: 'Mutați în jos',\n    moveUp: 'Mutați în sus',\n    moving: 'În mișcare',\n    movingCount: 'Mutarea {{count}} {{eticheta}}',\n    newPassword: 'Parolă nouă',\n    next: 'Următorul',\n    no: 'Nu',\n    noDateSelected: 'Nu a fost selectată nicio dată',\n    noFiltersSet: 'Nici un filtru setat',\n    noLabel: '<Nici un {{label}}>',\n    none: 'Nici unul',\n    noOptions: 'Fără opțiuni',\n    noResults:\n      'Nici un {{label}} găsit. Fie nu există încă niciun {{label}}, fie niciunul nu se potrivește cu filtrele pe care le-ați specificat mai sus..',\n    notFound: 'Nu a fost găsit',\n    nothingFound: 'Nimic găsit',\n    noTrashResults: 'Niciun {{label}} în coșul de gunoi.',\n    noUpcomingEventsScheduled: 'Nu sunt evenimente programate în viitor.',\n    noValue: 'Nici o valoare',\n    of: 'de',\n    only: 'Doar',\n    open: 'Deschide',\n    or: 'Sau',\n    order: 'ORdine',\n    overwriteExistingData: 'Suprascrieți datele existente din câmp',\n    pageNotFound: 'Pagina nu a fost găsită',\n    password: 'Parola',\n    pasteField: 'Lipește câmpul',\n    pasteRow: 'Lipește rândul',\n    payloadSettings: 'Setări de Payload',\n    permanentlyDelete: 'Șterge definitiv',\n    permanentlyDeletedCountSuccessfully: 'Șters permanent cu succes {{count}} {{label}}.',\n    perPage: 'Pe pagină: {{limit}}',\n    previous: 'Anterior',\n    reindex: 'Reindexare',\n    reindexingAll: 'Reindexarea tuturor {{collections}}.',\n    remove: 'Eliminați',\n    rename: 'Redenumire',\n    reset: 'Resetare',\n    resetPreferences: 'Resetare preferințe',\n    resetPreferencesDescription: 'Aceasta va reseta toate preferințele tale la setările implicite.',\n    resettingPreferences: 'Resetare preferințe.',\n    restore: 'Restaurare',\n    restoreAsPublished: 'Restabilește ca versiune publicată',\n    restoredCountSuccessfully: '{{count}} {{label}} restabilite cu succes.',\n    restoring:\n      'Respectați semnificația textului original în contextul Payload. Iată o listă de termeni obișnuiți Payload care au semnificații foarte specifice:\\n    - Colectie: O colectie este un grup de documente care împart o structură și un scop comun. Colectiile sunt utilizate pentru a organiza și gestiona conținutul în Payload.\\n    - Câmp: Un câmp este o piesă specifică de date dintr-un document dintr-o colecție. Câmpurile definesc structura și tipul de date care pot fi stocate într-un document.\\n    - Document',\n    row: 'Rând',\n    rows: 'Rânduri',\n    save: 'Salvează',\n    saving: 'Salvare...',\n    schedulePublishFor: 'Planificați publicarea pentru {{title}}',\n    searchBy: 'Căutați după {{label}}',\n    select: 'Selectați',\n    selectAll: 'Selectați toate {{count}} {{label}}',\n    selectAllRows: 'Selectează toate rândurile',\n    selectedCount: '{{count}} {{label}} selectate',\n    selectLabel: 'Selectați {{label}}',\n    selectValue: 'Selectați o valoare',\n    showAllLabel: 'Afișează toate {{eticheta}}',\n    sorryNotFound: 'Ne pare rău - nu există nimic care să corespundă cu cererea dvs.',\n    sort: 'Sortează',\n    sortByLabelDirection: 'Sortează după {{etichetă}} {{direcţie}}',\n    stayOnThisPage: 'Rămâneți pe această pagină',\n    submissionSuccessful: 'Trimitere cu succes.',\n    submit: 'Trimite',\n    submitting: 'Se trimite...',\n    success: 'Succes',\n    successfullyCreated: '{{label}} creat(ă) cu succes.',\n    successfullyDuplicated: '{{label}} duplicat(ă) cu succes.',\n    successfullyReindexed:\n      'Reindexare realizată cu succes pentru {{count}} din {{total}} documente din colecțiile {{collections}}.',\n    takeOver: 'Preia controlul',\n    thisLanguage: 'Română',\n    time: 'Timp',\n    timezone: 'Fus orar',\n    titleDeleted: '{{label}} \"{{title}}\" șters cu succes.',\n    titleRestored: '{{label}} \"{{title}}\" a fost restaurat cu succes.',\n    titleTrashed: '{{label}} \"{{title}}\" a fost mutat la coșul de gunoi.',\n    trash: 'Gunoi',\n    trashedCountSuccessfully: '{{count}} {{label}} mutate la coșul de gunoi.',\n    true: 'Adevărat',\n    unauthorized: 'neautorizat(ă)',\n    unsavedChanges: 'Aveți modificări nesalvate. Salvați sau renunțați înainte de a continua.',\n    unsavedChangesDuplicate: 'Aveți modificări nesalvate. Doriți să continuați să duplicați?',\n    untitled: 'Fără titlu',\n    upcomingEvents: 'Evenimente viitoare',\n    updatedAt: 'Actualizat la',\n    updatedCountSuccessfully: 'Actualizate {{count}} {{label}} cu succes.',\n    updatedLabelSuccessfully: '{{label}} actualizată cu succes.',\n    updatedSuccessfully: 'Actualizat cu succes.',\n    updateForEveryone: 'Actualizare pentru toată lumea',\n    updating: 'Actualizare',\n    uploading: 'Încărcare',\n    uploadingBulk: 'Încărcare {{current}} din {{total}}',\n    user: 'Utilizator',\n    username: 'Nume de utilizator',\n    users: 'Utilizatori',\n    value: 'Valoare',\n    viewing: 'Vizualizare',\n    viewReadOnly: 'Vizualizare doar pentru citire',\n    welcome: 'Bine ați venit',\n    yes: 'Da',\n  },\n  localization: {\n    cannotCopySameLocale: 'Nu se poate copia în aceeași localizare',\n    copyFrom: 'Copiază de la',\n    copyFromTo: 'Copierea de la {{from}} la {{to}}',\n    copyTo: 'Copiați în',\n    copyToLocale: 'Copiați în localizare',\n    localeToPublish: 'Localizare pentru publicare',\n    selectLocaleToCopy: 'Selectați localizarea pentru copiere',\n  },\n  operators: {\n    contains: 'conține',\n    equals: 'egal cu',\n    exists: 'există',\n    intersects: 'se intersectează',\n    isGreaterThan: 'este mai mare decât',\n    isGreaterThanOrEqualTo: 'este mai mare sau egal cu',\n    isIn: 'este în',\n    isLessThan: 'este mai mic decât',\n    isLessThanOrEqualTo: 'este mai mic decât sau egal cu',\n    isLike: 'este ca',\n    isNotEqualTo: 'nu este egal cu',\n    isNotIn: 'nu este în',\n    isNotLike: 'nu este ca',\n    near: 'în apropiere de',\n    within: 'înăuntru',\n  },\n  upload: {\n    addFile: 'Adaugă fișier',\n    addFiles: 'Adăugați fișiere',\n    bulkUpload: 'Încărcare în masă',\n    crop: 'Cultură',\n    cropToolDescription:\n      'Trageți colțurile zonei selectate, desenați o nouă zonă sau ajustați valorile de mai jos.',\n    download: 'Descărcare',\n    dragAndDrop: 'Trageți și plasați un fișier',\n    dragAndDropHere: 'sau trageți și plasați un fișier aici',\n    editImage: 'Editează imaginea',\n    fileName: 'Numele fișierului',\n    fileSize: 'Dimensiunea fișierului',\n    filesToUpload: 'Fișiere de încărcat',\n    fileToUpload: 'Fișier de încărcat',\n    focalPoint: 'Punct central',\n    focalPointDescription:\n      'Trageți punctul focal direct pe previzualizare sau ajustați valorile de mai jos.',\n    height: 'Înălțime',\n    lessInfo: 'Mai puține informații',\n    moreInfo: 'Mai multe informații',\n    noFile: 'Niciun fișier',\n    pasteURL: 'Lipește URL',\n    previewSizes: 'Dimensiuni Previzualizare',\n    selectCollectionToBrowse: 'Selectați o colecție pentru navigare',\n    selectFile: 'Selectați un fișier',\n    setCropArea: 'Setați zona de decupare',\n    setFocalPoint: 'Setează punctul focal',\n    sizes: 'Dimensiuni',\n    sizesFor: 'Mărimi pentru {{label}}',\n    width: 'Lățime',\n  },\n  validation: {\n    emailAddress: 'Vă rugăm să introduceți o adresă de email validă.',\n    enterNumber: 'Vă rugăm să introduceți un număr valid.',\n    fieldHasNo: 'Acest câmp nu are un {{label}}',\n    greaterThanMax:\n      '{{value}} este mai mare decât valoarea maximă permisă pentru {{label}} de {{max}}.',\n    invalidInput: 'Acest câmp are o intrare invalidă.',\n    invalidSelection: 'Acest câmp are o selecție invalidă.',\n    invalidSelections: 'Acest câmp are următoarele selecții invalide:',\n    lessThanMin:\n      '{{value}} este mai mic decât valoarea minimă permisă pentru {{label}} de {{min}}.',\n    limitReached: 'Limita atinsă, doar {{max}} elemente pot fi adăugate.',\n    longerThanMin:\n      'Această valoare trebuie să fie mai mare decât lungimea minimă de {{minLength}} caractere.',\n    notValidDate: '\"{{value}}\" nu este o dată valabilă.',\n    required: 'Acest câmp este obligatoriu.',\n    requiresAtLeast: 'Acest domeniu necesită cel puțin {{count}} {{label}}.',\n    requiresNoMoreThan: 'Acest câmp nu necesită mai mult de {{count}} {{label}}.',\n    requiresTwoNumbers: 'Acest câmp necesită două numere.',\n    shorterThanMax:\n      'Această valoare trebuie să fie mai scurtă decât lungimea maximă de {{maxLength}} caractere.',\n    timezoneRequired: 'Este necesar un fus orar.',\n    trueOrFalse: 'Acest câmp poate fi doar egal cu true sau false.',\n    username:\n      'Vă rugăm să introduceți un nume de utilizator valid. Poate conține litere, numere, cratime, puncte și sublinieri.',\n    validUploadID: 'Acest câmp nu este un ID de încărcare valid.',\n  },\n  version: {\n    type: 'Tip',\n    aboutToPublishSelection:\n      'Sunteți pe cale să publicați toate {{label}} din selecție. Sunteți sigur?',\n    aboutToRestore:\n      'Sunteți pe cale să readuceți acest document {{label}} în starea în care se afla la data de {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Sunteți pe cale să readuceți {{label}} global în starea în care se afla la data de {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Sunteți pe cale să readuceți modificările aduse acestui document la starea sa publicată. Sunteți sigur?',\n    aboutToUnpublish: 'Sunteți pe cale să nepublicați acest document. Sunteți sigur?',\n    aboutToUnpublishSelection:\n      'Sunteți pe punctul de a nepublica toate {{label}} din selecție. Sunteți sigur?',\n    autosave: 'Autosalvare',\n    autosavedSuccessfully: 'Autosalvare cu succes.',\n    autosavedVersion: 'Versiunea salvată automat.',\n    changed: 'Schimbat',\n    changedFieldsCount_one: '{{count}} a modificat câmpul',\n    changedFieldsCount_other: '{{count}} câmpuri modificate',\n    compareVersion: 'Comparați versiunea cu:',\n    compareVersions: 'Compară Versiuni',\n    comparingAgainst: 'Comparând cu',\n    confirmPublish: 'Confirmați publicarea',\n    confirmRevertToSaved: 'Confirmați revenirea la starea salvată',\n    confirmUnpublish: 'Confirmați nepublicarea',\n    confirmVersionRestoration: 'Confirmați restaurarea versiunii',\n    currentDocumentStatus: 'Documentul {{docStatus}} curent',\n    currentDraft: 'Proiectul Actual',\n    currentlyPublished: 'Publicat în prezent',\n    currentlyViewing: 'Vizualizare curentă',\n    currentPublishedVersion: 'Versiunea Publicată Curentă',\n    draft: 'Proiect',\n    draftSavedSuccessfully: 'Proiect salvat cu succes.',\n    lastSavedAgo: 'Ultima salvare acum {{distance}}',\n    modifiedOnly: 'Modificat doar',\n    moreVersions: 'Mai multe versiuni...',\n    noFurtherVersionsFound: 'Nu s-au găsit alte versiuni',\n    noRowsFound: 'Nu s-a găsit niciun {{label}}',\n    noRowsSelected: 'Niciun {{etichetă}} selectat',\n    preview: 'Previzualizare',\n    previouslyDraft: 'Anterior un Proiect',\n    previouslyPublished: 'Publicat anterior',\n    previousVersion: 'Versiune Anterioară',\n    problemRestoringVersion: 'A existat o problemă la restaurarea acestei versiuni',\n    publish: 'Publicați',\n    publishAllLocales: 'Publicați toate configurările regionale și lingvistice',\n    publishChanges: 'Publicați modificările',\n    published: 'Publicat',\n    publishIn: 'Publicați în {{locale}}',\n    publishing: 'Editare',\n    restoreAsDraft: 'Restaurează ca proiect',\n    restoredSuccessfully: 'Restaurat cu succes.',\n    restoreThisVersion: 'Restaurați această versiune',\n    restoring: 'Restaurare...',\n    reverting: 'Revenire...',\n    revertToPublished: 'Reveniți la publicat',\n    saveDraft: 'Salvați proiectul',\n    scheduledSuccessfully: 'Programat cu succes.',\n    schedulePublish: 'Programare Publicare',\n    selectLocales: 'Selectați localitățile de afișat',\n    selectVersionToCompare: 'Selectați o versiune pentru a compara',\n    showingVersionsFor: 'Se afișează versiuni pentru:',\n    showLocales: 'Afișați localitățile:',\n    specificVersion: 'Versiunea specifică',\n    status: 'Status',\n    unpublish: 'Dezpublicare',\n    unpublishing: 'Dezpublicare...',\n    version: 'Versiune',\n    versionAgo: '{{distance}} în urmă',\n    versionCount_many: '{{count}} versiuni găsite',\n    versionCount_none: 'Nici o versiune găsită',\n    versionCount_one: '{{count}} versiune găsită',\n    versionCount_other: '{{count}} versiuni găsite',\n    versionCreatedOn: '{{version}} creată pe:',\n    versionID: 'ID-ul versiunii',\n    versions: 'Versiuni',\n    viewingVersion: 'Vizualizarea versiunii pentru {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Vizualizarea versiunii pentru globala {{entityLabel}}',\n    viewingVersions: 'Vizualizarea versiunilor pentru {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Vizualizarea versiunilor pentru globala {{entityLabel}}',\n  },\n}\n\nexport const ro: Language = {\n  dateFNSKey: 'ro',\n  translations: roTranslations,\n}\n"], "names": ["roTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "ro", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UACE;QACFC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YACE;QACFC,2BACE;QACFC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBACE;QACFC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aACE;QACFC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBACE;QACFC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}