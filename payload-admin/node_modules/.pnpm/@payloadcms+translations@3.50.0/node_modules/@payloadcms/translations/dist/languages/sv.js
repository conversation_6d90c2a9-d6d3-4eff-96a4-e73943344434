export const svTranslations = {
    authentication: {
        account: 'Ko<PERSON>',
        accountOfCurrentUser: 'Konto för nuvarande användare',
        accountVerified: 'Kontot har verifierats',
        alreadyActivated: '<PERSON>an aktiverad',
        alreadyLoggedIn: '<PERSON>an inloggad',
        apiKey: 'API-nyckel',
        authenticated: 'Autentiserad',
        backToLogin: 'Till<PERSON><PERSON> till inloggningen',
        beginCreateFirstUser: '<PERSON>ör att komma igång, skapa din första användare.',
        changePassword: 'Byt lösenord',
        checkYourEmailForPasswordReset: 'Om e-postadressen är kopplad till ett konto kommer du inom kort att få instruktioner för att återställa ditt lösenord. Vänligen kontrollera din skräppost-mapp om du inte ser e-postmeddelandet i din inkorg.',
        confirmGeneration: 'Bekräfta generering',
        confirmPassword: 'Bekräfta lösenord',
        createFirstUser: 'Skapa första användaren',
        emailNotValid: 'Angiven e-postadress är inte giltig',
        emailOrUsername: 'E-post eller användarnamn',
        emailSent: 'Meddelande skickat',
        emailVerified: 'E-posten har verifierats',
        enableAPIKey: 'Aktivera API-nyckel',
        failedToUnlock: 'Det gick inte att låsa upp',
        forceUnlock: 'Tvinga upplåsning',
        forgotPassword: 'Glömt lösenord',
        forgotPasswordEmailInstructions: 'Vänligen ange din e-postadress nedan. Du kommer att få ett e-postmeddelande med instruktioner om hur du återställer ditt lösenord.',
        forgotPasswordQuestion: 'Glömt lösenordet?',
        forgotPasswordUsernameInstructions: 'Ange ditt användarnamn nedan. Instruktioner om hur du återställer ditt lösenord kommer att skickas till e-postadressen kopplad till ditt användarnamn.',
        generate: 'Generera',
        generateNewAPIKey: 'Generera ny API-nyckel',
        generatingNewAPIKeyWillInvalidate: 'Att generera en ny API-nyckel kommer <1>ogiltigförklara</1> föregående nyckel. Är du säker på att du vill fortsätta?',
        lockUntil: 'Lås tills',
        logBackIn: 'Logga in igen',
        loggedIn: 'För att logga in med en annan användare, bör du <0>logga ut</0> först.',
        loggedInChangePassword: 'För att ändra ditt lösenord, gå till ditt <0>konto</0> och redigera ditt lösenord där.',
        loggedOutInactivity: 'Du har blivit utloggad på grund av inaktivitet.',
        loggedOutSuccessfully: 'Du har loggats ut',
        loggingOut: 'Loggar ut...',
        login: 'Logga in',
        loginAttempts: 'Inloggningsförsök',
        loginUser: 'Logga in användare',
        loginWithAnotherUser: 'För att logga in med en annan användare, bör du <0>logga ut</0> först.',
        logOut: 'Logga ut',
        logout: 'Logga ut',
        logoutSuccessful: 'Utloggningen lyckades',
        logoutUser: 'Logga ut användare',
        newAccountCreated: 'Ett nytt konto har precis skapats som du kan komma åt <a href="{{serverURL}}">{{serverURL}}</a> Klicka på följande länk eller klistra in webbadressen nedan i din webbläsare för att verifiera din e-post: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Efter att ha verifierat din e-post kommer du att kunna logga in.',
        newAPIKeyGenerated: 'Ny API-nyckel genererad',
        newPassword: 'Nytt lösenord',
        passed: 'Autentisering godkänd',
        passwordResetSuccessfully: 'Lösenordet har återställts',
        resetPassword: 'Återställ lösenord',
        resetPasswordExpiration: 'Utgångstid för återställning av lösenord',
        resetPasswordToken: 'Återställningstoken för lösenord',
        resetYourPassword: 'Återställ ditt lösenord',
        stayLoggedIn: 'Förbli inloggad',
        successfullyRegisteredFirstUser: 'Registrerade den första användaren',
        successfullyUnlocked: 'Upplåst',
        tokenRefreshSuccessful: 'Tokenuppdatering lyckades',
        unableToVerify: 'Det går inte att verifiera',
        username: 'Användarnamn',
        usernameNotValid: 'Det angivna användarnamnet är inte giltigt',
        verified: 'Verifierad',
        verifiedSuccessfully: 'Verifierad',
        verify: 'Verifiera',
        verifyUser: 'Verifiera användare',
        verifyYourEmail: 'Verifiera din e-post',
        youAreInactive: 'Du har inte varit aktiv på ett tag och kommer inom kort att automatiskt loggas ut för din egen säkerhet. Vill du forsätta att vara inloggad?',
        youAreReceivingResetPassword: 'Du får detta för att du (eller någon annan) har begärt återställning av lösenordet för ditt konto. Klicka på följande länk eller klistra in den i din webbläsare för att slutföra processen:',
        youDidNotRequestPassword: 'Om du inte begärde detta, ignorera detta e-postmeddelande och ditt lösenord kommer att förbli oförändrat.'
    },
    error: {
        accountAlreadyActivated: 'Detta konto har redan aktiverats',
        autosaving: 'Det uppstod ett problem när det här dokumentet skulle sparas automatiskt.',
        correctInvalidFields: 'Vänligen korrigera ogiltiga fält',
        deletingFile: 'Det gick inte att ta bort filen',
        deletingTitle: 'Det uppstod ett fel vid borttagningen av {{title}}. Vänligen kontrollera din anslutning och försök igen.',
        documentNotFound: 'Dokumentet med ID {{id}} kunde inte hittas. Det kan ha raderats eller aldrig existerat, eller så kanske du inte har tillgång till det.',
        emailOrPasswordIncorrect: 'E-postadressen eller lösenordet som angivits är felaktigt.',
        followingFieldsInvalid_one: 'Följande fält är ogiltigt:',
        followingFieldsInvalid_other: 'Följande fält är ogiltiga:',
        incorrectCollection: 'Felaktig samling',
        insufficientClipboardPermissions: 'Åtkomst till urklipp nekades. Kontrollera dina behörigheter för urklipp.',
        invalidClipboardData: 'Ogiltiga urklippsdata.',
        invalidFileType: 'Ogiltig filtyp',
        invalidFileTypeValue: 'Ogiltig filtyp: {{value}}',
        invalidRequestArgs: 'Ogiltiga argument har skickats i begäran: {{args}}',
        loadingDocument: 'Det gick inte att läsa in dokumentet med ID {{id}}.',
        localesNotSaved_one: 'Följande språk kunde inte sparas:',
        localesNotSaved_other: 'Följande språk kunde inte sparas:',
        logoutFailed: 'Utloggning misslyckades',
        missingEmail: 'E-postadress saknas.',
        missingIDOfDocument: 'Saknar ID för dokumentet som ska uppdateras.',
        missingIDOfVersion: 'ID för versionen saknas.',
        missingRequiredData: 'Obligatorisk data saknas.',
        noFilesUploaded: 'Inga filer laddades upp.',
        noMatchedField: 'Inget matchande fält hittades för "{{label}}"',
        notAllowedToAccessPage: 'Du får inte komma åt den här sidan.',
        notAllowedToPerformAction: 'Du får inte utföra denna åtgärd.',
        notFound: 'Den begärda resursen hittades inte.',
        noUser: 'Ingen Användare',
        previewing: 'Det uppstod ett problem när det här dokumentet skulle förhandsgranskas.',
        problemUploadingFile: 'Det uppstod ett problem när filen laddades upp.',
        restoringTitle: 'Det uppstod ett fel vid återställning av {{title}}. Vänligen kontrollera din anslutning och försök igen.',
        tokenInvalidOrExpired: 'Token är antingen ogiltig eller har löpt ut.',
        tokenNotProvided: 'Token inte tillhandahållet.',
        unableToCopy: 'Kan inte kopiera.',
        unableToDeleteCount: 'Det gick inte att ta bort {{count}} av {{total}} {{label}}.',
        unableToReindexCollection: 'Fel vid omindexering av samlingen {{collection}}. Operationen avbröts.',
        unableToUpdateCount: 'Det gick inte att uppdatera {{count}} av {{total}} {{label}}.',
        unauthorized: 'Obehörig, du måste vara inloggad för att göra denna begäran.',
        unauthorizedAdmin: 'Obehörig, denna användare har inte åtkomst till adminpanelen.',
        unknown: 'Ett okänt fel har uppstått.',
        unPublishingDocument: 'Det uppstod ett problem när det här dokumentet skulle avpubliceras.',
        unspecific: 'Ett fel har uppstått.',
        unverifiedEmail: 'Vänligen verifiera din e-post innan du loggar in.',
        userEmailAlreadyRegistered: 'En användare med den angivna e-postadressen är redan registrerad.',
        userLocked: 'Den här användaren är låst på grund av för många misslyckade inloggningsförsök.',
        usernameAlreadyRegistered: 'En användare med det angivna användarnamnet är redan registrerad.',
        usernameOrPasswordIncorrect: 'Användarnamnet eller lösenordet som angavs är felaktigt.',
        valueMustBeUnique: 'Värdet måste vara unikt',
        verificationTokenInvalid: 'Verifieringstoken är ogiltigt'
    },
    fields: {
        addLabel: 'Lägg till {{label}}',
        addLink: 'Lägg till länk',
        addNew: 'Lägg till ny',
        addNewLabel: 'Lägg till ny {{label}}',
        addRelationship: 'Lägg till relation',
        addUpload: 'Lägg till uppladdning',
        block: 'block',
        blocks: 'block',
        blockType: 'Blocktyp',
        chooseBetweenCustomTextOrDocument: 'Välj mellan att ange en anpassad text-URL eller länka till ett annat dokument.',
        chooseDocumentToLink: 'Välj ett dokument att länka till',
        chooseFromExisting: 'Välj bland befintliga',
        chooseLabel: 'Välj {{label}}',
        collapseAll: 'Fäll ihop alla',
        customURL: 'Anpassad URL',
        editLabelData: 'Redigera {{label}} data',
        editLink: 'Redigera länk',
        editRelationship: 'Redigera relation',
        enterURL: 'Ange en URL',
        internalLink: 'Intern länk',
        itemsAndMore: '{{items}} och {{count}} mer',
        labelRelationship: '{{label}}-relation',
        latitude: 'Latitud',
        linkedTo: 'Länkad till <0>{{label}}</0>',
        linkType: 'Länktyp',
        longitude: 'Longitud',
        newLabel: 'Ny {{label}}',
        openInNewTab: 'Öppna i ny flik',
        passwordsDoNotMatch: 'Lösenorden matchar inte.',
        relatedDocument: 'Relaterat dokument',
        relationTo: 'Relation till',
        removeRelationship: 'Ta bort relation',
        removeUpload: 'Ta bort uppladdning',
        saveChanges: 'Spara ändringar',
        searchForBlock: 'Sök efter ett block',
        selectExistingLabel: 'Välj befintlig {{label}}',
        selectFieldsToEdit: 'Välj fält att redigera',
        showAll: 'Visa alla',
        swapRelationship: 'Byt förhållande',
        swapUpload: 'Byt uppladdning',
        textToDisplay: 'Text att visa',
        toggleBlock: 'Växla block',
        uploadNewLabel: 'Ladda upp ny {{label}}'
    },
    folder: {
        browseByFolder: 'Bläddra efter mapp',
        byFolder: 'Efter mapp',
        deleteFolder: 'Ta bort mapp',
        folderName: 'Mappnamn',
        folders: 'Mappar',
        folderTypeDescription: 'Välj vilken typ av samlingsdokument som ska tillåtas i denna mapp.',
        itemHasBeenMoved: '{{title}} har flyttats till {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} har flyttats till rotmappen',
        itemsMovedToFolder: '{{title}} flyttad till {{folderName}}',
        itemsMovedToRoot: '{{titel}} flyttades till rotmappen',
        moveFolder: 'Flytta mapp',
        moveItemsToFolderConfirmation: 'Du är på väg att flytta <1>{{count}} {{label}}</1> till <2>{{toFolder}}</2>. Är du säker?',
        moveItemsToRootConfirmation: 'Du är på väg att flytta <1>{{count}} {{label}}</1> till root-mappen. Är du säker?',
        moveItemToFolderConfirmation: 'Du håller på att flytta <1>{{title}}</1> till <2>{{toFolder}}</2>. Är du säker?',
        moveItemToRootConfirmation: 'Du är på väg att flytta <1>{{title}}</1> till rotmappen. Är du säker?',
        movingFromFolder: 'Flyttar {{title}} från {{fromFolder}}',
        newFolder: 'Ny mapp',
        noFolder: 'Ingen mapp',
        renameFolder: 'Byt namn på mapp',
        searchByNameInFolder: 'Sök efter namn i {{folderName}}',
        selectFolderForItem: 'Välj mapp för {{title}}'
    },
    general: {
        name: 'Namn',
        aboutToDelete: 'Du är på väg att ta bort {{label}} <1>{{title}}</1>. Är du säker?',
        aboutToDeleteCount_many: 'Du är på väg att ta bort {{count}} {{label}}',
        aboutToDeleteCount_one: 'Du är på väg att ta bort {{count}} {{label}}',
        aboutToDeleteCount_other: 'Du är på väg att ta bort {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Du är på väg att permanent radera {{label}} <1>{{title}}</1>. Är du säker?',
        aboutToPermanentlyDeleteTrash: 'Du är på väg att permanent radera <0>{{count}}</0> <1>{{label}}</1> från papperskorgen. Är du säker?',
        aboutToRestore: 'Du är på väg att återställa {{label}} <1>{{title}}</1>. Är du säker?',
        aboutToRestoreAsDraft: 'Du är på väg att återställa {{label}} <1>{{title}}</1> som ett utkast. Är du säker?',
        aboutToRestoreAsDraftCount: 'Du är på väg att återställa {{count}} {{label}} som utkast',
        aboutToRestoreCount: 'Du är på väg att återställa {{count}} {{label}}',
        aboutToTrash: 'Du håller på att flytta {{label}} <1>{{title}}</1> till papperskorgen. Är du säker?',
        aboutToTrashCount: 'Du håller på att flytta {{count}} {{label}} till papperskorgen',
        addBelow: 'Lägg till nedanför',
        addFilter: 'Lägg till filter',
        adminTheme: 'Adminutseende',
        all: 'Alla',
        allCollections: 'Alla samlingar',
        allLocales: 'Alla språk',
        and: 'Och',
        anotherUser: 'En annan användare',
        anotherUserTakenOver: 'En annan användare har tagit över redigeringen av detta dokument.',
        applyChanges: 'Verkställ ändringar',
        ascending: 'Stigande',
        automatic: 'Automatiskt',
        backToDashboard: 'Tillbaka till översikten',
        cancel: 'Avbryt',
        changesNotSaved: 'Dina ändringar har inte sparats. Om du lämnar nu kommer du att förlora dina ändringar.',
        clear: 'Rensa',
        clearAll: 'Rensa alla',
        close: 'Stäng',
        collapse: 'Fäll ihop',
        collections: 'Samlingar',
        columns: 'Kolumner',
        columnToSort: 'Kolumn att sortera',
        confirm: 'Bekräfta',
        confirmCopy: 'Bekräfta kopia',
        confirmDeletion: 'Bekräfta radering',
        confirmDuplication: 'Bekräfta dubblering',
        confirmMove: 'Bekräfta flytt',
        confirmReindex: 'Omindexera alla {{collections}}?',
        confirmReindexAll: 'Omindexera alla samlingar?',
        confirmReindexDescription: 'Detta kommer att ta bort befintliga index och omindexera dokumenten i {{collections}}-samlingarna.',
        confirmReindexDescriptionAll: 'Detta kommer att ta bort befintliga index och omindexera dokumenten i alla samlingar.',
        confirmRestoration: 'Bekräfta återställning',
        copied: 'Kopierad',
        copy: 'Kopiera',
        copyField: 'Kopiera fält',
        copying: 'Kopierar...',
        copyRow: 'Kopiera rad',
        copyWarning: 'Du håller på att skriva över {{to}} med {{from}} för {{label}} {{title}}. Är du säker?',
        create: 'Skapa',
        created: 'Skapat',
        createdAt: 'Skapat',
        createNew: 'Skapa ny',
        createNewLabel: 'Skapa ny {{label}}',
        creating: 'Skapar...',
        creatingNewLabel: 'Skapar ny {{label}}',
        currentlyEditing: 'redigerar för närvarande detta dokument. Om du tar över kommer de att blockeras från att fortsätta redigera och kan också förlora osparade ändringar.',
        custom: 'Anpassad',
        dark: 'Mörkt',
        dashboard: 'Översikt',
        delete: 'Ta bort',
        deleted: 'Raderad',
        deletedAt: 'Raderad Vid',
        deletedCountSuccessfully: 'Raderade {{count}} {{label}}',
        deletedSuccessfully: 'Borttaget',
        deletePermanently: 'Hoppa över papperskorgen och radera permanent',
        deleting: 'Tar bort...',
        depth: 'Djup',
        descending: 'Fallande',
        deselectAllRows: 'Avmarkera alla rader',
        document: 'Dokument',
        documentIsTrashed: 'Det här {{label}} har slagits i spill och är skrivskyddad.',
        documentLocked: 'Dokument låst',
        documents: 'Dokument',
        duplicate: 'Duplicera',
        duplicateWithoutSaving: 'Duplicera utan att spara ändringar',
        edit: 'Redigera',
        editAll: 'Redigera alla',
        editedSince: 'Redigerad sedan',
        editing: 'Redigerar',
        editingLabel_many: 'Redigerar {{count}} {{label}}',
        editingLabel_one: 'Redigerar {{count}} {{label}}',
        editingLabel_other: 'Redigerar {{count}} {{label}}',
        editingTakenOver: 'Redigering övertagen',
        editLabel: 'Redigera {{label}}',
        email: 'E-post',
        emailAddress: 'E-postadress',
        emptyTrash: 'Töm papperskorgen',
        emptyTrashLabel: 'Töm {{label}} papperskorgen',
        enterAValue: 'Ange ett värde',
        error: 'Fel',
        errors: 'Fel',
        exitLivePreview: 'Avsluta förhandsgranskning',
        export: 'Exportera',
        fallbackToDefaultLocale: 'Återgå till standardspråk',
        false: 'Falskt',
        filter: 'Filter',
        filters: 'Filter',
        filterWhere: 'Filtrera {{label}} där',
        globals: 'Globala',
        goBack: 'Gå tillbaka',
        groupByLabel: 'Gruppera efter {{label}}',
        import: 'Importera',
        isEditing: 'redigerar',
        item: 'artikel',
        items: 'artiklar',
        language: 'Språk',
        lastModified: 'Senast ändrad',
        leaveAnyway: 'Lämna ändå',
        leaveWithoutSaving: 'Lämna utan att spara',
        light: 'Ljust',
        livePreview: 'Förhandsgranskning',
        loading: 'Laddar...',
        locale: 'Språk',
        locales: 'Språk',
        menu: 'Meny',
        moreOptions: 'Fler alternativ',
        move: 'Flytta',
        moveConfirm: 'Du håller på att flytta {{count}} {{label}} till <1>{{destination}}</1>. Är du säker?',
        moveCount: 'Flytta {{count}} {{label}}',
        moveDown: 'Flytta ner',
        moveUp: 'Flytta upp',
        moving: 'Flyttar',
        movingCount: 'Flyttar {{count}} {{label}}',
        newPassword: 'Nytt lösenord',
        next: 'Nästa',
        no: 'Nej',
        noDateSelected: 'Inget datum valt',
        noFiltersSet: 'Inga filter inställda',
        noLabel: '<Ingen {{label}}>',
        none: 'Ingen',
        noOptions: 'Inga alternativ',
        noResults: 'Inga {{label}} hittades. Antingen finns inga {{label}} ännu eller så matchar inga filtren du har angett ovan.',
        notFound: 'Hittades inte',
        nothingFound: 'Inget hittades',
        noTrashResults: 'Inget {{label}} i papperskorgen.',
        noUpcomingEventsScheduled: 'Inga kommande händelser är planerade.',
        noValue: 'Inget värde',
        of: 'av',
        only: 'Endast',
        open: 'Öppna',
        or: 'Eller',
        order: 'Ordning',
        overwriteExistingData: 'Skriv över befintlig fältdata',
        pageNotFound: 'Sidan hittas inte',
        password: 'Lösenord',
        pasteField: 'Klistra in fält',
        pasteRow: 'Klistra in rad',
        payloadSettings: 'Systeminställningar',
        permanentlyDelete: 'Radera Permanent',
        permanentlyDeletedCountSuccessfully: '{{count}} {{label}} har raderats permanent.',
        perPage: 'Per Sida: {{limit}}',
        previous: 'Föregående',
        reindex: 'Omindexera',
        reindexingAll: 'Omindexerar alla {{collections}}...',
        remove: 'Ta bort',
        rename: 'Byt namn',
        reset: 'Återställ',
        resetPreferences: 'Återställ preferenser',
        resetPreferencesDescription: 'Detta kommer att återställa alla dina preferenser till standardinställningarna.',
        resettingPreferences: 'Återställer preferenser...',
        restore: 'Återställ',
        restoreAsPublished: 'Återställ som publicerad version',
        restoredCountSuccessfully: 'Återställde {{count}} {{label}} framgångsrikt.',
        restoring: 'Respektera innebörden av den ursprungliga texten inom kontexten av Payload. Här är en lista över gemensamma Payload-termer som bär väldigt specifika betydelser:\n    - Samling: En samling är en grupp dokument som delar en gemensam struktur och syfte. Samlingar används för att organisera och hantera innehåll i Payload.\n    - Fält: Ett fält är en specifik data inom ett dokument i en samling. Fält definierar strukturen och typen av data som kan lagras i ett dokument.\n    - Dokument: Ett dokument är en',
        row: 'Rad',
        rows: 'Rader',
        save: 'Spara',
        saving: 'Sparar...',
        schedulePublishFor: 'Schemalägg publicering för {{title}}',
        searchBy: 'Sök efter {{label}}',
        select: 'Välj',
        selectAll: 'Välj alla {{count}} {{label}}',
        selectAllRows: 'Välj alla rader',
        selectedCount: '{{count}} {{label}} har valts',
        selectLabel: 'Välj {{label}}',
        selectValue: 'Välj ett värde',
        showAllLabel: 'Visa alla {{label}}',
        sorryNotFound: 'Tyvärr, det finns inget som motsvarar din begäran.',
        sort: 'Sortera',
        sortByLabelDirection: 'Sortera efter {{label}} {{direction}}',
        stayOnThisPage: 'Stanna på denna sida',
        submissionSuccessful: 'Skickat',
        submit: 'Skicka',
        submitting: 'Skickar...',
        success: 'Lyckades',
        successfullyCreated: '{{label}} skapades',
        successfullyDuplicated: '{{label}} duplicerades',
        successfullyReindexed: 'Lyckades omindexera {{count}} av {{total}} dokument från {{collections}} samlingar.',
        takeOver: 'Ta över',
        thisLanguage: 'Svenska',
        time: 'Tid',
        timezone: 'Tidszon',
        titleDeleted: '{{label}} "{{title}}" togs bort',
        titleRestored: '{{label}} "{{title}}" har framgångsrikt återställts.',
        titleTrashed: '{{label}} "{{title}}" flyttades till papperskorgen.',
        trash: 'Skräp',
        trashedCountSuccessfully: '{{count}} {{label}} flyttades till papperskorgen.',
        true: 'Sann',
        unauthorized: 'Obehörig',
        unsavedChanges: 'Du har osparade ändringar. Spara innan du fortsätter.',
        unsavedChangesDuplicate: 'Du har osparade ändringar. Vill du fortsätta att duplicera?',
        untitled: 'Namnlös',
        upcomingEvents: 'Kommande händelser',
        updatedAt: 'Uppdaterat',
        updatedCountSuccessfully: 'Uppdaterade {{count}} {{label}}',
        updatedLabelSuccessfully: 'Uppdaterade {{label}}',
        updatedSuccessfully: 'Uppdaterades',
        updateForEveryone: 'Uppdatera för alla',
        updating: 'Uppdaterar...',
        uploading: 'Laddar upp...',
        uploadingBulk: 'Laddar upp {{current}} av {{total}}',
        user: 'Användare',
        username: 'Användarnamn',
        users: 'Användare',
        value: 'Värde',
        viewing: 'Visar',
        viewReadOnly: 'Visa som skrivskyddad',
        welcome: 'Välkommen',
        yes: 'Ja'
    },
    localization: {
        cannotCopySameLocale: 'Kan inte kopiera till samma språk',
        copyFrom: 'Kopiera från',
        copyFromTo: 'Kopierar från {{from}} till {{to}}',
        copyTo: 'Kopiera till',
        copyToLocale: 'Kopiera till språk',
        localeToPublish: 'Publicera språk',
        selectLocaleToCopy: 'Välj språk att kopiera'
    },
    operators: {
        contains: 'innehåller',
        equals: 'lika med',
        exists: 'finns',
        intersects: 'korsar',
        isGreaterThan: 'är större än',
        isGreaterThanOrEqualTo: 'är större än eller lika med',
        isIn: 'finns i',
        isLessThan: 'är mindre än',
        isLessThanOrEqualTo: 'är mindre än eller lika med',
        isLike: 'matchar med',
        isNotEqualTo: 'är inte lika med',
        isNotIn: 'finns inte i',
        isNotLike: 'matchar inte med',
        near: 'nära',
        within: 'inom'
    },
    upload: {
        addFile: 'Lägg till fil',
        addFiles: 'Lägg till filer',
        bulkUpload: 'Massuppladdning',
        crop: 'Beskär',
        cropToolDescription: 'Dra i hörnen på det valda området, rita ett nytt område eller justera värdena nedan.',
        download: 'Ladda ner',
        dragAndDrop: 'Dra och släpp en fil',
        dragAndDropHere: 'eller dra och släpp en fil här',
        editImage: 'Redigera bild',
        fileName: 'Filnamn',
        fileSize: 'Filstorlek',
        filesToUpload: 'Filer att ladda upp',
        fileToUpload: 'Fil att ladda upp',
        focalPoint: 'Fokuspunkt',
        focalPointDescription: 'Dra fokuspunkten direkt på förhandsgranskningen eller justera värdena nedan.',
        height: 'Höjd',
        lessInfo: 'Mindre info',
        moreInfo: 'Mer info',
        noFile: 'Ingen fil',
        pasteURL: 'Klistra in URL',
        previewSizes: 'Förhandsgranska storlekar',
        selectCollectionToBrowse: 'Välj en samling att bläddra i',
        selectFile: 'Välj en fil',
        setCropArea: 'Ange beskärning',
        setFocalPoint: 'Ställ in fokuspunkt',
        sizes: 'Storlekar',
        sizesFor: 'Storlekar för {{label}}',
        width: 'Bredd'
    },
    validation: {
        emailAddress: 'Vänligen ange en giltig e-postadress.',
        enterNumber: 'Vänligen skriv in ett giltigt nummer.',
        fieldHasNo: 'Detta fält har ingen {{label}}',
        greaterThanMax: '{{value}} är större än den maximalt tillåtna {{label}} av {{max}}.',
        invalidInput: 'Det här fältet har en ogiltig inmatning.',
        invalidSelection: 'Det här fältet har ett ogiltigt urval.',
        invalidSelections: 'Det här fältet har följande ogiltiga val:',
        lessThanMin: '{{value}} är mindre än den minst tillåtna {{label}} av {{min}}.',
        limitReached: 'Gränsen nådd, endast {{max}} objekt kan läggas till.',
        longerThanMin: 'Detta värde måste vara längre än minimilängden på {{minLength}} tecken.',
        notValidDate: '"{{value}}" är inte ett giltigt datum.',
        required: 'Detta fält är obligatoriskt.',
        requiresAtLeast: 'Detta fält kräver minst {{count}} {{label}}.',
        requiresNoMoreThan: 'Detta fält kräver inte mer än {{count}} {{label}}.',
        requiresTwoNumbers: 'Detta fält kräver två nummer.',
        shorterThanMax: 'Detta värde måste vara kortare än maxlängden på {{maxLength}} tecken.',
        timezoneRequired: 'En tidszon krävs.',
        trueOrFalse: 'Detta fält kan bara vara lika med sant eller falskt.',
        username: 'Var god ange ett giltigt användarnamn. Kan innehålla bokstäver, siffror, bindestreck, punkter och understreck.',
        validUploadID: 'Det här fältet är inte ett giltigt uppladdnings-ID'
    },
    version: {
        type: 'Typ',
        aboutToPublishSelection: 'Du kommer publicera alla {{label}} i urvalet. Är du säker?',
        aboutToRestore: 'Du kommer återställa detta {{label}} dokumentet till det tillståndet som det var den {{versionDate}}.',
        aboutToRestoreGlobal: 'Du kommer återställa det globala {{label}} till det tillståndet som det var den {{versionDate}}.',
        aboutToRevertToPublished: 'Du kommer återställa det här dokumentets ändringar till dess publicerade tillstånd. Är du säker?',
        aboutToUnpublish: 'Du kommer avpublicera detta dokument. Är du säker?',
        aboutToUnpublishSelection: 'Du är på väg att avpublicera alla {{label}} i urvalet. Är du säker?',
        autosave: 'Spara automatiskt',
        autosavedSuccessfully: 'Autosparades',
        autosavedVersion: 'Autosparad version',
        changed: 'Ändrad',
        changedFieldsCount_one: '{{count}} ändrat fält',
        changedFieldsCount_other: '{{count}} ändrade fält',
        compareVersion: 'Jämför version med:',
        compareVersions: 'Jämför versioner',
        comparingAgainst: 'Jämför mot',
        confirmPublish: 'Bekräfta publicering',
        confirmRevertToSaved: 'Bekräfta återgång till sparad version',
        confirmUnpublish: 'Bekräfta avpublicering',
        confirmVersionRestoration: 'Bekräfta versionsåterställning',
        currentDocumentStatus: 'Nuvarande {{docStatus}} dokument',
        currentDraft: 'Nuvarande utkast',
        currentlyPublished: 'För närvarande publicerad',
        currentlyViewing: 'Visar för tillfället',
        currentPublishedVersion: 'Aktuell publicerad version',
        draft: 'Utkast',
        draftSavedSuccessfully: 'Utkastet sparades',
        lastSavedAgo: 'Senast sparad för {{distance}} sedan',
        modifiedOnly: 'Endast modifierad',
        moreVersions: 'Fler versioner...',
        noFurtherVersionsFound: 'Inga fler versioner hittades',
        noRowsFound: 'Inga {{label}} hittades',
        noRowsSelected: 'Inget {{etikett}} valt',
        preview: 'Förhandsgranska',
        previouslyDraft: 'Tidigare ett Utkast',
        previouslyPublished: 'Tidigare publicerad',
        previousVersion: 'Föregående version',
        problemRestoringVersion: 'Det uppstod ett problem när den här versionen skulle återställas',
        publish: 'Publicera',
        publishAllLocales: 'Publicera alla språk',
        publishChanges: 'Publicera ändringar',
        published: 'Publicerad',
        publishIn: 'Publicera i {{locale}}',
        publishing: 'Publicerar...',
        restoreAsDraft: 'Återställ som utkast',
        restoredSuccessfully: 'Återställd',
        restoreThisVersion: 'Återställ den här versionen',
        restoring: 'Återställer...',
        reverting: 'Återställer...',
        revertToPublished: 'Återgå till publicerad',
        saveDraft: 'Spara Utkast',
        scheduledSuccessfully: 'Schemalagd',
        schedulePublish: 'Schemalägg publicering',
        selectLocales: 'Välj språk att visa',
        selectVersionToCompare: 'Välj en version att jämföra',
        showingVersionsFor: 'Visar versioner för:',
        showLocales: 'Visa språk:',
        specificVersion: 'Specifik version',
        status: 'Status',
        unpublish: 'Avpublicera',
        unpublishing: 'Avpublicerar...',
        version: 'Version',
        versionAgo: '{{distance}} sedan',
        versionCount_many: '{{count}} versioner hittades',
        versionCount_none: 'Inga versioner hittades',
        versionCount_one: '{{count}} version hittades',
        versionCount_other: '{{count}} versioner hittades',
        versionCreatedOn: '{{version}} skapad den:',
        versionID: 'Versions-ID',
        versions: 'Versioner',
        viewingVersion: 'Visar version för {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Visa version för den globala {{entityLabel}}',
        viewingVersions: 'Visar versioner för {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Visa versioner för den globala {{entityLabel}}'
    }
};
export const sv = {
    dateFNSKey: 'sv',
    translations: svTranslations
};

//# sourceMappingURL=sv.js.map