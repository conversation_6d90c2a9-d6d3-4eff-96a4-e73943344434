{"version": 3, "sources": ["../../src/languages/lt.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const ltTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Paskyra',\n    accountOfCurrentUser: 'Dabar<PERSON><PERSON> vartotojo paskyra',\n    accountVerified: '<PERSON><PERSON><PERSON><PERSON> sėkmingai patvirtinta.',\n    alreadyActivated: 'Jau aktyvuota',\n    alreadyLoggedIn: 'Jau prisijungęs',\n    apiKey: 'API raktas',\n    authenticated: 'Autentifikuotas',\n    backToLogin: 'Grįžti į prisijungimą',\n    beginCreateFirstUser: 'Prad<PERSON>kite, sukurdami savo pirmąjį vartotoją.',\n    changePassword: 'Keisti slaptažodį',\n    checkYourEmailForPasswordReset:\n      'Jei šis el. pašto adresas yra susijęs su paskyra, netrukus gausite instrukcijas, kaip atstatyti savo slaptažodį. Jei laiško nesimate savo gautiesiųjų d<PERSON>žutėje, patikrinkite savo šlamšto ar nereikalingų laiškų aplanką.',\n    confirmGeneration: 'Patvirtinkite generavimą',\n    confirmPassword: 'Patvirtinkite slaptažodį',\n    createFirstUser: 'Sukurkite pirmąjį vartotoją',\n    emailNotValid: 'Pateiktas el. paštas negalioja',\n    emailOrUsername: 'El. paštas arba vartotojo vardas',\n    emailSent: 'El. paštas išsiųstas',\n    emailVerified: 'El. paštas sėkmingai patvirtintas.',\n    enableAPIKey: 'Įgalinti API raktą',\n    failedToUnlock: 'Nepavyko atrakinti',\n    forceUnlock: 'Priverstinis atrakinimas',\n    forgotPassword: 'Pamiršote slaptažodį',\n    forgotPasswordEmailInstructions:\n      'Prašome įvesti savo el. paštą žemiau. Gausite el. laišką su instrukcijomis, kaip atstatyti savo slaptažodį.',\n    forgotPasswordQuestion: 'Pamiršote slaptažodį?',\n    forgotPasswordUsernameInstructions:\n      'Prašome įvesti savo vartotojo vardą žemiau. Instrukcijos, kaip atstatyti slaptažodį, bus išsiųstos į el. pašto adresą, susietą su jūsų vartotojo vardu.',\n    generate: 'Generuoti',\n    generateNewAPIKey: 'Sukurkite naują API raktą',\n    generatingNewAPIKeyWillInvalidate:\n      'Sugeneruojant naują API raktą, bus <1>anuliuotas</1> ankstesnis raktas. Ar tikrai norite tęsti?',\n    lockUntil: 'Užrakinti iki',\n    logBackIn: 'Prisijunkite vėl',\n    loggedIn: 'Norėdami prisijungti kitu vartotoju, turėtumėte iš pradžių <0>atsijungti</0>.',\n    loggedInChangePassword:\n      'Norėdami pakeisti slaptažodį, eikite į savo <0>paskyrą</0> ir ten redaguokite savo slaptažodį.',\n    loggedOutInactivity: 'Jūs buvote atjungtas dėl neveiklumo.',\n    loggedOutSuccessfully: 'Sėkmingai atsijungėte.',\n    loggingOut: 'Atsijungimas...',\n    login: 'Prisijungti',\n    loginAttempts: 'Prisijungimo bandymai',\n    loginUser: 'Prisijungti vartotojui',\n    loginWithAnotherUser:\n      'Norėdami prisijungti su kitu vartotoju, turėtumėte iš pradžių <0>atsijungti</0>.',\n    logOut: 'Atsijungti',\n    logout: 'Atsijungti',\n    logoutSuccessful: 'Sėkmingai atsijungta.',\n    logoutUser: 'Atjungti vartotoją',\n    newAccountCreated:\n      'Jums ką tik buvo sukurta nauja paskyra, kad galėtumėte prisijungti prie <a href=\"{{serverURL}}\">{{serverURL}}</a> Prašome paspausti ant šios nuorodos arba įklijuoti apačioje esantį URL į savo naršyklę, kad patvirtintumėte savo el. pašto adresą: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Patvirtinę savo el. pašto adresą, sėkmingai galėsite prisijungti.',\n    newAPIKeyGenerated: 'Sugeneruotas naujas API raktas.',\n    newPassword: 'Naujas slaptažodis',\n    passed: 'Autentifikacija sėkminga',\n    passwordResetSuccessfully: 'Slaptažodis sėkmingai atnaujintas.',\n    resetPassword: 'Atstatyti slaptažodį',\n    resetPasswordExpiration: 'Atstatyti slaptažodžio galiojimo laiką',\n    resetPasswordToken: 'Slaptažodžio atkūrimo žetonas',\n    resetYourPassword: 'Atstatykite savo slaptažodį',\n    stayLoggedIn: 'Likite prisijungę',\n    successfullyRegisteredFirstUser: 'Sėkmingai užregistruotas pirmas vartotojas.',\n    successfullyUnlocked: 'Sėkmingai atrakinta',\n    tokenRefreshSuccessful: 'Žetonų atnaujinimas sėkmingas.',\n    unableToVerify: 'Negalima patikrinti',\n    username: 'Vartotojo vardas',\n    usernameNotValid: 'Pateiktas vartotojo vardas yra netinkamas',\n    verified: 'Patvirtinta',\n    verifiedSuccessfully: 'Sėkmingai patvirtinta',\n    verify: 'Patikrinkite',\n    verifyUser: 'Patvirtinti vartotoją',\n    verifyYourEmail: 'Patvirtinkite savo el. paštą',\n    youAreInactive:\n      'Jūs kurį laiką neveikėte ir netrukus būsite automatiškai atjungtas dėl jūsų pačių saugumo. Ar norėtumėte likti prisijungęs?',\n    youAreReceivingResetPassword:\n      'Gavote šį pranešimą, nes jūs (arba kažkas kitas) paprašėte atstatyti slaptažodį savo paskyrai. Norėdami užbaigti procesą, spustelėkite šią nuorodą arba įklijuokite ją į savo naršyklę:',\n    youDidNotRequestPassword:\n      'Jei to neprašėte, prašome ignoruoti šį el. laišką ir jūsų slaptažodis išliks nepakeistas.',\n  },\n  error: {\n    accountAlreadyActivated: 'Ši paskyra jau aktyvuota.',\n    autosaving: 'Šio dokumento automatinio išsaugojimo metu kilo problema.',\n    correctInvalidFields: 'Prašome ištaisyti neteisingus laukus.',\n    deletingFile: 'Įvyko klaida trinant failą.',\n    deletingTitle:\n      'Įvyko klaida bandant ištrinti {{title}}. Patikrinkite savo ryšį ir bandykite dar kartą.',\n    documentNotFound:\n      'Dokumentas su ID {{id}} nerastas. Gali būti, kad jis buvo ištrintas arba niekada neegzistavo, arba jūs neturite prieigos prie jo.',\n    emailOrPasswordIncorrect: 'Pateiktas el. pašto adresas arba slaptažodis yra neteisingi.',\n    followingFieldsInvalid_one: 'Šis laukas yra netinkamas:',\n    followingFieldsInvalid_other: 'Šie laukai yra neteisingi:',\n    incorrectCollection: 'Neteisinga kolekcija',\n    insufficientClipboardPermissions:\n      'Prieiga prie iškarpinės atmesta. Patikrinkite savo iškarpinės teises.',\n    invalidClipboardData: 'Neteisingi iškarpinės duomenys.',\n    invalidFileType: 'Netinkamas failo tipas',\n    invalidFileTypeValue: 'Neteisingas failo tipas: {{value}}',\n    invalidRequestArgs: 'Netinkami argumentai perduoti užklausoje: {{args}}',\n    loadingDocument: 'Įvyko klaida įkeliant dokumentą, kurio ID yra {{id}}.',\n    localesNotSaved_one: 'Negalima išsaugoti šios lokalės:',\n    localesNotSaved_other: 'Šios lokalės negalėjo būti išsaugotos:',\n    logoutFailed: 'Atsijungimas nepavyko.',\n    missingEmail: 'Trūksta el. pašto.',\n    missingIDOfDocument: 'Trūksta dokumento, kurį reikia atnaujinti, ID.',\n    missingIDOfVersion: 'Trūksta versijos ID.',\n    missingRequiredData: 'Trūksta reikalingų duomenų.',\n    noFilesUploaded: 'Neįkelta jokių failų.',\n    noMatchedField: 'Nerasta atitinkamo lauko „{{label}}“',\n    notAllowedToAccessPage: 'Jums neleidžiama prieiti prie šio puslapio.',\n    notAllowedToPerformAction: 'Jums neleidžiama atlikti šio veiksmo.',\n    notFound: 'Pageidaujamas išteklius nerasta.',\n    noUser: 'Nėra vartotojo',\n    previewing: 'Šiam dokumentui peržiūrėti kilo problema.',\n    problemUploadingFile: 'Failo įkelti nepavyko dėl problemos.',\n    restoringTitle:\n      'Įvyko klaida atkuriant {{title}}. Prašome patikrinti savo ryšį ir bandyti dar kartą.',\n    tokenInvalidOrExpired: 'Žetonas yra neteisingas arba jo galiojimas pasibaigė.',\n    tokenNotProvided: 'Žetonas nesuteiktas.',\n    unableToCopy: 'Nepavyko nukopijuoti.',\n    unableToDeleteCount: 'Negalima ištrinti {{count}} iš {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Klaida perindeksuojant rinkinį {{collection}}. Operacija nutraukta.',\n    unableToUpdateCount: 'Nepavyko atnaujinti {{count}} iš {{total}} {{label}}.',\n    unauthorized: 'Neleistina, turite būti prisijungęs, kad galėtumėte teikti šį prašymą.',\n    unauthorizedAdmin:\n      'Neleidžiama, šis vartotojas neturi prieigos prie administratoriaus panelės.',\n    unknown: 'Įvyko nežinoma klaida.',\n    unPublishingDocument: 'Šio dokumento nepublikuojant kildavo problema.',\n    unspecific: 'Įvyko klaida.',\n    unverifiedEmail: 'Prieš prisijungdami patvirtinkite savo el. paštą.',\n    userEmailAlreadyRegistered: 'Vartotojas su nurodytu el. paštu jau yra užregistruotas.',\n    userLocked: 'Šis vartotojas užrakintas dėl per daug nepavykusių prisijungimo bandymų.',\n    usernameAlreadyRegistered: 'Vartotojas su nurodytu vartotojo vardu jau užregistruotas.',\n    usernameOrPasswordIncorrect: 'Pateiktas vartotojo vardas arba slaptažodis yra neteisingas.',\n    valueMustBeUnique: 'Vertė turi būti unikalu.',\n    verificationTokenInvalid: 'Patvirtinimo kodas yra negaliojantis.',\n  },\n  fields: {\n    addLabel: 'Pridėkite {{label}}',\n    addLink: 'Pridėti nuorodą',\n    addNew: 'Pridėti naują',\n    addNewLabel: 'Pridėti naują {{label}}',\n    addRelationship: 'Pridėti santykį',\n    addUpload: 'Pridėti Įkelti',\n    block: 'blokas',\n    blocks: 'blokai',\n    blockType: 'Blokas Tipas',\n    chooseBetweenCustomTextOrDocument:\n      'Pasirinkite tarp pasirinkimo įvesti tinkintą tekstą URL arba nuorodos į kitą dokumentą.',\n    chooseDocumentToLink: 'Pasirinkite dokumentą, prie kurio norite prisegti.',\n    chooseFromExisting: 'Pasirinkite iš esamų',\n    chooseLabel: 'Pasirinkite {{label}}',\n    collapseAll: 'Sutraukti viską',\n    customURL: 'Pasirinktinis URL',\n    editLabelData: 'Redaguoti {{label}} duomenis',\n    editLink: 'Redaguoti nuorodą',\n    editRelationship: 'Redaguoti santykius',\n    enterURL: 'Įveskite URL',\n    internalLink: 'Vidinis nuorodos',\n    itemsAndMore: '{{items}} ir dar {{count}}',\n    labelRelationship: '{{label}} Santykiai',\n    latitude: 'Platuma',\n    linkedTo: 'Susijęs su <0>{{label}}</0>',\n    linkType: 'Nuorodos tipas',\n    longitude: 'Ilgumažė',\n    newLabel: 'Naujas {{label}}',\n    openInNewTab: 'Atidaryti naujame skirtuke',\n    passwordsDoNotMatch: 'Slaptažodžiai nesutampa.',\n    relatedDocument: 'Susijęs dokumentas',\n    relationTo: 'Santykis su',\n    removeRelationship: 'Pašalinti ryšį',\n    removeUpload: 'Pašalinti įkėlimą',\n    saveChanges: 'Išsaugoti pakeitimus',\n    searchForBlock: 'Ieškokite bloko',\n    selectExistingLabel: 'Pasirinkite esamą {{label}}',\n    selectFieldsToEdit: 'Pasirinkite laukus, kuriuos norite redaguoti',\n    showAll: 'Rodyti viską',\n    swapRelationship: 'Apkeičiamas santykis',\n    swapUpload: 'Keitimo įkėlimas',\n    textToDisplay: 'Rodyti tekstą',\n    toggleBlock: 'Perjungti bloką',\n    uploadNewLabel: 'Įkelti naują {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Naršyti pagal aplanką',\n    byFolder: 'Pagal aplanką',\n    deleteFolder: 'Ištrinti aplanką',\n    folderName: 'Aplanko pavadinimas',\n    folders: 'Aplankai',\n    folderTypeDescription:\n      'Pasirinkite, kokio tipo rinkinio dokumentai turėtų būti leidžiami šiame aplanke.',\n    itemHasBeenMoved: '{{title}} buvo perkeltas į {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} buvo perkeltas į pagrindinį katalogą',\n    itemsMovedToFolder: '{{title}} perkeltas į {{folderName}}',\n    itemsMovedToRoot: '{{title}} perkeltas į šakninį aplanką',\n    moveFolder: 'Perkelti aplanką',\n    moveItemsToFolderConfirmation:\n      'Jūs ketinate perkelti <1>{{count}} {{label}}</1> į <2>{{toFolder}}</2>. Ar esate tikri?',\n    moveItemsToRootConfirmation:\n      'Jūs ketinate perkelti <1>{{count}} {{label}}</1> į šakninį aplanką. Ar esate tikri?',\n    moveItemToFolderConfirmation:\n      'Jūs ketinate perkelti <1>{{title}}</1> į <2>{{toFolder}}</2>. Ar esate įsitikinęs?',\n    moveItemToRootConfirmation:\n      'Jūs ketinate perkelti <1>{{title}}</1> į pagrindinį aplanką. Ar esate tikras?',\n    movingFromFolder: 'Perkeliamas {{title}} iš {{fromFolder}}',\n    newFolder: 'Naujas aplankas',\n    noFolder: 'Nėra aplanko',\n    renameFolder: 'Pervadinti aplanką',\n    searchByNameInFolder: 'Ieškoti pagal vardą {{folderName}}',\n    selectFolderForItem: 'Pasirinkite aplanką skirtą {{title}}',\n  },\n  general: {\n    name: 'Vardas',\n    aboutToDelete: 'Jūs ketinate ištrinti {{label}} <1>{{title}}</1>. Ar esate tikri?',\n    aboutToDeleteCount_many: 'Jūs ketinate ištrinti {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Jūs ketinate ištrinti {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Jūs ketinate ištrinti {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Jūs ketinate visam laikui ištrinti {{label}} <1>{{title}}</1>. Ar esate įsitikinęs?',\n    aboutToPermanentlyDeleteTrash:\n      'Jūs ketinate visam laikui ištrinti <0>{{count}}</0> <1>{{label}}</1> iš šiukšliadėžės. Ar esate įsitikinęs?',\n    aboutToRestore: 'Jūs ketinate atkurti {{label}} <1>{{title}}</1>. Ar esate tikri?',\n    aboutToRestoreAsDraft:\n      'Jūs ketinate atkurti {{label}} <1>{{title}}</1> kaip juodraštį. Ar esate įsitikinęs?',\n    aboutToRestoreAsDraftCount: 'Jūs ketinate atkurti {{count}} {{label}} kaip juodraštį',\n    aboutToRestoreCount: 'Jūs ketinate atkurti {{count}} {{label}}',\n    aboutToTrash:\n      'Jūs ketinate perkelti {{label}} <1>{{title}}</1> į šiukšliadėžę. Ar esate tikras?',\n    aboutToTrashCount: 'Jūs ketinate perkelti {{count}} {{label}} į šiukšlinę',\n    addBelow: 'Pridėti žemiau',\n    addFilter: 'Pridėti filtrą',\n    adminTheme: 'Admin temos',\n    all: 'Visi',\n    allCollections: 'Visos kolekcijos',\n    allLocales: 'Visi lokalai',\n    and: 'Ir',\n    anotherUser: 'Kitas vartotojas',\n    anotherUserTakenOver: 'Kitas naudotojas perėmė šio dokumento redagavimą.',\n    applyChanges: 'Taikyti pakeitimus',\n    ascending: 'Kylantis',\n    automatic: 'Automatinis',\n    backToDashboard: 'Atgal į informacinę skydelį',\n    cancel: 'Atšaukti',\n    changesNotSaved:\n      'Jūsų pakeitimai nebuvo išsaugoti. Jei dabar išeisite, prarasite savo pakeitimus.',\n    clear: 'Aišku',\n    clearAll: 'Išvalyti viską',\n    close: 'Uždaryti',\n    collapse: 'Susikolimas',\n    collections: 'Kolekcijos',\n    columns: 'Stulpeliai',\n    columnToSort: 'Rūšiuoti stulpelį',\n    confirm: 'Patvirtinti',\n    confirmCopy: 'Patvirtinkite kopiją',\n    confirmDeletion: 'Patvirtinkite šalinimą',\n    confirmDuplication: 'Patvirtinkite dubliavimą',\n    confirmMove: 'Patvirtinkite perkėlimą',\n    confirmReindex: 'Perindeksuoti visas {{collections}}?',\n    confirmReindexAll: 'Perindeksuoti visas kolekcijas?',\n    confirmReindexDescription:\n      'Tai pašalins esamus indeksus ir iš naujo indeksuos dokumentus kolekcijose {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Tai pašalins esamas indeksus ir perindeksuos dokumentus visose kolekcijose.',\n    confirmRestoration: 'Patvirtinkite atkūrimą',\n    copied: 'Nukopijuota',\n    copy: 'Kopijuoti',\n    copyField: 'Kopijuoti lauką',\n    copying: 'Kopijavimas',\n    copyRow: 'Kopijuoti eilutę',\n    copyWarning:\n      'Jūs ketinate perrašyti {{to}} į {{from}} šildymui {{label}} {{title}}. Ar esate tikri?',\n    create: 'Sukurti',\n    created: 'Sukurta',\n    createdAt: 'Sukurta',\n    createNew: 'Sukurti naują',\n    createNewLabel: 'Sukurti naują {{label}}',\n    creating: 'Kuriant',\n    creatingNewLabel: 'Kuriamas naujas {{label}}',\n    currentlyEditing:\n      'šiuo metu redaguoja šį dokumentą. Jei perimsite, jie bus užblokuoti ir negalės toliau redaguoti, o taip pat gali prarasti neišsaugotus pakeitimus.',\n    custom: 'Paprastas',\n    dark: 'Tamsus',\n    dashboard: 'Prietaisų skydelis',\n    delete: 'Ištrinti',\n    deleted: 'Ištrinta',\n    deletedAt: 'Ištrinta',\n    deletedCountSuccessfully: 'Sėkmingai ištrinta {{count}} {{label}}.',\n    deletedSuccessfully: 'Sėkmingai ištrinta.',\n    deletePermanently: 'Praleiskite šiukšliadėžę ir ištrinkite visam laikui',\n    deleting: 'Trinama...',\n    depth: 'Gylis',\n    descending: 'Mažėjantis',\n    deselectAllRows: 'Atžymėkite visas eilutes',\n    document: 'Dokumentas',\n    documentIsTrashed: 'Šis {{label}} yra ištrintas ir yra tik skaitymui.',\n    documentLocked: 'Dokumentas užrakintas',\n    documents: 'Dokumentai',\n    duplicate: 'Dublikatas',\n    duplicateWithoutSaving: 'Dubliuoti be įrašytų pakeitimų',\n    edit: 'Redaguoti',\n    editAll: 'Redaguoti viską',\n    editedSince: 'Redaguota nuo',\n    editing: 'Redagavimas',\n    editingLabel_many: 'Redaguojama {{count}} {{label}}',\n    editingLabel_one: 'Redaguojama {{count}} {{label}}',\n    editingLabel_other: 'Redaguojamas {{count}} {{label}}',\n    editingTakenOver: 'Redagavimas perimtas',\n    editLabel: 'Redaguoti {{label}}',\n    email: 'El. paštas',\n    emailAddress: 'El. pašto adresas',\n    emptyTrash: 'Ištuštinti šiukšliadėžę',\n    emptyTrashLabel: 'Ištuštuokite {{label}} šiukšliadėžę',\n    enterAValue: 'Įveskite reikšmę',\n    error: 'Klaida',\n    errors: 'Klaidos',\n    exitLivePreview: 'Išeiti iš tiesioginės peržiūros',\n    export: 'Eksportas',\n    fallbackToDefaultLocale: 'Grįžkite į numatytąją vietovę',\n    false: 'Netiesa',\n    filter: 'Filtruoti',\n    filters: 'Filtrai',\n    filterWhere: 'Filtruoti {{label}}, kur',\n    globals: 'Globalai',\n    goBack: 'Grįžkite',\n    groupByLabel: 'Grupuoti pagal {{label}}',\n    import: 'Importas',\n    isEditing: 'redaguoja',\n    item: 'daiktas',\n    items: 'elementai',\n    language: 'Kalba',\n    lastModified: 'Paskutinį kartą modifikuota',\n    leaveAnyway: 'Vis tiek išeikite',\n    leaveWithoutSaving: 'Išeikite neišsaugoję',\n    light: 'Šviesa',\n    livePreview: 'Tiesioginė peržiūra',\n    loading: 'Kraunama',\n    locale: 'Lokalė',\n    locales: 'Lokalės',\n    menu: 'Meniu',\n    moreOptions: 'Daugiau parinkčių',\n    move: 'Judėti',\n    moveConfirm:\n      'Jūs ketinate perkelti {{count}} {{label}} į <1>{{destination}}</1>. Ar esate tikri?',\n    moveCount: 'Perkelti {{count}} {{label}}',\n    moveDown: 'Perkelti žemyn',\n    moveUp: 'Pakilti',\n    moving: 'Keliauja',\n    movingCount: 'Perkeliama {{count}} {{label}}',\n    newPassword: 'Naujas slaptažodis',\n    next: 'Toliau',\n    no: 'Ne',\n    noDateSelected: 'Pasirinktos datos nėra',\n    noFiltersSet: 'Nenustatyti jokie filtrai',\n    noLabel: '<Ne {{label}}>',\n    none: 'Jokios',\n    noOptions: 'Jokių variantų',\n    noResults:\n      'Nerasta jokių {{label}}. Arba dar nėra sukurtų {{label}}, arba jie neatitinka nurodytų filtrų aukščiau.',\n    notFound: 'Nerasta',\n    nothingFound: 'Nieko nerasta',\n    noTrashResults: 'Nėra {{label}} šiukšliadėžėje.',\n    noUpcomingEventsScheduled: 'Nėra suplanuotų būsimų renginių.',\n    noValue: 'Nėra vertės',\n    of: 'apie',\n    only: 'Tik',\n    open: 'Atidaryti',\n    or: 'Arba',\n    order: 'Užsakyti',\n    overwriteExistingData: 'Perrašyti esamus lauko duomenis',\n    pageNotFound: 'Puslapis nerastas',\n    password: 'Slaptažodis',\n    pasteField: 'Įklijuoti lauką',\n    pasteRow: 'Įklijuoti eilutę',\n    payloadSettings: 'Payload nustatymai',\n    permanentlyDelete: 'Visam laikui pašalinti',\n    permanentlyDeletedCountSuccessfully: 'Sėkmingai visam laikui ištrinta {{count}} {{label}}.',\n    perPage: 'Puslapyje: {{limit}}',\n    previous: 'Ankstesnis',\n    reindex: 'Perindeksuoti',\n    reindexingAll: 'Perindeksuojamos visos {{collections}}.',\n    remove: 'Pašalinti',\n    rename: 'Pervadinti',\n    reset: 'Atstatyti',\n    resetPreferences: 'Atstatyti nuostatas',\n    resetPreferencesDescription: 'Tai atstatys visas jūsų nuostatas į numatytąsias reikšmes.',\n    resettingPreferences: 'Nustatymų atstatymas.',\n    restore: 'Atkurti',\n    restoreAsPublished: 'Atkurti kaip publikuotą versiją',\n    restoredCountSuccessfully: 'Sėkmingai atkurtas {{count}} {{label}}.',\n    restoring: 'Atkurimas...',\n    row: 'Eilutė',\n    rows: 'Eilutės',\n    save: 'Išsaugoti',\n    saving: 'Išsaugoti...',\n    schedulePublishFor: 'Suplanuokite publikaciją „{{title}}“',\n    searchBy: 'Ieškokite pagal {{label}}',\n    select: 'Pasirinkite',\n    selectAll: 'Pasirinkite visus {{count}} {{label}}',\n    selectAllRows: 'Pasirinkite visas eilutes',\n    selectedCount: '{{count}} {{label}} pasirinkta',\n    selectLabel: 'Pasirinkite {{label}}',\n    selectValue: 'Pasirinkite reikšmę',\n    showAllLabel: 'Rodyti visus {{label}}',\n    sorryNotFound: 'Atsiprašau - nėra nieko, atitinkančio jūsų užklausą.',\n    sort: 'Rūšiuoti',\n    sortByLabelDirection: 'Rūšiuoti pagal {{label}} {{direction}}',\n    stayOnThisPage: 'Likite šiame puslapyje',\n    submissionSuccessful: 'Pateikimas sėkmingas.',\n    submit: 'Pateikti',\n    submitting: 'Pateikiama...',\n    success: 'Sėkmė',\n    successfullyCreated: '{{label}} sėkmingai sukurtas.',\n    successfullyDuplicated: '{{label}} sėkmingai dubliuotas.',\n    successfullyReindexed:\n      'Sėkmingai perindeksuota {{count}} iš {{total}} dokumentų iš {{collections}}',\n    takeOver: 'Perimti',\n    thisLanguage: 'Lietuvių',\n    time: 'Laikas',\n    timezone: 'Laiko juosta',\n    titleDeleted: '{{label}} \"{{title}}\" sėkmingai ištrinta.',\n    titleRestored: '{{label}} \"{{title}}\" sėkmingai atkurta.',\n    titleTrashed: '{{label}} \"{{title}}\" perkeltas į šiukšliadėžę.',\n    trash: 'Šiukšlės',\n    trashedCountSuccessfully: '{{count}} {{label}} perkeltas į šiukšlinę.',\n    true: 'Tiesa',\n    unauthorized: 'Neleistinas',\n    unsavedChanges: 'Turite neišsaugotų pakeitimų. Išsaugokite arba atmestkite prieš tęsdami.',\n    unsavedChangesDuplicate: 'Jūs turite neišsaugotų pakeitimų. Ar norėtumėte tęsti dubliavimą?',\n    untitled: 'Neužpavadinamas',\n    upcomingEvents: 'Artimieji renginiai',\n    updatedAt: 'Atnaujinta',\n    updatedCountSuccessfully: '{{count}} {{label}} sėkmingai atnaujinta.',\n    updatedLabelSuccessfully: 'Sėkmingai atnaujinta {{label}}.',\n    updatedSuccessfully: 'Sėkmingai atnaujinta.',\n    updateForEveryone: 'Atnaujinimas visiems',\n    updating: 'Atnaujinimas',\n    uploading: 'Įkeliama',\n    uploadingBulk: 'Įkeliamas {{current}} iš {{total}}',\n    user: 'Vartotojas',\n    username: 'Vartotojo vardas',\n    users: 'Vartotojai',\n    value: 'Vertė',\n    viewing: 'Peržiūrėti',\n    viewReadOnly: 'Peržiūrėti tik skaitymui',\n    welcome: 'Sveiki',\n    yes: 'Taip',\n  },\n  localization: {\n    cannotCopySameLocale: 'Negalima kopijuoti į tą pačią vietovę',\n    copyFrom: 'Kopijuoti iš',\n    copyFromTo: 'Kopijavimas iš {{from}} į {{to}}',\n    copyTo: 'Kopijuoti į',\n    copyToLocale: 'Kopijuoti į vietovę',\n    localeToPublish: 'Publikuoti lokacijoje',\n    selectLocaleToCopy: 'Pasirinkite lokalės kopijavimui',\n  },\n  operators: {\n    contains: 'yra',\n    equals: 'lygus',\n    exists: 'egzistuoja',\n    intersects: 'susikerta',\n    isGreaterThan: 'yra didesnis nei',\n    isGreaterThanOrEqualTo: 'yra didesnis arba lygus',\n    isIn: 'yra',\n    isLessThan: 'yra mažiau nei',\n    isLessThanOrEqualTo: 'yra mažiau arba lygu',\n    isLike: 'yra panašu',\n    isNotEqualTo: 'nelygu',\n    isNotIn: 'nėra',\n    isNotLike: 'nėra panašus',\n    near: 'šalia',\n    within: 'viduje',\n  },\n  upload: {\n    addFile: 'Pridėti failą',\n    addFiles: 'Pridėti failus',\n    bulkUpload: 'Masinis įkėlimas',\n    crop: 'Pasėlis',\n    cropToolDescription:\n      'Temkite pasirinktos srities kampus, nubrėžkite naują sritį arba koreguokite žemiau esančias reikšmes.',\n    download: 'Atsisiųsti',\n    dragAndDrop: 'Temkite ir numeskite failą',\n    dragAndDropHere: 'arba nuvilkite failą čia',\n    editImage: 'Redaguoti vaizdą',\n    fileName: 'Failo pavadinimas',\n    fileSize: 'Failo dydis',\n    filesToUpload: 'Įkelti failai',\n    fileToUpload: 'Įkelti failą',\n    focalPoint: 'Fokuso Taškas',\n    focalPointDescription:\n      'Temkite fokusavimo tašką tiesiogiai peržiūroje arba reguliuokite žemiau esančias reikšmes.',\n    height: 'Aukštis',\n    lessInfo: 'Mažiau informacijos',\n    moreInfo: 'Daugiau informacijos',\n    noFile: 'Nėra failo',\n    pasteURL: 'Įklijuokite URL',\n    previewSizes: 'Peržiūros dydžiai',\n    selectCollectionToBrowse: 'Pasirinkite kolekciją, kurią norėtumėte naršyti',\n    selectFile: 'Pasirinkite failą',\n    setCropArea: 'Nustatykite pjovimo plotą',\n    setFocalPoint: 'Nustatyti fokuso tašką',\n    sizes: 'Dydžiai',\n    sizesFor: 'Dydžiai skirti {{label}}',\n    width: 'Plotis',\n  },\n  validation: {\n    emailAddress: 'Įveskite galiojantį el. pašto adresą.',\n    enterNumber: 'Įveskite galiojantį skaičių.',\n    fieldHasNo: 'Šiame lauke nėra {{label}}',\n    greaterThanMax:\n      '{{value}} yra didesnė nei leidžiama maksimali {{label}} reikšmė, kuri yra {{max}}.',\n    invalidInput: 'Šis laukas turi netinkamą įvestį.',\n    invalidSelection: 'Šiame lauke yra netinkamas pasirinkimas.',\n    invalidSelections: 'Šiame lauke yra šios netinkamos parinktys:',\n    lessThanMin:\n      '{{value}} yra mažesnė nei leidžiama minimali {{label}} reikšmė, kuri yra {{min}}.',\n    limitReached: 'Pasiektas limitas, galima pridėti tik {{max}} daiktus.',\n    longerThanMin:\n      'Ši reikšmė turi būti ilgesnė nei minimalus simbolių skaičius, kuris yra {{minLength}} simboliai.',\n    notValidDate: '\"{{value}}\" nėra galiojanti data.',\n    required: 'Šis laukas yra privalomas.',\n    requiresAtLeast: 'Šis laukas reikalauja bent {{count}} {{label}}.',\n    requiresNoMoreThan: 'Šiame laukelyje gali būti ne daugiau kaip {{count}} {{label}}.',\n    requiresTwoNumbers: 'Šiame lauke reikia įvesti du skaičius.',\n    shorterThanMax: 'Ši reikšmė turi būti trumpesnė nei maksimalus {{maxLength}} simbolių ilgis.',\n    timezoneRequired: 'Reikia nustatyti laiko juostą.',\n    trueOrFalse: 'Šis laukas gali būti lygus tik „true“ ar „false“.',\n    username:\n      'Įveskite galiojantį vartotojo vardą. Galima naudoti raides, skaičius, brūkšnelius, taškus ir pabraukimus.',\n    validUploadID: 'Šis laukas nėra tinkamas įkėlimo ID.',\n  },\n  version: {\n    type: 'Įveskite',\n    aboutToPublishSelection: 'Jūs ketinate išleisti visus {{label}} išrinktame. Ar esate tikri?',\n    aboutToRestore:\n      'Jūs ketinate atkurti šį {{label}} dokumentą į būklę, kurioje jis buvo {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Jūs ketinate atkurti visuotinę {{label}} būklę, kokia ji buvo {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Jūs ketinate atšaukti šio dokumento pakeitimus ir grįžti prie publikuotos versijos. Ar esate įsitikinęs?',\n    aboutToUnpublish: 'Jūs ketinate panaikinti šio dokumento publikavimą. Ar esate tikri?',\n    aboutToUnpublishSelection:\n      'Jūs ketinate atšaukti visų {{label}} pasirinkime. Ar esate įsitikinęs?',\n    autosave: 'Automatinis išsaugojimas',\n    autosavedSuccessfully: 'Sėkmingai automatiškai išsaugota.',\n    autosavedVersion: 'Automatiškai išsaugota versija',\n    changed: 'Pakeistas',\n    changedFieldsCount_one: '{{count}} pakeistas laukas',\n    changedFieldsCount_other: '{{count}} pakeisti laukai',\n    compareVersion: 'Palyginkite versiją su:',\n    compareVersions: 'Palyginkite versijas',\n    comparingAgainst: 'Lyginant su',\n    confirmPublish: 'Patvirtinkite publikaciją',\n    confirmRevertToSaved: 'Patvirtinkite grįžimą į įrašytą',\n    confirmUnpublish: 'Patvirtinkite nepublikavimą',\n    confirmVersionRestoration: 'Patvirtinkite versijos atkūrimą',\n    currentDocumentStatus: 'Dabartinis {{docStatus}} dokumentas',\n    currentDraft: 'Dabartinis projektas',\n    currentlyPublished: 'Šiuo metu publikuojama',\n    currentlyViewing: 'Šiuo metu peržiūrima',\n    currentPublishedVersion: 'Dabartinė publikuota versija',\n    draft: 'Projektas',\n    draftSavedSuccessfully: 'Juosmuo sėkmingai išsaugotas.',\n    lastSavedAgo: 'Paskutinį kartą išsaugota prieš {{distance}}',\n    modifiedOnly: 'Tik modifikuotas',\n    moreVersions: 'Daugiau versijų...',\n    noFurtherVersionsFound: 'Nerasta daugiau versijų',\n    noRowsFound: 'Nerasta {{label}}',\n    noRowsSelected: 'Pasirinkta ne viena {{label}}',\n    preview: 'Peržiūra',\n    previouslyDraft: 'Ankstesnis juodraštis',\n    previouslyPublished: 'Ankstesnė publikacija',\n    previousVersion: 'Ankstesnė versija',\n    problemRestoringVersion: 'Buvo problema atkuriant šią versiją',\n    publish: 'Paskelbti',\n    publishAllLocales: 'Publikuokite visus lokalizacijas',\n    publishChanges: 'Paskelbti pakeitimus',\n    published: 'Paskelbta',\n    publishIn: 'Paskelbti {{locale}}',\n    publishing: 'Leidyba',\n    restoreAsDraft: 'Atkurti kaip juodraštį',\n    restoredSuccessfully: 'Sėkmingai atkurtas.',\n    restoreThisVersion: 'Atkurti šią versiją',\n    restoring: 'Atkuriamas...',\n    reverting: 'Grįžtama...',\n    revertToPublished: 'Grįžti prie publikuotojo',\n    saveDraft: 'Išsaugoti juodraštį',\n    scheduledSuccessfully: 'Sėkmingai suplanuota.',\n    schedulePublish: 'Suplanuokite publikaciją',\n    selectLocales: 'Pasirinkite lokales, kurias norėtumėte rodyti',\n    selectVersionToCompare: 'Pasirinkite versiją, kurią norite palyginti',\n    showingVersionsFor: 'Rodomos versijos:',\n    showLocales: 'Rodyti lokalizacijas:',\n    specificVersion: 'Specifinė versija',\n    status: 'Būsena',\n    unpublish: 'Nebepublikuoti',\n    unpublishing: 'Nebepublikuojama...',\n    version: 'Versija',\n    versionAgo: 'prieš {{distance}}',\n    versionCount_many: 'Rasta {{count}} versijų',\n    versionCount_none: 'Nerasta jokių versijų',\n    versionCount_one: 'Rasta {{count}} versija',\n    versionCount_other: 'Rasta {{count}} versijų',\n    versionCreatedOn: '{{version}} sukurtas:',\n    versionID: 'Versijos ID',\n    versions: 'Versijos',\n    viewingVersion: 'Peržiūrėkite versiją {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Peržiūrint visuotinę {{entityLabel}} versiją',\n    viewingVersions: 'Peržiūrint versijas {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Peržiūrėti globalaus {{entityLabel}} versijas',\n  },\n}\n\nexport const lt: Language = {\n  dateFNSKey: 'lt',\n  translations: ltTranslations,\n}\n"], "names": ["ltTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "lt", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBACE;QACFC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBACE;QACFC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aACE;QACFC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}