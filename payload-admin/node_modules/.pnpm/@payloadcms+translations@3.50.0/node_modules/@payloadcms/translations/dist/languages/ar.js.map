{"version": 3, "sources": ["../../src/languages/ar.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const arTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'الحساب',\n    accountOfCurrentUser: 'حساب المستخدم الحالي',\n    accountVerified: 'تم التحقق من الحساب بنجاح.',\n    alreadyActivated: 'تمّ التّفعيل بالفعل',\n    alreadyLoggedIn: 'تمّ تسجيل الدّخول بالفعل',\n    apiKey: 'مفتاح API',\n    authenticated: 'مصادق عليه',\n    backToLogin: 'العودة لتسجيل الدخول',\n    beginCreateFirstUser: 'للبدء, قم بإنشاء المستخدم الأوّل.',\n    changePassword: 'تغيير كلمة المرور',\n    checkYourEmailForPasswordReset:\n      'إذا كان عنوان البريد الإلكتروني مرتبطًا بحساب، فستتلقى تعليمات لإعادة تعيين كلمة المرور قريبًا. يرجى التحقق من مجلد البريد العشوائي أو السبام إذا لم تر البريد الإلكتروني في صندوق الوارد.',\n    confirmGeneration: 'تأكيد التّوليد',\n    confirmPassword: 'تأكيد كلمة المرور',\n    createFirstUser: 'إنشاء المستخدم الأوّل',\n    emailNotValid: 'البريد الإلكتروني غير صالح',\n    emailOrUsername: 'البريد الإلكتروني أو اسم المستخدم',\n    emailSent: 'تمّ ارسال البريد الإلكتروني',\n    emailVerified: 'تم التحقق من البريد الإلكتروني بنجاح.',\n    enableAPIKey: 'تفعيل مفتاح API',\n    failedToUnlock: 'فشل فتح القفل',\n    forceUnlock: 'إجبار فتح القفل',\n    forgotPassword: 'نسيت كلمة المرور',\n    forgotPasswordEmailInstructions:\n      'يرجى إدخال البريد الالكتروني أدناه. ستتلقّى رسالة بريد إلكتروني تحتوي على إرشادات حول كيفيّة إعادة تعيين كلمة المرور الخاصّة بك.',\n    forgotPasswordQuestion: 'هل نسيت كلمة المرور؟',\n    forgotPasswordUsernameInstructions:\n      'يرجى إدخال اسم المستخدم الخاص بك أدناه. سيتم إرسال تعليمات حول كيفية إعادة تعيين كلمة المرور الخاصة بك إلى عنوان البريد الإلكتروني المرتبط باسم المستخدم الخاص بك.',\n    generate: 'توليد',\n    generateNewAPIKey: 'توليد مفتاح API جديد',\n    generatingNewAPIKeyWillInvalidate:\n      'سيؤدّي إنشاء مفتاح API جديد إلى <1> إبطال </ 1> المفتاح السّابق. هل أنت متأكّد أنّك تريد المتابعة؟',\n    lockUntil: 'قفل حتى',\n    logBackIn: 'تسجيل الدّخول من جديد',\n    loggedIn: 'لتسجيل الدّخول مع مستخدم آخر ، يجب عليك <0> تسجيل الخروج </0> أوّلاً.',\n    loggedInChangePassword:\n      'لتغيير كلمة المرور الخاصّة بك ، انتقل إلى <0>حسابك</0> وقم بتعديل كلمة المرور هناك.',\n    loggedOutInactivity: 'لقد تمّ تسجيل الخروج بسبب عدم النّشاط.',\n    loggedOutSuccessfully: 'لقد تمّ تسجيل خروجك بنجاح.',\n    loggingOut: 'تسجيل الخروج...',\n    login: 'تسجيل الدخول',\n    loginAttempts: 'محاولات تسجيل الدخول',\n    loginUser: 'تسجيل دخول المستخدم',\n    loginWithAnotherUser: 'لتسجيل الدخول مع مستخدم آخر ، يجب عليك <0> تسجيل الخروج </0> أوّلاً.',\n    logOut: 'تسجيل الخروج',\n    logout: 'تسجيل الخروج',\n    logoutSuccessful: 'تم تسجيل الخروج بنجاح.',\n    logoutUser: 'تسجيل خروج المستخدم',\n    newAccountCreated:\n      'تمّ إنشاء حساب جديد لتتمكّن من الوصول إلى <a href=\"{{serverURL}}\"> {{serverURL}} </a> الرّجاء النّقر فوق الرّابط التّالي أو لصق عنوان URL أدناه في متصفّحّك لتأكيد بريدك الإلكتروني : <a href=\"{{verificationURL}}\"> {{verificationURL}} </a> <br> بعد التّحقّق من بريدك الإلكتروني ، ستتمكّن من تسجيل الدّخول بنجاح.',\n    newAPIKeyGenerated: 'تمّ توليد مفتاح API جديد.',\n    newPassword: 'كلمة مرور جديدة',\n    passed: 'تمت المصادقة',\n    passwordResetSuccessfully: 'تمت إعادة تعيين كلمة المرور بنجاح.',\n    resetPassword: 'إعادة تعيين كلمة المرور',\n    resetPasswordExpiration: 'انتهاء صلاحيّة إعادة تعيين كلمة المرور',\n    resetPasswordToken: 'رمز إعادة تعيين كلمة المرور',\n    resetYourPassword: 'إعادة تعيين كلمة المرور الخاصّة بك',\n    stayLoggedIn: 'ابق متّصلًا',\n    successfullyRegisteredFirstUser: 'تم تسجيل العضو الأول بنجاح.',\n    successfullyUnlocked: 'تمّ فتح القفل بنجاح',\n    tokenRefreshSuccessful: 'تم تجديد الرمز بنجاح.',\n    unableToVerify: 'غير قادر على التحقق من',\n    username: 'اسم المستخدم',\n    usernameNotValid: 'اسم المستخدم المقدم غير صالح',\n    verified: 'تمّ التحقّق',\n    verifiedSuccessfully: 'تمّ التحقّق بنجاح',\n    verify: 'قم بالتّحقّق',\n    verifyUser: 'قم بالتّحقّق من المستخدم',\n    verifyYourEmail: 'قم بتأكيد بريدك الألكتروني',\n    youAreInactive:\n      'لم تكن نشطًا منذ فترة قصيرة وسيتمّ تسجيل خروجك قريبًا تلقائيًا من أجل أمنك. هل ترغب في البقاء مسجّلا؟',\n    youAreReceivingResetPassword:\n      'أنت تتلقّى هذا البريد الالكتروني لأنّك (أو لأنّ شخص آخر) طلبت إعادة تعيين كلمة المرور لحسابك. الرّجاء النّقر فوق الرّابط التّالي ، أو لصق هذا الرّابط في متصفّحك لإكمال العمليّة:',\n    youDidNotRequestPassword:\n      'إن لم تطلب هذا ، يرجى تجاهل هذا البريد الإلكتروني وستبقى كلمة مرورك ذاتها بدون تغيير.',\n  },\n  error: {\n    accountAlreadyActivated: 'تم تفعيل هذا الحساب بالفعل.',\n    autosaving: 'حدثت مشكلة أثناء حفظ هذا المستند تلقائيًا.',\n    correctInvalidFields: 'يرجى تصحيح الحقول غير الصالحة.',\n    deletingFile: 'حدث خطأ أثناء حذف الملف.',\n    deletingTitle:\n      'حدث خطأ أثناء حذف {{title}}. يرجى التحقق من الاتصال الخاص بك والمحاولة مرة أخرى.',\n    documentNotFound:\n      'لم يتم العثور على المستند بالمعرف {{id}}. قد يكون قد تم حذفه أو لم يكن موجودًا أصلاً ، أو قد لا يكون لديك الوصول إليه.',\n    emailOrPasswordIncorrect: 'البريد الإلكتروني أو كلمة المرور المقدمة غير صحيحة.',\n    followingFieldsInvalid_one: 'الحقل التالي غير صالح:',\n    followingFieldsInvalid_other: 'الحقول التالية غير صالحة:',\n    incorrectCollection: 'مجموعة غير صحيحة',\n    insufficientClipboardPermissions: 'تم رفض الوصول إلى الحافظة. يرجى التحقق من أذونات الحافظة.',\n    invalidClipboardData: 'بيانات الحافظة غير صالحة.',\n    invalidFileType: 'نوع ملف غير صالح',\n    invalidFileTypeValue: 'نوع ملف غير صالح: {{value}}',\n    invalidRequestArgs: 'تم تمرير وسيطات غير صالحة في الطلب: {{args}}',\n    loadingDocument: 'حدثت مشكلة أثناء تحميل المستند برقم التعريف {{id}}.',\n    localesNotSaved_one: 'لم يتم حفظ اللغة التالية:',\n    localesNotSaved_other: 'لم يتم حفظ اللغات التالية:',\n    logoutFailed: 'فشل في تسجيل الخروج.',\n    missingEmail: 'البريد الإلكتروني مفقود.',\n    missingIDOfDocument: 'معرّف المستند المراد تحديثه مفقود.',\n    missingIDOfVersion: 'معرّف النسخة مفقود.',\n    missingRequiredData: 'توجد بيانات مطلوبة مفقودة.',\n    noFilesUploaded: 'لم يتمّ رفع أيّة ملفّات.',\n    noMatchedField: 'لم يتمّ العثور على حقل مطابق لـ \"{{label}}\"',\n    notAllowedToAccessPage: 'لا يسمح لك الوصول إلى هذه الصّفحة.',\n    notAllowedToPerformAction: 'لا يسمح لك القيام بهذه العمليّة.',\n    notFound: 'لم يتمّ العثور على المورد المطلوب.',\n    noUser: 'لا يوجد مستخدم',\n    previewing: 'حدث خطأ في اثناء معاينة هذا المستند.',\n    problemUploadingFile: 'حدث خطأ اثناء رفع الملفّ.',\n    restoringTitle: 'حدث خطأ أثناء استعادة {{title}}. يرجى التحقق من اتصالك وحاول مرة أخرى.',\n    tokenInvalidOrExpired: 'الرّمز إمّا غير صالح أو منتهي الصّلاحيّة.',\n    tokenNotProvided: 'لم يتم تقديم الرمز.',\n    unableToCopy: 'تعذر النسخ.',\n    unableToDeleteCount: 'يتعذّر حذف {{count}} من {{total}} {{label}}.',\n    unableToReindexCollection: 'خطأ في إعادة فهرسة المجموعة {{collection}}. تم إيقاف العملية.',\n    unableToUpdateCount: 'يتعذّر تحديث {{count}} من {{total}} {{label}}.',\n    unauthorized: 'غير مصرّح لك ، عليك أن تقوم بتسجيل الدّخول لتتمكّن من تقديم هذا الطّلب.',\n    unauthorizedAdmin: 'غير مصرّح لك بالوصول إلى لوحة التحكّم.',\n    unknown: 'حدث خطأ غير معروف.',\n    unPublishingDocument: 'حدث خطأ أثناء إلغاء نشر هذا المستند.',\n    unspecific: 'حدث خطأ.',\n    unverifiedEmail: 'يرجى التحقق من بريدك الإلكتروني قبل تسجيل الدخول.',\n    userEmailAlreadyRegistered: 'يوجد مستخدم مسجل بالفعل بهذا البريد الإلكتروني.',\n    userLocked: 'تمّ قفل هذا المستخدم نظرًا لوجود عدد كبير من محاولات تسجيل الدّخول الغير ناجحة.',\n    usernameAlreadyRegistered: 'المستخدم بالاسم المعطى مسجل بالفعل.',\n    usernameOrPasswordIncorrect: 'اسم المستخدم أو كلمة المرور التي تم تقديمها غير صحيحة.',\n    valueMustBeUnique: 'على القيمة أن تكون فريدة',\n    verificationTokenInvalid: 'رمز التحقّق غير صالح.',\n  },\n  fields: {\n    addLabel: 'أضف {{label}}',\n    addLink: 'أضف رابط',\n    addNew: 'أضف جديد',\n    addNewLabel: 'أضف {{label}} جديد',\n    addRelationship: 'أضف علاقة',\n    addUpload: 'أضف تحميل',\n    block: 'وحدة محتوى',\n    blocks: 'وحدات المحتوى',\n    blockType: 'نوع وحدة المحتوى',\n    chooseBetweenCustomTextOrDocument: 'اختر بين إدخال عنوان URL نصّي مخصّص أو الرّبط بمستند آخر.',\n    chooseDocumentToLink: 'اختر مستندًا للربط',\n    chooseFromExisting: 'اختر من القائمة',\n    chooseLabel: 'اختر {{label}}',\n    collapseAll: 'طيّ الكلّ',\n    customURL: 'URL مخصّص',\n    editLabelData: 'عدّل بيانات {{label}}',\n    editLink: 'عدّل الرّابط',\n    editRelationship: 'عدّل العلاقة',\n    enterURL: 'ادخل عنوان URL',\n    internalLink: 'رابط داخلي',\n    itemsAndMore: '{{items}} و {{count}} أخرى',\n    labelRelationship: '{{label}} علاقة',\n    latitude: 'خطّ العرض',\n    linkedTo: 'تمّ الرّبط ل <0>{{label}}</0>',\n    linkType: 'نوع الرّابط',\n    longitude: 'خطّ الطّول',\n    newLabel: '{{label}} جديد',\n    openInNewTab: 'الفتح في علامة تبويب جديدة',\n    passwordsDoNotMatch: 'كلمة المرور غير مطابقة.',\n    relatedDocument: 'مستند مربوط',\n    relationTo: 'ربط ل',\n    removeRelationship: 'حذف العلاقة',\n    removeUpload: 'حذف المحتوى المرفوع',\n    saveChanges: 'حفظ التّغييرات',\n    searchForBlock: 'ابحث عن وحدة محتوى',\n    selectExistingLabel: 'اختيار {{label}} من القائمة',\n    selectFieldsToEdit: 'حدّد الحقول اللتي تريد تعديلها',\n    showAll: 'إظهار الكلّ',\n    swapRelationship: 'تبديل العلاقة',\n    swapUpload: 'تبديل المحتوى المرفوع',\n    textToDisplay: 'النصّ الذي تريد إظهاره',\n    toggleBlock: 'Toggle block',\n    uploadNewLabel: 'رفع {{label}} جديد',\n  },\n  folder: {\n    browseByFolder: 'تصفح حسب المجلد',\n    byFolder: 'حسب المجلد',\n    deleteFolder: 'حذف المجلد',\n    folderName: 'اسم المجلد',\n    folders: 'مجلدات',\n    folderTypeDescription: 'حدد نوع المستندات التي يجب السماح بها في هذا المجلد من المجموعات.',\n    itemHasBeenMoved: 'تم نقل {{title}} إلى {{folderName}}',\n    itemHasBeenMovedToRoot: 'تم نقل {{title}} إلى المجلد الجذر',\n    itemsMovedToFolder: '{{title}} تم نقله إلى {{folderName}}',\n    itemsMovedToRoot: '{{title}} تم نقله إلى المجلد الجذر',\n    moveFolder: 'نقل المجلد',\n    moveItemsToFolderConfirmation:\n      'أنت على وشك نقل <1>{{count}} {{label}}</1> إلى <2>{{toFolder}}</2>. هل أنت متأكد؟',\n    moveItemsToRootConfirmation:\n      'أنت على وشك نقل <1>{{count}} {{label}}</1> إلى المجلد الجذر. هل أنت متأكد؟',\n    moveItemToFolderConfirmation:\n      'أنت على وشك نقل <1>{{title}}</1> إلى <2>{{toFolder}}</2>. هل أنت متأكد؟',\n    moveItemToRootConfirmation: 'أنت على وشك نقل <1>{{title}}</1> إلى المجلد الجذر. هل أنت متأكد؟',\n    movingFromFolder: 'نقل {{title}} من {{fromFolder}}',\n    newFolder: 'مجلد جديد',\n    noFolder: 'لا يوجد مجلد',\n    renameFolder: 'إعادة تسمية المجلد',\n    searchByNameInFolder: 'البحث عن طريق الاسم في {{folderName}}',\n    selectFolderForItem: 'اختر المجلد لـ {{title}}',\n  },\n  general: {\n    name: 'اسم',\n    aboutToDelete: 'أنت على وشك حذف {{label}} <1>{{title}}</1>. هل أنت متأكّد؟',\n    aboutToDeleteCount_many: 'أنت على وشك حذف {{count}} {{label}}',\n    aboutToDeleteCount_one: 'أنت على وشك حذف {{count}} {{label}}',\n    aboutToDeleteCount_other: 'أنت على وشك حذف {{count}} {{label}}',\n    aboutToPermanentlyDelete: 'أنت على وشك حذف {{label}} <1>{{title}}</1> نهائيا. هل أنت متأكد؟',\n    aboutToPermanentlyDeleteTrash:\n      'أنت على وشك حذف <0>{{count}}</0> <1>{{label}}</1> نهائياً من سلة المهملات. هل أنت متأكد؟',\n    aboutToRestore: 'أنت على وشك استعادة {{label}} <1>{{title}}</1>. هل أنت متأكد؟',\n    aboutToRestoreAsDraft: 'أنت على وشك استعادة {{label}} <1>{{title}}</1> كمسودة. هل أنت متأكد؟',\n    aboutToRestoreAsDraftCount: 'أنت على وشك استعادة {{count}} {{label}} كمسودة',\n    aboutToRestoreCount: 'أنت على وشك استعادة {{count}} {{label}}',\n    aboutToTrash: 'أنت على وشك نقل {{label}} <1>{{title}}</1> إلى القمامة. هل أنت متأكد؟',\n    aboutToTrashCount: 'أنت على وشك نقل {{count}} {{label}} إلى المهملات',\n    addBelow: 'أضف في الاسفل',\n    addFilter: 'أضف فلتر',\n    adminTheme: 'شكل واجهة المستخدم',\n    all: 'الكل',\n    allCollections: 'جميع المجموعات',\n    allLocales: 'جميع المواقع',\n    and: 'و',\n    anotherUser: 'مستخدم آخر',\n    anotherUserTakenOver: 'قام مستخدم آخر بالاستيلاء على تحرير هذا المستند.',\n    applyChanges: 'طبق التغييرات',\n    ascending: 'تصاعدي',\n    automatic: 'تلقائي',\n    backToDashboard: 'العودة للوحة التّحكّم',\n    cancel: 'إلغاء',\n    changesNotSaved: 'لم يتمّ حفظ التّغييرات. إن غادرت الآن ، ستفقد تغييراتك.',\n    clear: 'واضح',\n    clearAll: 'امسح الكل',\n    close: 'إغلاق',\n    collapse: 'طيّ',\n    collections: 'المجموعات',\n    columns: 'الأعمدة',\n    columnToSort: 'التّرتيب حسب العامود',\n    confirm: 'تأكيد',\n    confirmCopy: 'تأكيد النسخ',\n    confirmDeletion: 'تأكيد الحذف',\n    confirmDuplication: 'تأكيد التّكرار',\n    confirmMove: 'تأكيد النقل',\n    confirmReindex: 'إعادة فهرسة جميع {{collections}}؟',\n    confirmReindexAll: 'إعادة فهرسة جميع المجموعات؟',\n    confirmReindexDescription:\n      'سيؤدي هذا إلى إزالة الفهارس الحالية وإعادة فهرسة المستندات في مجموعات {{collections}}.',\n    confirmReindexDescriptionAll:\n      'سيؤدي هذا إلى إزالة الفهارس الحالية وإعادة فهرسة المستندات في جميع المجموعات.',\n    confirmRestoration: 'تأكيد الاستعادة',\n    copied: 'تمّ النّسخ',\n    copy: 'نسخ',\n    copyField: 'نسخ الحقل',\n    copying: 'نسخ',\n    copyRow: 'نسخ الصف',\n    copyWarning: 'أنت على وشك الكتابة فوق {{to}} بـ {{from}} لـ {{label}} {{title}}. هل أنت متأكد؟',\n    create: 'إنشاء',\n    created: 'تمّ الإنشاء',\n    createdAt: 'تمّ الإنشاء في',\n    createNew: 'أنشاء جديد',\n    createNewLabel: 'إنشاء {{label}} جديد',\n    creating: 'يتمّ الإنشاء',\n    creatingNewLabel: 'جاري إنشاء {{label}} جديد',\n    currentlyEditing:\n      'يقوم حاليًا بتحرير هذا المستند. إذا توليت، سيتم منعه من الاستمرار في التحرير وقد يفقد التغييرات غير المحفوظة.',\n    custom: 'مخصص',\n    dark: 'غامق',\n    dashboard: 'لوحة التّحكّم',\n    delete: 'حذف',\n    deleted: 'تم الحذف',\n    deletedAt: 'تم الحذف في',\n    deletedCountSuccessfully: 'تمّ حذف {{count}} {{label}} بنجاح.',\n    deletedSuccessfully: 'تمّ الحذف بنجاح.',\n    deletePermanently: 'تجاوز السلة واحذف بشكل دائم',\n    deleting: 'يتمّ الحذف...',\n    depth: 'عمق',\n    descending: 'تنازلي',\n    deselectAllRows: 'إلغاء تحديد جميع الصفوف',\n    document: 'وثيقة',\n    documentIsTrashed: 'تم تحويل {{label}} هذا إلى المهملات وهو للقراءة فقط.',\n    documentLocked: 'تم قفل المستند',\n    documents: 'وثائق',\n    duplicate: 'استنساخ',\n    duplicateWithoutSaving: 'استنساخ بدون حفظ التغييرات',\n    edit: 'تعديل',\n    editAll: 'تحرير الكل',\n    editedSince: 'تم التحرير منذ',\n    editing: 'جاري التعديل',\n    editingLabel_many: 'تعديل {{count}} {{label}}',\n    editingLabel_one: 'تعديل {{count}} {{label}}',\n    editingLabel_other: 'تعديل {{count}} {{label}}',\n    editingTakenOver: 'تم الاستيلاء على التحرير',\n    editLabel: 'تعديل {{label}}',\n    email: 'البريد الإلكتروني',\n    emailAddress: 'عنوان البريد الإلكتروني',\n    emptyTrash: 'أفرغ القمامة',\n    emptyTrashLabel: 'أفرغ سلة المحذوفات {{label}}',\n    enterAValue: 'أدخل قيمة',\n    error: 'خطأ',\n    errors: 'أخطاء',\n    exitLivePreview: 'إغلاق المعاينة المباشرة',\n    export: 'تصدير',\n    fallbackToDefaultLocale: 'الرجوع إلى اللغة الافتراضية',\n    false: 'كاذب',\n    filter: 'تصفية',\n    filters: 'عوامل التصفية',\n    filterWhere: 'تصفية {{label}} حيث',\n    globals: 'عامة',\n    goBack: 'العودة',\n    groupByLabel: 'التجميع حسب {{label}}',\n    import: 'استيراد',\n    isEditing: 'يحرر',\n    item: 'عنصر',\n    items: 'عناصر',\n    language: 'اللغة',\n    lastModified: 'آخر تعديل',\n    leaveAnyway: 'المغادرة على أي حال',\n    leaveWithoutSaving: 'المغادرة بدون حفظ',\n    light: 'فاتح',\n    livePreview: 'معاينة مباشرة',\n    loading: 'يتمّ التّحميل',\n    locale: 'اللّغة',\n    locales: 'اللّغات',\n    menu: 'قائمة',\n    moreOptions: 'خيارات أكثر',\n    move: 'تحرك',\n    moveConfirm: 'أنت على وشك نقل {{count}} {{label}} إلى <1>{{destination}}</1>. هل أنت متأكد؟',\n    moveCount: 'انقل {{count}} {{label}}',\n    moveDown: 'التّحريك إلى الأسفل',\n    moveUp: 'التّحريك إلى الأعلى',\n    moving: 'التحرك',\n    movingCount: 'نقل {{count}} {{label}}',\n    newPassword: 'كلمة مرور جديدة',\n    next: 'التالي',\n    no: 'لا',\n    noDateSelected: 'لم يتم اختيار تاريخ',\n    noFiltersSet: 'لم يتم تعيين أي عوامل تصفية',\n    noLabel: '<لا {{label}}>',\n    none: 'لا شيء',\n    noOptions: 'لا خيارات',\n    noResults:\n      'لا يوجد {{label}}. إما أن لا {{label}} موجودة حتى الآن أو لا تتطابق مع عوامل التصفية التي حددتها أعلاه.',\n    notFound: 'غير موجود',\n    nothingFound: 'لم يتم العثور على شيء',\n    noTrashResults: 'لا {{label}} في المهملات.',\n    noUpcomingEventsScheduled: 'لا يوجد أحداث مقبلة مجدولة.',\n    noValue: 'لا يوجد قيمة',\n    of: 'من',\n    only: 'فقط',\n    open: 'فتح',\n    or: 'أو',\n    order: 'التّرتيب',\n    overwriteExistingData: 'استبدل بيانات الحقل الموجودة',\n    pageNotFound: 'الصّفحة غير موجودة',\n    password: 'كلمة المرور',\n    pasteField: 'لصق الحقل',\n    pasteRow: 'لصق الصف',\n    payloadSettings: 'الإعدادات',\n    permanentlyDelete: 'حذف بشكل دائم',\n    permanentlyDeletedCountSuccessfully: 'تم حذف {{count}} {{label}} بشكل دائم بنجاح.',\n    perPage: 'لكلّ صفحة: {{limit}}',\n    previous: 'سابق',\n    reindex: 'إعادة الفهرسة',\n    reindexingAll: 'جاري إعادة فهرسة جميع {{collections}}.',\n    remove: 'إزالة',\n    rename: 'إعادة تسمية',\n    reset: 'إعادة تعيين',\n    resetPreferences: 'إعادة تعيين التفضيلات',\n    resetPreferencesDescription:\n      'سيؤدي ذلك إلى إعادة تعيين جميع تفضيلاتك إلى الإعدادات الافتراضية.',\n    resettingPreferences: 'إعادة تعيين التفضيلات.',\n    restore: 'استعادة',\n    restoreAsPublished: 'استعادة كإصدار منشور',\n    restoredCountSuccessfully: 'تمت استعادة {{count}} {{label}} بنجاح.',\n    restoring:\n      'احترم معنى النص الأصلي في سياق Payload. هنا قائمة بالمصطلحات الشائعة في Payload التي تحمل معانٍ محددة جدًا:\\n    - Collection: المجموعة هي مجموعة من الوثائق التي تتشارك في الهيكل والغرض المشترك. تُستخدم المجموعات لتنظيم وإدارة المحتوى في Payload.',\n    row: 'سطر',\n    rows: 'أسطُر',\n    save: 'حفظ',\n    saving: 'جاري الحفظ...',\n    schedulePublishFor: 'جدولة النشر لـ {{العنوان}}',\n    searchBy: 'البحث عن طريق {{label}}',\n    select: 'اختر',\n    selectAll: 'تحديد كل {{count}} {{label}}',\n    selectAllRows: 'حدد جميع الصفوف',\n    selectedCount: 'تم تحديد {{count}} {{label}}',\n    selectLabel: 'حدد {{label}}',\n    selectValue: 'اختيار قيمة',\n    showAllLabel: 'عرض كل {{label}}',\n    sorryNotFound: 'عذرًا - لا يوجد شيء يتوافق مع طلبك.',\n    sort: 'ترتيب',\n    sortByLabelDirection: 'رتّب حسب {{label}} {{direction}}',\n    stayOnThisPage: 'البقاء على هذه الصفحة',\n    submissionSuccessful: 'تمت الإرسال بنجاح.',\n    submit: 'إرسال',\n    submitting: 'جاري التقديم...',\n    success: 'النجاح',\n    successfullyCreated: '{{label}} تم إنشاؤها بنجاح.',\n    successfullyDuplicated: '{{label}} تم استنساخها بنجاح.',\n    successfullyReindexed:\n      'تم إعادة فهرسة {{count}} من أصل {{total}} مستندات من {{collections}} مجموعات بنجاح.',\n    takeOver: 'تولي',\n    thisLanguage: 'العربية',\n    time: 'الوقت',\n    timezone: 'المنطقة الزمنية',\n    titleDeleted: 'تم حذف {{label}} \"{{title}}\" بنجاح.',\n    titleRestored: 'تمت استعادة \"{{title}}\" \"{{label}}\" بنجاح.',\n    titleTrashed: '\"{{label}}\" \"{{title}}\" تم نقلها إلى سلة المهملات.',\n    trash: 'سلة المهملات',\n    trashedCountSuccessfully: '{{count}} {{label}} تم نقلها إلى سلة المهملات.',\n    true: 'صحيح',\n    unauthorized: 'غير مصرح به',\n    unsavedChanges: 'لديك تغييرات غير محفوظة. قم بالحفظ أو التجاهل قبل المتابعة.',\n    unsavedChangesDuplicate: 'لديك تغييرات لم يتم حفظها. هل تريد الاستمرار في الاستنساخ؟',\n    untitled: 'بدون عنوان',\n    upcomingEvents: 'الأحداث القادمة',\n    updatedAt: 'تم التحديث في',\n    updatedCountSuccessfully: 'تم تحديث {{count}} {{label}} بنجاح.',\n    updatedLabelSuccessfully: 'تم تحديث {{label}} بنجاح.',\n    updatedSuccessfully: 'تم التحديث بنجاح.',\n    updateForEveryone: 'تحديث للجميع',\n    updating: 'جار التحديث',\n    uploading: 'جار الرفع',\n    uploadingBulk: 'جاري التحميل {{current}} من {{total}}',\n    user: 'المستخدم',\n    username: 'اسم المستخدم',\n    users: 'المستخدمين',\n    value: 'القيمة',\n    viewing: 'عرض',\n    viewReadOnly: 'عرض للقراءة فقط',\n    welcome: 'مرحبًا',\n    yes: 'نعم',\n  },\n  localization: {\n    cannotCopySameLocale: 'لا يمكن النسخ إلى نفس الموقع',\n    copyFrom: 'نسخ من',\n    copyFromTo: 'النسخ من {{from}} إلى {{to}}',\n    copyTo: 'انسخ إلى',\n    copyToLocale: 'نسخ إلى الموقع المحلي',\n    localeToPublish: 'الموقع للنشر',\n    selectLocaleToCopy: 'حدد الموقع المحلي للنسخ',\n  },\n  operators: {\n    contains: 'يحتوي',\n    equals: 'يساوي',\n    exists: 'موجود',\n    intersects: 'يتقاطع',\n    isGreaterThan: 'أكبر من',\n    isGreaterThanOrEqualTo: 'أكبر أو يساوي',\n    isIn: 'موجود في',\n    isLessThan: 'أصغر من',\n    isLessThanOrEqualTo: 'أصغر أو يساوي',\n    isLike: 'هو مثل',\n    isNotEqualTo: 'لا يساوي',\n    isNotIn: 'غير موجود في',\n    isNotLike: 'ليس مثل',\n    near: 'قريب من',\n    within: 'في غضون',\n  },\n  upload: {\n    addFile: 'إضافة ملف',\n    addFiles: 'أضف ملفات',\n    bulkUpload: 'تحميل بالجملة',\n    crop: 'محصول',\n    cropToolDescription: 'اسحب الزوايا المحددة للمنطقة، رسم منطقة جديدة أو قم بضبط القيم أدناه.',\n    download: 'تحميل',\n    dragAndDrop: 'قم بسحب وإسقاط ملفّ',\n    dragAndDropHere: 'أو اسحب الملفّ وأفلته هنا',\n    editImage: 'تعديل الصورة',\n    fileName: 'اسم الملفّ',\n    fileSize: 'حجم الملفّ',\n    filesToUpload: 'ملفات للتحميل',\n    fileToUpload: 'ملف للتحميل',\n    focalPoint: 'نقطة التركيز',\n    focalPointDescription: 'اسحب النقطة المركزية مباشرة على المعاينة أو قم بضبط القيم أدناه.',\n    height: 'الطّول',\n    lessInfo: 'معلومات أقلّ',\n    moreInfo: 'معلومات أكثر',\n    noFile: 'لا يوجد ملف',\n    pasteURL: 'لصق الرابط',\n    previewSizes: 'أحجام المعاينة',\n    selectCollectionToBrowse: 'حدّد مجموعة لاستعراضها',\n    selectFile: 'اختر ملفّ',\n    setCropArea: 'حدد منطقة القص',\n    setFocalPoint: 'حدد النقطة البؤرية',\n    sizes: 'الاحجام',\n    sizesFor: 'أحجام لـ {{label}}',\n    width: 'العرض',\n  },\n  validation: {\n    emailAddress: 'يرجى إدخال عنوان بريد إلكتروني صحيح.',\n    enterNumber: 'يرجى إدخال رقم صحيح.',\n    fieldHasNo: 'هذا الحقل ليس لديه {{label}}',\n    greaterThanMax: '{{value}} أكبر من الحد الأقصى المسموح به {{label}} الذي يبلغ {{max}}.',\n    invalidInput: 'هذا الحقل لديه إدخال غير صالح.',\n    invalidSelection: 'هذا الحقل لديه اختيار غير صالح.',\n    invalidSelections: 'هذا الحقل لديه الاختيارات الغير صالحة التالية:',\n    lessThanMin: '{{value}} أقل من الحد الأدنى المسموح به {{label}} الذي يبلغ {{min}}.',\n    limitReached: 'تم الوصول إلى الحد الأقصى، يمكن إضافة {{max}} عناصر فقط.',\n    longerThanMin: 'يجب أن يكون هذا القيمة أطول من الحد الأدنى للطول الذي هو {{minLength}} أحرف.',\n    notValidDate: '\"{{value}}\" ليس تاريخا صالحا.',\n    required: 'هذا الحقل مطلوب.',\n    requiresAtLeast: 'هذا الحقل يتطلب على الأقل {{count}} {{label}}.',\n    requiresNoMoreThan: 'هذا الحقل يتطلب عدم تجاوز {{count}} {{label}}.',\n    requiresTwoNumbers: 'هذا الحقل يتطلب رقمين.',\n    shorterThanMax: 'يجب أن تكون هذه القيمة أقصر من الحد الأقصى للطول الذي هو {{maxLength}} أحرف.',\n    timezoneRequired: 'مطلوب منطقة زمنية.',\n    trueOrFalse: 'يمكن أن يكون هذا الحقل مساويًا فقط للقيمتين صحيح أو خطأ.',\n    username:\n      'يرجى إدخال اسم مستخدم صالح. يمكن أن يحتوي على أحرف، أرقام، شرطات، فواصل وشرطات سفلية.',\n    validUploadID: 'هذا الحقل ليس معرّف تحميل صالح.',\n  },\n  version: {\n    type: 'النّوع',\n    aboutToPublishSelection: 'أنت على وشك نشر كلّ {{label}} في التّحديد. هل أنت متأكّد؟',\n    aboutToRestore:\n      'أنت على وشك استرجاع هذا المستند {{label}} إلى الحالة التّي كان عليها في {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'أنت على وشك استرجاع الاعداد العامّ {{label}} إلى الحالة التي كان عليها في {{versionDate}}.',\n    aboutToRevertToPublished: 'أنت على وشك إعادة هذا المستند إلى حالته المنشورة. هل أنت متأكّد؟',\n    aboutToUnpublish: 'أنت على وشك إلغاء نشر هذا المستند. هل أنت متأكّد؟',\n    aboutToUnpublishSelection: 'أنت على وشك إلغاء نشر كلّ {{label}} في التّحديد. هل أنت متأكّد؟',\n    autosave: 'حفظ تلقائي',\n    autosavedSuccessfully: 'تمّ الحفظ التّلقائي بنجاح.',\n    autosavedVersion: 'النّسخة المحفوظة تلقائياً',\n    changed: 'تمّ التّغيير',\n    changedFieldsCount_one: '{{count}} قام بتغيير الحقل',\n    changedFieldsCount_other: '{{count}} حقول تم تغييرها',\n    compareVersion: 'مقارنة النّسخة مع:',\n    compareVersions: 'قارن الإصدارات',\n    comparingAgainst: 'مقارنة مع',\n    confirmPublish: 'تأكيد النّشر',\n    confirmRevertToSaved: 'تأكيد الرّجوع للنسخة المنشورة',\n    confirmUnpublish: 'تأكيد إلغاء النّشر',\n    confirmVersionRestoration: 'تأكيد إستعادة النّسخة',\n    currentDocumentStatus: 'المستند {{docStatus}} الحالي',\n    currentDraft: 'المسودة الحالية',\n    currentlyPublished: 'نشر حاليا',\n    currentlyViewing: 'تمت المشاهدة حاليا',\n    currentPublishedVersion: 'النسخة المنشورة الحالية',\n    draft: 'مسودّة',\n    draftSavedSuccessfully: 'تمّ حفظ المسودّة بنجاح.',\n    lastSavedAgo: 'تم الحفظ آخر مرة قبل {{distance}}',\n    modifiedOnly: 'تم التعديل فقط',\n    moreVersions: 'المزيد من الإصدارات...',\n    noFurtherVersionsFound: 'لم يتمّ العثور على نسخات أخرى',\n    noRowsFound: 'لم يتمّ العثور على {{label}}',\n    noRowsSelected: 'لم يتم اختيار {{label}}',\n    preview: 'معاينة',\n    previouslyDraft: 'سابقا مسودة',\n    previouslyPublished: 'نشر سابقا',\n    previousVersion: 'النسخة السابقة',\n    problemRestoringVersion: 'حدث خطأ في استعادة هذه النّسخة',\n    publish: 'نشر',\n    publishAllLocales: 'نشر جميع المواقع',\n    publishChanges: 'نشر التّغييرات',\n    published: 'تمّ النّشر',\n    publishIn: 'نشر في {{locale}}',\n    publishing: 'نشر',\n    restoreAsDraft: 'استعادة كمسودة',\n    restoredSuccessfully: 'تمّت الاستعادة بنحاح.',\n    restoreThisVersion: 'استعادة هذه النّسخة',\n    restoring: 'تتمّ الاستعادة...',\n    reverting: 'يتمّ الاسترجاع...',\n    revertToPublished: 'الرّجوع للنسخة المنشورة',\n    saveDraft: 'حفظ المسودّة',\n    scheduledSuccessfully: 'تم الجدولة بنجاح.',\n    schedulePublish: 'جدول النشر',\n    selectLocales: 'حدّد اللّغات المراد عرضها',\n    selectVersionToCompare: 'حدّد نسخة للمقارنة',\n    showingVersionsFor: 'يتمّ عرض النًّسخ ل:',\n    showLocales: 'اظهر اللّغات:',\n    specificVersion: 'الإصدار المحدد',\n    status: 'الحالة',\n    unpublish: 'الغاء النّشر',\n    unpublishing: 'يتمّ الغاء النّشر...',\n    version: 'النّسخة',\n    versionAgo: 'منذ {{distance}}',\n    versionCount_many: 'تمّ العثور على {{count}} نُسخ',\n    versionCount_none: 'لم يتمّ العثور على أيّ من النّسخ',\n    versionCount_one: 'تمّ العثور على {{count}} من النّسخ',\n    versionCount_other: 'تمّ العثور على {{count}} نُسخ',\n    versionCreatedOn: 'تمّ ﻹنشاء النّسخة في {{version}}:',\n    versionID: 'مُعرّف النّسخة',\n    versions: 'النُّسَخ',\n    viewingVersion: 'يتمّ استعراض نسخة ل {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'يتمّ استعراض نسخة للاعداد العامّ {{entityLabel}}',\n    viewingVersions: 'يتمّ استعراض النُّسَخ ل {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'يتمّ استعراض النُّسَخ للاعداد العامّ {{entityLabel}}',\n  },\n}\n\nexport const ar: Language = {\n  dateFNSKey: 'ar',\n  translations: arTranslations,\n}\n"], "names": ["arTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "ar", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCAAkC;QAClCC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBAAgB;QAChBC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BAA2B;QAC3BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCAAmC;QACnCC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,+BACE;QACFC,gBAAgB;QAChBC,uBAAuB;QACvBC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBAAqB;QACrBC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BAA0B;QAC1BC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}