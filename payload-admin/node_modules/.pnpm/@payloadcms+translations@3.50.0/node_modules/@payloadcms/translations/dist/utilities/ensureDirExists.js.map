{"version": 3, "sources": ["../../src/utilities/ensureDirExists.ts"], "sourcesContent": ["import fs from 'fs'\n\nexport function ensureDirectoryExists(directory: string) {\n  try {\n    if (!fs.existsSync(directory)) {\n      fs.mkdirSync(directory, { recursive: true })\n    }\n  } catch (error) {\n    const msg = error instanceof Error ? error.message : 'Unknown error'\n    // eslint-disable-next-line no-console\n    console.error(`Error creating directory '${directory}': ${msg}`)\n  }\n}\n"], "names": ["fs", "ensureDirectoryExists", "directory", "existsSync", "mkdirSync", "recursive", "error", "msg", "Error", "message", "console"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AAEnB,OAAO,SAASC,sBAAsBC,SAAiB;IACrD,IAAI;QACF,IAAI,CAACF,GAAGG,UAAU,CAACD,YAAY;YAC7BF,GAAGI,SAAS,CAACF,WAAW;gBAAEG,WAAW;YAAK;QAC5C;IACF,EAAE,OAAOC,OAAO;QACd,MAAMC,MAAMD,iBAAiBE,QAAQF,MAAMG,OAAO,GAAG;QACrD,sCAAsC;QACtCC,QAAQJ,KAAK,CAAC,CAAC,0BAA0B,EAAEJ,UAAU,GAAG,EAAEK,KAAK;IACjE;AACF"}