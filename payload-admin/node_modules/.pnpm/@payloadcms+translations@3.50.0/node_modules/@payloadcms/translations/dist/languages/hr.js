export const hrTranslations = {
    authentication: {
        account: '<PERSON><PERSON><PERSON>',
        accountOfCurrentUser: '<PERSON><PERSON><PERSON> trenutnog korisnika',
        accountVerified: '<PERSON><PERSON><PERSON> je uspješno verificiran.',
        alreadyActivated: 'Već aktivirano',
        alreadyLoggedIn: '<PERSON>e<PERSON> prijavljeni',
        apiKey: 'API ključ',
        authenticated: 'Autenticiran',
        backToLogin: 'Natrag na prijavu',
        beginCreateFirstUser: 'Za početak, izradite prvog korisnika.',
        changePassword: 'Promijeni lozinku',
        checkYourEmailForPasswordReset: 'Ako je e-mail adresa povezana s računom, uskoro ćete primiti upute za resetiranje lozinke. Molimo provjerite svoju mapu za neželjenu poštu ili spam ako ne vidite e-mail u svojoj pristigloj pošti.',
        confirmGeneration: 'Potvrdi generiranje',
        confirmPassword: 'Potvrdi lozinku',
        createFirstUser: 'I<PERSON>radi prvog korisnika',
        emailNotValid: 'E-mail nije ispravan',
        emailOrUsername: 'E-mail ili korisničko ime',
        emailSent: 'E-mail poslan',
        emailVerified: 'E-mail uspješno verificiran.',
        enableAPIKey: 'Omogući API ključ',
        failedToUnlock: 'Otključavanje nije uspjelo.',
        forceUnlock: 'Prisilno otključaj',
        forgotPassword: 'Zaboravljena lozinka',
        forgotPasswordEmailInstructions: 'Molimo unesite svoju e-mail adresu. Primit ćete poruku s uputama za ponovno postavljanje lozinke.',
        forgotPasswordQuestion: 'Zaboravljena lozinka?',
        forgotPasswordUsernameInstructions: 'Molimo unesite vaše korisničko ime ispod. Upute o tome kako resetirati vašu lozinku bit će poslane na e-adresu povezanu s vašim korisničkim imenom.',
        generate: 'Generiraj',
        generateNewAPIKey: 'Generiraj novi API ključ',
        generatingNewAPIKeyWillInvalidate: 'Generiranje novog API ključa će <1>poništiti</1> prethodni ključ. Jeste li sigurni da želite nastaviti?',
        lockUntil: 'Zaključaj dok',
        logBackIn: 'Ponovno se prijavite',
        loggedIn: 'Za prijavu s drugim korisničkim računom potrebno je prvo <0>odjaviti se</0>',
        loggedInChangePassword: 'Da biste promijenili lozinku, otvorite svoj <0>račun</0> i promijenite je tamo.',
        loggedOutInactivity: 'Odjavljeni ste zbog neaktivnosti.',
        loggedOutSuccessfully: 'Uspješno ste odjavljeni.',
        loggingOut: 'Odjava u tijeku...',
        login: 'Prijava',
        loginAttempts: 'Pokušaji prijave',
        loginUser: 'Prijava korisnika',
        loginWithAnotherUser: 'Za prijavu s drugim korisničkim računom potrebno je prvo <0>odjaviti se</0>',
        logOut: 'Odjava',
        logout: 'Odjava',
        logoutSuccessful: 'Odjava uspješna.',
        logoutUser: 'Odjava korisnika',
        newAccountCreated: 'Novi račun je izrađen. Pristupite računu klikom na: <a href="{{serverURL}}">{{serverURL}}</a>. Molimo kliknite na sljedeću poveznicu ili zalijepite URL, koji se nalazi ispod, u preglednik da biste potvrdili svoju e-mail adresu: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Nakon što potvrdite e-mail adresu, moći ćete se prijaviti.',
        newAPIKeyGenerated: 'New API ključ generiran.',
        newPassword: 'Nova lozinka',
        passed: 'Autentifikacija je prošla',
        passwordResetSuccessfully: 'Lozinka uspješno resetirana.',
        resetPassword: 'Resetiranje lozinke',
        resetPasswordExpiration: 'Rok trajanja resetiranja lozinke',
        resetPasswordToken: 'Resetiranje tokena lozinke',
        resetYourPassword: 'Resetirajte svoju lozinku',
        stayLoggedIn: 'Ostanite prijavljeni',
        successfullyRegisteredFirstUser: 'Uspješno registriran prvi korisnik.',
        successfullyUnlocked: 'Uspješno otključano',
        tokenRefreshSuccessful: 'Osvježavanje tokena uspješno.',
        unableToVerify: 'Nije moguće potvrditi',
        username: 'Korisničko ime',
        usernameNotValid: 'Uneseno korisničko ime nije valjano.',
        verified: 'Potvrđeno',
        verifiedSuccessfully: 'Uspješno potvrđeno',
        verify: 'Potvrdi',
        verifyUser: 'Potvrdi korisnika',
        verifyYourEmail: 'Potvrdi svoju e-mail adresu',
        youAreInactive: 'Neaktivni ste već neko vrijeme i uskoro ćete biti automatski odjavljeni zbog vlastite sigurnosti. Želite li ostati prijavljeni?',
        youAreReceivingResetPassword: 'Primili ste ovo jer ste Vi (ili netko drugi) zatražili promjenu lozinke za Vaš račun. Molimo kliknite na poveznicu ili zalijepite ovo u svoje preglednik da biste završili proces:',
        youDidNotRequestPassword: 'Ako niste zatražili ovo, molimo ignorirajte ovaj e-mail i Vaša će lozinka ostati nepromijenjena.'
    },
    error: {
        accountAlreadyActivated: 'Ovaj račun je već aktiviran.',
        autosaving: 'Nastao je problem pri automatskom spremanju ovog dokumenta.',
        correctInvalidFields: 'Molimo ispravite neispravna polja.',
        deletingFile: 'Dogodila se pogreška pri brisanju datoteke.',
        deletingTitle: 'Dogodila se pogreška pri brisanju {{title}}. Molimo provjerite svoju internet vezu i pokušajte ponovno.',
        documentNotFound: 'Dokument s ID-om {{id}} nije mogao biti pronađen. Možda je izbrisan ili nikad nije postojao, ili možda nemate pristup njemu.',
        emailOrPasswordIncorrect: 'E-mail adresa ili lozinka netočni.',
        followingFieldsInvalid_one: 'Ovo polje je neispravno:',
        followingFieldsInvalid_other: 'Ova polja su neispravna:',
        incorrectCollection: 'Neispravna kolekcija',
        insufficientClipboardPermissions: 'Pristup međuspremniku odbijen. Provjerite svoja dopuštenja za međuspremnik.',
        invalidClipboardData: 'Nevažeći podaci u međuspremniku.',
        invalidFileType: 'Neispravan tip datoteke',
        invalidFileTypeValue: 'Neispravan tip datoteke: {{value}}',
        invalidRequestArgs: 'Nevažeći argumenti u zahtjevu: {{args}}',
        loadingDocument: 'Došlo je do problema pri učitavanju dokumenta čiji je ID {{id}}.',
        localesNotSaved_one: 'Sljedeću lokalnu postavku nije bilo moguće spremiti:',
        localesNotSaved_other: 'Sljedeće lokalne postavke nije bilo moguće spremiti:',
        logoutFailed: 'Odjava nije uspjela.',
        missingEmail: 'Nedostaje e-mail.',
        missingIDOfDocument: 'Nedostaje ID dokumenta da bi se ažurirao.',
        missingIDOfVersion: 'Nedostaje ID verzije.',
        missingRequiredData: 'Nedostaju obvezni podaci.',
        noFilesUploaded: 'Nijedna datoteka nije učitana.',
        noMatchedField: 'Nema podudarajućih polja za "{{label}}"',
        notAllowedToAccessPage: 'Nemate dopuštenje pristupiti ovoj stranici.',
        notAllowedToPerformAction: 'Nemate dopuštenje izvršiti ovu radnju.',
        notFound: 'Traženi resurs nije pronađen.',
        noUser: 'Nema korisnika',
        previewing: 'Došlo je do problema pri pregledavanju ovog dokumenta.',
        problemUploadingFile: 'Došlo je do problema pri učitavanju datoteke.',
        restoringTitle: 'Došlo je do pogreške prilikom vraćanja {{title}}. Provjerite svoju vezu i pokušajte ponovno.',
        tokenInvalidOrExpired: 'Token je neispravan ili je istekao.',
        tokenNotProvided: 'Token nije pružen.',
        unableToCopy: 'Nije moguće kopirati.',
        unableToDeleteCount: 'Nije moguće izbrisati {{count}} od {{total}} {{label}}.',
        unableToReindexCollection: 'Pogreška pri ponovnom indeksiranju kolekcije {{collection}}. Operacija je prekinuta.',
        unableToUpdateCount: 'Nije moguće ažurirati {{count}} od {{total}} {{label}}.',
        unauthorized: 'Neovlašteno, morate biti prijavljeni da biste uputili ovaj zahtjev.',
        unauthorizedAdmin: 'Neovlašteno, ovaj korisnik nema pristup administratorskom panelu.',
        unknown: 'Došlo je do nepoznate pogreške.',
        unPublishingDocument: 'Došlo je do problema pri poništavanju objave ovog dokumenta.',
        unspecific: 'Došlo je do pogreške.',
        unverifiedEmail: 'Molimo potvrdite svoju e-mail adresu prije prijave.',
        userEmailAlreadyRegistered: 'Korisnik s navedenom e-mail adresom je već registriran.',
        userLocked: 'Ovaj korisnik je zaključan zbog previše neuspješnih pokušaja prijave.',
        usernameAlreadyRegistered: 'Korisnik s navedenim korisničkim imenom već je registriran.',
        usernameOrPasswordIncorrect: 'Korisničko ime ili lozinka koju ste unijeli su netočni.',
        valueMustBeUnique: 'Vrijednost mora biti jedinstvena.',
        verificationTokenInvalid: 'Verifikacijski token je neispravan.'
    },
    fields: {
        addLabel: 'Dodaj {{label}}',
        addLink: 'Dodaj poveznicu',
        addNew: 'Dodaj novi',
        addNewLabel: 'Dodaj novi {{label}}',
        addRelationship: 'Dodaj odnos',
        addUpload: 'Dodaj učitavanje',
        block: 'blokiranje',
        blocks: 'blokiranja',
        blockType: 'Vrsta blokiranja',
        chooseBetweenCustomTextOrDocument: 'Izaberite između unošenja prilagođenog teksta URL ili poveznice na drugi dokument.',
        chooseDocumentToLink: 'Odaberite dokument koji želite povezati.',
        chooseFromExisting: 'Odaberite iz postojećih.',
        chooseLabel: 'Odaberite {{label}}',
        collapseAll: 'Sažmi sve',
        customURL: 'Prilagođeni URL',
        editLabelData: 'Uredi {{label}} podatke',
        editLink: 'Uredi poveznicu',
        editRelationship: 'Uredi odnos',
        enterURL: 'Unesi URL',
        internalLink: 'Interna poveznika',
        itemsAndMore: '{{items}} i {{count}} više',
        labelRelationship: '{{label}} veza',
        latitude: 'Zemljopisna širina',
        linkedTo: 'Povezan s <0>{{label}}</0>',
        linkType: 'Tip poveznce',
        longitude: 'Zemljopisna dužina',
        newLabel: 'Novo {{label}}',
        openInNewTab: 'Otvori u novoj kartici.',
        passwordsDoNotMatch: 'Lozinke nisu iste.',
        relatedDocument: 'Povezani dokument',
        relationTo: 'Veza sa',
        removeRelationship: 'Ukloni vezu',
        removeUpload: 'Ukloni prijenos',
        saveChanges: 'Spremi promjene',
        searchForBlock: 'Potraži blok',
        selectExistingLabel: 'Odaberi postojeće {{label}}',
        selectFieldsToEdit: 'Odaberite polja za uređivanje',
        showAll: 'Pokaži sve',
        swapRelationship: 'Zamijeni vezu',
        swapUpload: 'Zamijeni prijenos',
        textToDisplay: 'Tekst za prikaz',
        toggleBlock: 'Prebaci blok',
        uploadNewLabel: 'Učitaj novi {{label}}'
    },
    folder: {
        browseByFolder: 'Pregledajte po mapi',
        byFolder: 'Po mapi',
        deleteFolder: 'Izbriši mapu',
        folderName: 'Naziv mape',
        folders: 'Mape',
        folderTypeDescription: 'Odaberite koja vrsta dokumenata kolekcije treba biti dozvoljena u ovoj mapi.',
        itemHasBeenMoved: '{{title}} je premješten u {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} je premješten u korijensku mapu.',
        itemsMovedToFolder: '{{title}} premješteno u {{folderName}}',
        itemsMovedToRoot: '{{title}} premješten u korijensku mapu',
        moveFolder: 'Premjesti mapu',
        moveItemsToFolderConfirmation: 'Upravo se spremate premjestiti <1>{{count}} {{label}}</1> u <2>{{toFolder}}</2>. Jeste li sigurni?',
        moveItemsToRootConfirmation: 'Na korak ste da premjestite <1>{{count}} {{label}}</1> u korijensku mapu. Jeste li sigurni?',
        moveItemToFolderConfirmation: 'Upravo ćete premjestiti <1>{{title}}</1> u <2>{{toFolder}}</2>. Jeste li sigurni?',
        moveItemToRootConfirmation: 'Upravo ćete premjestiti <1>{{title}}</1> u osnovnu mapu. Jeste li sigurni?',
        movingFromFolder: 'Premještanje {{title}} iz {{fromFolder}}',
        newFolder: 'Nova mapa',
        noFolder: 'Nema mape',
        renameFolder: 'Preimenuj mapu',
        searchByNameInFolder: 'Pretraživanje po imenu u {{folderName}}',
        selectFolderForItem: 'Odaberite mapu za {{title}}'
    },
    general: {
        name: 'Ime',
        aboutToDelete: 'Izbrisat ćete {{label}} <1>{{title}}</1>. Jeste li sigurni?',
        aboutToDeleteCount_many: 'Upravo ćete izbrisati {{count}} {{label}}',
        aboutToDeleteCount_one: 'Upravo ćete izbrisati {{count}} {{label}}',
        aboutToDeleteCount_other: 'Upravo ćete izbrisati {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Na rubu ste trajnog brisanja {{label}} <1>{{title}}</1>. Jeste li sigurni?',
        aboutToPermanentlyDeleteTrash: 'Na rubu ste trajnog brisanja <0>{{count}}</0> <1>{{label}}</1> iz smeća. Jeste li sigurni?',
        aboutToRestore: 'Na rubu ste obnoviti {{label}} <1>{{title}}</1>. Jeste li sigurni?',
        aboutToRestoreAsDraft: 'Uskoro ćete vratiti {{label}} <1>{{title}}</1> kao skicu. Jeste li sigurni?',
        aboutToRestoreAsDraftCount: 'Uskoro ćete obnoviti {{count}} {{label}} kao nacrt',
        aboutToRestoreCount: 'Uskoro ćete obnoviti {{count}} {{label}}',
        aboutToTrash: 'Na rubu ste premještanja {{label}} <1>{{title}}</1> u otpad. Jeste li sigurni?',
        aboutToTrashCount: 'Na korak ste od premještanja {{count}} {{label}} u smeće',
        addBelow: 'Dodaj ispod',
        addFilter: 'Dodaj filter',
        adminTheme: 'Administratorska tema',
        all: 'Svi',
        allCollections: 'Sve kolekcije',
        allLocales: 'Sve lokalne postavke',
        and: 'i',
        anotherUser: 'Drugi korisnik',
        anotherUserTakenOver: 'Drugi korisnik je preuzeo uređivanje ovog dokumenta.',
        applyChanges: 'Primijeni promjene',
        ascending: 'Uzlazno',
        automatic: 'Automatsko',
        backToDashboard: 'Natrag na nadzornu ploču',
        cancel: 'Otkaži',
        changesNotSaved: 'Vaše promjene nisu spremljene. Ako izađete sada, izgubit ćete promjene.',
        clear: 'Jasan',
        clearAll: 'Očisti sve',
        close: 'Zatvori',
        collapse: 'Sažmi',
        collections: 'Kolekcije',
        columns: 'Stupci',
        columnToSort: 'Stupac za sortiranje',
        confirm: 'Potvrdi',
        confirmCopy: 'Potvrdi kopiju',
        confirmDeletion: 'Potvrdi brisanje',
        confirmDuplication: 'Potvrdi duplikaciju',
        confirmMove: 'Potvrdi premještanje',
        confirmReindex: 'Ponovno indeksirati sve {{collections}}?',
        confirmReindexAll: 'Ponovno indeksirati sve kolekcije?',
        confirmReindexDescription: 'Ovo će ukloniti postojeće indekse i ponovno indeksirati dokumente u {{collections}} kolekcijama.',
        confirmReindexDescriptionAll: 'Ovo će ukloniti postojeće indekse i ponovno indeksirati dokumente u svim kolekcijama.',
        confirmRestoration: 'Potvrdite obnovu',
        copied: 'Kopirano',
        copy: 'Kopiraj',
        copyField: 'Kopiraj polje',
        copying: 'Kopiranje',
        copyRow: 'Kopiraj redak',
        copyWarning: 'Na rubu ste prepisivanja {{to}} s {{from}} za {{label}} {{title}}. Jeste li sigurni?',
        create: 'Izradi',
        created: 'Kreirano',
        createdAt: 'Izrađeno u',
        createNew: 'Izradi novo',
        createNewLabel: 'Izradi novo {{label}}',
        creating: 'U izradi',
        creatingNewLabel: 'Izrađivanje novog {{label}}',
        currentlyEditing: 'trenutno uređuje ovaj dokument. Ako preuzmete, bit će im onemogućeno daljnje uređivanje i mogu izgubiti nespremljene promjene.',
        custom: 'Prilagođen',
        dark: 'Tamno',
        dashboard: 'Nadzorna ploča',
        delete: 'Izbriši',
        deleted: 'Izbrisano',
        deletedAt: 'Izbrisano U',
        deletedCountSuccessfully: 'Uspješno izbrisano {{count}} {{label}}.',
        deletedSuccessfully: 'Uspješno izbrisano.',
        deletePermanently: 'Preskoči koš i trajno izbriši',
        deleting: 'Brisanje...',
        depth: 'Dubina',
        descending: 'Silazno',
        deselectAllRows: 'Odznači sve redove',
        document: 'Dokument',
        documentIsTrashed: 'Ova {{label}} je u smeću i dostupna je samo za čitanje.',
        documentLocked: 'Dokument je zaključan',
        documents: 'Dokumenti',
        duplicate: 'Duplikat',
        duplicateWithoutSaving: 'Dupliciraj bez spremanja promjena',
        edit: 'Uredi',
        editAll: 'Uredi sve',
        editedSince: 'Uređeno od',
        editing: 'Uređivanje',
        editingLabel_many: 'Uređivanje {{count}} {{label}}',
        editingLabel_one: 'Uređivanje {{count}} {{label}}',
        editingLabel_other: 'Uređivanje {{count}} {{label}}',
        editingTakenOver: 'Uređivanje preuzeto',
        editLabel: 'Uredi {{label}}',
        email: 'Email',
        emailAddress: 'Email adresa',
        emptyTrash: 'Isprazni smeće',
        emptyTrashLabel: 'Isprazni {{label}} kantu za smeće',
        enterAValue: 'Unesi vrijednost',
        error: 'Greška',
        errors: 'Greške',
        exitLivePreview: 'Izađi iz Pregleda uživo',
        export: 'Izvoz',
        fallbackToDefaultLocale: 'Vraćanje na zadani jezik',
        false: 'Netočno',
        filter: 'Filter',
        filters: 'Filteri',
        filterWhere: 'Filter {{label}} gdje',
        globals: 'Globali',
        goBack: 'Vrati se',
        groupByLabel: 'Grupiraj po {{label}}',
        import: 'Uvoz',
        isEditing: 'uređuje',
        item: 'stavka',
        items: 'stavke',
        language: 'Jezik',
        lastModified: 'Zadnja promjena',
        leaveAnyway: 'Svejedno napusti',
        leaveWithoutSaving: 'Napusti bez spremanja',
        light: 'Svijetlo',
        livePreview: 'Pregled',
        loading: 'Učitavanje',
        locale: 'Jezik',
        locales: 'Prijevodi',
        menu: 'Izbornik',
        moreOptions: 'Više opcija',
        move: 'Pomakni',
        moveConfirm: 'Upravo ćete premjestiti {{count}} {{label}} u <1>{{destination}}</1>. Jeste li sigurni?',
        moveCount: 'Pomakni {{count}} {{label}}',
        moveDown: 'Pomakni dolje',
        moveUp: 'Pomakni gore',
        moving: 'Pomicanje',
        movingCount: 'Pomicanje {{count}} {{label}}',
        newPassword: 'Nova lozinka',
        next: 'Sljedeće',
        no: 'Ne',
        noDateSelected: 'Nije odabran datum',
        noFiltersSet: 'Nema postavljenih filtera',
        noLabel: '<Nema {{label}}>',
        none: 'Nijedan',
        noOptions: 'Nema opcija',
        noResults: 'Nije pronađen nijedan {{label}}. Ili {{label}} još uvijek ne postoji ili nijedan od odgovara postavljenim filterima.',
        notFound: 'Nije pronađeno',
        nothingFound: 'Ništa nije pronađeno',
        noTrashResults: 'Nema {{label}} u smeću.',
        noUpcomingEventsScheduled: 'Nema zakazanih nadolazećih događanja.',
        noValue: 'Bez vrijednosti',
        of: 'od',
        only: 'Samo',
        open: 'Otvori',
        or: 'ili',
        order: 'Poredak',
        overwriteExistingData: 'Prepišite postojeće podatke u polju',
        pageNotFound: 'Stranica nije pronađena',
        password: 'Lozinka',
        pasteField: 'Zalijepi polje',
        pasteRow: 'Zalijepi redak',
        payloadSettings: 'Payload postavke',
        permanentlyDelete: 'Trajno izbriši',
        permanentlyDeletedCountSuccessfully: 'Trajno izbrisano {{count}} {{label}} uspješno.',
        perPage: 'Po stranici: {{limit}}',
        previous: 'Prethodni',
        reindex: 'Ponovno indeksiraj',
        reindexingAll: 'Ponovno indeksiranje svih {{collections}}.',
        remove: 'Ukloni',
        rename: 'Preimenuj',
        reset: 'Ponovno postavi',
        resetPreferences: 'Ponovno postavljanje postavki',
        resetPreferencesDescription: 'Ovo će vratiti sve vaše postavke na zadane vrijednosti.',
        resettingPreferences: 'Ponovno postavljanje postavki.',
        restore: 'Obnovi',
        restoreAsPublished: 'Vrati kao objavljenu verziju',
        restoredCountSuccessfully: 'Uspješno obnovljeno {{count}} {{label}}.',
        restoring: 'Poštujte značenje izvornog teksta unutar konteksta Payloada. Evo popisa uobičajenih pojmova Payloada koji imaju vrlo specifična značenja:\n    - Kolekcija: Kolekcija je skup dokumenata koji dijele zajedničku strukturu i svrhu. Kolekcije se koriste za organiziranje i upravljanje sadržajem u Payloadu.\n    - Polje: Polje je specifičan dio podataka unutar dokumenta u kolekciji. Polja definiraju strukturu i vrstu podataka koji',
        row: 'Red',
        rows: 'Redovi',
        save: 'Spremi',
        saving: 'Spremanje...',
        schedulePublishFor: 'Zakazano objavljivanje za {{title}}',
        searchBy: 'Traži po {{label}}',
        select: 'Odaberite',
        selectAll: 'Odaberite sve {{count}} {{label}}',
        selectAllRows: 'Odaberite sve redove',
        selectedCount: '{{count}} {{label}} odabrano',
        selectLabel: 'Odaberite {{label}}',
        selectValue: 'Odaberi vrijednost',
        showAllLabel: 'Prikaži sve {{label}}',
        sorryNotFound: 'Nažalost, ne postoji ništa što odgovara vašem zahtjevu.',
        sort: 'Sortiraj',
        sortByLabelDirection: 'Sortiraj prema {{label}} {{direction}}',
        stayOnThisPage: 'Ostani na ovoj stranici',
        submissionSuccessful: 'Uspješno slanje',
        submit: 'Podnesi',
        submitting: 'Podnošenje...',
        success: 'Uspjeh',
        successfullyCreated: '{{label}} uspješno izrađeno.',
        successfullyDuplicated: '{{label}} uspješno duplicirano.',
        successfullyReindexed: 'Uspješno ponovno indeksirano {{count}} od {{total}} dokumenata iz {{collections}} kolekcija.',
        takeOver: 'Preuzmi',
        thisLanguage: 'Hrvatski',
        time: 'Vrijeme',
        timezone: 'Vremenska zona',
        titleDeleted: '{{label}} "{{title}}" uspješno izbrisano.',
        titleRestored: '{{label}} "{{title}}" uspješno je obnovljeno.',
        titleTrashed: '{{label}} "{{title}}" premješteno u smeće.',
        trash: 'Otpad',
        trashedCountSuccessfully: '{{count}} {{label}} premješteno u smeće.',
        true: 'Istinito',
        unauthorized: 'Neovlašteno',
        unsavedChanges: 'Imate nespremljene promjene. Spremite ili odbacite prije nastavka.',
        unsavedChangesDuplicate: 'Imate nespremljene promjene. Želite li nastaviti s dupliciranjem?',
        untitled: 'Bez naslova',
        upcomingEvents: 'Nadolazeći događaji',
        updatedAt: 'Ažurirano u',
        updatedCountSuccessfully: 'Uspješno ažurirano {{count}} {{label}}.',
        updatedLabelSuccessfully: 'Uspješno ažurirano {{label}}.',
        updatedSuccessfully: 'Uspješno ažurirano.',
        updateForEveryone: 'Ažuriranje za sve',
        updating: 'Ažuriranje',
        uploading: 'Prijenos',
        uploadingBulk: 'Prenosim {{current}} od {{total}}',
        user: 'Korisnik',
        username: 'Korisničko ime',
        users: 'Korisnici',
        value: 'Vrijednost',
        viewing: 'Pregledavanje',
        viewReadOnly: 'Pogledaj samo za čitanje',
        welcome: 'Dobrodošli',
        yes: 'Da'
    },
    localization: {
        cannotCopySameLocale: 'Ne može se kopirati na istu lokaciju',
        copyFrom: 'Kopiraj iz',
        copyFromTo: 'Kopiranje iz {{from}} u {{to}}',
        copyTo: 'Kopiraj na',
        copyToLocale: 'Kopiraj na lokaciju',
        localeToPublish: 'Lokacija za objavu',
        selectLocaleToCopy: 'Odaberite mjesto za kopiranje'
    },
    operators: {
        contains: 'sadrži',
        equals: 'jednako',
        exists: 'postoji',
        intersects: 'presijeca',
        isGreaterThan: 'je veće od',
        isGreaterThanOrEqualTo: 'je veće od ili jednako',
        isIn: 'je u',
        isLessThan: 'manje je od',
        isLessThanOrEqualTo: 'manje je ili jednako',
        isLike: 'je kao',
        isNotEqualTo: 'nije jednako',
        isNotIn: 'nije unutra',
        isNotLike: 'nije kao',
        near: 'blizu',
        within: 'unutar'
    },
    upload: {
        addFile: 'Dodaj datoteku',
        addFiles: 'Dodaj datoteke',
        bulkUpload: 'Masovno dodavanje',
        crop: 'Izreži',
        cropToolDescription: 'Povucite kutove odabranog područja, nacrtajte novo područje ili prilagodite vrijednosti ispod.',
        download: 'Preuzmi',
        dragAndDrop: 'Povucite i ispustite datoteku',
        dragAndDropHere: 'ili povucite i ispustite datoteku ovdje',
        editImage: 'Uredi sliku',
        fileName: 'Ime datoteke',
        fileSize: 'Veličina datoteke',
        filesToUpload: 'Datoteke za učitavanje',
        fileToUpload: 'Datoteka za prijenos',
        focalPoint: 'Središnja točka',
        focalPointDescription: 'Povucite središnju točku izravno na pregledu ili prilagodite vrijednosti ispod.',
        height: 'Visina',
        lessInfo: 'Manje informacija',
        moreInfo: 'Više informacija',
        noFile: 'Nema datoteke',
        pasteURL: 'Zalijepi URL',
        previewSizes: 'Veličine pregleda',
        selectCollectionToBrowse: 'Odaberite kolekciju za pregled',
        selectFile: 'Odaberite datoteku',
        setCropArea: 'Postavi područje usjeva',
        setFocalPoint: 'Postavi fokusnu točku',
        sizes: 'Veličine',
        sizesFor: 'Veličine za {{label}}',
        width: 'Širina'
    },
    validation: {
        emailAddress: 'Molimo unesite valjanu e-mail adresu.',
        enterNumber: 'Molimo unesite valjani broj.',
        fieldHasNo: 'Ovo polje nema {{label}}',
        greaterThanMax: '{{value}} exceeds the maximum allowable {{label}} limit of {{max}}.',
        invalidInput: 'Ovo polje ima neispravan unos.',
        invalidSelection: 'Ovo polje ima neispravan odabir.',
        invalidSelections: 'Ovo polje ima sljedeće neispravne odabire:',
        lessThanMin: '{{value}} is below the minimum allowable {{label}} limit of {{min}}.',
        limitReached: 'Dosegnut je limit, može se dodati samo {{max}} stavki.',
        longerThanMin: 'Ova vrijednost mora biti duža od minimalne dužine od {{minLength}} znakova',
        notValidDate: '"{{value}}" nije valjan datum.',
        required: 'Ovo polje je obvezno.',
        requiresAtLeast: 'Ovo polje zahtjeva minimalno {{count}} {{label}}.',
        requiresNoMoreThan: 'Ovo polje zahtjeva ne više od {{count}} {{label}}.',
        requiresTwoNumbers: 'Ovo polje zahtjeva dva broja.',
        shorterThanMax: 'Ova vrijednost mora biti kraća od maksimalne dužine od {{maxLength}} znakova',
        timezoneRequired: 'Potrebna je vremenska zona.',
        trueOrFalse: 'Ovo polje može biti samo točno ili netočno',
        username: 'Unesite važeće korisničko ime. Može sadržavati slova, brojeve, crtice, točke i donje crte.',
        validUploadID: 'Ovo polje nije valjani ID prijenosa.'
    },
    version: {
        type: 'Tip',
        aboutToPublishSelection: 'Upravo ćete objaviti sve {{label}} u izboru. Jeste li sigurni?',
        aboutToRestore: 'Vratit ćete {{label}} dokument u stanje u kojem je bio {{versionDate}}',
        aboutToRestoreGlobal: 'Vratit ćete globalni {{label}} u stanje u kojem je bio {{versionDate}}.',
        aboutToRevertToPublished: 'Vratit ćete promjene u dokumentu u objavljeno stanje. Jeste li sigurni? ',
        aboutToUnpublish: 'Poništit ćete objavu ovog dokumenta. Jeste li sigurni?',
        aboutToUnpublishSelection: 'Upravo ćete poništiti objavu svih {{label}} u odabiru. Jeste li sigurni?',
        autosave: 'Automatsko spremanje',
        autosavedSuccessfully: 'Automatsko spremanje uspješno.',
        autosavedVersion: 'Verzija automatski spremljenog dokumenta',
        changed: 'Promijenjeno',
        changedFieldsCount_one: '{{count}} promijenjeno polje',
        changedFieldsCount_other: '{{count}} promijenjena polja',
        compareVersion: 'Usporedi verziju sa:',
        compareVersions: 'Usporedi verzije',
        comparingAgainst: 'U usporedbi s',
        confirmPublish: 'Potvrdi objavu',
        confirmRevertToSaved: 'Potvrdite vraćanje na spremljeno',
        confirmUnpublish: 'Potvrdite poništavanje objave',
        confirmVersionRestoration: 'Potvrdite vraćanje verzije',
        currentDocumentStatus: 'Trenutni {{docStatus}} dokumenta',
        currentDraft: 'Trenutni Nacrt',
        currentlyPublished: 'Trenutno objavljeno',
        currentlyViewing: 'Trenutno pregledavate',
        currentPublishedVersion: 'Trenutno Objavljena Verzija',
        draft: 'Nacrt',
        draftSavedSuccessfully: 'Nacrt uspješno spremljen.',
        lastSavedAgo: 'Zadnji put spremljeno prije {{distance}',
        modifiedOnly: 'Samo modificirano',
        moreVersions: 'Više verzija...',
        noFurtherVersionsFound: 'Nisu pronađene daljnje verzije',
        noRowsFound: '{{label}} nije pronađeno',
        noRowsSelected: 'Nije odabrana {{oznaka}}',
        preview: 'Pregled',
        previouslyDraft: 'Prethodno Nacrt',
        previouslyPublished: 'Prethodno objavljeno',
        previousVersion: 'Prethodna verzija',
        problemRestoringVersion: 'Nastao je problem pri vraćanju ove verzije',
        publish: 'Objaviti',
        publishAllLocales: 'Objavi sve lokalne postavke',
        publishChanges: 'Objavi promjene',
        published: 'Objavljeno',
        publishIn: 'Objavi na {{locale}}',
        publishing: 'Objavljivanje',
        restoreAsDraft: 'Vrati kao skicu',
        restoredSuccessfully: 'Uspješno vraćeno.',
        restoreThisVersion: 'Vrati ovu verziju',
        restoring: 'Vraćanje...',
        reverting: 'Vraćanje...',
        revertToPublished: 'Vrati na objavljeno',
        saveDraft: 'Sačuvaj nacrt',
        scheduledSuccessfully: 'Uspješno zakazano.',
        schedulePublish: 'Raspored objavljivanja',
        selectLocales: 'Odaberite jezike',
        selectVersionToCompare: 'Odaberite verziju za usporedbu',
        showingVersionsFor: 'Pokazujem verzije za:',
        showLocales: 'Prikaži jezike:',
        specificVersion: 'Specifična verzija',
        status: 'Status',
        unpublish: 'Poništi objavu',
        unpublishing: 'Poništavanje objave...',
        version: 'Verzija',
        versionAgo: 'prije {{distance}}',
        versionCount_many: '{{count}} pronađenih verzija',
        versionCount_none: 'Nema pronađenih verzija',
        versionCount_one: '{{count}} pronađena verzija',
        versionCount_other: '{{count}} pronađenih verzija',
        versionCreatedOn: '{{version}} izrađenih:',
        versionID: 'ID verzije',
        versions: 'Verzije',
        viewingVersion: 'Pregled verzije za {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Pregled verzije za globalni {{entityLabel}}',
        viewingVersions: 'Pregled verzija za {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Pregled verzije za globalni {{entityLabel}}'
    }
};
export const hr = {
    dateFNSKey: 'hr',
    translations: hrTranslations
};

//# sourceMappingURL=hr.js.map