{"version": 3, "sources": ["../src/clientKeys.ts"], "sourcesContent": ["import type { DefaultTranslationKeys } from './types.js'\n\nfunction createClientTranslationKeys<T extends DefaultTranslationKeys[]>(keys: T) {\n  return keys\n}\n\nexport const clientTranslationKeys = createClientTranslationKeys([\n  'authentication:account',\n  'authentication:accountOfCurrentUser',\n  'authentication:accountVerified',\n  'authentication:alreadyActivated',\n  'authentication:alreadyLoggedIn',\n  'authentication:apiKey',\n  'authentication:authenticated',\n  'authentication:backToLogin',\n  'authentication:beginCreateFirstUser',\n  'authentication:changePassword',\n  'authentication:checkYourEmailForPasswordReset',\n  'authentication:confirmGeneration',\n  'authentication:confirmPassword',\n  'authentication:createFirstUser',\n  'authentication:emailNotValid',\n  'authentication:usernameNotValid',\n  'authentication:emailOrUsername',\n  'authentication:emailSent',\n  'authentication:emailVerified',\n  'authentication:enableAPIKey',\n  'authentication:failedToUnlock',\n  'authentication:forceUnlock',\n  'authentication:forgotPassword',\n  'authentication:forgotPasswordEmailInstructions',\n  'authentication:forgotPasswordUsernameInstructions',\n  'authentication:forgotPasswordQuestion',\n  'authentication:generate',\n  'authentication:generateNewAPIKey',\n  'authentication:generatingNewAPIKeyWillInvalidate',\n  'authentication:logBackIn',\n  'authentication:loggedOutInactivity',\n  'authentication:loggedOutSuccessfully',\n  'authentication:loggingOut',\n  'authentication:login',\n  'authentication:logOut',\n  'authentication:loggedIn',\n  'authentication:loggedInChangePassword',\n  'authentication:logout',\n  'authentication:logoutUser',\n  'authentication:logoutSuccessful',\n  'authentication:newAPIKeyGenerated',\n  'authentication:newPassword',\n  'authentication:passed',\n  'authentication:passwordResetSuccessfully',\n  'authentication:resetPassword',\n  'authentication:stayLoggedIn',\n  'authentication:successfullyRegisteredFirstUser',\n  'authentication:successfullyUnlocked',\n  'authentication:username',\n  'authentication:unableToVerify',\n  'authentication:tokenRefreshSuccessful',\n  'authentication:verified',\n  'authentication:verifiedSuccessfully',\n  'authentication:verify',\n  'authentication:verifyUser',\n  'authentication:youAreInactive',\n\n  'error:autosaving',\n  'error:correctInvalidFields',\n  'error:deletingTitle',\n  'error:documentNotFound',\n  'error:emailOrPasswordIncorrect',\n  'error:usernameOrPasswordIncorrect',\n  'error:loadingDocument',\n  'error:insufficientClipboardPermissions',\n  'error:invalidClipboardData',\n  'error:invalidRequestArgs',\n  'error:invalidFileType',\n  'error:logoutFailed',\n  'error:noMatchedField',\n  'error:notAllowedToAccessPage',\n  'error:previewing',\n  'error:unableToCopy',\n  'error:unableToDeleteCount',\n  'error:unableToReindexCollection',\n  'error:unableToUpdateCount',\n  'error:unauthorized',\n  'error:unauthorizedAdmin',\n  'error:unknown',\n  'error:unspecific',\n  'error:unverifiedEmail',\n  'error:userEmailAlreadyRegistered',\n  'error:usernameAlreadyRegistered',\n  'error:tokenNotProvided',\n  'error:unPublishingDocument',\n  'error:problemUploadingFile',\n  'error:restoringTitle',\n\n  'fields:addLabel',\n  'fields:addLink',\n  'fields:addNew',\n  'fields:addNewLabel',\n  'fields:addRelationship',\n  'fields:addUpload',\n  'fields:block',\n  'fields:blocks',\n  'fields:blockType',\n  'fields:chooseBetweenCustomTextOrDocument',\n  'fields:customURL',\n  'fields:chooseDocumentToLink',\n  'fields:openInNewTab',\n  'fields:enterURL',\n  'fields:internalLink',\n  'fields:chooseFromExisting',\n  'fields:linkType',\n  'fields:textToDisplay',\n  'fields:collapseAll',\n  'fields:editLink',\n  'fields:editRelationship',\n  'fields:itemsAndMore',\n  'fields:labelRelationship',\n  'fields:latitude',\n  'fields:linkedTo',\n  'fields:longitude',\n  'fields:passwordsDoNotMatch',\n  'fields:removeRelationship',\n  'fields:removeUpload',\n  'fields:saveChanges',\n  'fields:searchForBlock',\n  'fields:selectFieldsToEdit',\n  'fields:showAll',\n  'fields:swapRelationship',\n  'fields:swapUpload',\n  'fields:toggleBlock',\n  'fields:uploadNewLabel',\n\n  'folder:byFolder',\n  'folder:browseByFolder',\n  'folder:deleteFolder',\n  'folder:folders',\n  'folder:folderTypeDescription',\n  'folder:folderName',\n  'folder:itemsMovedToFolder',\n  'folder:itemsMovedToRoot',\n  'folder:itemHasBeenMoved',\n  'folder:itemHasBeenMovedToRoot',\n  'folder:moveFolder',\n  'folder:movingFromFolder',\n  'folder:moveItemsToFolderConfirmation',\n  'folder:moveItemsToRootConfirmation',\n  'folder:moveItemToFolderConfirmation',\n  'folder:moveItemToRootConfirmation',\n  'folder:noFolder',\n  'folder:newFolder',\n  'folder:renameFolder',\n  'folder:searchByNameInFolder',\n  'folder:selectFolderForItem',\n\n  'general:all',\n  'general:aboutToDeleteCount',\n  'general:aboutToDelete',\n  'general:aboutToPermanentlyDelete',\n  'general:aboutToPermanentlyDeleteTrash',\n  'general:aboutToRestore',\n  'general:aboutToRestoreAsDraft',\n  'general:aboutToRestoreAsDraftCount',\n  'general:aboutToRestoreCount',\n  'general:aboutToTrash',\n  'general:aboutToTrashCount',\n  'general:addBelow',\n  'general:addFilter',\n  'general:adminTheme',\n  'general:allCollections',\n  'general:and',\n  'general:anotherUser',\n  'general:anotherUserTakenOver',\n  'general:applyChanges',\n  'general:ascending',\n  'general:automatic',\n  'general:backToDashboard',\n  'general:cancel',\n  'general:changesNotSaved',\n  'general:close',\n  'general:collapse',\n  'general:collections',\n  'general:confirmMove',\n  'general:yes',\n  'general:no',\n  'general:columns',\n  'general:columnToSort',\n  'general:confirm',\n  'general:confirmCopy',\n  'general:confirmDeletion',\n  'general:confirmDuplication',\n  'general:confirmReindex',\n  'general:confirmReindexAll',\n  'general:confirmReindexDescription',\n  'general:confirmReindexDescriptionAll',\n  'general:confirmRestoration',\n  'general:copied',\n  'general:clear',\n  'general:clearAll',\n  'general:copy',\n  'general:copyField',\n  'general:copyRow',\n  'general:copyWarning',\n  'general:copying',\n  'general:create',\n  'general:created',\n  'general:createdAt',\n  'general:createNew',\n  'general:createNewLabel',\n  'general:creating',\n  'general:creatingNewLabel',\n  'general:currentlyEditing',\n  'general:custom',\n  'general:dark',\n  'general:dashboard',\n  'general:delete',\n  'general:deleted',\n  'general:deletedAt',\n  'general:deletePermanently',\n  'general:deletedSuccessfully',\n  'general:deletedCountSuccessfully',\n  'general:deleting',\n  'general:descending',\n  'general:depth',\n  'general:deselectAllRows',\n  'general:document',\n  'general:documentIsTrashed',\n  'general:documentLocked',\n  'general:documents',\n  'general:duplicate',\n  'general:duplicateWithoutSaving',\n  'general:edit',\n  'general:editAll',\n  'general:editing',\n  'general:editingLabel',\n  'general:editingTakenOver',\n  'general:editLabel',\n  'general:editedSince',\n  'general:email',\n  'general:emailAddress',\n  'general:emptyTrash',\n  'general:emptyTrashLabel',\n  'general:enterAValue',\n  'general:error',\n  'general:errors',\n  'general:fallbackToDefaultLocale',\n  'general:false',\n  'general:filters',\n  'general:filterWhere',\n  'general:globals',\n  'general:goBack',\n  'general:groupByLabel',\n  'general:isEditing',\n  'general:item',\n  'general:items',\n  'general:language',\n  'general:lastModified',\n  'general:leaveAnyway',\n  'general:leaveWithoutSaving',\n  'general:light',\n  'general:livePreview',\n  'general:exitLivePreview',\n  'general:loading',\n  'general:locale',\n  'general:locales',\n  'general:menu',\n  'general:moreOptions',\n  'general:move',\n  'general:moveConfirm',\n  'general:moveCount',\n  'general:moveDown',\n  'general:moveUp',\n  'general:moving',\n  'general:movingCount',\n  'general:name',\n  'general:next',\n  'general:noDateSelected',\n  'general:noFiltersSet',\n  'general:noLabel',\n  'general:none',\n  'general:noOptions',\n  'general:noResults',\n  'general:notFound',\n  'general:nothingFound',\n  'general:noTrashResults',\n  'general:noUpcomingEventsScheduled',\n  'general:noValue',\n  'general:of',\n  'general:open',\n  'general:only',\n  'general:or',\n  'general:order',\n  'general:overwriteExistingData',\n  'general:pageNotFound',\n  'general:password',\n  'general:pasteField',\n  'general:pasteRow',\n  'general:payloadSettings',\n  'general:permanentlyDelete',\n  'general:permanentlyDeletedCountSuccessfully',\n  'general:perPage',\n  'general:previous',\n  'general:reindex',\n  'general:reindexingAll',\n  'general:remove',\n  'general:rename',\n  'general:reset',\n  'general:resetPreferences',\n  'general:resetPreferencesDescription',\n  'general:resettingPreferences',\n  'general:restore',\n  'general:restoreAsPublished',\n  'general:restoredCountSuccessfully',\n  'general:restoring',\n  'general:row',\n  'general:rows',\n  'general:save',\n  'general:schedulePublishFor',\n  'general:saving',\n  'general:searchBy',\n  'general:select',\n  'general:selectAll',\n  'general:selectAllRows',\n  'general:selectedCount',\n  'general:selectLabel',\n  'general:selectValue',\n  'general:showAllLabel',\n  'general:sorryNotFound',\n  'general:sort',\n  'general:sortByLabelDirection',\n  'general:stayOnThisPage',\n  'general:submissionSuccessful',\n  'general:submit',\n  'general:submitting',\n  'general:success',\n  'general:successfullyCreated',\n  'general:successfullyDuplicated',\n  'general:successfullyReindexed',\n  'general:takeOver',\n  'general:thisLanguage',\n  'general:time',\n  'general:timezone',\n  'general:titleDeleted',\n  'general:titleTrashed',\n  'general:titleRestored',\n  'general:trash',\n  'general:trashedCountSuccessfully',\n  'general:import',\n  'general:export',\n  'general:allLocales',\n  'general:true',\n  'general:upcomingEvents',\n  'general:users',\n  'general:user',\n  'general:username',\n  'general:unauthorized',\n  'general:unsavedChanges',\n  'general:unsavedChangesDuplicate',\n  'general:untitled',\n  'general:updatedAt',\n  'general:updatedLabelSuccessfully',\n  'general:updatedCountSuccessfully',\n  'general:updateForEveryone',\n  'general:updatedSuccessfully',\n  'general:updating',\n  'general:value',\n  'general:viewing',\n  'general:viewReadOnly',\n  'general:uploading',\n  'general:uploadingBulk',\n  'general:welcome',\n\n  'localization:localeToPublish',\n  'localization:copyToLocale',\n  'localization:copyFromTo',\n  'localization:selectLocaleToCopy',\n  'localization:cannotCopySameLocale',\n  'localization:copyFrom',\n  'localization:copyTo',\n\n  'operators:equals',\n  'operators:exists',\n  'operators:isNotIn',\n  'operators:isIn',\n  'operators:contains',\n  'operators:isLike',\n  'operators:isNotLike',\n  'operators:isNotEqualTo',\n  'operators:near',\n  'operators:isGreaterThan',\n  'operators:isLessThan',\n  'operators:isGreaterThanOrEqualTo',\n  'operators:isLessThanOrEqualTo',\n  'operators:within',\n  'operators:intersects',\n\n  'upload:addFile',\n  'upload:addFiles',\n  'upload:bulkUpload',\n  'upload:crop',\n  'upload:cropToolDescription',\n  'upload:dragAndDrop',\n  'upload:editImage',\n  'upload:fileToUpload',\n  'upload:filesToUpload',\n  'upload:focalPoint',\n  'upload:focalPointDescription',\n  'upload:height',\n  'upload:pasteURL',\n  'upload:previewSizes',\n  'upload:selectCollectionToBrowse',\n  'upload:selectFile',\n  'upload:setCropArea',\n  'upload:setFocalPoint',\n  'upload:sizesFor',\n  'upload:sizes',\n  'upload:width',\n  'upload:fileName',\n  'upload:fileSize',\n  'upload:noFile',\n  'upload:download',\n\n  'validation:emailAddress',\n  'validation:enterNumber',\n  'validation:fieldHasNo',\n  'validation:greaterThanMax',\n  'validation:invalidInput',\n  'validation:invalidSelection',\n  'validation:invalidSelections',\n  'validation:lessThanMin',\n  'validation:limitReached',\n  'validation:longerThanMin',\n  'validation:notValidDate',\n  'validation:required',\n  'validation:requiresAtLeast',\n  'validation:requiresNoMoreThan',\n  'validation:requiresTwoNumbers',\n  'validation:shorterThanMax',\n  'validation:trueOrFalse',\n  'validation:timezoneRequired',\n  'validation:username',\n  'validation:validUploadID',\n\n  'version:aboutToPublishSelection',\n  'version:aboutToRestore',\n  'version:aboutToRestoreGlobal',\n  'version:aboutToRevertToPublished',\n  'version:aboutToUnpublish',\n  'version:aboutToUnpublishSelection',\n  'version:autosave',\n  'version:autosavedSuccessfully',\n  'version:autosavedVersion',\n  'version:versionAgo',\n  'version:moreVersions',\n  'version:changed',\n  'version:changedFieldsCount',\n  'version:confirmRevertToSaved',\n  'version:compareVersions',\n  'version:comparingAgainst',\n  'version:currentlyViewing',\n  'version:confirmPublish',\n  'version:confirmUnpublish',\n  'version:confirmVersionRestoration',\n  'version:currentDraft',\n  'version:currentPublishedVersion',\n  'version:currentlyPublished',\n  'version:draft',\n  'version:draftSavedSuccessfully',\n  'version:lastSavedAgo',\n  'version:modifiedOnly',\n  'version:noFurtherVersionsFound',\n  'version:noRowsFound',\n  'version:noRowsSelected',\n  'version:preview',\n  'version:previouslyDraft',\n  'version:previouslyPublished',\n  'version:previousVersion',\n  'version:problemRestoringVersion',\n  'version:publish',\n  'version:publishAllLocales',\n  'version:publishChanges',\n  'version:published',\n  'version:publishIn',\n  'version:publishing',\n  'version:restoreAsDraft',\n  'version:restoredSuccessfully',\n  'version:restoreThisVersion',\n  'version:restoring',\n  'version:reverting',\n  'version:revertToPublished',\n  'version:saveDraft',\n  'version:scheduledSuccessfully',\n  'version:schedulePublish',\n  'version:selectLocales',\n  'version:selectVersionToCompare',\n  'version:showLocales',\n  'version:specificVersion',\n  'version:status',\n  'version:type',\n  'version:unpublish',\n  'version:unpublishing',\n  'version:versionCreatedOn',\n  'version:versionID',\n  'version:version',\n  'version:versions',\n  'version:viewingVersion',\n  'version:viewingVersionGlobal',\n  'version:viewingVersions',\n  'version:viewingVersionsGlobal',\n])\n"], "names": ["createClientTranslationKeys", "keys", "clientTranslationKeys"], "mappings": "AAEA,SAASA,4BAAgEC,IAAO;IAC9E,OAAOA;AACT;AAEA,OAAO,MAAMC,wBAAwBF,4BAA4B;IAC/D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,EAAC"}