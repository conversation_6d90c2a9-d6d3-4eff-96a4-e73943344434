import type { DefaultTranslationKeys, DefaultTranslationsObject, I18n, Language } from '../types.js';
/**
 * @function getTranslationString
 *
 * Gets a translation string from a translations object
 *
 * @returns string
 */
export declare const getTranslationString: <TTranslations = {
    authentication: {
        account: string;
        accountOfCurrentUser: string;
        accountVerified: string;
        alreadyActivated: string;
        alreadyLoggedIn: string;
        apiKey: string;
        authenticated: string;
        backToLogin: string;
        beginCreateFirstUser: string;
        changePassword: string;
        checkYourEmailForPasswordReset: string;
        confirmGeneration: string;
        confirmPassword: string;
        createFirstUser: string;
        emailNotValid: string;
        emailOrUsername: string;
        emailSent: string;
        emailVerified: string;
        enableAPIKey: string;
        failedToUnlock: string;
        forceUnlock: string;
        forgotPassword: string;
        forgotPasswordEmailInstructions: string;
        forgotPasswordUsernameInstructions: string;
        usernameNotValid: string;
        forgotPasswordQuestion: string;
        generate: string;
        generateNewAPIKey: string;
        generatingNewAPIKeyWillInvalidate: string;
        lockUntil: string;
        logBackIn: string;
        loggedIn: string;
        loggedInChangePassword: string;
        loggedOutInactivity: string;
        loggedOutSuccessfully: string;
        loggingOut: string;
        login: string;
        loginAttempts: string;
        loginUser: string;
        loginWithAnotherUser: string;
        logOut: string;
        logout: string;
        logoutSuccessful: string;
        logoutUser: string;
        newAccountCreated: string;
        newAPIKeyGenerated: string;
        newPassword: string;
        passed: string;
        passwordResetSuccessfully: string;
        resetPassword: string;
        resetPasswordExpiration: string;
        resetPasswordToken: string;
        resetYourPassword: string;
        stayLoggedIn: string;
        successfullyRegisteredFirstUser: string;
        successfullyUnlocked: string;
        tokenRefreshSuccessful: string;
        unableToVerify: string;
        username: string;
        verified: string;
        verifiedSuccessfully: string;
        verify: string;
        verifyUser: string;
        verifyYourEmail: string;
        youAreInactive: string;
        youAreReceivingResetPassword: string;
        youDidNotRequestPassword: string;
    };
    error: {
        accountAlreadyActivated: string;
        autosaving: string;
        correctInvalidFields: string;
        deletingFile: string;
        deletingTitle: string;
        documentNotFound: string;
        emailOrPasswordIncorrect: string;
        followingFieldsInvalid_one: string;
        followingFieldsInvalid_other: string;
        incorrectCollection: string;
        insufficientClipboardPermissions: string;
        invalidClipboardData: string;
        invalidFileType: string;
        invalidFileTypeValue: string;
        invalidRequestArgs: string;
        loadingDocument: string;
        localesNotSaved_one: string;
        localesNotSaved_other: string;
        logoutFailed: string;
        missingEmail: string;
        missingIDOfDocument: string;
        missingIDOfVersion: string;
        missingRequiredData: string;
        noFilesUploaded: string;
        noMatchedField: string;
        notAllowedToAccessPage: string;
        notAllowedToPerformAction: string;
        notFound: string;
        noUser: string;
        previewing: string;
        problemUploadingFile: string;
        restoringTitle: string;
        tokenInvalidOrExpired: string;
        tokenNotProvided: string;
        unableToCopy: string;
        unableToDeleteCount: string;
        unableToReindexCollection: string;
        unableToUpdateCount: string;
        unauthorized: string;
        unauthorizedAdmin: string;
        unknown: string;
        unPublishingDocument: string;
        unspecific: string;
        unverifiedEmail: string;
        userEmailAlreadyRegistered: string;
        userLocked: string;
        usernameAlreadyRegistered: string;
        usernameOrPasswordIncorrect: string;
        valueMustBeUnique: string;
        verificationTokenInvalid: string;
    };
    fields: {
        addLabel: string;
        addLink: string;
        addNew: string;
        addNewLabel: string;
        addRelationship: string;
        addUpload: string;
        block: string;
        blocks: string;
        blockType: string;
        chooseBetweenCustomTextOrDocument: string;
        chooseDocumentToLink: string;
        chooseFromExisting: string;
        chooseLabel: string;
        collapseAll: string;
        customURL: string;
        editLabelData: string;
        editLink: string;
        editRelationship: string;
        enterURL: string;
        internalLink: string;
        itemsAndMore: string;
        labelRelationship: string;
        latitude: string;
        linkedTo: string;
        linkType: string;
        longitude: string;
        newLabel: string;
        openInNewTab: string;
        passwordsDoNotMatch: string;
        relatedDocument: string;
        relationTo: string;
        removeRelationship: string;
        removeUpload: string;
        saveChanges: string;
        searchForBlock: string;
        selectExistingLabel: string;
        selectFieldsToEdit: string;
        showAll: string;
        swapRelationship: string;
        swapUpload: string;
        textToDisplay: string;
        toggleBlock: string;
        uploadNewLabel: string;
    };
    folder: {
        browseByFolder: string;
        byFolder: string;
        deleteFolder: string;
        folderName: string;
        folders: string;
        folderTypeDescription: string;
        itemHasBeenMoved: string;
        itemHasBeenMovedToRoot: string;
        itemsMovedToFolder: string;
        itemsMovedToRoot: string;
        moveFolder: string;
        moveItemsToFolderConfirmation: string;
        moveItemsToRootConfirmation: string;
        moveItemToFolderConfirmation: string;
        moveItemToRootConfirmation: string;
        movingFromFolder: string;
        newFolder: string;
        noFolder: string;
        renameFolder: string;
        searchByNameInFolder: string;
        selectFolderForItem: string;
    };
    general: {
        name: string;
        aboutToDelete: string;
        aboutToDeleteCount_many: string;
        aboutToDeleteCount_one: string;
        aboutToDeleteCount_other: string;
        aboutToPermanentlyDelete: string;
        aboutToPermanentlyDeleteTrash: string;
        aboutToRestore: string;
        aboutToRestoreAsDraft: string;
        aboutToRestoreAsDraftCount: string;
        aboutToRestoreCount: string;
        aboutToTrash: string;
        aboutToTrashCount: string;
        addBelow: string;
        addFilter: string;
        adminTheme: string;
        all: string;
        allCollections: string;
        allLocales: string;
        and: string;
        anotherUser: string;
        anotherUserTakenOver: string;
        applyChanges: string;
        ascending: string;
        automatic: string;
        backToDashboard: string;
        cancel: string;
        changesNotSaved: string;
        clear: string;
        clearAll: string;
        close: string;
        collapse: string;
        collections: string;
        columns: string;
        columnToSort: string;
        confirm: string;
        confirmCopy: string;
        confirmDeletion: string;
        confirmDuplication: string;
        confirmMove: string;
        confirmReindex: string;
        confirmReindexAll: string;
        confirmReindexDescription: string;
        confirmReindexDescriptionAll: string;
        confirmRestoration: string;
        copied: string;
        copy: string;
        copyField: string;
        copying: string;
        copyRow: string;
        copyWarning: string;
        create: string;
        created: string;
        createdAt: string;
        createNew: string;
        createNewLabel: string;
        creating: string;
        creatingNewLabel: string;
        currentlyEditing: string;
        custom: string;
        dark: string;
        dashboard: string;
        delete: string;
        deleted: string;
        deletedAt: string;
        deletedCountSuccessfully: string;
        deletedSuccessfully: string;
        deletePermanently: string;
        deleting: string;
        depth: string;
        descending: string;
        deselectAllRows: string;
        document: string;
        documentIsTrashed: string;
        documentLocked: string;
        documents: string;
        duplicate: string;
        duplicateWithoutSaving: string;
        edit: string;
        editAll: string;
        editedSince: string;
        editing: string;
        editingLabel_many: string;
        editingLabel_one: string;
        editingLabel_other: string;
        editingTakenOver: string;
        editLabel: string;
        email: string;
        emailAddress: string;
        emptyTrash: string;
        emptyTrashLabel: string;
        enterAValue: string;
        error: string;
        errors: string;
        exitLivePreview: string;
        export: string;
        fallbackToDefaultLocale: string;
        false: string;
        filter: string;
        filters: string;
        filterWhere: string;
        globals: string;
        goBack: string;
        groupByLabel: string;
        import: string;
        isEditing: string;
        item: string;
        items: string;
        language: string;
        lastModified: string;
        leaveAnyway: string;
        leaveWithoutSaving: string;
        light: string;
        livePreview: string;
        loading: string;
        locale: string;
        locales: string;
        menu: string;
        moreOptions: string;
        move: string;
        moveConfirm: string;
        moveCount: string;
        moveDown: string;
        moveUp: string;
        moving: string;
        movingCount: string;
        newPassword: string;
        next: string;
        no: string;
        noDateSelected: string;
        noFiltersSet: string;
        noLabel: string;
        none: string;
        noOptions: string;
        noResults: string;
        notFound: string;
        nothingFound: string;
        noTrashResults: string;
        noUpcomingEventsScheduled: string;
        noValue: string;
        of: string;
        only: string;
        open: string;
        or: string;
        order: string;
        overwriteExistingData: string;
        pageNotFound: string;
        password: string;
        pasteField: string;
        pasteRow: string;
        payloadSettings: string;
        permanentlyDelete: string;
        permanentlyDeletedCountSuccessfully: string;
        perPage: string;
        previous: string;
        reindex: string;
        reindexingAll: string;
        remove: string;
        rename: string;
        reset: string;
        resetPreferences: string;
        resetPreferencesDescription: string;
        resettingPreferences: string;
        restore: string;
        restoreAsPublished: string;
        restoredCountSuccessfully: string;
        restoring: string;
        row: string;
        rows: string;
        save: string;
        saving: string;
        schedulePublishFor: string;
        searchBy: string;
        select: string;
        selectAll: string;
        selectAllRows: string;
        selectedCount: string;
        selectLabel: string;
        selectValue: string;
        showAllLabel: string;
        sorryNotFound: string;
        sort: string;
        sortByLabelDirection: string;
        stayOnThisPage: string;
        submissionSuccessful: string;
        submit: string;
        submitting: string;
        success: string;
        successfullyCreated: string;
        successfullyDuplicated: string;
        successfullyReindexed: string;
        takeOver: string;
        thisLanguage: string;
        time: string;
        timezone: string;
        titleDeleted: string;
        titleRestored: string;
        titleTrashed: string;
        trash: string;
        trashedCountSuccessfully: string;
        true: string;
        unauthorized: string;
        unsavedChanges: string;
        unsavedChangesDuplicate: string;
        untitled: string;
        upcomingEvents: string;
        updatedAt: string;
        updatedCountSuccessfully: string;
        updatedLabelSuccessfully: string;
        updatedSuccessfully: string;
        updateForEveryone: string;
        updating: string;
        uploading: string;
        uploadingBulk: string;
        user: string;
        username: string;
        users: string;
        value: string;
        viewing: string;
        viewReadOnly: string;
        welcome: string;
        yes: string;
    };
    localization: {
        cannotCopySameLocale: string;
        copyFrom: string;
        copyFromTo: string;
        copyTo: string;
        copyToLocale: string;
        localeToPublish: string;
        selectLocaleToCopy: string;
    };
    operators: {
        contains: string;
        equals: string;
        exists: string;
        intersects: string;
        isGreaterThan: string;
        isGreaterThanOrEqualTo: string;
        isIn: string;
        isLessThan: string;
        isLessThanOrEqualTo: string;
        isLike: string;
        isNotEqualTo: string;
        isNotIn: string;
        isNotLike: string;
        near: string;
        within: string;
    };
    upload: {
        addFile: string;
        addFiles: string;
        bulkUpload: string;
        crop: string;
        cropToolDescription: string;
        download: string;
        dragAndDrop: string;
        dragAndDropHere: string;
        editImage: string;
        fileName: string;
        fileSize: string;
        filesToUpload: string;
        fileToUpload: string;
        focalPoint: string;
        focalPointDescription: string;
        height: string;
        lessInfo: string;
        moreInfo: string;
        noFile: string;
        pasteURL: string;
        previewSizes: string;
        selectCollectionToBrowse: string;
        selectFile: string;
        setCropArea: string;
        setFocalPoint: string;
        sizes: string;
        sizesFor: string;
        width: string;
    };
    validation: {
        emailAddress: string;
        enterNumber: string;
        fieldHasNo: string;
        greaterThanMax: string;
        invalidInput: string;
        invalidSelection: string;
        invalidSelections: string;
        lessThanMin: string;
        limitReached: string;
        longerThanMin: string;
        notValidDate: string;
        required: string;
        requiresAtLeast: string;
        requiresNoMoreThan: string;
        requiresTwoNumbers: string;
        shorterThanMax: string;
        timezoneRequired: string;
        trueOrFalse: string;
        username: string;
        validUploadID: string;
    };
    version: {
        type: string;
        aboutToPublishSelection: string;
        aboutToRestore: string;
        aboutToRestoreGlobal: string;
        aboutToRevertToPublished: string;
        aboutToUnpublish: string;
        aboutToUnpublishSelection: string;
        autosave: string;
        autosavedSuccessfully: string;
        autosavedVersion: string;
        changed: string;
        changedFieldsCount_one: string;
        changedFieldsCount_other: string;
        compareVersion: string;
        compareVersions: string;
        comparingAgainst: string;
        confirmPublish: string;
        confirmRevertToSaved: string;
        confirmUnpublish: string;
        confirmVersionRestoration: string;
        currentDocumentStatus: string;
        currentDraft: string;
        currentlyPublished: string;
        currentlyViewing: string;
        currentPublishedVersion: string;
        draft: string;
        draftSavedSuccessfully: string;
        lastSavedAgo: string;
        modifiedOnly: string;
        moreVersions: string;
        noFurtherVersionsFound: string;
        noRowsFound: string;
        noRowsSelected: string;
        preview: string;
        previouslyDraft: string;
        previouslyPublished: string;
        previousVersion: string;
        problemRestoringVersion: string;
        publish: string;
        publishAllLocales: string;
        publishChanges: string;
        published: string;
        publishIn: string;
        publishing: string;
        restoreAsDraft: string;
        restoredSuccessfully: string;
        restoreThisVersion: string;
        restoring: string;
        reverting: string;
        revertToPublished: string;
        saveDraft: string;
        scheduledSuccessfully: string;
        schedulePublish: string;
        selectLocales: string;
        selectVersionToCompare: string;
        showingVersionsFor: string;
        showLocales: string;
        specificVersion: string;
        status: string;
        unpublish: string;
        unpublishing: string;
        version: string;
        versionAgo: string;
        versionCount_many: string;
        versionCount_none: string;
        versionCount_one: string;
        versionCount_other: string;
        versionCreatedOn: string;
        versionID: string;
        versions: string;
        viewingVersion: string;
        viewingVersionGlobal: string;
        viewingVersions: string;
        viewingVersionsGlobal: string;
    };
}, TTranslationKeys = "authentication:account" | "authentication:accountOfCurrentUser" | "authentication:accountVerified" | "authentication:alreadyActivated" | "authentication:alreadyLoggedIn" | "authentication:apiKey" | "authentication:authenticated" | "authentication:backToLogin" | "authentication:beginCreateFirstUser" | "authentication:changePassword" | "authentication:checkYourEmailForPasswordReset" | "authentication:confirmGeneration" | "authentication:confirmPassword" | "authentication:createFirstUser" | "authentication:emailNotValid" | "authentication:emailOrUsername" | "authentication:emailSent" | "authentication:emailVerified" | "authentication:enableAPIKey" | "authentication:failedToUnlock" | "authentication:forceUnlock" | "authentication:forgotPassword" | "authentication:forgotPasswordEmailInstructions" | "authentication:forgotPasswordUsernameInstructions" | "authentication:usernameNotValid" | "authentication:forgotPasswordQuestion" | "authentication:generate" | "authentication:generateNewAPIKey" | "authentication:generatingNewAPIKeyWillInvalidate" | "authentication:lockUntil" | "authentication:logBackIn" | "authentication:loggedIn" | "authentication:loggedInChangePassword" | "authentication:loggedOutInactivity" | "authentication:loggedOutSuccessfully" | "authentication:loggingOut" | "authentication:login" | "authentication:loginAttempts" | "authentication:loginUser" | "authentication:loginWithAnotherUser" | "authentication:logOut" | "authentication:logout" | "authentication:logoutSuccessful" | "authentication:logoutUser" | "authentication:newAccountCreated" | "authentication:newAPIKeyGenerated" | "authentication:newPassword" | "authentication:passed" | "authentication:passwordResetSuccessfully" | "authentication:resetPassword" | "authentication:resetPasswordExpiration" | "authentication:resetPasswordToken" | "authentication:resetYourPassword" | "authentication:stayLoggedIn" | "authentication:successfullyRegisteredFirstUser" | "authentication:successfullyUnlocked" | "authentication:tokenRefreshSuccessful" | "authentication:unableToVerify" | "authentication:username" | "authentication:verified" | "authentication:verifiedSuccessfully" | "authentication:verify" | "authentication:verifyUser" | "authentication:verifyYourEmail" | "authentication:youAreInactive" | "authentication:youAreReceivingResetPassword" | "authentication:youDidNotRequestPassword" | "error:accountAlreadyActivated" | "error:autosaving" | "error:correctInvalidFields" | "error:deletingFile" | "error:deletingTitle" | "error:documentNotFound" | "error:emailOrPasswordIncorrect" | "error:incorrectCollection" | "error:insufficientClipboardPermissions" | "error:invalidClipboardData" | "error:invalidFileType" | "error:invalidFileTypeValue" | "error:invalidRequestArgs" | "error:loadingDocument" | "error:logoutFailed" | "error:missingEmail" | "error:missingIDOfDocument" | "error:missingIDOfVersion" | "error:missingRequiredData" | "error:noFilesUploaded" | "error:noMatchedField" | "error:notAllowedToAccessPage" | "error:notAllowedToPerformAction" | "error:notFound" | "error:noUser" | "error:previewing" | "error:problemUploadingFile" | "error:restoringTitle" | "error:tokenInvalidOrExpired" | "error:tokenNotProvided" | "error:unableToCopy" | "error:unableToDeleteCount" | "error:unableToReindexCollection" | "error:unableToUpdateCount" | "error:unauthorized" | "error:unauthorizedAdmin" | "error:unknown" | "error:unPublishingDocument" | "error:unspecific" | "error:unverifiedEmail" | "error:userEmailAlreadyRegistered" | "error:userLocked" | "error:usernameAlreadyRegistered" | "error:usernameOrPasswordIncorrect" | "error:valueMustBeUnique" | "error:verificationTokenInvalid" | "fields:block" | "fields:blocks" | "fields:addLabel" | "fields:addLink" | "fields:addNew" | "fields:addNewLabel" | "fields:addRelationship" | "fields:addUpload" | "fields:blockType" | "fields:chooseBetweenCustomTextOrDocument" | "fields:chooseDocumentToLink" | "fields:chooseFromExisting" | "fields:chooseLabel" | "fields:collapseAll" | "fields:customURL" | "fields:editLabelData" | "fields:editLink" | "fields:editRelationship" | "fields:enterURL" | "fields:internalLink" | "fields:itemsAndMore" | "fields:labelRelationship" | "fields:latitude" | "fields:linkedTo" | "fields:linkType" | "fields:longitude" | "fields:newLabel" | "fields:openInNewTab" | "fields:passwordsDoNotMatch" | "fields:relatedDocument" | "fields:relationTo" | "fields:removeRelationship" | "fields:removeUpload" | "fields:saveChanges" | "fields:searchForBlock" | "fields:selectExistingLabel" | "fields:selectFieldsToEdit" | "fields:showAll" | "fields:swapRelationship" | "fields:swapUpload" | "fields:textToDisplay" | "fields:toggleBlock" | "fields:uploadNewLabel" | "folder:browseByFolder" | "folder:byFolder" | "folder:deleteFolder" | "folder:folderName" | "folder:folders" | "folder:folderTypeDescription" | "folder:itemHasBeenMoved" | "folder:itemHasBeenMovedToRoot" | "folder:itemsMovedToFolder" | "folder:itemsMovedToRoot" | "folder:moveFolder" | "folder:moveItemsToFolderConfirmation" | "folder:moveItemsToRootConfirmation" | "folder:moveItemToFolderConfirmation" | "folder:moveItemToRootConfirmation" | "folder:movingFromFolder" | "folder:newFolder" | "folder:noFolder" | "folder:renameFolder" | "folder:searchByNameInFolder" | "folder:selectFolderForItem" | "general:item" | "general:items" | "general:of" | "general:language" | "general:error" | "general:newPassword" | "general:username" | "general:notFound" | "general:unauthorized" | "general:name" | "general:aboutToDelete" | "general:aboutToPermanentlyDelete" | "general:aboutToPermanentlyDeleteTrash" | "general:aboutToRestore" | "general:aboutToRestoreAsDraft" | "general:aboutToRestoreAsDraftCount" | "general:aboutToRestoreCount" | "general:aboutToTrash" | "general:aboutToTrashCount" | "general:addBelow" | "general:addFilter" | "general:adminTheme" | "general:all" | "general:allCollections" | "general:allLocales" | "general:and" | "general:anotherUser" | "general:anotherUserTakenOver" | "general:applyChanges" | "general:ascending" | "general:automatic" | "general:backToDashboard" | "general:cancel" | "general:changesNotSaved" | "general:clear" | "general:clearAll" | "general:close" | "general:collapse" | "general:collections" | "general:columns" | "general:columnToSort" | "general:confirm" | "general:confirmCopy" | "general:confirmDeletion" | "general:confirmDuplication" | "general:confirmMove" | "general:confirmReindex" | "general:confirmReindexAll" | "general:confirmReindexDescription" | "general:confirmReindexDescriptionAll" | "general:confirmRestoration" | "general:copied" | "general:copy" | "general:copyField" | "general:copying" | "general:copyRow" | "general:copyWarning" | "general:create" | "general:created" | "general:createdAt" | "general:createNew" | "general:createNewLabel" | "general:creating" | "general:creatingNewLabel" | "general:currentlyEditing" | "general:custom" | "general:dark" | "general:dashboard" | "general:delete" | "general:deleted" | "general:deletedAt" | "general:deletedCountSuccessfully" | "general:deletedSuccessfully" | "general:deletePermanently" | "general:deleting" | "general:depth" | "general:descending" | "general:deselectAllRows" | "general:document" | "general:documentIsTrashed" | "general:documentLocked" | "general:documents" | "general:duplicate" | "general:duplicateWithoutSaving" | "general:edit" | "general:editAll" | "general:editedSince" | "general:editing" | "general:editingTakenOver" | "general:editLabel" | "general:email" | "general:emailAddress" | "general:emptyTrash" | "general:emptyTrashLabel" | "general:enterAValue" | "general:errors" | "general:exitLivePreview" | "general:export" | "general:fallbackToDefaultLocale" | "general:false" | "general:filter" | "general:filters" | "general:filterWhere" | "general:globals" | "general:goBack" | "general:groupByLabel" | "general:import" | "general:isEditing" | "general:lastModified" | "general:leaveAnyway" | "general:leaveWithoutSaving" | "general:light" | "general:livePreview" | "general:loading" | "general:locale" | "general:locales" | "general:menu" | "general:moreOptions" | "general:move" | "general:moveConfirm" | "general:moveCount" | "general:moveDown" | "general:moveUp" | "general:moving" | "general:movingCount" | "general:next" | "general:no" | "general:noDateSelected" | "general:noFiltersSet" | "general:noLabel" | "general:none" | "general:noOptions" | "general:noResults" | "general:nothingFound" | "general:noTrashResults" | "general:noUpcomingEventsScheduled" | "general:noValue" | "general:only" | "general:open" | "general:or" | "general:order" | "general:overwriteExistingData" | "general:pageNotFound" | "general:password" | "general:pasteField" | "general:pasteRow" | "general:payloadSettings" | "general:permanentlyDelete" | "general:permanentlyDeletedCountSuccessfully" | "general:perPage" | "general:previous" | "general:reindex" | "general:reindexingAll" | "general:remove" | "general:rename" | "general:reset" | "general:resetPreferences" | "general:resetPreferencesDescription" | "general:resettingPreferences" | "general:restore" | "general:restoreAsPublished" | "general:restoredCountSuccessfully" | "general:restoring" | "general:row" | "general:rows" | "general:save" | "general:saving" | "general:schedulePublishFor" | "general:searchBy" | "general:select" | "general:selectAll" | "general:selectAllRows" | "general:selectedCount" | "general:selectLabel" | "general:selectValue" | "general:showAllLabel" | "general:sorryNotFound" | "general:sort" | "general:sortByLabelDirection" | "general:stayOnThisPage" | "general:submissionSuccessful" | "general:submit" | "general:submitting" | "general:success" | "general:successfullyCreated" | "general:successfullyDuplicated" | "general:successfullyReindexed" | "general:takeOver" | "general:thisLanguage" | "general:time" | "general:timezone" | "general:titleDeleted" | "general:titleRestored" | "general:titleTrashed" | "general:trash" | "general:trashedCountSuccessfully" | "general:true" | "general:unsavedChanges" | "general:unsavedChangesDuplicate" | "general:untitled" | "general:upcomingEvents" | "general:updatedAt" | "general:updatedCountSuccessfully" | "general:updatedLabelSuccessfully" | "general:updatedSuccessfully" | "general:updateForEveryone" | "general:updating" | "general:uploading" | "general:uploadingBulk" | "general:user" | "general:users" | "general:value" | "general:viewing" | "general:viewReadOnly" | "general:welcome" | "general:yes" | "localization:cannotCopySameLocale" | "localization:copyFrom" | "localization:copyFromTo" | "localization:copyTo" | "localization:copyToLocale" | "localization:localeToPublish" | "localization:selectLocaleToCopy" | "operators:contains" | "operators:equals" | "operators:exists" | "operators:intersects" | "operators:near" | "operators:within" | "operators:isGreaterThan" | "operators:isGreaterThanOrEqualTo" | "operators:isIn" | "operators:isLessThan" | "operators:isLessThanOrEqualTo" | "operators:isLike" | "operators:isNotEqualTo" | "operators:isNotIn" | "operators:isNotLike" | "upload:addFile" | "upload:addFiles" | "upload:bulkUpload" | "upload:crop" | "upload:cropToolDescription" | "upload:download" | "upload:dragAndDrop" | "upload:dragAndDropHere" | "upload:editImage" | "upload:fileName" | "upload:fileSize" | "upload:filesToUpload" | "upload:fileToUpload" | "upload:focalPoint" | "upload:focalPointDescription" | "upload:height" | "upload:lessInfo" | "upload:moreInfo" | "upload:noFile" | "upload:pasteURL" | "upload:previewSizes" | "upload:selectCollectionToBrowse" | "upload:selectFile" | "upload:setCropArea" | "upload:setFocalPoint" | "upload:sizes" | "upload:sizesFor" | "upload:width" | "validation:username" | "validation:emailAddress" | "validation:enterNumber" | "validation:fieldHasNo" | "validation:greaterThanMax" | "validation:invalidInput" | "validation:invalidSelection" | "validation:invalidSelections" | "validation:lessThanMin" | "validation:limitReached" | "validation:longerThanMin" | "validation:notValidDate" | "validation:required" | "validation:requiresAtLeast" | "validation:requiresNoMoreThan" | "validation:requiresTwoNumbers" | "validation:shorterThanMax" | "validation:timezoneRequired" | "validation:trueOrFalse" | "validation:validUploadID" | "version:version" | "version:aboutToRestore" | "version:restoring" | "version:type" | "version:aboutToPublishSelection" | "version:aboutToRestoreGlobal" | "version:aboutToRevertToPublished" | "version:aboutToUnpublish" | "version:aboutToUnpublishSelection" | "version:autosave" | "version:autosavedSuccessfully" | "version:autosavedVersion" | "version:changed" | "version:compareVersion" | "version:compareVersions" | "version:comparingAgainst" | "version:confirmPublish" | "version:confirmRevertToSaved" | "version:confirmUnpublish" | "version:confirmVersionRestoration" | "version:currentDocumentStatus" | "version:currentDraft" | "version:currentlyPublished" | "version:currentlyViewing" | "version:currentPublishedVersion" | "version:draft" | "version:draftSavedSuccessfully" | "version:lastSavedAgo" | "version:modifiedOnly" | "version:moreVersions" | "version:noFurtherVersionsFound" | "version:noRowsFound" | "version:noRowsSelected" | "version:preview" | "version:previouslyDraft" | "version:previouslyPublished" | "version:previousVersion" | "version:problemRestoringVersion" | "version:publish" | "version:publishAllLocales" | "version:publishChanges" | "version:published" | "version:publishIn" | "version:publishing" | "version:restoreAsDraft" | "version:restoredSuccessfully" | "version:restoreThisVersion" | "version:reverting" | "version:revertToPublished" | "version:saveDraft" | "version:scheduledSuccessfully" | "version:schedulePublish" | "version:selectLocales" | "version:selectVersionToCompare" | "version:showingVersionsFor" | "version:showLocales" | "version:specificVersion" | "version:status" | "version:unpublish" | "version:unpublishing" | "version:versionAgo" | "version:versionCount_none" | "version:versionCreatedOn" | "version:versionID" | "version:versions" | "version:viewingVersion" | "version:viewingVersionGlobal" | "version:viewingVersions" | "version:viewingVersionsGlobal" | "error:followingFieldsInvalid" | "error:localesNotSaved" | "general:aboutToDeleteCount" | "general:editingLabel" | "version:changedFieldsCount" | "version:versionCount">({ count, key, translations, }: {
    count?: number;
    key: TTranslationKeys;
    translations: Language<TTranslations>["translations"];
}) => string;
/**
 * @function t
 *
 * Merges config defined translations with translations passed in as an argument
 * returns a function that can be used to translate a string
 *
 * @returns string
 */
export declare function t<TTranslations = DefaultTranslationsObject, TTranslationKeys = DefaultTranslationKeys>({ key, translations, vars, }: {
    key: TTranslationKeys;
    translations?: Language<TTranslations>['translations'];
    vars?: Record<string, any>;
}): string;
export declare const initI18n: (args: {
    config: import("../types.js").I18nOptions;
    context: "api";
    language: import("../types.js").AcceptedLanguages;
} | {
    config: import("../types.js").I18nOptions<import("../types.js").ClientTranslationsObject>;
    context: "client";
    language: import("../types.js").AcceptedLanguages;
}) => Promise<I18n>;
//# sourceMappingURL=init.d.ts.map