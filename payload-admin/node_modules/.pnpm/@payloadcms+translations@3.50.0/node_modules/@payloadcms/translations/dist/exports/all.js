import { ar } from '../languages/ar.js';
import { az } from '../languages/az.js';
import { bg } from '../languages/bg.js';
import { bnBd } from '../languages/bnBd.js';
import { bnIn } from '../languages/bnIn.js';
import { ca } from '../languages/ca.js';
import { cs } from '../languages/cs.js';
import { da } from '../languages/da.js';
import { de } from '../languages/de.js';
import { en } from '../languages/en.js';
import { es } from '../languages/es.js';
import { et } from '../languages/et.js';
import { fa } from '../languages/fa.js';
import { fr } from '../languages/fr.js';
import { he } from '../languages/he.js';
import { hr } from '../languages/hr.js';
import { hu } from '../languages/hu.js';
import { hy } from '../languages/hy.js';
import { id } from '../languages/id.js';
import { it } from '../languages/it.js';
import { ja } from '../languages/ja.js';
import { ko } from '../languages/ko.js';
import { lt } from '../languages/lt.js';
import { lv } from '../languages/lv.js';
import { my } from '../languages/my.js';
import { nb } from '../languages/nb.js';
import { nl } from '../languages/nl.js';
import { pl } from '../languages/pl.js';
import { pt } from '../languages/pt.js';
import { ro } from '../languages/ro.js';
import { rs } from '../languages/rs.js';
import { rsLatin } from '../languages/rsLatin.js';
import { ru } from '../languages/ru.js';
import { sk } from '../languages/sk.js';
import { sl } from '../languages/sl.js';
import { sv } from '../languages/sv.js';
import { th } from '../languages/th.js';
import { tr } from '../languages/tr.js';
import { uk } from '../languages/uk.js';
import { vi } from '../languages/vi.js';
import { zh } from '../languages/zh.js';
import { zhTw } from '../languages/zhTw.js';
export const translations = {
    ar,
    az,
    bg,
    'bn-BD': bnBd,
    'bn-IN': bnIn,
    ca,
    cs,
    da,
    de,
    en,
    es,
    et,
    fa,
    fr,
    he,
    hr,
    hu,
    hy,
    id,
    it,
    ja,
    ko,
    lt,
    lv,
    my,
    nb,
    nl,
    pl,
    pt,
    ro,
    rs,
    'rs-latin': rsLatin,
    ru,
    sk,
    sl,
    sv,
    th,
    tr,
    uk,
    vi,
    zh,
    'zh-TW': zhTw
};

//# sourceMappingURL=all.js.map