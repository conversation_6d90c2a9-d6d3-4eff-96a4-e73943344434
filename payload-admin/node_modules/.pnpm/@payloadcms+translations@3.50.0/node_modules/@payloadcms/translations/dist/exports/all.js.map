{"version": 3, "sources": ["../../src/exports/all.ts"], "sourcesContent": ["import type { SupportedLanguages } from '../types.js'\n\nimport { ar } from '../languages/ar.js'\nimport { az } from '../languages/az.js'\nimport { bg } from '../languages/bg.js'\nimport { bnBd } from '../languages/bnBd.js'\nimport { bnIn } from '../languages/bnIn.js'\nimport { ca } from '../languages/ca.js'\nimport { cs } from '../languages/cs.js'\nimport { da } from '../languages/da.js'\nimport { de } from '../languages/de.js'\nimport { en } from '../languages/en.js'\nimport { es } from '../languages/es.js'\nimport { et } from '../languages/et.js'\nimport { fa } from '../languages/fa.js'\nimport { fr } from '../languages/fr.js'\nimport { he } from '../languages/he.js'\nimport { hr } from '../languages/hr.js'\nimport { hu } from '../languages/hu.js'\nimport { hy } from '../languages/hy.js'\nimport { id } from '../languages/id.js'\nimport { it } from '../languages/it.js'\nimport { ja } from '../languages/ja.js'\nimport { ko } from '../languages/ko.js'\nimport { lt } from '../languages/lt.js'\nimport { lv } from '../languages/lv.js'\nimport { my } from '../languages/my.js'\nimport { nb } from '../languages/nb.js'\nimport { nl } from '../languages/nl.js'\nimport { pl } from '../languages/pl.js'\nimport { pt } from '../languages/pt.js'\nimport { ro } from '../languages/ro.js'\nimport { rs } from '../languages/rs.js'\nimport { rsLatin } from '../languages/rsLatin.js'\nimport { ru } from '../languages/ru.js'\nimport { sk } from '../languages/sk.js'\nimport { sl } from '../languages/sl.js'\nimport { sv } from '../languages/sv.js'\nimport { th } from '../languages/th.js'\nimport { tr } from '../languages/tr.js'\nimport { uk } from '../languages/uk.js'\nimport { vi } from '../languages/vi.js'\nimport { zh } from '../languages/zh.js'\nimport { zhTw } from '../languages/zhTw.js'\n\nexport const translations = {\n  ar,\n  az,\n  bg,\n  'bn-BD': bnBd,\n  'bn-IN': bnIn,\n  ca,\n  cs,\n  da,\n  de,\n  en,\n  es,\n  et,\n  fa,\n  fr,\n  he,\n  hr,\n  hu,\n  hy,\n\n  id,\n  it,\n  ja,\n  ko,\n  lt,\n  lv,\n  my,\n  nb,\n  nl,\n  pl,\n  pt,\n  ro,\n  rs,\n  'rs-latin': rsLatin,\n  ru,\n  sk,\n  sl,\n  sv,\n  th,\n  tr,\n  uk,\n  vi,\n  zh,\n  'zh-TW': zhTw,\n} as SupportedLanguages\n"], "names": ["ar", "az", "bg", "bnBd", "bnIn", "ca", "cs", "da", "de", "en", "es", "et", "fa", "fr", "he", "hr", "hu", "hy", "id", "it", "ja", "ko", "lt", "lv", "my", "nb", "nl", "pl", "pt", "ro", "rs", "rsLatin", "ru", "sk", "sl", "sv", "th", "tr", "uk", "vi", "zh", "zhTw", "translations"], "mappings": "AAEA,SAASA,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,OAAO,QAAQ,0BAAyB;AACjD,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,EAAE,QAAQ,qBAAoB;AACvC,SAASC,IAAI,QAAQ,uBAAsB;AAE3C,OAAO,MAAMC,eAAe;IAC1B1C;IACAC;IACAC;IACA,SAASC;IACT,SAASC;IACTC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IAEAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACA,YAAYC;IACZC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACA,SAASC;AACX,EAAuB"}