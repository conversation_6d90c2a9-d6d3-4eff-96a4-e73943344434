{"version": 3, "sources": ["../../src/languages/hr.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const hrTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: '<PERSON><PERSON><PERSON>',\n    accountOfCurrentUser: 'Ra<PERSON>un trenutnog korisnika',\n    accountVerified: '<PERSON><PERSON><PERSON> je uspješno verificiran.',\n    alreadyActivated: 'Već aktivirano',\n    alreadyLoggedIn: 'Već prijavljeni',\n    apiKey: 'API ključ',\n    authenticated: 'Autenticiran',\n    backToLogin: 'Natrag na prijavu',\n    beginCreateFirstUser: 'Za početak, izradite prvog korisnika.',\n    changePassword: 'Promijeni lozinku',\n    checkYourEmailForPasswordReset:\n      'Ako je e-mail adresa povezana s računom, uskoro ćete primiti upute za resetiranje lozinke. Molimo provjerite svoju mapu za neželjenu poštu ili spam ako ne vidite e-mail u svojoj pristigloj pošti.',\n    confirmGeneration: 'Potvrdi generiranje',\n    confirmPassword: 'Potvrdi lozinku',\n    createFirstUser: 'Izradi prvog korisnika',\n    emailNotValid: 'E-mail nije ispravan',\n    emailOrUsername: 'E-mail ili korisničko ime',\n    emailSent: 'E-mail poslan',\n    emailVerified: 'E-mail uspješno verificiran.',\n    enableAPIKey: 'Omogući API ključ',\n    failedToUnlock: 'Otključavanje nije uspjelo.',\n    forceUnlock: 'Prisilno otključaj',\n    forgotPassword: 'Zaboravljena lozinka',\n    forgotPasswordEmailInstructions:\n      'Molimo unesite svoju e-mail adresu. Primit ćete poruku s uputama za ponovno postavljanje lozinke.',\n    forgotPasswordQuestion: 'Zaboravljena lozinka?',\n    forgotPasswordUsernameInstructions:\n      'Molimo unesite vaše korisničko ime ispod. Upute o tome kako resetirati vašu lozinku bit će poslane na e-adresu povezanu s vašim korisničkim imenom.',\n    generate: 'Generiraj',\n    generateNewAPIKey: 'Generiraj novi API ključ',\n    generatingNewAPIKeyWillInvalidate:\n      'Generiranje novog API ključa će <1>poništiti</1> prethodni ključ. Jeste li sigurni da želite nastaviti?',\n    lockUntil: 'Zaključaj dok',\n    logBackIn: 'Ponovno se prijavite',\n    loggedIn: 'Za prijavu s drugim korisničkim računom potrebno je prvo <0>odjaviti se</0>',\n    loggedInChangePassword:\n      'Da biste promijenili lozinku, otvorite svoj <0>račun</0> i promijenite je tamo.',\n    loggedOutInactivity: 'Odjavljeni ste zbog neaktivnosti.',\n    loggedOutSuccessfully: 'Uspješno ste odjavljeni.',\n    loggingOut: 'Odjava u tijeku...',\n    login: 'Prijava',\n    loginAttempts: 'Pokušaji prijave',\n    loginUser: 'Prijava korisnika',\n    loginWithAnotherUser:\n      'Za prijavu s drugim korisničkim računom potrebno je prvo <0>odjaviti se</0>',\n    logOut: 'Odjava',\n    logout: 'Odjava',\n    logoutSuccessful: 'Odjava uspješna.',\n    logoutUser: 'Odjava korisnika',\n    newAccountCreated:\n      'Novi račun je izrađen. Pristupite računu klikom na: <a href=\"{{serverURL}}\">{{serverURL}}</a>. Molimo kliknite na sljedeću poveznicu ili zalijepite URL, koji se nalazi ispod, u preglednik da biste potvrdili svoju e-mail adresu: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Nakon što potvrdite e-mail adresu, moći ćete se prijaviti.',\n    newAPIKeyGenerated: 'New API ključ generiran.',\n    newPassword: 'Nova lozinka',\n    passed: 'Autentifikacija je prošla',\n    passwordResetSuccessfully: 'Lozinka uspješno resetirana.',\n    resetPassword: 'Resetiranje lozinke',\n    resetPasswordExpiration: 'Rok trajanja resetiranja lozinke',\n    resetPasswordToken: 'Resetiranje tokena lozinke',\n    resetYourPassword: 'Resetirajte svoju lozinku',\n    stayLoggedIn: 'Ostanite prijavljeni',\n    successfullyRegisteredFirstUser: 'Uspješno registriran prvi korisnik.',\n    successfullyUnlocked: 'Uspješno otključano',\n    tokenRefreshSuccessful: 'Osvježavanje tokena uspješno.',\n    unableToVerify: 'Nije moguće potvrditi',\n    username: 'Korisničko ime',\n    usernameNotValid: 'Uneseno korisničko ime nije valjano.',\n    verified: 'Potvrđeno',\n    verifiedSuccessfully: 'Uspješno potvrđeno',\n    verify: 'Potvrdi',\n    verifyUser: 'Potvrdi korisnika',\n    verifyYourEmail: 'Potvrdi svoju e-mail adresu',\n    youAreInactive:\n      'Neaktivni ste već neko vrijeme i uskoro ćete biti automatski odjavljeni zbog vlastite sigurnosti. Želite li ostati prijavljeni?',\n    youAreReceivingResetPassword:\n      'Primili ste ovo jer ste Vi (ili netko drugi) zatražili promjenu lozinke za Vaš račun. Molimo kliknite na poveznicu ili zalijepite ovo u svoje preglednik da biste završili proces:',\n    youDidNotRequestPassword:\n      'Ako niste zatražili ovo, molimo ignorirajte ovaj e-mail i Vaša će lozinka ostati nepromijenjena.',\n  },\n  error: {\n    accountAlreadyActivated: 'Ovaj račun je već aktiviran.',\n    autosaving: 'Nastao je problem pri automatskom spremanju ovog dokumenta.',\n    correctInvalidFields: 'Molimo ispravite neispravna polja.',\n    deletingFile: 'Dogodila se pogreška pri brisanju datoteke.',\n    deletingTitle:\n      'Dogodila se pogreška pri brisanju {{title}}. Molimo provjerite svoju internet vezu i pokušajte ponovno.',\n    documentNotFound:\n      'Dokument s ID-om {{id}} nije mogao biti pronađen. Možda je izbrisan ili nikad nije postojao, ili možda nemate pristup njemu.',\n    emailOrPasswordIncorrect: 'E-mail adresa ili lozinka netočni.',\n    followingFieldsInvalid_one: 'Ovo polje je neispravno:',\n    followingFieldsInvalid_other: 'Ova polja su neispravna:',\n    incorrectCollection: 'Neispravna kolekcija',\n    insufficientClipboardPermissions:\n      'Pristup međuspremniku odbijen. Provjerite svoja dopuštenja za međuspremnik.',\n    invalidClipboardData: 'Nevažeći podaci u međuspremniku.',\n    invalidFileType: 'Neispravan tip datoteke',\n    invalidFileTypeValue: 'Neispravan tip datoteke: {{value}}',\n    invalidRequestArgs: 'Nevažeći argumenti u zahtjevu: {{args}}',\n    loadingDocument: 'Došlo je do problema pri učitavanju dokumenta čiji je ID {{id}}.',\n    localesNotSaved_one: 'Sljedeću lokalnu postavku nije bilo moguće spremiti:',\n    localesNotSaved_other: 'Sljedeće lokalne postavke nije bilo moguće spremiti:',\n    logoutFailed: 'Odjava nije uspjela.',\n    missingEmail: 'Nedostaje e-mail.',\n    missingIDOfDocument: 'Nedostaje ID dokumenta da bi se ažurirao.',\n    missingIDOfVersion: 'Nedostaje ID verzije.',\n    missingRequiredData: 'Nedostaju obvezni podaci.',\n    noFilesUploaded: 'Nijedna datoteka nije učitana.',\n    noMatchedField: 'Nema podudarajućih polja za \"{{label}}\"',\n    notAllowedToAccessPage: 'Nemate dopuštenje pristupiti ovoj stranici.',\n    notAllowedToPerformAction: 'Nemate dopuštenje izvršiti ovu radnju.',\n    notFound: 'Traženi resurs nije pronađen.',\n    noUser: 'Nema korisnika',\n    previewing: 'Došlo je do problema pri pregledavanju ovog dokumenta.',\n    problemUploadingFile: 'Došlo je do problema pri učitavanju datoteke.',\n    restoringTitle:\n      'Došlo je do pogreške prilikom vraćanja {{title}}. Provjerite svoju vezu i pokušajte ponovno.',\n    tokenInvalidOrExpired: 'Token je neispravan ili je istekao.',\n    tokenNotProvided: 'Token nije pružen.',\n    unableToCopy: 'Nije moguće kopirati.',\n    unableToDeleteCount: 'Nije moguće izbrisati {{count}} od {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Pogreška pri ponovnom indeksiranju kolekcije {{collection}}. Operacija je prekinuta.',\n    unableToUpdateCount: 'Nije moguće ažurirati {{count}} od {{total}} {{label}}.',\n    unauthorized: 'Neovlašteno, morate biti prijavljeni da biste uputili ovaj zahtjev.',\n    unauthorizedAdmin: 'Neovlašteno, ovaj korisnik nema pristup administratorskom panelu.',\n    unknown: 'Došlo je do nepoznate pogreške.',\n    unPublishingDocument: 'Došlo je do problema pri poništavanju objave ovog dokumenta.',\n    unspecific: 'Došlo je do pogreške.',\n    unverifiedEmail: 'Molimo potvrdite svoju e-mail adresu prije prijave.',\n    userEmailAlreadyRegistered: 'Korisnik s navedenom e-mail adresom je već registriran.',\n    userLocked: 'Ovaj korisnik je zaključan zbog previše neuspješnih pokušaja prijave.',\n    usernameAlreadyRegistered: 'Korisnik s navedenim korisničkim imenom već je registriran.',\n    usernameOrPasswordIncorrect: 'Korisničko ime ili lozinka koju ste unijeli su netočni.',\n    valueMustBeUnique: 'Vrijednost mora biti jedinstvena.',\n    verificationTokenInvalid: 'Verifikacijski token je neispravan.',\n  },\n  fields: {\n    addLabel: 'Dodaj {{label}}',\n    addLink: 'Dodaj poveznicu',\n    addNew: 'Dodaj novi',\n    addNewLabel: 'Dodaj novi {{label}}',\n    addRelationship: 'Dodaj odnos',\n    addUpload: 'Dodaj učitavanje',\n    block: 'blokiranje',\n    blocks: 'blokiranja',\n    blockType: 'Vrsta blokiranja',\n    chooseBetweenCustomTextOrDocument:\n      'Izaberite između unošenja prilagođenog teksta URL ili poveznice na drugi dokument.',\n    chooseDocumentToLink: 'Odaberite dokument koji želite povezati.',\n    chooseFromExisting: 'Odaberite iz postojećih.',\n    chooseLabel: 'Odaberite {{label}}',\n    collapseAll: 'Sažmi sve',\n    customURL: 'Prilagođeni URL',\n    editLabelData: 'Uredi {{label}} podatke',\n    editLink: 'Uredi poveznicu',\n    editRelationship: 'Uredi odnos',\n    enterURL: 'Unesi URL',\n    internalLink: 'Interna poveznika',\n    itemsAndMore: '{{items}} i {{count}} više',\n    labelRelationship: '{{label}} veza',\n    latitude: 'Zemljopisna širina',\n    linkedTo: 'Povezan s <0>{{label}}</0>',\n    linkType: 'Tip poveznce',\n    longitude: 'Zemljopisna dužina',\n    newLabel: 'Novo {{label}}',\n    openInNewTab: 'Otvori u novoj kartici.',\n    passwordsDoNotMatch: 'Lozinke nisu iste.',\n    relatedDocument: 'Povezani dokument',\n    relationTo: 'Veza sa',\n    removeRelationship: 'Ukloni vezu',\n    removeUpload: 'Ukloni prijenos',\n    saveChanges: 'Spremi promjene',\n    searchForBlock: 'Potraži blok',\n    selectExistingLabel: 'Odaberi postojeće {{label}}',\n    selectFieldsToEdit: 'Odaberite polja za uređivanje',\n    showAll: 'Pokaži sve',\n    swapRelationship: 'Zamijeni vezu',\n    swapUpload: 'Zamijeni prijenos',\n    textToDisplay: 'Tekst za prikaz',\n    toggleBlock: 'Prebaci blok',\n    uploadNewLabel: 'Učitaj novi {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Pregledajte po mapi',\n    byFolder: 'Po mapi',\n    deleteFolder: 'Izbriši mapu',\n    folderName: 'Naziv mape',\n    folders: 'Mape',\n    folderTypeDescription:\n      'Odaberite koja vrsta dokumenata kolekcije treba biti dozvoljena u ovoj mapi.',\n    itemHasBeenMoved: '{{title}} je premješten u {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} je premješten u korijensku mapu.',\n    itemsMovedToFolder: '{{title}} premješteno u {{folderName}}',\n    itemsMovedToRoot: '{{title}} premješten u korijensku mapu',\n    moveFolder: 'Premjesti mapu',\n    moveItemsToFolderConfirmation:\n      'Upravo se spremate premjestiti <1>{{count}} {{label}}</1> u <2>{{toFolder}}</2>. Jeste li sigurni?',\n    moveItemsToRootConfirmation:\n      'Na korak ste da premjestite <1>{{count}} {{label}}</1> u korijensku mapu. Jeste li sigurni?',\n    moveItemToFolderConfirmation:\n      'Upravo ćete premjestiti <1>{{title}}</1> u <2>{{toFolder}}</2>. Jeste li sigurni?',\n    moveItemToRootConfirmation:\n      'Upravo ćete premjestiti <1>{{title}}</1> u osnovnu mapu. Jeste li sigurni?',\n    movingFromFolder: 'Premještanje {{title}} iz {{fromFolder}}',\n    newFolder: 'Nova mapa',\n    noFolder: 'Nema mape',\n    renameFolder: 'Preimenuj mapu',\n    searchByNameInFolder: 'Pretraživanje po imenu u {{folderName}}',\n    selectFolderForItem: 'Odaberite mapu za {{title}}',\n  },\n  general: {\n    name: 'Ime',\n    aboutToDelete: 'Izbrisat ćete {{label}} <1>{{title}}</1>. Jeste li sigurni?',\n    aboutToDeleteCount_many: 'Upravo ćete izbrisati {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Upravo ćete izbrisati {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Upravo ćete izbrisati {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Na rubu ste trajnog brisanja {{label}} <1>{{title}}</1>. Jeste li sigurni?',\n    aboutToPermanentlyDeleteTrash:\n      'Na rubu ste trajnog brisanja <0>{{count}}</0> <1>{{label}}</1> iz smeća. Jeste li sigurni?',\n    aboutToRestore: 'Na rubu ste obnoviti {{label}} <1>{{title}}</1>. Jeste li sigurni?',\n    aboutToRestoreAsDraft:\n      'Uskoro ćete vratiti {{label}} <1>{{title}}</1> kao skicu. Jeste li sigurni?',\n    aboutToRestoreAsDraftCount: 'Uskoro ćete obnoviti {{count}} {{label}} kao nacrt',\n    aboutToRestoreCount: 'Uskoro ćete obnoviti {{count}} {{label}}',\n    aboutToTrash: 'Na rubu ste premještanja {{label}} <1>{{title}}</1> u otpad. Jeste li sigurni?',\n    aboutToTrashCount: 'Na korak ste od premještanja {{count}} {{label}} u smeće',\n    addBelow: 'Dodaj ispod',\n    addFilter: 'Dodaj filter',\n    adminTheme: 'Administratorska tema',\n    all: 'Svi',\n    allCollections: 'Sve kolekcije',\n    allLocales: 'Sve lokalne postavke',\n    and: 'i',\n    anotherUser: 'Drugi korisnik',\n    anotherUserTakenOver: 'Drugi korisnik je preuzeo uređivanje ovog dokumenta.',\n    applyChanges: 'Primijeni promjene',\n    ascending: 'Uzlazno',\n    automatic: 'Automatsko',\n    backToDashboard: 'Natrag na nadzornu ploču',\n    cancel: 'Otkaži',\n    changesNotSaved: 'Vaše promjene nisu spremljene. Ako izađete sada, izgubit ćete promjene.',\n    clear: 'Jasan',\n    clearAll: 'Očisti sve',\n    close: 'Zatvori',\n    collapse: 'Sažmi',\n    collections: 'Kolekcije',\n    columns: 'Stupci',\n    columnToSort: 'Stupac za sortiranje',\n    confirm: 'Potvrdi',\n    confirmCopy: 'Potvrdi kopiju',\n    confirmDeletion: 'Potvrdi brisanje',\n    confirmDuplication: 'Potvrdi duplikaciju',\n    confirmMove: 'Potvrdi premještanje',\n    confirmReindex: 'Ponovno indeksirati sve {{collections}}?',\n    confirmReindexAll: 'Ponovno indeksirati sve kolekcije?',\n    confirmReindexDescription:\n      'Ovo će ukloniti postojeće indekse i ponovno indeksirati dokumente u {{collections}} kolekcijama.',\n    confirmReindexDescriptionAll:\n      'Ovo će ukloniti postojeće indekse i ponovno indeksirati dokumente u svim kolekcijama.',\n    confirmRestoration: 'Potvrdite obnovu',\n    copied: 'Kopirano',\n    copy: 'Kopiraj',\n    copyField: 'Kopiraj polje',\n    copying: 'Kopiranje',\n    copyRow: 'Kopiraj redak',\n    copyWarning:\n      'Na rubu ste prepisivanja {{to}} s {{from}} za {{label}} {{title}}. Jeste li sigurni?',\n    create: 'Izradi',\n    created: 'Kreirano',\n    createdAt: 'Izrađeno u',\n    createNew: 'Izradi novo',\n    createNewLabel: 'Izradi novo {{label}}',\n    creating: 'U izradi',\n    creatingNewLabel: 'Izrađivanje novog {{label}}',\n    currentlyEditing:\n      'trenutno uređuje ovaj dokument. Ako preuzmete, bit će im onemogućeno daljnje uređivanje i mogu izgubiti nespremljene promjene.',\n    custom: 'Prilagođen',\n    dark: 'Tamno',\n    dashboard: 'Nadzorna ploča',\n    delete: 'Izbriši',\n    deleted: 'Izbrisano',\n    deletedAt: 'Izbrisano U',\n    deletedCountSuccessfully: 'Uspješno izbrisano {{count}} {{label}}.',\n    deletedSuccessfully: 'Uspješno izbrisano.',\n    deletePermanently: 'Preskoči koš i trajno izbriši',\n    deleting: 'Brisanje...',\n    depth: 'Dubina',\n    descending: 'Silazno',\n    deselectAllRows: 'Odznači sve redove',\n    document: 'Dokument',\n    documentIsTrashed: 'Ova {{label}} je u smeću i dostupna je samo za čitanje.',\n    documentLocked: 'Dokument je zaključan',\n    documents: 'Dokumenti',\n    duplicate: 'Duplikat',\n    duplicateWithoutSaving: 'Dupliciraj bez spremanja promjena',\n    edit: 'Uredi',\n    editAll: 'Uredi sve',\n    editedSince: 'Uređeno od',\n    editing: 'Uređivanje',\n    editingLabel_many: 'Uređivanje {{count}} {{label}}',\n    editingLabel_one: 'Uređivanje {{count}} {{label}}',\n    editingLabel_other: 'Uređivanje {{count}} {{label}}',\n    editingTakenOver: 'Uređivanje preuzeto',\n    editLabel: 'Uredi {{label}}',\n    email: 'Email',\n    emailAddress: 'Email adresa',\n    emptyTrash: 'Isprazni smeće',\n    emptyTrashLabel: 'Isprazni {{label}} kantu za smeće',\n    enterAValue: 'Unesi vrijednost',\n    error: 'Greška',\n    errors: 'Greške',\n    exitLivePreview: 'Izađi iz Pregleda uživo',\n    export: 'Izvoz',\n    fallbackToDefaultLocale: 'Vraćanje na zadani jezik',\n    false: 'Netočno',\n    filter: 'Filter',\n    filters: 'Filteri',\n    filterWhere: 'Filter {{label}} gdje',\n    globals: 'Globali',\n    goBack: 'Vrati se',\n    groupByLabel: 'Grupiraj po {{label}}',\n    import: 'Uvoz',\n    isEditing: 'uređuje',\n    item: 'stavka',\n    items: 'stavke',\n    language: 'Jezik',\n    lastModified: 'Zadnja promjena',\n    leaveAnyway: 'Svejedno napusti',\n    leaveWithoutSaving: 'Napusti bez spremanja',\n    light: 'Svijetlo',\n    livePreview: 'Pregled',\n    loading: 'Učitavanje',\n    locale: 'Jezik',\n    locales: 'Prijevodi',\n    menu: 'Izbornik',\n    moreOptions: 'Više opcija',\n    move: 'Pomakni',\n    moveConfirm:\n      'Upravo ćete premjestiti {{count}} {{label}} u <1>{{destination}}</1>. Jeste li sigurni?',\n    moveCount: 'Pomakni {{count}} {{label}}',\n    moveDown: 'Pomakni dolje',\n    moveUp: 'Pomakni gore',\n    moving: 'Pomicanje',\n    movingCount: 'Pomicanje {{count}} {{label}}',\n    newPassword: 'Nova lozinka',\n    next: 'Sljedeće',\n    no: 'Ne',\n    noDateSelected: 'Nije odabran datum',\n    noFiltersSet: 'Nema postavljenih filtera',\n    noLabel: '<Nema {{label}}>',\n    none: 'Nijedan',\n    noOptions: 'Nema opcija',\n    noResults:\n      'Nije pronađen nijedan {{label}}. Ili {{label}} još uvijek ne postoji ili nijedan od odgovara postavljenim filterima.',\n    notFound: 'Nije pronađeno',\n    nothingFound: 'Ništa nije pronađeno',\n    noTrashResults: 'Nema {{label}} u smeću.',\n    noUpcomingEventsScheduled: 'Nema zakazanih nadolazećih događanja.',\n    noValue: 'Bez vrijednosti',\n    of: 'od',\n    only: 'Samo',\n    open: 'Otvori',\n    or: 'ili',\n    order: 'Poredak',\n    overwriteExistingData: 'Prepišite postojeće podatke u polju',\n    pageNotFound: 'Stranica nije pronađena',\n    password: 'Lozinka',\n    pasteField: 'Zalijepi polje',\n    pasteRow: 'Zalijepi redak',\n    payloadSettings: 'Payload postavke',\n    permanentlyDelete: 'Trajno izbriši',\n    permanentlyDeletedCountSuccessfully: 'Trajno izbrisano {{count}} {{label}} uspješno.',\n    perPage: 'Po stranici: {{limit}}',\n    previous: 'Prethodni',\n    reindex: 'Ponovno indeksiraj',\n    reindexingAll: 'Ponovno indeksiranje svih {{collections}}.',\n    remove: 'Ukloni',\n    rename: 'Preimenuj',\n    reset: 'Ponovno postavi',\n    resetPreferences: 'Ponovno postavljanje postavki',\n    resetPreferencesDescription: 'Ovo će vratiti sve vaše postavke na zadane vrijednosti.',\n    resettingPreferences: 'Ponovno postavljanje postavki.',\n    restore: 'Obnovi',\n    restoreAsPublished: 'Vrati kao objavljenu verziju',\n    restoredCountSuccessfully: 'Uspješno obnovljeno {{count}} {{label}}.',\n    restoring:\n      'Poštujte značenje izvornog teksta unutar konteksta Payloada. Evo popisa uobičajenih pojmova Payloada koji imaju vrlo specifična značenja:\\n    - Kolekcija: Kolekcija je skup dokumenata koji dijele zajedničku strukturu i svrhu. Kolekcije se koriste za organiziranje i upravljanje sadržajem u Payloadu.\\n    - Polje: Polje je specifičan dio podataka unutar dokumenta u kolekciji. Polja definiraju strukturu i vrstu podataka koji',\n    row: 'Red',\n    rows: 'Redovi',\n    save: 'Spremi',\n    saving: 'Spremanje...',\n    schedulePublishFor: 'Zakazano objavljivanje za {{title}}',\n    searchBy: 'Traži po {{label}}',\n    select: 'Odaberite',\n    selectAll: 'Odaberite sve {{count}} {{label}}',\n    selectAllRows: 'Odaberite sve redove',\n    selectedCount: '{{count}} {{label}} odabrano',\n    selectLabel: 'Odaberite {{label}}',\n    selectValue: 'Odaberi vrijednost',\n    showAllLabel: 'Prikaži sve {{label}}',\n    sorryNotFound: 'Nažalost, ne postoji ništa što odgovara vašem zahtjevu.',\n    sort: 'Sortiraj',\n    sortByLabelDirection: 'Sortiraj prema {{label}} {{direction}}',\n    stayOnThisPage: 'Ostani na ovoj stranici',\n    submissionSuccessful: 'Uspješno slanje',\n    submit: 'Podnesi',\n    submitting: 'Podnošenje...',\n    success: 'Uspjeh',\n    successfullyCreated: '{{label}} uspješno izrađeno.',\n    successfullyDuplicated: '{{label}} uspješno duplicirano.',\n    successfullyReindexed:\n      'Uspješno ponovno indeksirano {{count}} od {{total}} dokumenata iz {{collections}} kolekcija.',\n    takeOver: 'Preuzmi',\n    thisLanguage: 'Hrvatski',\n    time: 'Vrijeme',\n    timezone: 'Vremenska zona',\n    titleDeleted: '{{label}} \"{{title}}\" uspješno izbrisano.',\n    titleRestored: '{{label}} \"{{title}}\" uspješno je obnovljeno.',\n    titleTrashed: '{{label}} \"{{title}}\" premješteno u smeće.',\n    trash: 'Otpad',\n    trashedCountSuccessfully: '{{count}} {{label}} premješteno u smeće.',\n    true: 'Istinito',\n    unauthorized: 'Neovlašteno',\n    unsavedChanges: 'Imate nespremljene promjene. Spremite ili odbacite prije nastavka.',\n    unsavedChangesDuplicate: 'Imate nespremljene promjene. Želite li nastaviti s dupliciranjem?',\n    untitled: 'Bez naslova',\n    upcomingEvents: 'Nadolazeći događaji',\n    updatedAt: 'Ažurirano u',\n    updatedCountSuccessfully: 'Uspješno ažurirano {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Uspješno ažurirano {{label}}.',\n    updatedSuccessfully: 'Uspješno ažurirano.',\n    updateForEveryone: 'Ažuriranje za sve',\n    updating: 'Ažuriranje',\n    uploading: 'Prijenos',\n    uploadingBulk: 'Prenosim {{current}} od {{total}}',\n    user: 'Korisnik',\n    username: 'Korisničko ime',\n    users: 'Korisnici',\n    value: 'Vrijednost',\n    viewing: 'Pregledavanje',\n    viewReadOnly: 'Pogledaj samo za čitanje',\n    welcome: 'Dobrodošli',\n    yes: 'Da',\n  },\n  localization: {\n    cannotCopySameLocale: 'Ne može se kopirati na istu lokaciju',\n    copyFrom: 'Kopiraj iz',\n    copyFromTo: 'Kopiranje iz {{from}} u {{to}}',\n    copyTo: 'Kopiraj na',\n    copyToLocale: 'Kopiraj na lokaciju',\n    localeToPublish: 'Lokacija za objavu',\n    selectLocaleToCopy: 'Odaberite mjesto za kopiranje',\n  },\n  operators: {\n    contains: 'sadrži',\n    equals: 'jednako',\n    exists: 'postoji',\n    intersects: 'presijeca',\n    isGreaterThan: 'je veće od',\n    isGreaterThanOrEqualTo: 'je veće od ili jednako',\n    isIn: 'je u',\n    isLessThan: 'manje je od',\n    isLessThanOrEqualTo: 'manje je ili jednako',\n    isLike: 'je kao',\n    isNotEqualTo: 'nije jednako',\n    isNotIn: 'nije unutra',\n    isNotLike: 'nije kao',\n    near: 'blizu',\n    within: 'unutar',\n  },\n  upload: {\n    addFile: 'Dodaj datoteku',\n    addFiles: 'Dodaj datoteke',\n    bulkUpload: 'Masovno dodavanje',\n    crop: 'Izreži',\n    cropToolDescription:\n      'Povucite kutove odabranog područja, nacrtajte novo područje ili prilagodite vrijednosti ispod.',\n    download: 'Preuzmi',\n    dragAndDrop: 'Povucite i ispustite datoteku',\n    dragAndDropHere: 'ili povucite i ispustite datoteku ovdje',\n    editImage: 'Uredi sliku',\n    fileName: 'Ime datoteke',\n    fileSize: 'Veličina datoteke',\n    filesToUpload: 'Datoteke za učitavanje',\n    fileToUpload: 'Datoteka za prijenos',\n    focalPoint: 'Središnja točka',\n    focalPointDescription:\n      'Povucite središnju točku izravno na pregledu ili prilagodite vrijednosti ispod.',\n    height: 'Visina',\n    lessInfo: 'Manje informacija',\n    moreInfo: 'Više informacija',\n    noFile: 'Nema datoteke',\n    pasteURL: 'Zalijepi URL',\n    previewSizes: 'Veličine pregleda',\n    selectCollectionToBrowse: 'Odaberite kolekciju za pregled',\n    selectFile: 'Odaberite datoteku',\n    setCropArea: 'Postavi područje usjeva',\n    setFocalPoint: 'Postavi fokusnu točku',\n    sizes: 'Veličine',\n    sizesFor: 'Veličine za {{label}}',\n    width: 'Širina',\n  },\n  validation: {\n    emailAddress: 'Molimo unesite valjanu e-mail adresu.',\n    enterNumber: 'Molimo unesite valjani broj.',\n    fieldHasNo: 'Ovo polje nema {{label}}',\n    greaterThanMax: '{{value}} exceeds the maximum allowable {{label}} limit of {{max}}.',\n    invalidInput: 'Ovo polje ima neispravan unos.',\n    invalidSelection: 'Ovo polje ima neispravan odabir.',\n    invalidSelections: 'Ovo polje ima sljedeće neispravne odabire:',\n    lessThanMin: '{{value}} is below the minimum allowable {{label}} limit of {{min}}.',\n    limitReached: 'Dosegnut je limit, može se dodati samo {{max}} stavki.',\n    longerThanMin: 'Ova vrijednost mora biti duža od minimalne dužine od {{minLength}} znakova',\n    notValidDate: '\"{{value}}\" nije valjan datum.',\n    required: 'Ovo polje je obvezno.',\n    requiresAtLeast: 'Ovo polje zahtjeva minimalno {{count}} {{label}}.',\n    requiresNoMoreThan: 'Ovo polje zahtjeva ne više od {{count}} {{label}}.',\n    requiresTwoNumbers: 'Ovo polje zahtjeva dva broja.',\n    shorterThanMax: 'Ova vrijednost mora biti kraća od maksimalne dužine od {{maxLength}} znakova',\n    timezoneRequired: 'Potrebna je vremenska zona.',\n    trueOrFalse: 'Ovo polje može biti samo točno ili netočno',\n    username:\n      'Unesite važeće korisničko ime. Može sadržavati slova, brojeve, crtice, točke i donje crte.',\n    validUploadID: 'Ovo polje nije valjani ID prijenosa.',\n  },\n  version: {\n    type: 'Tip',\n    aboutToPublishSelection: 'Upravo ćete objaviti sve {{label}} u izboru. Jeste li sigurni?',\n    aboutToRestore: 'Vratit ćete {{label}} dokument u stanje u kojem je bio {{versionDate}}',\n    aboutToRestoreGlobal: 'Vratit ćete globalni {{label}} u stanje u kojem je bio {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Vratit ćete promjene u dokumentu u objavljeno stanje. Jeste li sigurni? ',\n    aboutToUnpublish: 'Poništit ćete objavu ovog dokumenta. Jeste li sigurni?',\n    aboutToUnpublishSelection:\n      'Upravo ćete poništiti objavu svih {{label}} u odabiru. Jeste li sigurni?',\n    autosave: 'Automatsko spremanje',\n    autosavedSuccessfully: 'Automatsko spremanje uspješno.',\n    autosavedVersion: 'Verzija automatski spremljenog dokumenta',\n    changed: 'Promijenjeno',\n    changedFieldsCount_one: '{{count}} promijenjeno polje',\n    changedFieldsCount_other: '{{count}} promijenjena polja',\n    compareVersion: 'Usporedi verziju sa:',\n    compareVersions: 'Usporedi verzije',\n    comparingAgainst: 'U usporedbi s',\n    confirmPublish: 'Potvrdi objavu',\n    confirmRevertToSaved: 'Potvrdite vraćanje na spremljeno',\n    confirmUnpublish: 'Potvrdite poništavanje objave',\n    confirmVersionRestoration: 'Potvrdite vraćanje verzije',\n    currentDocumentStatus: 'Trenutni {{docStatus}} dokumenta',\n    currentDraft: 'Trenutni Nacrt',\n    currentlyPublished: 'Trenutno objavljeno',\n    currentlyViewing: 'Trenutno pregledavate',\n    currentPublishedVersion: 'Trenutno Objavljena Verzija',\n    draft: 'Nacrt',\n    draftSavedSuccessfully: 'Nacrt uspješno spremljen.',\n    lastSavedAgo: 'Zadnji put spremljeno prije {{distance}',\n    modifiedOnly: 'Samo modificirano',\n    moreVersions: 'Više verzija...',\n    noFurtherVersionsFound: 'Nisu pronađene daljnje verzije',\n    noRowsFound: '{{label}} nije pronađeno',\n    noRowsSelected: 'Nije odabrana {{oznaka}}',\n    preview: 'Pregled',\n    previouslyDraft: 'Prethodno Nacrt',\n    previouslyPublished: 'Prethodno objavljeno',\n    previousVersion: 'Prethodna verzija',\n    problemRestoringVersion: 'Nastao je problem pri vraćanju ove verzije',\n    publish: 'Objaviti',\n    publishAllLocales: 'Objavi sve lokalne postavke',\n    publishChanges: 'Objavi promjene',\n    published: 'Objavljeno',\n    publishIn: 'Objavi na {{locale}}',\n    publishing: 'Objavljivanje',\n    restoreAsDraft: 'Vrati kao skicu',\n    restoredSuccessfully: 'Uspješno vraćeno.',\n    restoreThisVersion: 'Vrati ovu verziju',\n    restoring: 'Vraćanje...',\n    reverting: 'Vraćanje...',\n    revertToPublished: 'Vrati na objavljeno',\n    saveDraft: 'Sačuvaj nacrt',\n    scheduledSuccessfully: 'Uspješno zakazano.',\n    schedulePublish: 'Raspored objavljivanja',\n    selectLocales: 'Odaberite jezike',\n    selectVersionToCompare: 'Odaberite verziju za usporedbu',\n    showingVersionsFor: 'Pokazujem verzije za:',\n    showLocales: 'Prikaži jezike:',\n    specificVersion: 'Specifična verzija',\n    status: 'Status',\n    unpublish: 'Poništi objavu',\n    unpublishing: 'Poništavanje objave...',\n    version: 'Verzija',\n    versionAgo: 'prije {{distance}}',\n    versionCount_many: '{{count}} pronađenih verzija',\n    versionCount_none: 'Nema pronađenih verzija',\n    versionCount_one: '{{count}} pronađena verzija',\n    versionCount_other: '{{count}} pronađenih verzija',\n    versionCreatedOn: '{{version}} izrađenih:',\n    versionID: 'ID verzije',\n    versions: 'Verzije',\n    viewingVersion: 'Pregled verzije za {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Pregled verzije za globalni {{entityLabel}}',\n    viewingVersions: 'Pregled verzija za {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Pregled verzije za globalni {{entityLabel}}',\n  },\n}\n\nexport const hr: Language = {\n  dateFNSKey: 'hr',\n  translations: hrTranslations,\n}\n"], "names": ["hrTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "hr", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}