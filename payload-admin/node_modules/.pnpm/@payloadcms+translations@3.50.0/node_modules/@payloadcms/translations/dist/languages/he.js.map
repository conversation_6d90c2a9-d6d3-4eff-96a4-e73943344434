{"version": 3, "sources": ["../../src/languages/he.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const heTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'חשבו<PERSON>',\n    accountOfCurrentUser: 'חש<PERSON><PERSON>ן המשתמש הנוכחי',\n    accountVerified: 'הח<PERSON><PERSON><PERSON>ן אומת בהצלחה.',\n    alreadyActivated: 'כבר הופעל',\n    alreadyLoggedIn: 'כבר מחובר',\n    apiKey: 'מפתח API',\n    authenticated: 'מאומת',\n    backToLogin: 'חזרה להתחברות',\n    beginCreateFirstUser: 'כדי להתחיל, יש ליצור את המשתמש הראשון שלך.',\n    changePassword: 'שינוי סיסמה',\n    checkYourEmailForPasswordReset:\n      'אם כתובת הדוא\"ל מקושרת לחשבון, תקבל הוראות לאיפוס הסיסמה שלך בקרוב. אנא בדוק את תיקיית הספאם או הדואר הזבל אם אתה לא רואה את הדוא\"ל בתיבת הדואר הנכנס שלך.',\n    confirmGeneration: 'אישור יצירה',\n    confirmPassword: 'אישור סיסמה',\n    createFirstUser: 'יצירת משתמש ראשון',\n    emailNotValid: 'הדוא\"ל שסופק אינו תקין',\n    emailOrUsername: 'דוא\"ל או שם משתמש',\n    emailSent: 'הודעת דואר נשלחה',\n    emailVerified: 'דוא\"ל אומת בהצלחה.',\n    enableAPIKey: 'הפעלת מפתח API',\n    failedToUnlock: 'ביטול נעילה נכשל',\n    forceUnlock: 'אלץ ביטול נעילה',\n    forgotPassword: 'שכחתי סיסמה',\n    forgotPasswordEmailInstructions:\n      'אנא הזן את כתובת הדוא\"ל שלך למטה. תקבל הודעה עם הוראות לאיפוס הסיסמה שלך.',\n    forgotPasswordQuestion: 'שכחת סיסמה?',\n    forgotPasswordUsernameInstructions:\n      'אנא הזן את שם המשתמש שלך למטה. הוראות על איך לאפס את הסיסמה שלך ישלחו לכתובת הדוא\"ל המשויכת לשם המשתמש שלך.',\n    generate: 'יצירה',\n    generateNewAPIKey: 'יצירת מפתח API חדש',\n    generatingNewAPIKeyWillInvalidate:\n      'יצירת מפתח API חדש תבטל את המפתח הקודם. האם אתה בטוח שברצונך להמשיך?',\n    lockUntil: 'נעילה עד',\n    logBackIn: 'התחברות מחדש',\n    loggedIn: 'כדי להתחבר עם משתמש אחר, יש להתנתק תחילה.',\n    loggedInChangePassword:\n      'כדי לשנות את הסיסמה שלך, יש לעבור ל<a href=\"{{serverURL}}\">חשבון</a> שלך ולערוך את הסיסמה שם.',\n    loggedOutInactivity: 'התנתקת בשל חוסר פעילות.',\n    loggedOutSuccessfully: 'התנתקת בהצלחה.',\n    loggingOut: 'מתנתק...',\n    login: 'התחברות',\n    loginAttempts: 'נסיונות התחברות',\n    loginUser: 'התחברות משתמש',\n    loginWithAnotherUser: 'כדי להתחבר עם משתמש אחר, עליך <0>להתנתק</0> תחילה.',\n    logOut: 'התנתקות',\n    logout: 'התנתקות',\n    logoutSuccessful: 'התנתקות הצליחה.',\n    logoutUser: 'התנתקות משתמש',\n    newAccountCreated:\n      'נוצר חשבון חדש עבורך כדי לגשת אל <a href=\"{{serverURL}}\">{{serverURL}}</a>. אנא לחץ על הקישור הבא או הדבק את ה-URL בדפדפן שלך כדי לאמת את הדוא\"ל שלך: <a href=\"{{verificationURL}}\">{{verificationURL}}</a>.<br> לאחר אימות כתובת הדוא\"ל, תוכל להתחבר בהצלחה.',\n    newAPIKeyGenerated: 'נוצר מפתח API חדש.',\n    newPassword: 'סיסמה חדשה',\n    passed: 'אימות הצליח',\n    passwordResetSuccessfully: 'איפוס הסיסמה הצליח.',\n    resetPassword: 'איפוס סיסמה',\n    resetPasswordExpiration: 'אפס את תוקף תפוגת הסיסמה',\n    resetPasswordToken: 'אפס את טוקן הסיסמה',\n    resetYourPassword: 'אפס את הסיסמה שלך',\n    stayLoggedIn: 'הישאר מחובר',\n    successfullyRegisteredFirstUser: 'נרשמת בהצלחה כמשתמש הראשון.',\n    successfullyUnlocked: 'נעילה בוטלה בהצלחה.',\n    tokenRefreshSuccessful: 'רענון הטוקן הצליח.',\n    unableToVerify: 'לא ניתן לאמת',\n    username: 'שם משתמש',\n    usernameNotValid: 'שם המשתמש שסופק אינו חוקי',\n    verified: 'אומת',\n    verifiedSuccessfully: 'אומת בהצלחה',\n    verify: 'אמת',\n    verifyUser: 'אמת משתמש',\n    verifyYourEmail: 'אמת את כתובת הדוא\"ל שלך',\n    youAreInactive:\n      'לא היית פעיל לזמן קצר ובקרוב תתנתק אוטומטית כדי לשמור על האבטחה של חשבונך. האם ברצונך להישאר מחובר?',\n    youAreReceivingResetPassword:\n      'קיבלת הודעה זו מכיוון שאתה (או מישהו אחר) ביקשת לאפס את הסיסמה של החשבון שלך. אנא לחץ על הקישור הבא או הדבק אותו בשורת הכתובת בדפדפן שלך כדי להשלים את התהליך:',\n    youDidNotRequestPassword: 'אם לא ביקשת זאת, אנא התעלם מההודעה והסיסמה שלך תישאר ללא שינוי.',\n  },\n  error: {\n    accountAlreadyActivated: 'חשבון זה כבר הופעל.',\n    autosaving: 'אירעה בעיה בזמן שמירה אוטומטית של מסמך זה.',\n    correctInvalidFields: 'נא לתקן שדות לא תקינים.',\n    deletingFile: 'אירעה שגיאה במחיקת הקובץ.',\n    deletingTitle: 'אירעה שגיאה במחיקת {{title}}. נא בדוק את החיבור שלך ונסה שנית.',\n    documentNotFound:\n      'המסמך עם המזהה {{id}} לא נמצא. ייתכן שהוא נמחק או שלעולם לא היה, או שאין לך גישה אליו.',\n    emailOrPasswordIncorrect: 'כתובת הדוא\"ל או הסיסמה שסופקו אינם נכונים.',\n    followingFieldsInvalid_one: 'השדה הבא אינו תקין:',\n    followingFieldsInvalid_other: 'השדות הבאים אינם תקינים:',\n    incorrectCollection: 'אוסף שגוי',\n    insufficientClipboardPermissions: 'הגישה ללוח הרחב נדחתה. אנא בדוק את הרשאות הלוח הרחב שלך.',\n    invalidClipboardData: 'נתוני לוח רחב לא חוקיים.',\n    invalidFileType: 'סוג קובץ לא תקין',\n    invalidFileTypeValue: 'סוג קובץ לא תקין: {{value}}',\n    invalidRequestArgs: 'ארגומנטים לא חוקיים הועברו בבקשה: {{args}}',\n    loadingDocument: 'אירעה בעיה בטעינת המסמך עם מזהה {{id}}.',\n    localesNotSaved_one: 'לא ניתן לשמור את השפה הבאה:',\n    localesNotSaved_other: 'לא ניתן לשמור את השפות הבאות:',\n    logoutFailed: 'התנתקות נכשלה.',\n    missingEmail: 'חסרה כתובת דוא\"ל.',\n    missingIDOfDocument: 'חסר מזהה המסמך לעדכון.',\n    missingIDOfVersion: 'חסר מזהה הגרסה.',\n    missingRequiredData: 'חסרים נתונים חובה.',\n    noFilesUploaded: 'לא הועלו קבצים.',\n    noMatchedField: 'לא נמצא שדה מתאים עבור \"{{label}}\"',\n    notAllowedToAccessPage: 'אין לך הרשאה לגשת לדף זה.',\n    notAllowedToPerformAction: 'אין לך הרשאה לבצע פעולה זו.',\n    notFound: 'המשאב המבוקש לא נמצא.',\n    noUser: 'אין משתמש',\n    previewing: 'אירעה בעיה בתצוגה מקדימה של מסמך זה.',\n    problemUploadingFile: 'אירעה בעיה בזמן העלאת הקובץ.',\n    restoringTitle: 'אירעה שגיאה בעת שחזור {{title}}. אנא בדוק את החיבור שלך ונסה שוב.',\n    tokenInvalidOrExpired: 'הטוקן אינו תקין או שפג תוקפו.',\n    tokenNotProvided: 'טוקן לא סופק.',\n    unableToCopy: 'לא ניתן להעתיק.',\n    unableToDeleteCount: 'לא ניתן למחוק {{count}} מתוך {{total}} {{label}}.',\n    unableToReindexCollection: 'שגיאה בהחזרת אינדקס של אוסף {{collection}}. הפעולה בוטלה.',\n    unableToUpdateCount: 'לא ניתן לעדכן {{count}} מתוך {{total}} {{label}}.',\n    unauthorized: 'אין הרשאה, עליך להתחבר כדי לבצע בקשה זו.',\n    unauthorizedAdmin: 'אין הרשאה, משתמש זה אינו יכול לגשת לפאנל הניהול.',\n    unknown: 'אירעה שגיאה לא ידועה.',\n    unPublishingDocument: 'אירעה בעיה בביטול הפרסום של מסמך זה.',\n    unspecific: 'אירעה שגיאה.',\n    unverifiedEmail: 'אנא אמת את כתובת האימייל שלך לפני ההתחברות.',\n    userEmailAlreadyRegistered: 'משתמש עם האימייל הנתון כבר רשום.',\n    userLocked: 'המשתמש נעול עקב מספר נסיונות התחברות כושלים.',\n    usernameAlreadyRegistered: 'משתמש עם שם המשתמש שניתן כבר רשום.',\n    usernameOrPasswordIncorrect: 'שם המשתמש או הסיסמה שסופקו אינם נכונים.',\n    valueMustBeUnique: 'הערך חייב להיות ייחודי',\n    verificationTokenInvalid: 'טוקן אימות אינו תקין.',\n  },\n  fields: {\n    addLabel: 'הוסף {{label}}',\n    addLink: 'הוסף קישור',\n    addNew: 'הוסף חדש',\n    addNewLabel: 'הוסף {{label}} חדש',\n    addRelationship: 'הוסף יחס',\n    addUpload: 'הוסף העלאה',\n    block: 'בלוק',\n    blocks: 'בלוקים',\n    blockType: 'סוג בלוק',\n    chooseBetweenCustomTextOrDocument: 'בחר בין הזנת טקסט מותאם אישית או קישור למסמך אחר.',\n    chooseDocumentToLink: 'בחר מסמך לקישור',\n    chooseFromExisting: 'בחר מתוך הקיימים',\n    chooseLabel: 'בחר {{label}}',\n    collapseAll: 'כווץ הכל',\n    customURL: 'כתובת URL מותאמת אישית',\n    editLabelData: 'ערוך נתוני {{label}}',\n    editLink: 'ערוך קישור',\n    editRelationship: 'ערוך יחס',\n    enterURL: 'הזן URL',\n    internalLink: 'קישור פנימי',\n    itemsAndMore: '{{items}} ועוד {{count}}',\n    labelRelationship: '{{label}} יחס',\n    latitude: 'קו רוחב',\n    linkedTo: 'מקושר ל<0>{{label}}</0>',\n    linkType: 'סוג קישור',\n    longitude: 'קו אורך',\n    newLabel: '{{label}} חדש',\n    openInNewTab: 'פתח בכרטיסייה חדשה',\n    passwordsDoNotMatch: 'הסיסמאות אינן תואמות.',\n    relatedDocument: 'מסמך קשור',\n    relationTo: 'יחס אל',\n    removeRelationship: 'הסר יחס',\n    removeUpload: 'הסר העלאה',\n    saveChanges: 'שמור שינויים',\n    searchForBlock: 'חפש בלוק',\n    selectExistingLabel: 'בחר {{label}} קיים',\n    selectFieldsToEdit: 'בחר שדות לעריכה',\n    showAll: 'הצג הכל',\n    swapRelationship: 'החלף יחס',\n    swapUpload: 'החלף העלאה',\n    textToDisplay: 'טקסט לתצוגה',\n    toggleBlock: 'החלף בלוק',\n    uploadNewLabel: 'העלאת {{label}} חדשה',\n  },\n  folder: {\n    browseByFolder: 'עיין לפי תיקייה',\n    byFolder: 'לפי תיקייה',\n    deleteFolder: 'מחק תיקייה',\n    folderName: 'שם תיקייה',\n    folders: 'תיקיות',\n    folderTypeDescription: 'בחר איזה סוג של מסמכים מהאוסף יותרו להיות בתיקייה זו.',\n    itemHasBeenMoved: '\"{{title}}\" הועבר ל- \"{{folderName}}\"',\n    itemHasBeenMovedToRoot: '\"{{title}}\" הועבר לתיקיית השורש',\n    itemsMovedToFolder: '{{title}} הועבר אל {{folderName}}',\n    itemsMovedToRoot: '\"{{title}}\" הועבר לתיקייה הראשית',\n    moveFolder: 'העבר תיקייה',\n    moveItemsToFolderConfirmation:\n      'אתה עומד להעביר <1>{{count}} {{label}}</1> אל <2>{{toFolder}}</2>. האם אתה בטוח?',\n    moveItemsToRootConfirmation:\n      'אתה עומד להעביר <1>{{count}} {{label}}</1> לתיקייה הראשית. האם אתה בטוח?',\n    moveItemToFolderConfirmation:\n      'אתה עומד להעביר <1>{{title}}</1> ל-<2>{{toFolder}}</2>. האם אתה בטוח?',\n    moveItemToRootConfirmation: 'אתה עומד להעביר <1>{{title}}</1> לתיקייה הראשית. האם אתה בטוח?',\n    movingFromFolder: 'מזיז {{title}} מ-{{fromFolder}}',\n    newFolder: 'תיקייה חדשה',\n    noFolder: 'אין תיקייה',\n    renameFolder: 'שנה שם לתיקיה',\n    searchByNameInFolder: 'חיפוש לפי שם ב{{folderName}}',\n    selectFolderForItem: 'בחר תיקייה עבור {{title}}',\n  },\n  general: {\n    name: 'שם',\n    aboutToDelete: 'אתה עומד למחוק את {{label}} <1>{{title}}</1>. האם אתה בטוח?',\n    aboutToDeleteCount_many: 'אתה עומד למחוק {{count}} {{label}}',\n    aboutToDeleteCount_one: 'אתה עומד למחוק {{label}} אחד',\n    aboutToDeleteCount_other: 'אתה עומד למחוק {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'אתה עומד למחוק לצמיתות את ה{{label}} <1>{{title}}</1>. האם אתה בטוח?',\n    aboutToPermanentlyDeleteTrash:\n      'אתה עומד למחוק לצמיתות <0>{{count}}</0> <1>{{label}}</1> מהאשפה. האם אתה בטוח?',\n    aboutToRestore: 'אתה עומד לשחזר את {{label}} <1>{{title}}</1>. האם אתה בטוח?',\n    aboutToRestoreAsDraft: 'אתה עומד לשחזר את ה{{label}} <1>{{title}}</1> כטיוטה. האם אתה בטוח?',\n    aboutToRestoreAsDraftCount: 'אתה עומד לשחזר {{count}} {{label}} כטיוטה',\n    aboutToRestoreCount: 'אתה עומד לשחזר {{count}} {{label}}',\n    aboutToTrash: 'אתה עומד להעביר את ה{{label}} <1>{{title}}</1> לפח. האם אתה בטוח?',\n    aboutToTrashCount: 'אתה עומד להעביר {{count}} {{label}} לפח אשפה',\n    addBelow: 'הוסף מתחת',\n    addFilter: 'הוסף מסנן',\n    adminTheme: 'ערכת נושא ממשק הניהול',\n    all: 'כל',\n    allCollections: 'כל האוספים',\n    allLocales: 'כל המקומות',\n    and: 'וגם',\n    anotherUser: 'משתמש אחר',\n    anotherUserTakenOver: 'משתמש אחר השתלט על עריכת מסמך זה.',\n    applyChanges: 'החל שינויים',\n    ascending: 'בסדר עולה',\n    automatic: 'אוטומטי',\n    backToDashboard: 'חזרה ללוח המחוונים',\n    cancel: 'ביטול',\n    changesNotSaved: 'השינויים שלך לא נשמרו. אם תצא כעת, תאבד את השינויים שלך.',\n    clear:\n      'בהתחשב במשמעות של הטקסט המקורי בהקשר של Payload. הנה רשימה של מונחים מקוריים של Payload שנושאים משמעויות מסוימות:\\n- אוסף: אוסף הוא קבוצה של מסמכים ששותפים למבנה ולמטרה משות',\n    clearAll: 'נקה הכל',\n    close: 'סגור',\n    collapse: 'כווץ',\n    collections: 'אוספים',\n    columns: 'עמודות',\n    columnToSort: 'עמודה למיון',\n    confirm: 'אישור',\n    confirmCopy: 'אשר עותק',\n    confirmDeletion: 'אישור מחיקה',\n    confirmDuplication: 'אישור שכפול',\n    confirmMove: 'אשר העברה',\n    confirmReindex: 'האם להחזיר אינדקס לכל {{collections}}?',\n    confirmReindexAll: 'האם להחזיר אינדקס לכל האוספים?',\n    confirmReindexDescription:\n      'זה יסיר את האינדקסים הקיימים ויחזיר אינדקס למסמכים באוספים {{collections}}.',\n    confirmReindexDescriptionAll: 'זה יסיר את האינדקסים הקיימים ויחזיר אינדקס למסמכים בכל האוספים.',\n    confirmRestoration: 'אשר שחזור',\n    copied: 'הועתק',\n    copy: 'העתק',\n    copyField: 'העתק שדה',\n    copying: 'העתקה',\n    copyRow: 'העתק שורה',\n    copyWarning:\n      'אתה עומד לדרוס את {{to}} באמצעות {{from}} עבור {{label}} {{title}}. האם אתה בטוח?',\n    create: 'יצירה',\n    created: 'נוצר',\n    createdAt: 'נוצר בתאריך',\n    createNew: 'יצירת חדש',\n    createNewLabel: 'יצירת {{label}} חדש',\n    creating: 'יצירה',\n    creatingNewLabel: 'יצירת {{label}} חדש',\n    currentlyEditing:\n      'עורך כעת את המסמך הזה. אם תשתלט, הם ייחסמו מהמשך העריכה וייתכן שגם יאבדו שינויים שלא נשמרו.',\n    custom: 'מותאם אישית',\n    dark: 'כהה',\n    dashboard: 'לוח מחוונים',\n    delete: 'מחיקה',\n    deleted: 'נמחק',\n    deletedAt: 'נמחק ב',\n    deletedCountSuccessfully: 'נמחקו {{count}} {{label}} בהצלחה.',\n    deletedSuccessfully: 'נמחק בהצלחה.',\n    deletePermanently: 'דלג על פח האשפה ומחק לצמיתות',\n    deleting: 'מוחק...',\n    depth: 'עומק',\n    descending: 'בסדר יורד',\n    deselectAllRows: 'בטל בחירת כל השורות',\n    document: 'מסמך',\n    documentIsTrashed: 'ה{{label}} הזה במיחזור ובמצב לקריאה בלבד.',\n    documentLocked: 'המסמך ננעל',\n    documents: 'מסמכים',\n    duplicate: 'שכפול',\n    duplicateWithoutSaving: 'שכפול ללא שמירת שינויים',\n    edit: 'עריכה',\n    editAll: 'עריכה הכל',\n    editedSince: 'נערך מאז',\n    editing: 'עריכה',\n    editingLabel_many: 'עריכת {{count}} {{label}}',\n    editingLabel_one: 'עריכת {{label}} אחד',\n    editingLabel_other: 'עריכת {{count}} {{label}}',\n    editingTakenOver: 'העריכה נלקחה על ידי',\n    editLabel: 'עריכת {{label}}',\n    email: 'דוא\"ל',\n    emailAddress: 'כתובת דוא\"ל',\n    emptyTrash: 'רוקן את הזבל',\n    emptyTrashLabel: 'רוקן את האשפה {{label}}',\n    enterAValue: 'הזן ערך',\n    error: 'שגיאה',\n    errors: 'שגיאות',\n    exitLivePreview: 'צא מתצוגה חיה',\n    export: 'יצוא',\n    fallbackToDefaultLocale: 'חזרה לשפת ברירת המחדל',\n    false: 'False',\n    filter: 'סינון',\n    filters: 'מסננים',\n    filterWhere: 'סנן {{label}} בהם',\n    globals: 'גלובלים',\n    goBack: 'חזור',\n    groupByLabel: 'קבץ לפי {{label}}',\n    import: 'יבוא',\n    isEditing: 'עורך',\n    item: 'פריט',\n    items: 'פריטים',\n    language: 'שפה',\n    lastModified: 'נערך לאחרונה',\n    leaveAnyway: 'צא בכל זאת',\n    leaveWithoutSaving: 'צא מבלי לשמור',\n    light: 'בהיר',\n    livePreview: 'תצוגה מקדימה חיה',\n    loading: 'טוען',\n    locale: 'שפה',\n    locales: 'שפות',\n    menu: 'תפריט',\n    moreOptions: 'אפשרויות נוספות',\n    move: 'הזוז',\n    moveConfirm: 'אתה עומד להעביר {{count}} {{label}} ל-<1>{{destination}}</1>. האם אתה בטוח?',\n    moveCount: 'הזז {{count}} {{label}}',\n    moveDown: 'הזז למטה',\n    moveUp: 'הזז למעלה',\n    moving: 'מזיז',\n    movingCount: 'מזיז {{count}} {{label}}',\n    newPassword: 'סיסמה חדשה',\n    next: 'הבא',\n    no: 'לא',\n    noDateSelected: 'לא נבחר תאריך',\n    noFiltersSet: 'לא הוגדרו מסננים',\n    noLabel: '<ללא {{label}}>',\n    none: 'ללא',\n    noOptions: 'אין אפשרויות',\n    noResults: 'לא נמצאו {{label}}. אין עדיין {{label}}, או שאינם תואמים למסננים שנבחרו.',\n    notFound: 'לא נמצא',\n    nothingFound: 'לא נמצא כלום',\n    noTrashResults: 'אין {{label}} בפח.',\n    noUpcomingEventsScheduled: 'אין אירועים מתוכנתים בהמשך.',\n    noValue: 'אין ערך',\n    of: 'מתוך',\n    only: 'רק',\n    open: 'פתח',\n    or: 'או',\n    order: 'סדר',\n    overwriteExistingData: 'דרוס את נתוני השדה הקיימים',\n    pageNotFound: 'הדף לא נמצא',\n    password: 'סיסמה',\n    pasteField: 'הדבק שדה',\n    pasteRow: 'הדבק שורה',\n    payloadSettings: 'הגדרות מערכת Payload',\n    permanentlyDelete: 'מחק לצמיתות',\n    permanentlyDeletedCountSuccessfully: 'נמחקו לצמיתות {{count}} {{label}} בהצלחה.',\n    perPage: '{{limit}} בכל עמוד',\n    previous: 'קודם',\n    reindex: 'החזרת אינדקס',\n    reindexingAll: 'החזרת אינדקס לכל {{collections}}.',\n    remove: 'הסר',\n    rename: 'שנה שם',\n    reset: 'איפוס',\n    resetPreferences: 'איפוס העדפות',\n    resetPreferencesDescription: 'זאת תאפס את כל ההעדפות שלך להגדרות ברירת המחדל.',\n    resettingPreferences: 'מאפס העדפות.',\n    restore: 'שחזור',\n    restoreAsPublished: 'שחזר כגרסה שפורסמה',\n    restoredCountSuccessfully: 'שוחזרו בהצלחה {{count}} {{label}}.',\n    restoring:\n      'שמעו למשמעות של הטקסט המקורי בהקשר של Payload. הנה רשימה של מונחים נפוצים של Payload שנושאים משמעויות מאוד מסוימות:\\n- אוסף: אוסף הוא קבוצה של מסמכים ששותפים למבנה ולמטרה מש',\n    row: 'שורה',\n    rows: 'שורות',\n    save: 'שמירה',\n    saving: 'שומר...',\n    schedulePublishFor: 'לתזמן פרסום עבור {{כותרת}}',\n    searchBy: 'חיפוש לפי {{label}}',\n    select: 'בחר',\n    selectAll: 'בחר את כל {{count}} ה{{label}}',\n    selectAllRows: 'בחר את כל השורות',\n    selectedCount: '{{count}} {{label}} נבחרו',\n    selectLabel: '{{label}} בחר',\n    selectValue: 'בחר ערך',\n    showAllLabel: 'הצג את כל ה{{label}}',\n    sorryNotFound: 'מצטערים - אין תוצאות התואמות את הבקשה.',\n    sort: 'מיין',\n    sortByLabelDirection: 'מיין לפי {{label}} {{direction}}',\n    stayOnThisPage: 'הישאר בדף זה',\n    submissionSuccessful: 'נשלח בהצלחה.',\n    submit: 'שלח',\n    submitting: 'מגיש...',\n    success: 'הצלחה',\n    successfullyCreated: '{{label}} נוצר בהצלחה.',\n    successfullyDuplicated: '{{label}} שוכפל בהצלחה.',\n    successfullyReindexed:\n      'הוחזרו בהצלחה אינדקס {{count}} מתוך {{total}} מסמכים מ{{collections}} אוספים.',\n    takeOver: 'קח פיקוד',\n    thisLanguage: 'עברית',\n    time: 'זמן',\n    timezone: 'אזור זמן',\n    titleDeleted: '{{label}} \"{{title}}\" נמחק בהצלחה.',\n    titleRestored: 'התווית \"{{title}}\" שוחזרה בהצלחה.',\n    titleTrashed: '{{label}} \"{{title}}\" הועבר לפח.',\n    trash: 'זבל',\n    trashedCountSuccessfully: '{{count}} {{label}} הועברו לפח.',\n    true: 'True',\n    unauthorized: 'אין הרשאה',\n    unsavedChanges: 'יש לך שינויים שלא נשמרו. שמור או מחק לפני שתמשיך.',\n    unsavedChangesDuplicate: 'יש לך שינויים שלא נשמרו. האם ברצונך להמשיך לשכפל?',\n    untitled: 'ללא כותרת',\n    upcomingEvents: 'אירועים קרובים',\n    updatedAt: 'עודכן בתאריך',\n    updatedCountSuccessfully: 'עודכן {{count}} {{label}} בהצלחה.',\n    updatedLabelSuccessfully: 'עודכן {{label}} בהצלחה.',\n    updatedSuccessfully: 'עודכן בהצלחה.',\n    updateForEveryone: 'עדכון לכולם',\n    updating: 'מעדכן',\n    uploading: 'מעלה',\n    uploadingBulk: 'מעלה {{current}} מתוך {{total}}',\n    user: 'משתמש',\n    username: 'שם משתמש',\n    users: 'משתמשים',\n    value: 'ערך',\n    viewing: 'צפיה',\n    viewReadOnly: 'הצג קריאה בלבד',\n    welcome: 'ברוך הבא',\n    yes: 'כן',\n  },\n  localization: {\n    cannotCopySameLocale: 'לא ניתן להעתיק לאותו מקום',\n    copyFrom: 'העתק מ',\n    copyFromTo: 'העתקה מ-{{from}} ל-{{to}}',\n    copyTo: 'העתק אל',\n    copyToLocale: 'העתק למקום',\n    localeToPublish: 'מיקום לפרסום',\n    selectLocaleToCopy: 'בחר מיקום להעתקה',\n  },\n  operators: {\n    contains: 'מכיל',\n    equals: 'שווה ל',\n    exists: 'קיים',\n    intersects: 'מצטלב',\n    isGreaterThan: 'גדול מ',\n    isGreaterThanOrEqualTo: 'גדול או שווה ל',\n    isIn: 'נמצא ב',\n    isLessThan: 'קטן מ',\n    isLessThanOrEqualTo: 'קטן או שווה ל',\n    isLike: 'דומה ל',\n    isNotEqualTo: 'לא שווה ל',\n    isNotIn: 'לא נמצא ב',\n    isNotLike: 'אינו דומה',\n    near: 'קרוב ל',\n    within: 'בתוך',\n  },\n  upload: {\n    addFile: 'הוסף קובץ',\n    addFiles: 'הוסף קבצים',\n    bulkUpload: 'העלאה בתפוצה רחבה',\n    crop: 'חתוך',\n    cropToolDescription: 'גרור את הפינות של האזור שנבחר, צייר אזור חדש או התאם את הערכים למטה.',\n    download: 'הורדה',\n    dragAndDrop: 'גרור ושחרר קובץ',\n    dragAndDropHere: 'או גרור ושחרר קובץ לכאן',\n    editImage: 'ערוך תמונה',\n    fileName: 'שם קובץ',\n    fileSize: 'גודל קובץ',\n    filesToUpload: 'קבצים להעלאה',\n    fileToUpload: 'קובץ להעלאה',\n    focalPoint: 'נקודת מיקוד',\n    focalPointDescription: 'גרור את נקודת המיקוד ישירות על התצוגה המקדימה או התאם את הערכים למטה.',\n    height: 'גובה',\n    lessInfo: 'פחות מידע',\n    moreInfo: 'מידע נוסף',\n    noFile: 'אין קובץ',\n    pasteURL: 'הדבק כתובת אתר',\n    previewSizes: 'גדלי תצוגה מקדימה',\n    selectCollectionToBrowse: 'בחר אוסף לצפייה',\n    selectFile: 'בחר קובץ',\n    setCropArea: 'הגדר אזור חיתוך',\n    setFocalPoint: 'הגדר נקודת מיקוד',\n    sizes: 'גדלים',\n    sizesFor: 'גדלים עבור {{label}}',\n    width: 'רוחב',\n  },\n  validation: {\n    emailAddress: 'נא להזין כתובת דוא\"ל תקנית.',\n    enterNumber: 'נא להזין מספר תקני.',\n    fieldHasNo: 'שדה זה אינו מכיל {{label}}',\n    greaterThanMax: '{{value}} גדול מהערך המרבי המותר של {{label}} שהוא {{max}}.',\n    invalidInput: 'שדה זה מכיל קלט לא תקני.',\n    invalidSelection: 'שדה זה מכיל בחירה לא תקנית.',\n    invalidSelections: 'שדה זה מכיל את הבחירות הבאות שאינן תקניות:',\n    lessThanMin: '{{value}} קטן מהערך המינימלי המותר של {{label}} שהוא {{min}}.',\n    limitReached: 'הגעת למגבלה, ניתן להוסיף רק {{max}} פריטים.',\n    longerThanMin: 'ערך זה חייב להיות ארוך מאורך המינימום של {{minLength}} תווים.',\n    notValidDate: '\"{{value}}\" אינו תאריך תקני.',\n    required: 'שדה זה הוא שדה חובה.',\n    requiresAtLeast: 'שדה זה דורש לפחות {{count}} {{label}}.',\n    requiresNoMoreThan: 'שדה זה דורש לא יותר מ-{{count}} {{label}}.',\n    requiresTwoNumbers: 'שדה זה דורש שני מספרים.',\n    shorterThanMax: 'ערך זה חייב להיות קצר מ-{{maxLength}} תווים.',\n    timezoneRequired: 'נדרשת אזור זמן.',\n    trueOrFalse: 'שדה זה יכול להיות רק true או false.',\n    username: 'אנא הזן שם משתמש חוקי. יכול להכיל אותיות, מספרים, מקפים, נקודות וקווים תחתונים.',\n    validUploadID: 'שדה זה אינו מזהה העלאה תקני.',\n  },\n  version: {\n    type: 'סוג',\n    aboutToPublishSelection: 'אתה עומד לפרסם את כל ה{{label}} שנבחרו. האם אתה בטוח?',\n    aboutToRestore: 'אתה עומד לשחזר את מסמך {{label}} למצב שהיה בו בתאריך {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'אתה עומד לשחזר את {{label}} הגלובלי למצב שהיה בו בתאריך {{versionDate}}.',\n    aboutToRevertToPublished: 'אתה עומד להחזיר את השינויים במסמך הזה לגרסה שפורסמה. האם אתה בטוח?',\n    aboutToUnpublish: 'אתה עומד לבטל את הפרסום של מסמך זה. האם אתה בטוח?',\n    aboutToUnpublishSelection: 'אתה עומד לבטל את הפרסום של כל ה{{label}} שנבחרו. האם אתה בטוח?',\n    autosave: 'שמירה אוטומטית',\n    autosavedSuccessfully: 'נשמר בהצלחה.',\n    autosavedVersion: 'גרסת שמירה אוטומטית',\n    changed: 'שונה',\n    changedFieldsCount_one: '{{count}} שינה שדה',\n    changedFieldsCount_other: '{{count}} שדות ששונו',\n    compareVersion: 'השווה לגרסה:',\n    compareVersions: 'השווה גרסאות',\n    comparingAgainst: 'השוואה לעומת',\n    confirmPublish: 'אישור פרסום',\n    confirmRevertToSaved: 'אישור שחזור לגרסה שנשמרה',\n    confirmUnpublish: 'אישור ביטול פרסום',\n    confirmVersionRestoration: 'אישור שחזור גרסה',\n    currentDocumentStatus: 'מסמך {{docStatus}} נוכחי',\n    currentDraft: 'טיוטה נוכחית',\n    currentlyPublished: 'פורסם כרגע',\n    currentlyViewing: 'מציג כרגע',\n    currentPublishedVersion: 'הגרסה שפורסמה כעת',\n    draft: 'טיוטה',\n    draftSavedSuccessfully: 'טיוטה נשמרה בהצלחה.',\n    lastSavedAgo: 'נשמר לאחרונה לפני {{distance}}',\n    modifiedOnly: 'מותאם בלבד',\n    moreVersions: 'עוד גרסאות...',\n    noFurtherVersionsFound: 'לא נמצאו עוד גרסאות',\n    noRowsFound: 'לא נמצאו {{label}}',\n    noRowsSelected: 'לא נבחר {{תווית}}',\n    preview: 'תצוגה מקדימה',\n    previouslyDraft: 'לשעבר טיוטה',\n    previouslyPublished: 'פורסם בעבר',\n    previousVersion: 'גרסה קודמת',\n    problemRestoringVersion: 'הייתה בעיה בשחזור הגרסה הזו',\n    publish: 'פרסם',\n    publishAllLocales: 'פרסם את כל המיקומים',\n    publishChanges: 'פרסם שינויים',\n    published: 'פורסם',\n    publishIn: 'פרסם ב-{{locale}}',\n    publishing: 'מפרסם',\n    restoreAsDraft: 'שחזר כטיוטה',\n    restoredSuccessfully: 'שוחזר בהצלחה.',\n    restoreThisVersion: 'שחזר גרסה זו',\n    restoring: 'משחזר...',\n    reverting: 'משחזר...',\n    revertToPublished: 'שחזר לגרסה שפורסמה',\n    saveDraft: 'שמור טיוטה',\n    scheduledSuccessfully: 'תוזמן בהצלחה.',\n    schedulePublish: 'לוח זמנים לפרסום',\n    selectLocales: 'בחר שפות לתצוגה',\n    selectVersionToCompare: 'בחר גרסה להשוואה',\n    showingVersionsFor: 'מציג גרסאות עבור:',\n    showLocales: 'הצג שפות:',\n    specificVersion: 'גרסה מסוימת',\n    status: 'סטטוס',\n    unpublish: 'בטל פרסום',\n    unpublishing: 'מבטל פרסום...',\n    version: 'גרסה',\n    versionAgo: 'לפני {{distance}}',\n    versionCount_many: '{{count}} גרסאות נמצאו',\n    versionCount_none: 'לא נמצאו גרסאות',\n    versionCount_one: 'נמצאה גרסה אחת',\n    versionCount_other: '{{count}} גרסאות נמצאו',\n    versionCreatedOn: '{{version}} נוצר בתאריך:',\n    versionID: 'מזהה גרסה',\n    versions: 'גרסאות',\n    viewingVersion: 'צופה בגרסה עבור {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'צופה בגרסה עבור {{entityLabel}} הגלובלי',\n    viewingVersions: 'צופה בגרסאות עבור {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'צופה בגרסאות עבור {{entityLabel}} הגלובלי',\n  },\n}\n\nexport const he: Language = {\n  dateFNSKey: 'he',\n  translations: heTranslations,\n}\n"], "names": ["heTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "he", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BAA0B;IAC5B;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eAAe;QACfC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCAAkC;QAClCC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBAAgB;QAChBC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BAA2B;QAC3BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCAAmC;QACnCC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBAAuB;QACvBC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OACE;QACFC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BAA8B;QAC9BC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WAAW;QACX9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBAAqB;QACrBC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UAAU;QACVga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBACE;QACFC,0BAA0B;QAC1BC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}