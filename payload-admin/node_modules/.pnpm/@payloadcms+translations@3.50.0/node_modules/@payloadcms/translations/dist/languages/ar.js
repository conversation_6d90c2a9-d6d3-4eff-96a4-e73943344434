export const arTranslations = {
    authentication: {
        account: 'الحساب',
        accountOfCurrentUser: 'حساب المستخدم الحالي',
        accountVerified: 'تم التحقق من الحساب بنجاح.',
        alreadyActivated: 'تمّ التّفعيل بالفعل',
        alreadyLoggedIn: 'تمّ تسجيل الدّخول بالفعل',
        apiKey: 'مفتاح API',
        authenticated: 'مصادق عليه',
        backToLogin: 'العودة لتسجيل الدخول',
        beginCreateFirstUser: 'للبدء, قم بإنشاء المستخدم الأوّل.',
        changePassword: 'تغيير كلمة المرور',
        checkYourEmailForPasswordReset: 'إذا كان عنوان البريد الإلكتروني مرتبطًا بحساب، فستتلقى تعليمات لإعادة تعيين كلمة المرور قريبًا. يرجى التحقق من مجلد البريد العشوائي أو السبام إذا لم تر البريد الإلكتروني في صندوق الوارد.',
        confirmGeneration: 'تأكيد التّوليد',
        confirmPassword: 'تأكيد كلمة المرور',
        createFirstUser: 'إنشاء المستخدم الأوّل',
        emailNotValid: 'البريد الإلكتروني غير صالح',
        emailOrUsername: 'البريد الإلكتروني أو اسم المستخدم',
        emailSent: 'تمّ ارسال البريد الإلكتروني',
        emailVerified: 'تم التحقق من البريد الإلكتروني بنجاح.',
        enableAPIKey: 'تفعيل مفتاح API',
        failedToUnlock: 'فشل فتح القفل',
        forceUnlock: 'إجبار فتح القفل',
        forgotPassword: 'نسيت كلمة المرور',
        forgotPasswordEmailInstructions: 'يرجى إدخال البريد الالكتروني أدناه. ستتلقّى رسالة بريد إلكتروني تحتوي على إرشادات حول كيفيّة إعادة تعيين كلمة المرور الخاصّة بك.',
        forgotPasswordQuestion: 'هل نسيت كلمة المرور؟',
        forgotPasswordUsernameInstructions: 'يرجى إدخال اسم المستخدم الخاص بك أدناه. سيتم إرسال تعليمات حول كيفية إعادة تعيين كلمة المرور الخاصة بك إلى عنوان البريد الإلكتروني المرتبط باسم المستخدم الخاص بك.',
        generate: 'توليد',
        generateNewAPIKey: 'توليد مفتاح API جديد',
        generatingNewAPIKeyWillInvalidate: 'سيؤدّي إنشاء مفتاح API جديد إلى <1> إبطال </ 1> المفتاح السّابق. هل أنت متأكّد أنّك تريد المتابعة؟',
        lockUntil: 'قفل حتى',
        logBackIn: 'تسجيل الدّخول من جديد',
        loggedIn: 'لتسجيل الدّخول مع مستخدم آخر ، يجب عليك <0> تسجيل الخروج </0> أوّلاً.',
        loggedInChangePassword: 'لتغيير كلمة المرور الخاصّة بك ، انتقل إلى <0>حسابك</0> وقم بتعديل كلمة المرور هناك.',
        loggedOutInactivity: 'لقد تمّ تسجيل الخروج بسبب عدم النّشاط.',
        loggedOutSuccessfully: 'لقد تمّ تسجيل خروجك بنجاح.',
        loggingOut: 'تسجيل الخروج...',
        login: 'تسجيل الدخول',
        loginAttempts: 'محاولات تسجيل الدخول',
        loginUser: 'تسجيل دخول المستخدم',
        loginWithAnotherUser: 'لتسجيل الدخول مع مستخدم آخر ، يجب عليك <0> تسجيل الخروج </0> أوّلاً.',
        logOut: 'تسجيل الخروج',
        logout: 'تسجيل الخروج',
        logoutSuccessful: 'تم تسجيل الخروج بنجاح.',
        logoutUser: 'تسجيل خروج المستخدم',
        newAccountCreated: 'تمّ إنشاء حساب جديد لتتمكّن من الوصول إلى <a href="{{serverURL}}"> {{serverURL}} </a> الرّجاء النّقر فوق الرّابط التّالي أو لصق عنوان URL أدناه في متصفّحّك لتأكيد بريدك الإلكتروني : <a href="{{verificationURL}}"> {{verificationURL}} </a> <br> بعد التّحقّق من بريدك الإلكتروني ، ستتمكّن من تسجيل الدّخول بنجاح.',
        newAPIKeyGenerated: 'تمّ توليد مفتاح API جديد.',
        newPassword: 'كلمة مرور جديدة',
        passed: 'تمت المصادقة',
        passwordResetSuccessfully: 'تمت إعادة تعيين كلمة المرور بنجاح.',
        resetPassword: 'إعادة تعيين كلمة المرور',
        resetPasswordExpiration: 'انتهاء صلاحيّة إعادة تعيين كلمة المرور',
        resetPasswordToken: 'رمز إعادة تعيين كلمة المرور',
        resetYourPassword: 'إعادة تعيين كلمة المرور الخاصّة بك',
        stayLoggedIn: 'ابق متّصلًا',
        successfullyRegisteredFirstUser: 'تم تسجيل العضو الأول بنجاح.',
        successfullyUnlocked: 'تمّ فتح القفل بنجاح',
        tokenRefreshSuccessful: 'تم تجديد الرمز بنجاح.',
        unableToVerify: 'غير قادر على التحقق من',
        username: 'اسم المستخدم',
        usernameNotValid: 'اسم المستخدم المقدم غير صالح',
        verified: 'تمّ التحقّق',
        verifiedSuccessfully: 'تمّ التحقّق بنجاح',
        verify: 'قم بالتّحقّق',
        verifyUser: 'قم بالتّحقّق من المستخدم',
        verifyYourEmail: 'قم بتأكيد بريدك الألكتروني',
        youAreInactive: 'لم تكن نشطًا منذ فترة قصيرة وسيتمّ تسجيل خروجك قريبًا تلقائيًا من أجل أمنك. هل ترغب في البقاء مسجّلا؟',
        youAreReceivingResetPassword: 'أنت تتلقّى هذا البريد الالكتروني لأنّك (أو لأنّ شخص آخر) طلبت إعادة تعيين كلمة المرور لحسابك. الرّجاء النّقر فوق الرّابط التّالي ، أو لصق هذا الرّابط في متصفّحك لإكمال العمليّة:',
        youDidNotRequestPassword: 'إن لم تطلب هذا ، يرجى تجاهل هذا البريد الإلكتروني وستبقى كلمة مرورك ذاتها بدون تغيير.'
    },
    error: {
        accountAlreadyActivated: 'تم تفعيل هذا الحساب بالفعل.',
        autosaving: 'حدثت مشكلة أثناء حفظ هذا المستند تلقائيًا.',
        correctInvalidFields: 'يرجى تصحيح الحقول غير الصالحة.',
        deletingFile: 'حدث خطأ أثناء حذف الملف.',
        deletingTitle: 'حدث خطأ أثناء حذف {{title}}. يرجى التحقق من الاتصال الخاص بك والمحاولة مرة أخرى.',
        documentNotFound: 'لم يتم العثور على المستند بالمعرف {{id}}. قد يكون قد تم حذفه أو لم يكن موجودًا أصلاً ، أو قد لا يكون لديك الوصول إليه.',
        emailOrPasswordIncorrect: 'البريد الإلكتروني أو كلمة المرور المقدمة غير صحيحة.',
        followingFieldsInvalid_one: 'الحقل التالي غير صالح:',
        followingFieldsInvalid_other: 'الحقول التالية غير صالحة:',
        incorrectCollection: 'مجموعة غير صحيحة',
        insufficientClipboardPermissions: 'تم رفض الوصول إلى الحافظة. يرجى التحقق من أذونات الحافظة.',
        invalidClipboardData: 'بيانات الحافظة غير صالحة.',
        invalidFileType: 'نوع ملف غير صالح',
        invalidFileTypeValue: 'نوع ملف غير صالح: {{value}}',
        invalidRequestArgs: 'تم تمرير وسيطات غير صالحة في الطلب: {{args}}',
        loadingDocument: 'حدثت مشكلة أثناء تحميل المستند برقم التعريف {{id}}.',
        localesNotSaved_one: 'لم يتم حفظ اللغة التالية:',
        localesNotSaved_other: 'لم يتم حفظ اللغات التالية:',
        logoutFailed: 'فشل في تسجيل الخروج.',
        missingEmail: 'البريد الإلكتروني مفقود.',
        missingIDOfDocument: 'معرّف المستند المراد تحديثه مفقود.',
        missingIDOfVersion: 'معرّف النسخة مفقود.',
        missingRequiredData: 'توجد بيانات مطلوبة مفقودة.',
        noFilesUploaded: 'لم يتمّ رفع أيّة ملفّات.',
        noMatchedField: 'لم يتمّ العثور على حقل مطابق لـ "{{label}}"',
        notAllowedToAccessPage: 'لا يسمح لك الوصول إلى هذه الصّفحة.',
        notAllowedToPerformAction: 'لا يسمح لك القيام بهذه العمليّة.',
        notFound: 'لم يتمّ العثور على المورد المطلوب.',
        noUser: 'لا يوجد مستخدم',
        previewing: 'حدث خطأ في اثناء معاينة هذا المستند.',
        problemUploadingFile: 'حدث خطأ اثناء رفع الملفّ.',
        restoringTitle: 'حدث خطأ أثناء استعادة {{title}}. يرجى التحقق من اتصالك وحاول مرة أخرى.',
        tokenInvalidOrExpired: 'الرّمز إمّا غير صالح أو منتهي الصّلاحيّة.',
        tokenNotProvided: 'لم يتم تقديم الرمز.',
        unableToCopy: 'تعذر النسخ.',
        unableToDeleteCount: 'يتعذّر حذف {{count}} من {{total}} {{label}}.',
        unableToReindexCollection: 'خطأ في إعادة فهرسة المجموعة {{collection}}. تم إيقاف العملية.',
        unableToUpdateCount: 'يتعذّر تحديث {{count}} من {{total}} {{label}}.',
        unauthorized: 'غير مصرّح لك ، عليك أن تقوم بتسجيل الدّخول لتتمكّن من تقديم هذا الطّلب.',
        unauthorizedAdmin: 'غير مصرّح لك بالوصول إلى لوحة التحكّم.',
        unknown: 'حدث خطأ غير معروف.',
        unPublishingDocument: 'حدث خطأ أثناء إلغاء نشر هذا المستند.',
        unspecific: 'حدث خطأ.',
        unverifiedEmail: 'يرجى التحقق من بريدك الإلكتروني قبل تسجيل الدخول.',
        userEmailAlreadyRegistered: 'يوجد مستخدم مسجل بالفعل بهذا البريد الإلكتروني.',
        userLocked: 'تمّ قفل هذا المستخدم نظرًا لوجود عدد كبير من محاولات تسجيل الدّخول الغير ناجحة.',
        usernameAlreadyRegistered: 'المستخدم بالاسم المعطى مسجل بالفعل.',
        usernameOrPasswordIncorrect: 'اسم المستخدم أو كلمة المرور التي تم تقديمها غير صحيحة.',
        valueMustBeUnique: 'على القيمة أن تكون فريدة',
        verificationTokenInvalid: 'رمز التحقّق غير صالح.'
    },
    fields: {
        addLabel: 'أضف {{label}}',
        addLink: 'أضف رابط',
        addNew: 'أضف جديد',
        addNewLabel: 'أضف {{label}} جديد',
        addRelationship: 'أضف علاقة',
        addUpload: 'أضف تحميل',
        block: 'وحدة محتوى',
        blocks: 'وحدات المحتوى',
        blockType: 'نوع وحدة المحتوى',
        chooseBetweenCustomTextOrDocument: 'اختر بين إدخال عنوان URL نصّي مخصّص أو الرّبط بمستند آخر.',
        chooseDocumentToLink: 'اختر مستندًا للربط',
        chooseFromExisting: 'اختر من القائمة',
        chooseLabel: 'اختر {{label}}',
        collapseAll: 'طيّ الكلّ',
        customURL: 'URL مخصّص',
        editLabelData: 'عدّل بيانات {{label}}',
        editLink: 'عدّل الرّابط',
        editRelationship: 'عدّل العلاقة',
        enterURL: 'ادخل عنوان URL',
        internalLink: 'رابط داخلي',
        itemsAndMore: '{{items}} و {{count}} أخرى',
        labelRelationship: '{{label}} علاقة',
        latitude: 'خطّ العرض',
        linkedTo: 'تمّ الرّبط ل <0>{{label}}</0>',
        linkType: 'نوع الرّابط',
        longitude: 'خطّ الطّول',
        newLabel: '{{label}} جديد',
        openInNewTab: 'الفتح في علامة تبويب جديدة',
        passwordsDoNotMatch: 'كلمة المرور غير مطابقة.',
        relatedDocument: 'مستند مربوط',
        relationTo: 'ربط ل',
        removeRelationship: 'حذف العلاقة',
        removeUpload: 'حذف المحتوى المرفوع',
        saveChanges: 'حفظ التّغييرات',
        searchForBlock: 'ابحث عن وحدة محتوى',
        selectExistingLabel: 'اختيار {{label}} من القائمة',
        selectFieldsToEdit: 'حدّد الحقول اللتي تريد تعديلها',
        showAll: 'إظهار الكلّ',
        swapRelationship: 'تبديل العلاقة',
        swapUpload: 'تبديل المحتوى المرفوع',
        textToDisplay: 'النصّ الذي تريد إظهاره',
        toggleBlock: 'Toggle block',
        uploadNewLabel: 'رفع {{label}} جديد'
    },
    folder: {
        browseByFolder: 'تصفح حسب المجلد',
        byFolder: 'حسب المجلد',
        deleteFolder: 'حذف المجلد',
        folderName: 'اسم المجلد',
        folders: 'مجلدات',
        folderTypeDescription: 'حدد نوع المستندات التي يجب السماح بها في هذا المجلد من المجموعات.',
        itemHasBeenMoved: 'تم نقل {{title}} إلى {{folderName}}',
        itemHasBeenMovedToRoot: 'تم نقل {{title}} إلى المجلد الجذر',
        itemsMovedToFolder: '{{title}} تم نقله إلى {{folderName}}',
        itemsMovedToRoot: '{{title}} تم نقله إلى المجلد الجذر',
        moveFolder: 'نقل المجلد',
        moveItemsToFolderConfirmation: 'أنت على وشك نقل <1>{{count}} {{label}}</1> إلى <2>{{toFolder}}</2>. هل أنت متأكد؟',
        moveItemsToRootConfirmation: 'أنت على وشك نقل <1>{{count}} {{label}}</1> إلى المجلد الجذر. هل أنت متأكد؟',
        moveItemToFolderConfirmation: 'أنت على وشك نقل <1>{{title}}</1> إلى <2>{{toFolder}}</2>. هل أنت متأكد؟',
        moveItemToRootConfirmation: 'أنت على وشك نقل <1>{{title}}</1> إلى المجلد الجذر. هل أنت متأكد؟',
        movingFromFolder: 'نقل {{title}} من {{fromFolder}}',
        newFolder: 'مجلد جديد',
        noFolder: 'لا يوجد مجلد',
        renameFolder: 'إعادة تسمية المجلد',
        searchByNameInFolder: 'البحث عن طريق الاسم في {{folderName}}',
        selectFolderForItem: 'اختر المجلد لـ {{title}}'
    },
    general: {
        name: 'اسم',
        aboutToDelete: 'أنت على وشك حذف {{label}} <1>{{title}}</1>. هل أنت متأكّد؟',
        aboutToDeleteCount_many: 'أنت على وشك حذف {{count}} {{label}}',
        aboutToDeleteCount_one: 'أنت على وشك حذف {{count}} {{label}}',
        aboutToDeleteCount_other: 'أنت على وشك حذف {{count}} {{label}}',
        aboutToPermanentlyDelete: 'أنت على وشك حذف {{label}} <1>{{title}}</1> نهائيا. هل أنت متأكد؟',
        aboutToPermanentlyDeleteTrash: 'أنت على وشك حذف <0>{{count}}</0> <1>{{label}}</1> نهائياً من سلة المهملات. هل أنت متأكد؟',
        aboutToRestore: 'أنت على وشك استعادة {{label}} <1>{{title}}</1>. هل أنت متأكد؟',
        aboutToRestoreAsDraft: 'أنت على وشك استعادة {{label}} <1>{{title}}</1> كمسودة. هل أنت متأكد؟',
        aboutToRestoreAsDraftCount: 'أنت على وشك استعادة {{count}} {{label}} كمسودة',
        aboutToRestoreCount: 'أنت على وشك استعادة {{count}} {{label}}',
        aboutToTrash: 'أنت على وشك نقل {{label}} <1>{{title}}</1> إلى القمامة. هل أنت متأكد؟',
        aboutToTrashCount: 'أنت على وشك نقل {{count}} {{label}} إلى المهملات',
        addBelow: 'أضف في الاسفل',
        addFilter: 'أضف فلتر',
        adminTheme: 'شكل واجهة المستخدم',
        all: 'الكل',
        allCollections: 'جميع المجموعات',
        allLocales: 'جميع المواقع',
        and: 'و',
        anotherUser: 'مستخدم آخر',
        anotherUserTakenOver: 'قام مستخدم آخر بالاستيلاء على تحرير هذا المستند.',
        applyChanges: 'طبق التغييرات',
        ascending: 'تصاعدي',
        automatic: 'تلقائي',
        backToDashboard: 'العودة للوحة التّحكّم',
        cancel: 'إلغاء',
        changesNotSaved: 'لم يتمّ حفظ التّغييرات. إن غادرت الآن ، ستفقد تغييراتك.',
        clear: 'واضح',
        clearAll: 'امسح الكل',
        close: 'إغلاق',
        collapse: 'طيّ',
        collections: 'المجموعات',
        columns: 'الأعمدة',
        columnToSort: 'التّرتيب حسب العامود',
        confirm: 'تأكيد',
        confirmCopy: 'تأكيد النسخ',
        confirmDeletion: 'تأكيد الحذف',
        confirmDuplication: 'تأكيد التّكرار',
        confirmMove: 'تأكيد النقل',
        confirmReindex: 'إعادة فهرسة جميع {{collections}}؟',
        confirmReindexAll: 'إعادة فهرسة جميع المجموعات؟',
        confirmReindexDescription: 'سيؤدي هذا إلى إزالة الفهارس الحالية وإعادة فهرسة المستندات في مجموعات {{collections}}.',
        confirmReindexDescriptionAll: 'سيؤدي هذا إلى إزالة الفهارس الحالية وإعادة فهرسة المستندات في جميع المجموعات.',
        confirmRestoration: 'تأكيد الاستعادة',
        copied: 'تمّ النّسخ',
        copy: 'نسخ',
        copyField: 'نسخ الحقل',
        copying: 'نسخ',
        copyRow: 'نسخ الصف',
        copyWarning: 'أنت على وشك الكتابة فوق {{to}} بـ {{from}} لـ {{label}} {{title}}. هل أنت متأكد؟',
        create: 'إنشاء',
        created: 'تمّ الإنشاء',
        createdAt: 'تمّ الإنشاء في',
        createNew: 'أنشاء جديد',
        createNewLabel: 'إنشاء {{label}} جديد',
        creating: 'يتمّ الإنشاء',
        creatingNewLabel: 'جاري إنشاء {{label}} جديد',
        currentlyEditing: 'يقوم حاليًا بتحرير هذا المستند. إذا توليت، سيتم منعه من الاستمرار في التحرير وقد يفقد التغييرات غير المحفوظة.',
        custom: 'مخصص',
        dark: 'غامق',
        dashboard: 'لوحة التّحكّم',
        delete: 'حذف',
        deleted: 'تم الحذف',
        deletedAt: 'تم الحذف في',
        deletedCountSuccessfully: 'تمّ حذف {{count}} {{label}} بنجاح.',
        deletedSuccessfully: 'تمّ الحذف بنجاح.',
        deletePermanently: 'تجاوز السلة واحذف بشكل دائم',
        deleting: 'يتمّ الحذف...',
        depth: 'عمق',
        descending: 'تنازلي',
        deselectAllRows: 'إلغاء تحديد جميع الصفوف',
        document: 'وثيقة',
        documentIsTrashed: 'تم تحويل {{label}} هذا إلى المهملات وهو للقراءة فقط.',
        documentLocked: 'تم قفل المستند',
        documents: 'وثائق',
        duplicate: 'استنساخ',
        duplicateWithoutSaving: 'استنساخ بدون حفظ التغييرات',
        edit: 'تعديل',
        editAll: 'تحرير الكل',
        editedSince: 'تم التحرير منذ',
        editing: 'جاري التعديل',
        editingLabel_many: 'تعديل {{count}} {{label}}',
        editingLabel_one: 'تعديل {{count}} {{label}}',
        editingLabel_other: 'تعديل {{count}} {{label}}',
        editingTakenOver: 'تم الاستيلاء على التحرير',
        editLabel: 'تعديل {{label}}',
        email: 'البريد الإلكتروني',
        emailAddress: 'عنوان البريد الإلكتروني',
        emptyTrash: 'أفرغ القمامة',
        emptyTrashLabel: 'أفرغ سلة المحذوفات {{label}}',
        enterAValue: 'أدخل قيمة',
        error: 'خطأ',
        errors: 'أخطاء',
        exitLivePreview: 'إغلاق المعاينة المباشرة',
        export: 'تصدير',
        fallbackToDefaultLocale: 'الرجوع إلى اللغة الافتراضية',
        false: 'كاذب',
        filter: 'تصفية',
        filters: 'عوامل التصفية',
        filterWhere: 'تصفية {{label}} حيث',
        globals: 'عامة',
        goBack: 'العودة',
        groupByLabel: 'التجميع حسب {{label}}',
        import: 'استيراد',
        isEditing: 'يحرر',
        item: 'عنصر',
        items: 'عناصر',
        language: 'اللغة',
        lastModified: 'آخر تعديل',
        leaveAnyway: 'المغادرة على أي حال',
        leaveWithoutSaving: 'المغادرة بدون حفظ',
        light: 'فاتح',
        livePreview: 'معاينة مباشرة',
        loading: 'يتمّ التّحميل',
        locale: 'اللّغة',
        locales: 'اللّغات',
        menu: 'قائمة',
        moreOptions: 'خيارات أكثر',
        move: 'تحرك',
        moveConfirm: 'أنت على وشك نقل {{count}} {{label}} إلى <1>{{destination}}</1>. هل أنت متأكد؟',
        moveCount: 'انقل {{count}} {{label}}',
        moveDown: 'التّحريك إلى الأسفل',
        moveUp: 'التّحريك إلى الأعلى',
        moving: 'التحرك',
        movingCount: 'نقل {{count}} {{label}}',
        newPassword: 'كلمة مرور جديدة',
        next: 'التالي',
        no: 'لا',
        noDateSelected: 'لم يتم اختيار تاريخ',
        noFiltersSet: 'لم يتم تعيين أي عوامل تصفية',
        noLabel: '<لا {{label}}>',
        none: 'لا شيء',
        noOptions: 'لا خيارات',
        noResults: 'لا يوجد {{label}}. إما أن لا {{label}} موجودة حتى الآن أو لا تتطابق مع عوامل التصفية التي حددتها أعلاه.',
        notFound: 'غير موجود',
        nothingFound: 'لم يتم العثور على شيء',
        noTrashResults: 'لا {{label}} في المهملات.',
        noUpcomingEventsScheduled: 'لا يوجد أحداث مقبلة مجدولة.',
        noValue: 'لا يوجد قيمة',
        of: 'من',
        only: 'فقط',
        open: 'فتح',
        or: 'أو',
        order: 'التّرتيب',
        overwriteExistingData: 'استبدل بيانات الحقل الموجودة',
        pageNotFound: 'الصّفحة غير موجودة',
        password: 'كلمة المرور',
        pasteField: 'لصق الحقل',
        pasteRow: 'لصق الصف',
        payloadSettings: 'الإعدادات',
        permanentlyDelete: 'حذف بشكل دائم',
        permanentlyDeletedCountSuccessfully: 'تم حذف {{count}} {{label}} بشكل دائم بنجاح.',
        perPage: 'لكلّ صفحة: {{limit}}',
        previous: 'سابق',
        reindex: 'إعادة الفهرسة',
        reindexingAll: 'جاري إعادة فهرسة جميع {{collections}}.',
        remove: 'إزالة',
        rename: 'إعادة تسمية',
        reset: 'إعادة تعيين',
        resetPreferences: 'إعادة تعيين التفضيلات',
        resetPreferencesDescription: 'سيؤدي ذلك إلى إعادة تعيين جميع تفضيلاتك إلى الإعدادات الافتراضية.',
        resettingPreferences: 'إعادة تعيين التفضيلات.',
        restore: 'استعادة',
        restoreAsPublished: 'استعادة كإصدار منشور',
        restoredCountSuccessfully: 'تمت استعادة {{count}} {{label}} بنجاح.',
        restoring: 'احترم معنى النص الأصلي في سياق Payload. هنا قائمة بالمصطلحات الشائعة في Payload التي تحمل معانٍ محددة جدًا:\n    - Collection: المجموعة هي مجموعة من الوثائق التي تتشارك في الهيكل والغرض المشترك. تُستخدم المجموعات لتنظيم وإدارة المحتوى في Payload.',
        row: 'سطر',
        rows: 'أسطُر',
        save: 'حفظ',
        saving: 'جاري الحفظ...',
        schedulePublishFor: 'جدولة النشر لـ {{العنوان}}',
        searchBy: 'البحث عن طريق {{label}}',
        select: 'اختر',
        selectAll: 'تحديد كل {{count}} {{label}}',
        selectAllRows: 'حدد جميع الصفوف',
        selectedCount: 'تم تحديد {{count}} {{label}}',
        selectLabel: 'حدد {{label}}',
        selectValue: 'اختيار قيمة',
        showAllLabel: 'عرض كل {{label}}',
        sorryNotFound: 'عذرًا - لا يوجد شيء يتوافق مع طلبك.',
        sort: 'ترتيب',
        sortByLabelDirection: 'رتّب حسب {{label}} {{direction}}',
        stayOnThisPage: 'البقاء على هذه الصفحة',
        submissionSuccessful: 'تمت الإرسال بنجاح.',
        submit: 'إرسال',
        submitting: 'جاري التقديم...',
        success: 'النجاح',
        successfullyCreated: '{{label}} تم إنشاؤها بنجاح.',
        successfullyDuplicated: '{{label}} تم استنساخها بنجاح.',
        successfullyReindexed: 'تم إعادة فهرسة {{count}} من أصل {{total}} مستندات من {{collections}} مجموعات بنجاح.',
        takeOver: 'تولي',
        thisLanguage: 'العربية',
        time: 'الوقت',
        timezone: 'المنطقة الزمنية',
        titleDeleted: 'تم حذف {{label}} "{{title}}" بنجاح.',
        titleRestored: 'تمت استعادة "{{title}}" "{{label}}" بنجاح.',
        titleTrashed: '"{{label}}" "{{title}}" تم نقلها إلى سلة المهملات.',
        trash: 'سلة المهملات',
        trashedCountSuccessfully: '{{count}} {{label}} تم نقلها إلى سلة المهملات.',
        true: 'صحيح',
        unauthorized: 'غير مصرح به',
        unsavedChanges: 'لديك تغييرات غير محفوظة. قم بالحفظ أو التجاهل قبل المتابعة.',
        unsavedChangesDuplicate: 'لديك تغييرات لم يتم حفظها. هل تريد الاستمرار في الاستنساخ؟',
        untitled: 'بدون عنوان',
        upcomingEvents: 'الأحداث القادمة',
        updatedAt: 'تم التحديث في',
        updatedCountSuccessfully: 'تم تحديث {{count}} {{label}} بنجاح.',
        updatedLabelSuccessfully: 'تم تحديث {{label}} بنجاح.',
        updatedSuccessfully: 'تم التحديث بنجاح.',
        updateForEveryone: 'تحديث للجميع',
        updating: 'جار التحديث',
        uploading: 'جار الرفع',
        uploadingBulk: 'جاري التحميل {{current}} من {{total}}',
        user: 'المستخدم',
        username: 'اسم المستخدم',
        users: 'المستخدمين',
        value: 'القيمة',
        viewing: 'عرض',
        viewReadOnly: 'عرض للقراءة فقط',
        welcome: 'مرحبًا',
        yes: 'نعم'
    },
    localization: {
        cannotCopySameLocale: 'لا يمكن النسخ إلى نفس الموقع',
        copyFrom: 'نسخ من',
        copyFromTo: 'النسخ من {{from}} إلى {{to}}',
        copyTo: 'انسخ إلى',
        copyToLocale: 'نسخ إلى الموقع المحلي',
        localeToPublish: 'الموقع للنشر',
        selectLocaleToCopy: 'حدد الموقع المحلي للنسخ'
    },
    operators: {
        contains: 'يحتوي',
        equals: 'يساوي',
        exists: 'موجود',
        intersects: 'يتقاطع',
        isGreaterThan: 'أكبر من',
        isGreaterThanOrEqualTo: 'أكبر أو يساوي',
        isIn: 'موجود في',
        isLessThan: 'أصغر من',
        isLessThanOrEqualTo: 'أصغر أو يساوي',
        isLike: 'هو مثل',
        isNotEqualTo: 'لا يساوي',
        isNotIn: 'غير موجود في',
        isNotLike: 'ليس مثل',
        near: 'قريب من',
        within: 'في غضون'
    },
    upload: {
        addFile: 'إضافة ملف',
        addFiles: 'أضف ملفات',
        bulkUpload: 'تحميل بالجملة',
        crop: 'محصول',
        cropToolDescription: 'اسحب الزوايا المحددة للمنطقة، رسم منطقة جديدة أو قم بضبط القيم أدناه.',
        download: 'تحميل',
        dragAndDrop: 'قم بسحب وإسقاط ملفّ',
        dragAndDropHere: 'أو اسحب الملفّ وأفلته هنا',
        editImage: 'تعديل الصورة',
        fileName: 'اسم الملفّ',
        fileSize: 'حجم الملفّ',
        filesToUpload: 'ملفات للتحميل',
        fileToUpload: 'ملف للتحميل',
        focalPoint: 'نقطة التركيز',
        focalPointDescription: 'اسحب النقطة المركزية مباشرة على المعاينة أو قم بضبط القيم أدناه.',
        height: 'الطّول',
        lessInfo: 'معلومات أقلّ',
        moreInfo: 'معلومات أكثر',
        noFile: 'لا يوجد ملف',
        pasteURL: 'لصق الرابط',
        previewSizes: 'أحجام المعاينة',
        selectCollectionToBrowse: 'حدّد مجموعة لاستعراضها',
        selectFile: 'اختر ملفّ',
        setCropArea: 'حدد منطقة القص',
        setFocalPoint: 'حدد النقطة البؤرية',
        sizes: 'الاحجام',
        sizesFor: 'أحجام لـ {{label}}',
        width: 'العرض'
    },
    validation: {
        emailAddress: 'يرجى إدخال عنوان بريد إلكتروني صحيح.',
        enterNumber: 'يرجى إدخال رقم صحيح.',
        fieldHasNo: 'هذا الحقل ليس لديه {{label}}',
        greaterThanMax: '{{value}} أكبر من الحد الأقصى المسموح به {{label}} الذي يبلغ {{max}}.',
        invalidInput: 'هذا الحقل لديه إدخال غير صالح.',
        invalidSelection: 'هذا الحقل لديه اختيار غير صالح.',
        invalidSelections: 'هذا الحقل لديه الاختيارات الغير صالحة التالية:',
        lessThanMin: '{{value}} أقل من الحد الأدنى المسموح به {{label}} الذي يبلغ {{min}}.',
        limitReached: 'تم الوصول إلى الحد الأقصى، يمكن إضافة {{max}} عناصر فقط.',
        longerThanMin: 'يجب أن يكون هذا القيمة أطول من الحد الأدنى للطول الذي هو {{minLength}} أحرف.',
        notValidDate: '"{{value}}" ليس تاريخا صالحا.',
        required: 'هذا الحقل مطلوب.',
        requiresAtLeast: 'هذا الحقل يتطلب على الأقل {{count}} {{label}}.',
        requiresNoMoreThan: 'هذا الحقل يتطلب عدم تجاوز {{count}} {{label}}.',
        requiresTwoNumbers: 'هذا الحقل يتطلب رقمين.',
        shorterThanMax: 'يجب أن تكون هذه القيمة أقصر من الحد الأقصى للطول الذي هو {{maxLength}} أحرف.',
        timezoneRequired: 'مطلوب منطقة زمنية.',
        trueOrFalse: 'يمكن أن يكون هذا الحقل مساويًا فقط للقيمتين صحيح أو خطأ.',
        username: 'يرجى إدخال اسم مستخدم صالح. يمكن أن يحتوي على أحرف، أرقام، شرطات، فواصل وشرطات سفلية.',
        validUploadID: 'هذا الحقل ليس معرّف تحميل صالح.'
    },
    version: {
        type: 'النّوع',
        aboutToPublishSelection: 'أنت على وشك نشر كلّ {{label}} في التّحديد. هل أنت متأكّد؟',
        aboutToRestore: 'أنت على وشك استرجاع هذا المستند {{label}} إلى الحالة التّي كان عليها في {{versionDate}}.',
        aboutToRestoreGlobal: 'أنت على وشك استرجاع الاعداد العامّ {{label}} إلى الحالة التي كان عليها في {{versionDate}}.',
        aboutToRevertToPublished: 'أنت على وشك إعادة هذا المستند إلى حالته المنشورة. هل أنت متأكّد؟',
        aboutToUnpublish: 'أنت على وشك إلغاء نشر هذا المستند. هل أنت متأكّد؟',
        aboutToUnpublishSelection: 'أنت على وشك إلغاء نشر كلّ {{label}} في التّحديد. هل أنت متأكّد؟',
        autosave: 'حفظ تلقائي',
        autosavedSuccessfully: 'تمّ الحفظ التّلقائي بنجاح.',
        autosavedVersion: 'النّسخة المحفوظة تلقائياً',
        changed: 'تمّ التّغيير',
        changedFieldsCount_one: '{{count}} قام بتغيير الحقل',
        changedFieldsCount_other: '{{count}} حقول تم تغييرها',
        compareVersion: 'مقارنة النّسخة مع:',
        compareVersions: 'قارن الإصدارات',
        comparingAgainst: 'مقارنة مع',
        confirmPublish: 'تأكيد النّشر',
        confirmRevertToSaved: 'تأكيد الرّجوع للنسخة المنشورة',
        confirmUnpublish: 'تأكيد إلغاء النّشر',
        confirmVersionRestoration: 'تأكيد إستعادة النّسخة',
        currentDocumentStatus: 'المستند {{docStatus}} الحالي',
        currentDraft: 'المسودة الحالية',
        currentlyPublished: 'نشر حاليا',
        currentlyViewing: 'تمت المشاهدة حاليا',
        currentPublishedVersion: 'النسخة المنشورة الحالية',
        draft: 'مسودّة',
        draftSavedSuccessfully: 'تمّ حفظ المسودّة بنجاح.',
        lastSavedAgo: 'تم الحفظ آخر مرة قبل {{distance}}',
        modifiedOnly: 'تم التعديل فقط',
        moreVersions: 'المزيد من الإصدارات...',
        noFurtherVersionsFound: 'لم يتمّ العثور على نسخات أخرى',
        noRowsFound: 'لم يتمّ العثور على {{label}}',
        noRowsSelected: 'لم يتم اختيار {{label}}',
        preview: 'معاينة',
        previouslyDraft: 'سابقا مسودة',
        previouslyPublished: 'نشر سابقا',
        previousVersion: 'النسخة السابقة',
        problemRestoringVersion: 'حدث خطأ في استعادة هذه النّسخة',
        publish: 'نشر',
        publishAllLocales: 'نشر جميع المواقع',
        publishChanges: 'نشر التّغييرات',
        published: 'تمّ النّشر',
        publishIn: 'نشر في {{locale}}',
        publishing: 'نشر',
        restoreAsDraft: 'استعادة كمسودة',
        restoredSuccessfully: 'تمّت الاستعادة بنحاح.',
        restoreThisVersion: 'استعادة هذه النّسخة',
        restoring: 'تتمّ الاستعادة...',
        reverting: 'يتمّ الاسترجاع...',
        revertToPublished: 'الرّجوع للنسخة المنشورة',
        saveDraft: 'حفظ المسودّة',
        scheduledSuccessfully: 'تم الجدولة بنجاح.',
        schedulePublish: 'جدول النشر',
        selectLocales: 'حدّد اللّغات المراد عرضها',
        selectVersionToCompare: 'حدّد نسخة للمقارنة',
        showingVersionsFor: 'يتمّ عرض النًّسخ ل:',
        showLocales: 'اظهر اللّغات:',
        specificVersion: 'الإصدار المحدد',
        status: 'الحالة',
        unpublish: 'الغاء النّشر',
        unpublishing: 'يتمّ الغاء النّشر...',
        version: 'النّسخة',
        versionAgo: 'منذ {{distance}}',
        versionCount_many: 'تمّ العثور على {{count}} نُسخ',
        versionCount_none: 'لم يتمّ العثور على أيّ من النّسخ',
        versionCount_one: 'تمّ العثور على {{count}} من النّسخ',
        versionCount_other: 'تمّ العثور على {{count}} نُسخ',
        versionCreatedOn: 'تمّ ﻹنشاء النّسخة في {{version}}:',
        versionID: 'مُعرّف النّسخة',
        versions: 'النُّسَخ',
        viewingVersion: 'يتمّ استعراض نسخة ل {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'يتمّ استعراض نسخة للاعداد العامّ {{entityLabel}}',
        viewingVersions: 'يتمّ استعراض النُّسَخ ل {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'يتمّ استعراض النُّسَخ للاعداد العامّ {{entityLabel}}'
    }
};
export const ar = {
    dateFNSKey: 'ar',
    translations: arTranslations
};

//# sourceMappingURL=ar.js.map