{"version": 3, "sources": ["../../src/languages/sv.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const svTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Konto',\n    accountOfCurrentUser: 'Konto för nuvarande användare',\n    accountVerified: 'Kontot har verifierats',\n    alreadyActivated: 'Redan aktiverad',\n    alreadyLoggedIn: 'Redan inloggad',\n    apiKey: 'API-nyckel',\n    authenticated: 'Autentiserad',\n    backToLogin: 'Tillbaka till inloggningen',\n    beginCreateFirstUser: 'För att komma igång, skapa din första användare.',\n    changePassword: 'Byt lösenord',\n    checkYourEmailForPasswordReset:\n      'Om e-postadressen är kopplad till ett konto kommer du inom kort att få instruktioner för att återställa ditt lösenord. Vänligen kontrollera din skräppost-mapp om du inte ser e-postmeddelandet i din inkorg.',\n    confirmGeneration: 'Bekräfta generering',\n    confirmPassword: 'Bekräfta lösenord',\n    createFirstUser: 'Skapa första användaren',\n    emailNotValid: 'Angiven e-postadress är inte giltig',\n    emailOrUsername: 'E-post eller användarnamn',\n    emailSent: 'Meddelande skickat',\n    emailVerified: 'E-posten har verifierats',\n    enableAPIKey: 'Aktivera API-nyckel',\n    failedToUnlock: 'Det gick inte att låsa upp',\n    forceUnlock: 'Tvinga upplåsning',\n    forgotPassword: 'Glömt lösenord',\n    forgotPasswordEmailInstructions:\n      'Vänligen ange din e-postadress nedan. Du kommer att få ett e-postmeddelande med instruktioner om hur du återställer ditt lösenord.',\n    forgotPasswordQuestion: 'Glömt lösenordet?',\n    forgotPasswordUsernameInstructions:\n      'Ange ditt användarnamn nedan. Instruktioner om hur du återställer ditt lösenord kommer att skickas till e-postadressen kopplad till ditt användarnamn.',\n    generate: 'Generera',\n    generateNewAPIKey: 'Generera ny API-nyckel',\n    generatingNewAPIKeyWillInvalidate:\n      'Att generera en ny API-nyckel kommer <1>ogiltigförklara</1> föregående nyckel. Är du säker på att du vill fortsätta?',\n    lockUntil: 'Lås tills',\n    logBackIn: 'Logga in igen',\n    loggedIn: 'För att logga in med en annan användare, bör du <0>logga ut</0> först.',\n    loggedInChangePassword:\n      'För att ändra ditt lösenord, gå till ditt <0>konto</0> och redigera ditt lösenord där.',\n    loggedOutInactivity: 'Du har blivit utloggad på grund av inaktivitet.',\n    loggedOutSuccessfully: 'Du har loggats ut',\n    loggingOut: 'Loggar ut...',\n    login: 'Logga in',\n    loginAttempts: 'Inloggningsförsök',\n    loginUser: 'Logga in användare',\n    loginWithAnotherUser: 'För att logga in med en annan användare, bör du <0>logga ut</0> först.',\n    logOut: 'Logga ut',\n    logout: 'Logga ut',\n    logoutSuccessful: 'Utloggningen lyckades',\n    logoutUser: 'Logga ut användare',\n    newAccountCreated:\n      'Ett nytt konto har precis skapats som du kan komma åt <a href=\"{{serverURL}}\">{{serverURL}}</a> Klicka på följande länk eller klistra in webbadressen nedan i din webbläsare för att verifiera din e-post: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Efter att ha verifierat din e-post kommer du att kunna logga in.',\n    newAPIKeyGenerated: 'Ny API-nyckel genererad',\n    newPassword: 'Nytt lösenord',\n    passed: 'Autentisering godkänd',\n    passwordResetSuccessfully: 'Lösenordet har återställts',\n    resetPassword: 'Återställ lösenord',\n    resetPasswordExpiration: 'Utgångstid för återställning av lösenord',\n    resetPasswordToken: 'Återställningstoken för lösenord',\n    resetYourPassword: 'Återställ ditt lösenord',\n    stayLoggedIn: 'Förbli inloggad',\n    successfullyRegisteredFirstUser: 'Registrerade den första användaren',\n    successfullyUnlocked: 'Upplåst',\n    tokenRefreshSuccessful: 'Tokenuppdatering lyckades',\n    unableToVerify: 'Det går inte att verifiera',\n    username: 'Användarnamn',\n    usernameNotValid: 'Det angivna användarnamnet är inte giltigt',\n    verified: 'Verifierad',\n    verifiedSuccessfully: 'Verifierad',\n    verify: 'Verifiera',\n    verifyUser: 'Verifiera användare',\n    verifyYourEmail: 'Verifiera din e-post',\n    youAreInactive:\n      'Du har inte varit aktiv på ett tag och kommer inom kort att automatiskt loggas ut för din egen säkerhet. Vill du forsätta att vara inloggad?',\n    youAreReceivingResetPassword:\n      'Du får detta för att du (eller någon annan) har begärt återställning av lösenordet för ditt konto. Klicka på följande länk eller klistra in den i din webbläsare för att slutföra processen:',\n    youDidNotRequestPassword:\n      'Om du inte begärde detta, ignorera detta e-postmeddelande och ditt lösenord kommer att förbli oförändrat.',\n  },\n  error: {\n    accountAlreadyActivated: 'Detta konto har redan aktiverats',\n    autosaving: 'Det uppstod ett problem när det här dokumentet skulle sparas automatiskt.',\n    correctInvalidFields: 'Vänligen korrigera ogiltiga fält',\n    deletingFile: 'Det gick inte att ta bort filen',\n    deletingTitle:\n      'Det uppstod ett fel vid borttagningen av {{title}}. Vänligen kontrollera din anslutning och försök igen.',\n    documentNotFound:\n      'Dokumentet med ID {{id}} kunde inte hittas. Det kan ha raderats eller aldrig existerat, eller så kanske du inte har tillgång till det.',\n    emailOrPasswordIncorrect: 'E-postadressen eller lösenordet som angivits är felaktigt.',\n    followingFieldsInvalid_one: 'Följande fält är ogiltigt:',\n    followingFieldsInvalid_other: 'Följande fält är ogiltiga:',\n    incorrectCollection: 'Felaktig samling',\n    insufficientClipboardPermissions:\n      'Åtkomst till urklipp nekades. Kontrollera dina behörigheter för urklipp.',\n    invalidClipboardData: 'Ogiltiga urklippsdata.',\n    invalidFileType: 'Ogiltig filtyp',\n    invalidFileTypeValue: 'Ogiltig filtyp: {{value}}',\n    invalidRequestArgs: 'Ogiltiga argument har skickats i begäran: {{args}}',\n    loadingDocument: 'Det gick inte att läsa in dokumentet med ID {{id}}.',\n    localesNotSaved_one: 'Följande språk kunde inte sparas:',\n    localesNotSaved_other: 'Följande språk kunde inte sparas:',\n    logoutFailed: 'Utloggning misslyckades',\n    missingEmail: 'E-postadress saknas.',\n    missingIDOfDocument: 'Saknar ID för dokumentet som ska uppdateras.',\n    missingIDOfVersion: 'ID för versionen saknas.',\n    missingRequiredData: 'Obligatorisk data saknas.',\n    noFilesUploaded: 'Inga filer laddades upp.',\n    noMatchedField: 'Inget matchande fält hittades för \"{{label}}\"',\n    notAllowedToAccessPage: 'Du får inte komma åt den här sidan.',\n    notAllowedToPerformAction: 'Du får inte utföra denna åtgärd.',\n    notFound: 'Den begärda resursen hittades inte.',\n    noUser: 'Ingen Användare',\n    previewing: 'Det uppstod ett problem när det här dokumentet skulle förhandsgranskas.',\n    problemUploadingFile: 'Det uppstod ett problem när filen laddades upp.',\n    restoringTitle:\n      'Det uppstod ett fel vid återställning av {{title}}. Vänligen kontrollera din anslutning och försök igen.',\n    tokenInvalidOrExpired: 'Token är antingen ogiltig eller har löpt ut.',\n    tokenNotProvided: 'Token inte tillhandahållet.',\n    unableToCopy: 'Kan inte kopiera.',\n    unableToDeleteCount: 'Det gick inte att ta bort {{count}} av {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Fel vid omindexering av samlingen {{collection}}. Operationen avbröts.',\n    unableToUpdateCount: 'Det gick inte att uppdatera {{count}} av {{total}} {{label}}.',\n    unauthorized: 'Obehörig, du måste vara inloggad för att göra denna begäran.',\n    unauthorizedAdmin: 'Obehörig, denna användare har inte åtkomst till adminpanelen.',\n    unknown: 'Ett okänt fel har uppstått.',\n    unPublishingDocument: 'Det uppstod ett problem när det här dokumentet skulle avpubliceras.',\n    unspecific: 'Ett fel har uppstått.',\n    unverifiedEmail: 'Vänligen verifiera din e-post innan du loggar in.',\n    userEmailAlreadyRegistered: 'En användare med den angivna e-postadressen är redan registrerad.',\n    userLocked: 'Den här användaren är låst på grund av för många misslyckade inloggningsförsök.',\n    usernameAlreadyRegistered: 'En användare med det angivna användarnamnet är redan registrerad.',\n    usernameOrPasswordIncorrect: 'Användarnamnet eller lösenordet som angavs är felaktigt.',\n    valueMustBeUnique: 'Värdet måste vara unikt',\n    verificationTokenInvalid: 'Verifieringstoken är ogiltigt',\n  },\n  fields: {\n    addLabel: 'Lägg till {{label}}',\n    addLink: 'Lägg till länk',\n    addNew: 'Lägg till ny',\n    addNewLabel: 'Lägg till ny {{label}}',\n    addRelationship: 'Lägg till relation',\n    addUpload: 'Lägg till uppladdning',\n    block: 'block',\n    blocks: 'block',\n    blockType: 'Blocktyp',\n    chooseBetweenCustomTextOrDocument:\n      'Välj mellan att ange en anpassad text-URL eller länka till ett annat dokument.',\n    chooseDocumentToLink: 'Välj ett dokument att länka till',\n    chooseFromExisting: 'Välj bland befintliga',\n    chooseLabel: 'Välj {{label}}',\n    collapseAll: 'Fäll ihop alla',\n    customURL: 'Anpassad URL',\n    editLabelData: 'Redigera {{label}} data',\n    editLink: 'Redigera länk',\n    editRelationship: 'Redigera relation',\n    enterURL: 'Ange en URL',\n    internalLink: 'Intern länk',\n    itemsAndMore: '{{items}} och {{count}} mer',\n    labelRelationship: '{{label}}-relation',\n    latitude: 'Latitud',\n    linkedTo: 'Länkad till <0>{{label}}</0>',\n    linkType: 'Länktyp',\n    longitude: 'Longitud',\n    newLabel: 'Ny {{label}}',\n    openInNewTab: 'Öppna i ny flik',\n    passwordsDoNotMatch: 'Lösenorden matchar inte.',\n    relatedDocument: 'Relaterat dokument',\n    relationTo: 'Relation till',\n    removeRelationship: 'Ta bort relation',\n    removeUpload: 'Ta bort uppladdning',\n    saveChanges: 'Spara ändringar',\n    searchForBlock: 'Sök efter ett block',\n    selectExistingLabel: 'Välj befintlig {{label}}',\n    selectFieldsToEdit: 'Välj fält att redigera',\n    showAll: 'Visa alla',\n    swapRelationship: 'Byt förhållande',\n    swapUpload: 'Byt uppladdning',\n    textToDisplay: 'Text att visa',\n    toggleBlock: 'Växla block',\n    uploadNewLabel: 'Ladda upp ny {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Bläddra efter mapp',\n    byFolder: 'Efter mapp',\n    deleteFolder: 'Ta bort mapp',\n    folderName: 'Mappnamn',\n    folders: 'Mappar',\n    folderTypeDescription: 'Välj vilken typ av samlingsdokument som ska tillåtas i denna mapp.',\n    itemHasBeenMoved: '{{title}} har flyttats till {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} har flyttats till rotmappen',\n    itemsMovedToFolder: '{{title}} flyttad till {{folderName}}',\n    itemsMovedToRoot: '{{titel}} flyttades till rotmappen',\n    moveFolder: 'Flytta mapp',\n    moveItemsToFolderConfirmation:\n      'Du är på väg att flytta <1>{{count}} {{label}}</1> till <2>{{toFolder}}</2>. Är du säker?',\n    moveItemsToRootConfirmation:\n      'Du är på väg att flytta <1>{{count}} {{label}}</1> till root-mappen. Är du säker?',\n    moveItemToFolderConfirmation:\n      'Du håller på att flytta <1>{{title}}</1> till <2>{{toFolder}}</2>. Är du säker?',\n    moveItemToRootConfirmation:\n      'Du är på väg att flytta <1>{{title}}</1> till rotmappen. Är du säker?',\n    movingFromFolder: 'Flyttar {{title}} från {{fromFolder}}',\n    newFolder: 'Ny mapp',\n    noFolder: 'Ingen mapp',\n    renameFolder: 'Byt namn på mapp',\n    searchByNameInFolder: 'Sök efter namn i {{folderName}}',\n    selectFolderForItem: 'Välj mapp för {{title}}',\n  },\n  general: {\n    name: 'Namn',\n    aboutToDelete: 'Du är på väg att ta bort {{label}} <1>{{title}}</1>. Är du säker?',\n    aboutToDeleteCount_many: 'Du är på väg att ta bort {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Du är på väg att ta bort {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Du är på väg att ta bort {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Du är på väg att permanent radera {{label}} <1>{{title}}</1>. Är du säker?',\n    aboutToPermanentlyDeleteTrash:\n      'Du är på väg att permanent radera <0>{{count}}</0> <1>{{label}}</1> från papperskorgen. Är du säker?',\n    aboutToRestore: 'Du är på väg att återställa {{label}} <1>{{title}}</1>. Är du säker?',\n    aboutToRestoreAsDraft:\n      'Du är på väg att återställa {{label}} <1>{{title}}</1> som ett utkast. Är du säker?',\n    aboutToRestoreAsDraftCount: 'Du är på väg att återställa {{count}} {{label}} som utkast',\n    aboutToRestoreCount: 'Du är på väg att återställa {{count}} {{label}}',\n    aboutToTrash:\n      'Du håller på att flytta {{label}} <1>{{title}}</1> till papperskorgen. Är du säker?',\n    aboutToTrashCount: 'Du håller på att flytta {{count}} {{label}} till papperskorgen',\n    addBelow: 'Lägg till nedanför',\n    addFilter: 'Lägg till filter',\n    adminTheme: 'Adminutseende',\n    all: 'Alla',\n    allCollections: 'Alla samlingar',\n    allLocales: 'Alla språk',\n    and: 'Och',\n    anotherUser: 'En annan användare',\n    anotherUserTakenOver: 'En annan användare har tagit över redigeringen av detta dokument.',\n    applyChanges: 'Verkställ ändringar',\n    ascending: 'Stigande',\n    automatic: 'Automatiskt',\n    backToDashboard: 'Tillbaka till översikten',\n    cancel: 'Avbryt',\n    changesNotSaved:\n      'Dina ändringar har inte sparats. Om du lämnar nu kommer du att förlora dina ändringar.',\n    clear: 'Rensa',\n    clearAll: 'Rensa alla',\n    close: 'Stäng',\n    collapse: 'Fäll ihop',\n    collections: 'Samlingar',\n    columns: 'Kolumner',\n    columnToSort: 'Kolumn att sortera',\n    confirm: 'Bekräfta',\n    confirmCopy: 'Bekräfta kopia',\n    confirmDeletion: 'Bekräfta radering',\n    confirmDuplication: 'Bekräfta dubblering',\n    confirmMove: 'Bekräfta flytt',\n    confirmReindex: 'Omindexera alla {{collections}}?',\n    confirmReindexAll: 'Omindexera alla samlingar?',\n    confirmReindexDescription:\n      'Detta kommer att ta bort befintliga index och omindexera dokumenten i {{collections}}-samlingarna.',\n    confirmReindexDescriptionAll:\n      'Detta kommer att ta bort befintliga index och omindexera dokumenten i alla samlingar.',\n    confirmRestoration: 'Bekräfta återställning',\n    copied: 'Kopierad',\n    copy: 'Kopiera',\n    copyField: 'Kopiera fält',\n    copying: 'Kopierar...',\n    copyRow: 'Kopiera rad',\n    copyWarning:\n      'Du håller på att skriva över {{to}} med {{from}} för {{label}} {{title}}. Är du säker?',\n    create: 'Skapa',\n    created: 'Skapat',\n    createdAt: 'Skapat',\n    createNew: 'Skapa ny',\n    createNewLabel: 'Skapa ny {{label}}',\n    creating: 'Skapar...',\n    creatingNewLabel: 'Skapar ny {{label}}',\n    currentlyEditing:\n      'redigerar för närvarande detta dokument. Om du tar över kommer de att blockeras från att fortsätta redigera och kan också förlora osparade ändringar.',\n    custom: 'Anpassad',\n    dark: 'Mörkt',\n    dashboard: 'Översikt',\n    delete: 'Ta bort',\n    deleted: 'Raderad',\n    deletedAt: 'Raderad Vid',\n    deletedCountSuccessfully: 'Raderade {{count}} {{label}}',\n    deletedSuccessfully: 'Borttaget',\n    deletePermanently: 'Hoppa över papperskorgen och radera permanent',\n    deleting: 'Tar bort...',\n    depth: 'Djup',\n    descending: 'Fallande',\n    deselectAllRows: 'Avmarkera alla rader',\n    document: 'Dokument',\n    documentIsTrashed: 'Det här {{label}} har slagits i spill och är skrivskyddad.',\n    documentLocked: 'Dokument låst',\n    documents: 'Dokument',\n    duplicate: 'Duplicera',\n    duplicateWithoutSaving: 'Duplicera utan att spara ändringar',\n    edit: 'Redigera',\n    editAll: 'Redigera alla',\n    editedSince: 'Redigerad sedan',\n    editing: 'Redigerar',\n    editingLabel_many: 'Redigerar {{count}} {{label}}',\n    editingLabel_one: 'Redigerar {{count}} {{label}}',\n    editingLabel_other: 'Redigerar {{count}} {{label}}',\n    editingTakenOver: 'Redigering övertagen',\n    editLabel: 'Redigera {{label}}',\n    email: 'E-post',\n    emailAddress: 'E-postadress',\n    emptyTrash: 'Töm papperskorgen',\n    emptyTrashLabel: 'Töm {{label}} papperskorgen',\n    enterAValue: 'Ange ett värde',\n    error: 'Fel',\n    errors: 'Fel',\n    exitLivePreview: 'Avsluta förhandsgranskning',\n    export: 'Exportera',\n    fallbackToDefaultLocale: 'Återgå till standardspråk',\n    false: 'Falskt',\n    filter: 'Filter',\n    filters: 'Filter',\n    filterWhere: 'Filtrera {{label}} där',\n    globals: 'Globala',\n    goBack: 'Gå tillbaka',\n    groupByLabel: 'Gruppera efter {{label}}',\n    import: 'Importera',\n    isEditing: 'redigerar',\n    item: 'artikel',\n    items: 'artiklar',\n    language: 'Språk',\n    lastModified: 'Senast ändrad',\n    leaveAnyway: 'Lämna ändå',\n    leaveWithoutSaving: 'Lämna utan att spara',\n    light: 'Ljust',\n    livePreview: 'Förhandsgranskning',\n    loading: 'Laddar...',\n    locale: 'Språk',\n    locales: 'Språk',\n    menu: 'Meny',\n    moreOptions: 'Fler alternativ',\n    move: 'Flytta',\n    moveConfirm:\n      'Du håller på att flytta {{count}} {{label}} till <1>{{destination}}</1>. Är du säker?',\n    moveCount: 'Flytta {{count}} {{label}}',\n    moveDown: 'Flytta ner',\n    moveUp: 'Flytta upp',\n    moving: 'Flyttar',\n    movingCount: 'Flyttar {{count}} {{label}}',\n    newPassword: 'Nytt lösenord',\n    next: 'Nästa',\n    no: 'Nej',\n    noDateSelected: 'Inget datum valt',\n    noFiltersSet: 'Inga filter inställda',\n    noLabel: '<Ingen {{label}}>',\n    none: 'Ingen',\n    noOptions: 'Inga alternativ',\n    noResults:\n      'Inga {{label}} hittades. Antingen finns inga {{label}} ännu eller så matchar inga filtren du har angett ovan.',\n    notFound: 'Hittades inte',\n    nothingFound: 'Inget hittades',\n    noTrashResults: 'Inget {{label}} i papperskorgen.',\n    noUpcomingEventsScheduled: 'Inga kommande händelser är planerade.',\n    noValue: 'Inget värde',\n    of: 'av',\n    only: 'Endast',\n    open: 'Öppna',\n    or: 'Eller',\n    order: 'Ordning',\n    overwriteExistingData: 'Skriv över befintlig fältdata',\n    pageNotFound: 'Sidan hittas inte',\n    password: 'Lösenord',\n    pasteField: 'Klistra in fält',\n    pasteRow: 'Klistra in rad',\n    payloadSettings: 'Systeminställningar',\n    permanentlyDelete: 'Radera Permanent',\n    permanentlyDeletedCountSuccessfully: '{{count}} {{label}} har raderats permanent.',\n    perPage: 'Per Sida: {{limit}}',\n    previous: 'Föregående',\n    reindex: 'Omindexera',\n    reindexingAll: 'Omindexerar alla {{collections}}...',\n    remove: 'Ta bort',\n    rename: 'Byt namn',\n    reset: 'Återställ',\n    resetPreferences: 'Återställ preferenser',\n    resetPreferencesDescription:\n      'Detta kommer att återställa alla dina preferenser till standardinställningarna.',\n    resettingPreferences: 'Återställer preferenser...',\n    restore: 'Återställ',\n    restoreAsPublished: 'Återställ som publicerad version',\n    restoredCountSuccessfully: 'Återställde {{count}} {{label}} framgångsrikt.',\n    restoring:\n      'Respektera innebörden av den ursprungliga texten inom kontexten av Payload. Här är en lista över gemensamma Payload-termer som bär väldigt specifika betydelser:\\n    - Samling: En samling är en grupp dokument som delar en gemensam struktur och syfte. Samlingar används för att organisera och hantera innehåll i Payload.\\n    - Fält: Ett fält är en specifik data inom ett dokument i en samling. Fält definierar strukturen och typen av data som kan lagras i ett dokument.\\n    - Dokument: Ett dokument är en',\n    row: 'Rad',\n    rows: 'Rader',\n    save: 'Spara',\n    saving: 'Sparar...',\n    schedulePublishFor: 'Schemalägg publicering för {{title}}',\n    searchBy: 'Sök efter {{label}}',\n    select: 'Välj',\n    selectAll: 'Välj alla {{count}} {{label}}',\n    selectAllRows: 'Välj alla rader',\n    selectedCount: '{{count}} {{label}} har valts',\n    selectLabel: 'Välj {{label}}',\n    selectValue: 'Välj ett värde',\n    showAllLabel: 'Visa alla {{label}}',\n    sorryNotFound: 'Tyvärr, det finns inget som motsvarar din begäran.',\n    sort: 'Sortera',\n    sortByLabelDirection: 'Sortera efter {{label}} {{direction}}',\n    stayOnThisPage: 'Stanna på denna sida',\n    submissionSuccessful: 'Skickat',\n    submit: 'Skicka',\n    submitting: 'Skickar...',\n    success: 'Lyckades',\n    successfullyCreated: '{{label}} skapades',\n    successfullyDuplicated: '{{label}} duplicerades',\n    successfullyReindexed:\n      'Lyckades omindexera {{count}} av {{total}} dokument från {{collections}} samlingar.',\n    takeOver: 'Ta över',\n    thisLanguage: 'Svenska',\n    time: 'Tid',\n    timezone: 'Tidszon',\n    titleDeleted: '{{label}} \"{{title}}\" togs bort',\n    titleRestored: '{{label}} \"{{title}}\" har framgångsrikt återställts.',\n    titleTrashed: '{{label}} \"{{title}}\" flyttades till papperskorgen.',\n    trash: 'Skräp',\n    trashedCountSuccessfully: '{{count}} {{label}} flyttades till papperskorgen.',\n    true: 'Sann',\n    unauthorized: 'Obehörig',\n    unsavedChanges: 'Du har osparade ändringar. Spara innan du fortsätter.',\n    unsavedChangesDuplicate: 'Du har osparade ändringar. Vill du fortsätta att duplicera?',\n    untitled: 'Namnlös',\n    upcomingEvents: 'Kommande händelser',\n    updatedAt: 'Uppdaterat',\n    updatedCountSuccessfully: 'Uppdaterade {{count}} {{label}}',\n    updatedLabelSuccessfully: 'Uppdaterade {{label}}',\n    updatedSuccessfully: 'Uppdaterades',\n    updateForEveryone: 'Uppdatera för alla',\n    updating: 'Uppdaterar...',\n    uploading: 'Laddar upp...',\n    uploadingBulk: 'Laddar upp {{current}} av {{total}}',\n    user: 'Användare',\n    username: 'Användarnamn',\n    users: 'Användare',\n    value: 'Värde',\n    viewing: 'Visar',\n    viewReadOnly: 'Visa som skrivskyddad',\n    welcome: 'Välkommen',\n    yes: 'Ja',\n  },\n  localization: {\n    cannotCopySameLocale: 'Kan inte kopiera till samma språk',\n    copyFrom: 'Kopiera från',\n    copyFromTo: 'Kopierar från {{from}} till {{to}}',\n    copyTo: 'Kopiera till',\n    copyToLocale: 'Kopiera till språk',\n    localeToPublish: 'Publicera språk',\n    selectLocaleToCopy: 'Välj språk att kopiera',\n  },\n  operators: {\n    contains: 'innehåller',\n    equals: 'lika med',\n    exists: 'finns',\n    intersects: 'korsar',\n    isGreaterThan: 'är större än',\n    isGreaterThanOrEqualTo: 'är större än eller lika med',\n    isIn: 'finns i',\n    isLessThan: 'är mindre än',\n    isLessThanOrEqualTo: 'är mindre än eller lika med',\n    isLike: 'matchar med',\n    isNotEqualTo: 'är inte lika med',\n    isNotIn: 'finns inte i',\n    isNotLike: 'matchar inte med',\n    near: 'nära',\n    within: 'inom',\n  },\n  upload: {\n    addFile: 'Lägg till fil',\n    addFiles: 'Lägg till filer',\n    bulkUpload: 'Massuppladdning',\n    crop: 'Beskär',\n    cropToolDescription:\n      'Dra i hörnen på det valda området, rita ett nytt område eller justera värdena nedan.',\n    download: 'Ladda ner',\n    dragAndDrop: 'Dra och släpp en fil',\n    dragAndDropHere: 'eller dra och släpp en fil här',\n    editImage: 'Redigera bild',\n    fileName: 'Filnamn',\n    fileSize: 'Filstorlek',\n    filesToUpload: 'Filer att ladda upp',\n    fileToUpload: 'Fil att ladda upp',\n    focalPoint: 'Fokuspunkt',\n    focalPointDescription:\n      'Dra fokuspunkten direkt på förhandsgranskningen eller justera värdena nedan.',\n    height: 'Höjd',\n    lessInfo: 'Mindre info',\n    moreInfo: 'Mer info',\n    noFile: 'Ingen fil',\n    pasteURL: 'Klistra in URL',\n    previewSizes: 'Förhandsgranska storlekar',\n    selectCollectionToBrowse: 'Välj en samling att bläddra i',\n    selectFile: 'Välj en fil',\n    setCropArea: 'Ange beskärning',\n    setFocalPoint: 'Ställ in fokuspunkt',\n    sizes: 'Storlekar',\n    sizesFor: 'Storlekar för {{label}}',\n    width: 'Bredd',\n  },\n  validation: {\n    emailAddress: 'Vänligen ange en giltig e-postadress.',\n    enterNumber: 'Vänligen skriv in ett giltigt nummer.',\n    fieldHasNo: 'Detta fält har ingen {{label}}',\n    greaterThanMax: '{{value}} är större än den maximalt tillåtna {{label}} av {{max}}.',\n    invalidInput: 'Det här fältet har en ogiltig inmatning.',\n    invalidSelection: 'Det här fältet har ett ogiltigt urval.',\n    invalidSelections: 'Det här fältet har följande ogiltiga val:',\n    lessThanMin: '{{value}} är mindre än den minst tillåtna {{label}} av {{min}}.',\n    limitReached: 'Gränsen nådd, endast {{max}} objekt kan läggas till.',\n    longerThanMin: 'Detta värde måste vara längre än minimilängden på {{minLength}} tecken.',\n    notValidDate: '\"{{value}}\" är inte ett giltigt datum.',\n    required: 'Detta fält är obligatoriskt.',\n    requiresAtLeast: 'Detta fält kräver minst {{count}} {{label}}.',\n    requiresNoMoreThan: 'Detta fält kräver inte mer än {{count}} {{label}}.',\n    requiresTwoNumbers: 'Detta fält kräver två nummer.',\n    shorterThanMax: 'Detta värde måste vara kortare än maxlängden på {{maxLength}} tecken.',\n    timezoneRequired: 'En tidszon krävs.',\n    trueOrFalse: 'Detta fält kan bara vara lika med sant eller falskt.',\n    username:\n      'Var god ange ett giltigt användarnamn. Kan innehålla bokstäver, siffror, bindestreck, punkter och understreck.',\n    validUploadID: 'Det här fältet är inte ett giltigt uppladdnings-ID',\n  },\n  version: {\n    type: 'Typ',\n    aboutToPublishSelection: 'Du kommer publicera alla {{label}} i urvalet. Är du säker?',\n    aboutToRestore:\n      'Du kommer återställa detta {{label}} dokumentet till det tillståndet som det var den {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Du kommer återställa det globala {{label}} till det tillståndet som det var den {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Du kommer återställa det här dokumentets ändringar till dess publicerade tillstånd. Är du säker?',\n    aboutToUnpublish: 'Du kommer avpublicera detta dokument. Är du säker?',\n    aboutToUnpublishSelection:\n      'Du är på väg att avpublicera alla {{label}} i urvalet. Är du säker?',\n    autosave: 'Spara automatiskt',\n    autosavedSuccessfully: 'Autosparades',\n    autosavedVersion: 'Autosparad version',\n    changed: 'Ändrad',\n    changedFieldsCount_one: '{{count}} ändrat fält',\n    changedFieldsCount_other: '{{count}} ändrade fält',\n    compareVersion: 'Jämför version med:',\n    compareVersions: 'Jämför versioner',\n    comparingAgainst: 'Jämför mot',\n    confirmPublish: 'Bekräfta publicering',\n    confirmRevertToSaved: 'Bekräfta återgång till sparad version',\n    confirmUnpublish: 'Bekräfta avpublicering',\n    confirmVersionRestoration: 'Bekräfta versionsåterställning',\n    currentDocumentStatus: 'Nuvarande {{docStatus}} dokument',\n    currentDraft: 'Nuvarande utkast',\n    currentlyPublished: 'För närvarande publicerad',\n    currentlyViewing: 'Visar för tillfället',\n    currentPublishedVersion: 'Aktuell publicerad version',\n    draft: 'Utkast',\n    draftSavedSuccessfully: 'Utkastet sparades',\n    lastSavedAgo: 'Senast sparad för {{distance}} sedan',\n    modifiedOnly: 'Endast modifierad',\n    moreVersions: 'Fler versioner...',\n    noFurtherVersionsFound: 'Inga fler versioner hittades',\n    noRowsFound: 'Inga {{label}} hittades',\n    noRowsSelected: 'Inget {{etikett}} valt',\n    preview: 'Förhandsgranska',\n    previouslyDraft: 'Tidigare ett Utkast',\n    previouslyPublished: 'Tidigare publicerad',\n    previousVersion: 'Föregående version',\n    problemRestoringVersion: 'Det uppstod ett problem när den här versionen skulle återställas',\n    publish: 'Publicera',\n    publishAllLocales: 'Publicera alla språk',\n    publishChanges: 'Publicera ändringar',\n    published: 'Publicerad',\n    publishIn: 'Publicera i {{locale}}',\n    publishing: 'Publicerar...',\n    restoreAsDraft: 'Återställ som utkast',\n    restoredSuccessfully: 'Återställd',\n    restoreThisVersion: 'Återställ den här versionen',\n    restoring: 'Återställer...',\n    reverting: 'Återställer...',\n    revertToPublished: 'Återgå till publicerad',\n    saveDraft: 'Spara Utkast',\n    scheduledSuccessfully: 'Schemalagd',\n    schedulePublish: 'Schemalägg publicering',\n    selectLocales: 'Välj språk att visa',\n    selectVersionToCompare: 'Välj en version att jämföra',\n    showingVersionsFor: 'Visar versioner för:',\n    showLocales: 'Visa språk:',\n    specificVersion: 'Specifik version',\n    status: 'Status',\n    unpublish: 'Avpublicera',\n    unpublishing: 'Avpublicerar...',\n    version: 'Version',\n    versionAgo: '{{distance}} sedan',\n    versionCount_many: '{{count}} versioner hittades',\n    versionCount_none: 'Inga versioner hittades',\n    versionCount_one: '{{count}} version hittades',\n    versionCount_other: '{{count}} versioner hittades',\n    versionCreatedOn: '{{version}} skapad den:',\n    versionID: 'Versions-ID',\n    versions: 'Versioner',\n    viewingVersion: 'Visar version för {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Visa version för den globala {{entityLabel}}',\n    viewingVersions: 'Visar versioner för {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Visa versioner för den globala {{entityLabel}}',\n  },\n}\n\nexport const sv: Language = {\n  dateFNSKey: 'sv',\n  translations: svTranslations,\n}\n"], "names": ["svTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "sv", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}