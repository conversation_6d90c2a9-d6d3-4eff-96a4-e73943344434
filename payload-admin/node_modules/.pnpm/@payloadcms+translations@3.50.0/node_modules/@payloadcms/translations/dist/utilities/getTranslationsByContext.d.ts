import type { Language } from '../types.js';
export declare const getTranslationsByContext: (selectedLanguage: Language, context: "api" | "client") => {
    authentication: {
        account: string;
        accountOfCurrentUser: string;
        accountVerified: string;
        alreadyActivated: string;
        alreadyLoggedIn: string;
        apiKey: string;
        authenticated: string;
        backToLogin: string;
        beginCreateFirstUser: string;
        changePassword: string;
        checkYourEmailForPasswordReset: string;
        confirmGeneration: string;
        confirmPassword: string;
        createFirstUser: string;
        emailNotValid: string;
        emailOrUsername: string;
        emailSent: string;
        emailVerified: string;
        enableAPIKey: string;
        failedToUnlock: string;
        forceUnlock: string;
        forgotPassword: string;
        forgotPasswordEmailInstructions: string;
        forgotPasswordUsernameInstructions: string;
        usernameNotValid: string;
        forgotPasswordQuestion: string;
        generate: string;
        generateNewAPIKey: string;
        generatingNewAPIKeyWillInvalidate: string;
        lockUntil: string;
        logBackIn: string;
        loggedIn: string;
        loggedInChangePassword: string;
        loggedOutInactivity: string;
        loggedOutSuccessfully: string;
        loggingOut: string;
        login: string;
        loginAttempts: string;
        loginUser: string;
        loginWithAnotherUser: string;
        logOut: string;
        logout: string;
        logoutSuccessful: string;
        logoutUser: string;
        newAccountCreated: string;
        newAPIKeyGenerated: string;
        newPassword: string;
        passed: string;
        passwordResetSuccessfully: string;
        resetPassword: string;
        resetPasswordExpiration: string;
        resetPasswordToken: string;
        resetYourPassword: string;
        stayLoggedIn: string;
        successfullyRegisteredFirstUser: string;
        successfullyUnlocked: string;
        tokenRefreshSuccessful: string;
        unableToVerify: string;
        username: string;
        verified: string;
        verifiedSuccessfully: string;
        verify: string;
        verifyUser: string;
        verifyYourEmail: string;
        youAreInactive: string;
        youAreReceivingResetPassword: string;
        youDidNotRequestPassword: string;
    };
    error: {
        accountAlreadyActivated: string;
        autosaving: string;
        correctInvalidFields: string;
        deletingFile: string;
        deletingTitle: string;
        documentNotFound: string;
        emailOrPasswordIncorrect: string;
        followingFieldsInvalid_one: string;
        followingFieldsInvalid_other: string;
        incorrectCollection: string;
        insufficientClipboardPermissions: string;
        invalidClipboardData: string;
        invalidFileType: string;
        invalidFileTypeValue: string;
        invalidRequestArgs: string;
        loadingDocument: string;
        localesNotSaved_one: string;
        localesNotSaved_other: string;
        logoutFailed: string;
        missingEmail: string;
        missingIDOfDocument: string;
        missingIDOfVersion: string;
        missingRequiredData: string;
        noFilesUploaded: string;
        noMatchedField: string;
        notAllowedToAccessPage: string;
        notAllowedToPerformAction: string;
        notFound: string;
        noUser: string;
        previewing: string;
        problemUploadingFile: string;
        restoringTitle: string;
        tokenInvalidOrExpired: string;
        tokenNotProvided: string;
        unableToCopy: string;
        unableToDeleteCount: string;
        unableToReindexCollection: string;
        unableToUpdateCount: string;
        unauthorized: string;
        unauthorizedAdmin: string;
        unknown: string;
        unPublishingDocument: string;
        unspecific: string;
        unverifiedEmail: string;
        userEmailAlreadyRegistered: string;
        userLocked: string;
        usernameAlreadyRegistered: string;
        usernameOrPasswordIncorrect: string;
        valueMustBeUnique: string;
        verificationTokenInvalid: string;
    };
    fields: {
        addLabel: string;
        addLink: string;
        addNew: string;
        addNewLabel: string;
        addRelationship: string;
        addUpload: string;
        block: string;
        blocks: string;
        blockType: string;
        chooseBetweenCustomTextOrDocument: string;
        chooseDocumentToLink: string;
        chooseFromExisting: string;
        chooseLabel: string;
        collapseAll: string;
        customURL: string;
        editLabelData: string;
        editLink: string;
        editRelationship: string;
        enterURL: string;
        internalLink: string;
        itemsAndMore: string;
        labelRelationship: string;
        latitude: string;
        linkedTo: string;
        linkType: string;
        longitude: string;
        newLabel: string;
        openInNewTab: string;
        passwordsDoNotMatch: string;
        relatedDocument: string;
        relationTo: string;
        removeRelationship: string;
        removeUpload: string;
        saveChanges: string;
        searchForBlock: string;
        selectExistingLabel: string;
        selectFieldsToEdit: string;
        showAll: string;
        swapRelationship: string;
        swapUpload: string;
        textToDisplay: string;
        toggleBlock: string;
        uploadNewLabel: string;
    };
    folder: {
        browseByFolder: string;
        byFolder: string;
        deleteFolder: string;
        folderName: string;
        folders: string;
        folderTypeDescription: string;
        itemHasBeenMoved: string;
        itemHasBeenMovedToRoot: string;
        itemsMovedToFolder: string;
        itemsMovedToRoot: string;
        moveFolder: string;
        moveItemsToFolderConfirmation: string;
        moveItemsToRootConfirmation: string;
        moveItemToFolderConfirmation: string;
        moveItemToRootConfirmation: string;
        movingFromFolder: string;
        newFolder: string;
        noFolder: string;
        renameFolder: string;
        searchByNameInFolder: string;
        selectFolderForItem: string;
    };
    general: {
        name: string;
        aboutToDelete: string;
        aboutToDeleteCount_many: string;
        aboutToDeleteCount_one: string;
        aboutToDeleteCount_other: string;
        aboutToPermanentlyDelete: string;
        aboutToPermanentlyDeleteTrash: string;
        aboutToRestore: string;
        aboutToRestoreAsDraft: string;
        aboutToRestoreAsDraftCount: string;
        aboutToRestoreCount: string;
        aboutToTrash: string;
        aboutToTrashCount: string;
        addBelow: string;
        addFilter: string;
        adminTheme: string;
        all: string;
        allCollections: string;
        allLocales: string;
        and: string;
        anotherUser: string;
        anotherUserTakenOver: string;
        applyChanges: string;
        ascending: string;
        automatic: string;
        backToDashboard: string;
        cancel: string;
        changesNotSaved: string;
        clear: string;
        clearAll: string;
        close: string;
        collapse: string;
        collections: string;
        columns: string;
        columnToSort: string;
        confirm: string;
        confirmCopy: string;
        confirmDeletion: string;
        confirmDuplication: string;
        confirmMove: string;
        confirmReindex: string;
        confirmReindexAll: string;
        confirmReindexDescription: string;
        confirmReindexDescriptionAll: string;
        confirmRestoration: string;
        copied: string;
        copy: string;
        copyField: string;
        copying: string;
        copyRow: string;
        copyWarning: string;
        create: string;
        created: string;
        createdAt: string;
        createNew: string;
        createNewLabel: string;
        creating: string;
        creatingNewLabel: string;
        currentlyEditing: string;
        custom: string;
        dark: string;
        dashboard: string;
        delete: string;
        deleted: string;
        deletedAt: string;
        deletedCountSuccessfully: string;
        deletedSuccessfully: string;
        deletePermanently: string;
        deleting: string;
        depth: string;
        descending: string;
        deselectAllRows: string;
        document: string;
        documentIsTrashed: string;
        documentLocked: string;
        documents: string;
        duplicate: string;
        duplicateWithoutSaving: string;
        edit: string;
        editAll: string;
        editedSince: string;
        editing: string;
        editingLabel_many: string;
        editingLabel_one: string;
        editingLabel_other: string;
        editingTakenOver: string;
        editLabel: string;
        email: string;
        emailAddress: string;
        emptyTrash: string;
        emptyTrashLabel: string;
        enterAValue: string;
        error: string;
        errors: string;
        exitLivePreview: string;
        export: string;
        fallbackToDefaultLocale: string;
        false: string;
        filter: string;
        filters: string;
        filterWhere: string;
        globals: string;
        goBack: string;
        groupByLabel: string;
        import: string;
        isEditing: string;
        item: string;
        items: string;
        language: string;
        lastModified: string;
        leaveAnyway: string;
        leaveWithoutSaving: string;
        light: string;
        livePreview: string;
        loading: string;
        locale: string;
        locales: string;
        menu: string;
        moreOptions: string;
        move: string;
        moveConfirm: string;
        moveCount: string;
        moveDown: string;
        moveUp: string;
        moving: string;
        movingCount: string;
        newPassword: string;
        next: string;
        no: string;
        noDateSelected: string;
        noFiltersSet: string;
        noLabel: string;
        none: string;
        noOptions: string;
        noResults: string;
        notFound: string;
        nothingFound: string;
        noTrashResults: string;
        noUpcomingEventsScheduled: string;
        noValue: string;
        of: string;
        only: string;
        open: string;
        or: string;
        order: string;
        overwriteExistingData: string;
        pageNotFound: string;
        password: string;
        pasteField: string;
        pasteRow: string;
        payloadSettings: string;
        permanentlyDelete: string;
        permanentlyDeletedCountSuccessfully: string;
        perPage: string;
        previous: string;
        reindex: string;
        reindexingAll: string;
        remove: string;
        rename: string;
        reset: string;
        resetPreferences: string;
        resetPreferencesDescription: string;
        resettingPreferences: string;
        restore: string;
        restoreAsPublished: string;
        restoredCountSuccessfully: string;
        restoring: string;
        row: string;
        rows: string;
        save: string;
        saving: string;
        schedulePublishFor: string;
        searchBy: string;
        select: string;
        selectAll: string;
        selectAllRows: string;
        selectedCount: string;
        selectLabel: string;
        selectValue: string;
        showAllLabel: string;
        sorryNotFound: string;
        sort: string;
        sortByLabelDirection: string;
        stayOnThisPage: string;
        submissionSuccessful: string;
        submit: string;
        submitting: string;
        success: string;
        successfullyCreated: string;
        successfullyDuplicated: string;
        successfullyReindexed: string;
        takeOver: string;
        thisLanguage: string;
        time: string;
        timezone: string;
        titleDeleted: string;
        titleRestored: string;
        titleTrashed: string;
        trash: string;
        trashedCountSuccessfully: string;
        true: string;
        unauthorized: string;
        unsavedChanges: string;
        unsavedChangesDuplicate: string;
        untitled: string;
        upcomingEvents: string;
        updatedAt: string;
        updatedCountSuccessfully: string;
        updatedLabelSuccessfully: string;
        updatedSuccessfully: string;
        updateForEveryone: string;
        updating: string;
        uploading: string;
        uploadingBulk: string;
        user: string;
        username: string;
        users: string;
        value: string;
        viewing: string;
        viewReadOnly: string;
        welcome: string;
        yes: string;
    };
    localization: {
        cannotCopySameLocale: string;
        copyFrom: string;
        copyFromTo: string;
        copyTo: string;
        copyToLocale: string;
        localeToPublish: string;
        selectLocaleToCopy: string;
    };
    operators: {
        contains: string;
        equals: string;
        exists: string;
        intersects: string;
        isGreaterThan: string;
        isGreaterThanOrEqualTo: string;
        isIn: string;
        isLessThan: string;
        isLessThanOrEqualTo: string;
        isLike: string;
        isNotEqualTo: string;
        isNotIn: string;
        isNotLike: string;
        near: string;
        within: string;
    };
    upload: {
        addFile: string;
        addFiles: string;
        bulkUpload: string;
        crop: string;
        cropToolDescription: string;
        download: string;
        dragAndDrop: string;
        dragAndDropHere: string;
        editImage: string;
        fileName: string;
        fileSize: string;
        filesToUpload: string;
        fileToUpload: string;
        focalPoint: string;
        focalPointDescription: string;
        height: string;
        lessInfo: string;
        moreInfo: string;
        noFile: string;
        pasteURL: string;
        previewSizes: string;
        selectCollectionToBrowse: string;
        selectFile: string;
        setCropArea: string;
        setFocalPoint: string;
        sizes: string;
        sizesFor: string;
        width: string;
    };
    validation: {
        emailAddress: string;
        enterNumber: string;
        fieldHasNo: string;
        greaterThanMax: string;
        invalidInput: string;
        invalidSelection: string;
        invalidSelections: string;
        lessThanMin: string;
        limitReached: string;
        longerThanMin: string;
        notValidDate: string;
        required: string;
        requiresAtLeast: string;
        requiresNoMoreThan: string;
        requiresTwoNumbers: string;
        shorterThanMax: string;
        timezoneRequired: string;
        trueOrFalse: string;
        username: string;
        validUploadID: string;
    };
    version: {
        type: string;
        aboutToPublishSelection: string;
        aboutToRestore: string;
        aboutToRestoreGlobal: string;
        aboutToRevertToPublished: string;
        aboutToUnpublish: string;
        aboutToUnpublishSelection: string;
        autosave: string;
        autosavedSuccessfully: string;
        autosavedVersion: string;
        changed: string;
        changedFieldsCount_one: string;
        changedFieldsCount_other: string;
        compareVersion: string;
        compareVersions: string;
        comparingAgainst: string;
        confirmPublish: string;
        confirmRevertToSaved: string;
        confirmUnpublish: string;
        confirmVersionRestoration: string;
        currentDocumentStatus: string;
        currentDraft: string;
        currentlyPublished: string;
        currentlyViewing: string;
        currentPublishedVersion: string;
        draft: string;
        draftSavedSuccessfully: string;
        lastSavedAgo: string;
        modifiedOnly: string;
        moreVersions: string;
        noFurtherVersionsFound: string;
        noRowsFound: string;
        noRowsSelected: string;
        preview: string;
        previouslyDraft: string;
        previouslyPublished: string;
        previousVersion: string;
        problemRestoringVersion: string;
        publish: string;
        publishAllLocales: string;
        publishChanges: string;
        published: string;
        publishIn: string;
        publishing: string;
        restoreAsDraft: string;
        restoredSuccessfully: string;
        restoreThisVersion: string;
        restoring: string;
        reverting: string;
        revertToPublished: string;
        saveDraft: string;
        scheduledSuccessfully: string;
        schedulePublish: string;
        selectLocales: string;
        selectVersionToCompare: string;
        showingVersionsFor: string;
        showLocales: string;
        specificVersion: string;
        status: string;
        unpublish: string;
        unpublishing: string;
        version: string;
        versionAgo: string;
        versionCount_many: string;
        versionCount_none: string;
        versionCount_one: string;
        versionCount_other: string;
        versionCreatedOn: string;
        versionID: string;
        versions: string;
        viewingVersion: string;
        viewingVersionGlobal: string;
        viewingVersions: string;
        viewingVersionsGlobal: string;
    };
} | Record<string, unknown>;
//# sourceMappingURL=getTranslationsByContext.d.ts.map