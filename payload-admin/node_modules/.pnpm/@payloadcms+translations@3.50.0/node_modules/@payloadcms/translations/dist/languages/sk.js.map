{"version": 3, "sources": ["../../src/languages/sk.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const skTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Účet',\n    accountOfCurrentUser: 'Aktuálny používateľský účet',\n    accountVerified: 'Účet úspešne overený.',\n    alreadyActivated: 'Už aktivované',\n    alreadyLoggedIn: '<PERSON><PERSON> prihlásený',\n    apiKey: 'API kľúč',\n    authenticated: 'Overený',\n    backToLogin: 'Späť na prihlásenie',\n    beginCreateFirstUser: 'Začnite vytvorením prvého používateľa.',\n    changePassword: 'Z<PERSON>i<PERSON> heslo',\n    checkYourEmailForPasswordReset:\n      'Ak je e-mailová adresa spojená s účtom, čoskoro dostanete inštrukcie na obnovenie hesla. Skontrolujte prosím svoju spamovú alebo junk mail zložku, ak e-mail nevidíte vo svojej doručenej pošte.',\n    confirmGeneration: 'Potvrdiť generovanie',\n    confirmPassword: 'Potv<PERSON><PERSON> heslo',\n    createFirstUser: 'Vytvorenie prvého používateľa',\n    emailNotValid: 'Zadaný e-mail nie je platný',\n    emailOrUsername: 'E-mail alebo Užívateľské meno',\n    emailSent: 'E-mail bol odoslaný',\n    emailVerified: 'Email úspešne overený.',\n    enableAPIKey: 'Povolenie API kľúča',\n    failedToUnlock: 'Nepodarilo sa odomknúť',\n    forceUnlock: 'Vynútené odomknutie',\n    forgotPassword: 'Zabudli ste heslo?',\n    forgotPasswordEmailInstructions:\n      'Zadajte svoj e-mail nižšie. Dostanete e-mail s pokynmi na obnovenie hesla.',\n    forgotPasswordQuestion: 'Zabudli ste heslo?',\n    forgotPasswordUsernameInstructions:\n      'Prosím, zadajte nižšie svoje používateľské meno. Inštrukcie na obnovenie vášho hesla budú odoslané na e-mailovú adresu spojenú s vaším používateľským menom.',\n    generate: 'Generovať',\n    generateNewAPIKey: 'Vygenerovať nový API kľúč',\n    generatingNewAPIKeyWillInvalidate:\n      'Vytvorenie nového API kľúča <1>zneplatní</1> predchádzajúci kľúč. Ste si istí, že chcete pokračovať?',\n    lockUntil: 'Uzamknúť do',\n    logBackIn: 'Znovu sa prihlásiť',\n    loggedIn: 'Ak sa chcete prihlásiť pomocou iného používateľa, najprv sa <0>odhláste</0>.',\n    loggedInChangePassword:\n      'Ak chcete zmeniť heslo, prejdite na svoj <0>oúčet</0> a upravte heslo tam.',\n    loggedOutInactivity: 'Boli ste odhlásení z dôvodu nečinnosti.',\n    loggedOutSuccessfully: 'Boli ste úspešne odhlásení.',\n    loggingOut: 'Odhlásenie...',\n    login: 'Prihlásiť sa',\n    loginAttempts: 'Pokusy o prihlásenie',\n    loginUser: 'Prihlásenie používateľa',\n    loginWithAnotherUser:\n      'Ak sa chcete prihlásiť pomocou iného používateľa, najprv sa <0>odhláste</0>.',\n    logOut: 'Odhlásiť sa',\n    logout: 'Odhlásiť sa',\n    logoutSuccessful: 'Odhlásenie bolo úspešné.',\n    logoutUser: 'Odhlásiť používateľa',\n    newAccountCreated:\n      'Pre prístup k <a href=\"{{serverURL}}\">{{serverURL}}</a> bol pre vás vytvorený nový účet. Kliknite na nasledujúci odkaz alebo skopírujte URL do svojho prehliadača na overenie vášho emailu: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Po overení vášho emailu sa budete môcť úspešne prihlásiť.',\n    newAPIKeyGenerated: 'Bol vygenerovaný nový API kľúč.',\n    newPassword: 'Nové heslo',\n    passed: 'Overenie prešlo',\n    passwordResetSuccessfully: 'Úspešne zmenené heslo.',\n    resetPassword: 'Obnoviť heslo',\n    resetPasswordExpiration: 'Vypršanie platnosti obnovenia hesla',\n    resetPasswordToken: 'Token na resetovanie hesla',\n    resetYourPassword: 'Obnovte svoje heslo',\n    stayLoggedIn: 'Zostaňte prihlásení',\n    successfullyRegisteredFirstUser: 'Úspešne zaregistrovaný prvý používateľ.',\n    successfullyUnlocked: 'Úspešne odomknuté',\n    tokenRefreshSuccessful: 'Obnovenie tokenu bolo úspešné.',\n    unableToVerify: 'Nemožno overiť',\n    username: 'Používateľské meno',\n    usernameNotValid: 'Zadané užívateľské meno nie je platné.',\n    verified: 'Overené',\n    verifiedSuccessfully: 'Úspešne overené',\n    verify: 'Overiť',\n    verifyUser: 'Overiť používateľa',\n    verifyYourEmail: 'Overiť e-mail',\n    youAreInactive:\n      'Už nejaký čas ste neaktívny a čoskoro budete z bezpečnostných dôvodov automaticky odhlásený. Chcete zostať prihlásený?',\n    youAreReceivingResetPassword:\n      'Tento email dostanete, pretože ste (alebo niekto iný) požiadali o resetovanie hesla pre váš účet.',\n    youDidNotRequestPassword:\n      'Ak ste o to nepožiadali, ignorujte prosím tento e-mail a vaše heslo zostane nezmenené.',\n  },\n  error: {\n    accountAlreadyActivated: 'Tento účet už bol aktivovaný.',\n    autosaving: 'Pri automatickom ukladaní tohto dokumentu došlo k chybe.',\n    correctInvalidFields: 'Opravte neplatné polia.',\n    deletingFile: 'Pri mazaní súboru došlo k chybe.',\n    deletingTitle:\n      'Pri mazaní {{title}} došlo k chybe. Skontrolujte svoje pripojenie a skúste to znova.',\n    documentNotFound:\n      'Dokument s ID {{id}} sa nepodarilo nájsť. Možno bol vymazaný, nikdy neexistoval, alebo k nemu nemáte prístup.',\n    emailOrPasswordIncorrect: 'Zadaný email alebo heslo nie je správne.',\n    followingFieldsInvalid_one: 'Nasledujúce pole je neplatné:',\n    followingFieldsInvalid_other: 'Nasledujúce polia sú neplatné:',\n    incorrectCollection: 'Nesprávna kolekcia',\n    insufficientClipboardPermissions:\n      'Prístup do schránky bol zamietnutý. Skontrolujte svoje oprávnenia pre schránku.',\n    invalidClipboardData: 'Neplatné dáta v schránke.',\n    invalidFileType: 'Neplatný typ súboru',\n    invalidFileTypeValue: 'Neplatný typ súboru: {{value}}',\n    invalidRequestArgs: 'Neplatné argumenty odoslané v požiadavke: {{args}}',\n    loadingDocument: 'Pri načítaní dokumentu s ID {{id}} došlo k chybe.',\n    localesNotSaved_one: 'Nasledujúci jazyk sa nepodarilo uložiť:',\n    localesNotSaved_other: 'Nasledujúce jazyky sa nepodarilo uložiť:',\n    logoutFailed: 'Odhlásenie zlyhalo.',\n    missingEmail: 'Chýba e-mail.',\n    missingIDOfDocument: 'Chýba ID dokumentu na aktualizáciu.',\n    missingIDOfVersion: 'Chýba ID verzie.',\n    missingRequiredData: 'Chýbajú požadované údaje.',\n    noFilesUploaded: 'Nenahrali sa žiadne súbory.',\n    noMatchedField: 'Pre \"{{label}}\" nebolo nájdené žiadne zodpovedajúce pole',\n    notAllowedToAccessPage: 'Nemáte povolenie pristupovať k tejto stránke.',\n    notAllowedToPerformAction: 'Nemáte povolenie vykonávať túto akciu.',\n    notFound: 'Požadovaný zdroj nebol nájdený.',\n    noUser: 'Žiadny používateľ',\n    previewing: 'Pri náhľade tohto dokumentu došlo k chybe.',\n    problemUploadingFile: 'Pri nahrávaní súboru došlo k chybe.',\n    restoringTitle:\n      'Pri obnovovaní {{title}} sa vyskytla chyba. Skontrolujte prosím svoje pripojenie a skúste to znova.',\n    tokenInvalidOrExpired: 'Token je neplatný alebo vypršal.',\n    tokenNotProvided: 'Token nie je poskytnutý.',\n    unableToCopy: 'Kopírovanie nie je možné.',\n    unableToDeleteCount: 'Nie je možné zmazať {{count}} z {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Chyba pri reindexácii kolekcie {{collection}}. Operácia bola prerušená.',\n    unableToUpdateCount: 'Nie je možné aktualizovať {{count}} z {{total}} {{label}}.',\n    unauthorized: 'Neautorizováno, pro zadání tohoto požadavku musíte být přihlášeni.',\n    unauthorizedAdmin:\n      'Neoprávnený prístup, tento používateľ nemá prístup k administrátorskému panelu.',\n    unknown: 'Došlo k neznámej chybe.',\n    unPublishingDocument: 'Pri zrušení publikovania tohto dokumentu došlo k chybe.',\n    unspecific: 'Došlo k chybe.',\n    unverifiedEmail: 'Pred prihlásením si overte svoj e-mail.',\n    userEmailAlreadyRegistered: 'Používateľ s daným e-mailom je už zaregistrovaný.',\n    userLocked:\n      'Tento používateľ je uzamknutý kvôli príliš mnohým neúspešným pokusom o prihlásenie.',\n    usernameAlreadyRegistered: 'Používateľ s daným používateľským menom je už zaregistrovaný.',\n    usernameOrPasswordIncorrect: 'Zadané meno alebo heslo je nesprávne.',\n    valueMustBeUnique: 'Hodnota musí byť jedinečná',\n    verificationTokenInvalid: 'Overovací token je neplatný.',\n  },\n  fields: {\n    addLabel: 'Pridať {{label}}',\n    addLink: 'Pridať odkaz',\n    addNew: 'Pridať nový',\n    addNewLabel: 'Pridať nový {{label}}',\n    addRelationship: 'Pridať vzťah',\n    addUpload: 'Pridať nahrávanie',\n    block: 'blok',\n    blocks: 'bloky',\n    blockType: 'Typ bloku',\n    chooseBetweenCustomTextOrDocument:\n      'Vyberte medzi vložením vlastného textového URL alebo odkazovaním na iný dokument.',\n    chooseDocumentToLink: 'Vyberte dokument, na ktorý sa chcete odkázať',\n    chooseFromExisting: 'Vybrať z existujúcich',\n    chooseLabel: 'Vybrať {{label}}',\n    collapseAll: 'Zbaliť všetko',\n    customURL: 'Vlastné URL',\n    editLabelData: 'Upraviť dáta {{label}}',\n    editLink: 'Upraviť odkaz',\n    editRelationship: 'Upraviť vzťah',\n    enterURL: 'Zadajte URL',\n    internalLink: 'Interný odkaz',\n    itemsAndMore: '{{items}} a {{count}} ďalších',\n    labelRelationship: 'Vzťah {{label}}',\n    latitude: 'Zemepisná šírka',\n    linkedTo: 'Odkaz na <0>{{label}}</0>',\n    linkType: 'Typ odkazu',\n    longitude: 'Zemepisná dĺžka',\n    newLabel: 'Nový {{label}}',\n    openInNewTab: 'Otvoriť v novej záložke',\n    passwordsDoNotMatch: 'Heslá sa nezhodujú.',\n    relatedDocument: 'Súvisiaci dokument',\n    relationTo: 'Vzťah k',\n    removeRelationship: 'Odstrániť vzťah',\n    removeUpload: 'Odstrániť nahranie',\n    saveChanges: 'Uložiť zmeny',\n    searchForBlock: 'Hľadať blok',\n    selectExistingLabel: 'Vybrať existujúci {{label}}',\n    selectFieldsToEdit: 'Vyberte polia, ktoré chcete upraviť',\n    showAll: 'Zobraziť všetko',\n    swapRelationship: 'Zameniť vzťah',\n    swapUpload: 'Vymeniť nahranie',\n    textToDisplay: 'Text na zobrazenie',\n    toggleBlock: 'Prepnúť blok',\n    uploadNewLabel: 'Nahrať nový {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Prehliadať podľa priečinka',\n    byFolder: 'Podľa priečinka',\n    deleteFolder: 'Odstrániť priečinok',\n    folderName: 'Názov priečinka',\n    folders: 'Priečinky',\n    folderTypeDescription:\n      'Vyberte, ktorý typ dokumentov z kolekcie by mal byť povolený v tejto zložke.',\n    itemHasBeenMoved: '{{title}} bol presunutý do {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} bol presunutý do koreňového priečinka',\n    itemsMovedToFolder: '{{title}} presunuté do {{folderName}}',\n    itemsMovedToRoot: '{{title}} bol presunutý do koreňového priečinka',\n    moveFolder: 'Presunúť priečinok',\n    moveItemsToFolderConfirmation:\n      'Chystáte sa presunúť <1>{{count}} {{label}}</1> do <2>{{toFolder}}</2>. Ste si istý?',\n    moveItemsToRootConfirmation:\n      'Chystáte sa presunúť <1>{{count}} {{label}}</1> do koreňového priečinka. Ste si istý?',\n    moveItemToFolderConfirmation:\n      'Chystáte sa presunúť <1>{{title}}</1> do <2>{{toFolder}}</2>. Ste si istý?',\n    moveItemToRootConfirmation:\n      'Chystáte sa presunúť <1>{{title}}</1> do koreňového priečinka. Ste si istý?',\n    movingFromFolder: 'Presun {{title}} z {{fromFolder}}',\n    newFolder: 'Nový priečinok',\n    noFolder: 'Žiadna zložka',\n    renameFolder: 'Premenovať priečinok',\n    searchByNameInFolder: 'Hľadaj podľa mena v {{folderName}}',\n    selectFolderForItem: 'Vyberte priečinok pre {{title}}',\n  },\n  general: {\n    name: 'Meno',\n    aboutToDelete: 'Chystáte sa odstrániť {{label}} <1>{{title}}</1>. Ste si istí?',\n    aboutToDeleteCount_many: 'Chystáte sa zmazať {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Chystáte sa zmazať {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Chystáte sa zmazať {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Chystáte sa natrvalo vymazať {{label}} <1>{{title}}</1>. Ste si istý?',\n    aboutToPermanentlyDeleteTrash:\n      'Chystáte sa natrvalo odstrániť <0>{{count}}</0> <1>{{label}}</1> z koša. Ste si istý?',\n    aboutToRestore: 'Chystáte sa obnoviť {{label}} <1>{{title}}</1>. Ste si istý?',\n    aboutToRestoreAsDraft:\n      'Chystáte sa obnoviť {{label}} <1>{{title}}</1> ako koncept. Ste si istý?',\n    aboutToRestoreAsDraftCount: 'Chystáte sa obnoviť {{count}} {{label}} ako koncept',\n    aboutToRestoreCount: 'Chystáte sa obnoviť {{count}} {{label}}',\n    aboutToTrash: 'Chystáte sa presunúť {{label}} <1>{{title}}</1> do koša. Ste si istý?',\n    aboutToTrashCount: 'Chystáte sa presunúť {{count}} {{label}} do koša',\n    addBelow: 'Pridať pod',\n    addFilter: 'Pridať filter',\n    adminTheme: 'Motív administračného rozhrania',\n    all: 'Všetko',\n    allCollections: 'Všetky Kolekcie',\n    allLocales: 'Všetky lokality',\n    and: 'a',\n    anotherUser: 'Iný používateľ',\n    anotherUserTakenOver: 'Iný používateľ prevzal úpravy tohto dokumentu.',\n    applyChanges: 'Použiť zmeny',\n    ascending: 'Vzostupne',\n    automatic: 'Automatický',\n    backToDashboard: 'Späť na nástenku',\n    cancel: 'Zrušiť',\n    changesNotSaved: 'Vaše zmeny neboli uložené. Ak teraz odídete, stratíte svoje zmeny.',\n    clear: 'Jasný',\n    clearAll: 'Vymazať všetko',\n    close: 'Zavrieť',\n    collapse: 'Zbaliť',\n    collections: 'Kolekcia',\n    columns: 'Stĺpce',\n    columnToSort: 'Stĺpec na zoradenie',\n    confirm: 'Potvrdiť',\n    confirmCopy: 'Potvrdiť kópiu',\n    confirmDeletion: 'Potvrdiť odstránenie',\n    confirmDuplication: 'Potvrdiť duplikáciu',\n    confirmMove: 'Potvrdiť presun',\n    confirmReindex: 'Znova zaindexovať všetky {{collections}}?',\n    confirmReindexAll: 'Znova zaindexovať všetky kolekcie?',\n    confirmReindexDescription:\n      'Týmto sa odstránia existujúce indexy a znova sa zaindexujú dokumenty v kolekciách {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Týmto sa odstránia existujúce indexy a znova sa zaindexujú dokumenty vo všetkých kolekciách.',\n    confirmRestoration: 'Potvrďte obnovenie',\n    copied: 'Skopírované',\n    copy: 'Kopírovať',\n    copyField: 'Kopírovať pole',\n    copying: 'Kopírovanie',\n    copyRow: 'Kopírovať riadok',\n    copyWarning: 'Chystáte sa prepísať {{to}} na {{from}} pre {{label}} {{title}}. Ste si istý?',\n    create: 'Vytvoriť',\n    created: 'Vytvořeno',\n    createdAt: 'Vytvorené v',\n    createNew: 'Vytvoriť nové',\n    createNewLabel: 'Vytvoriť nový {{label}}',\n    creating: 'Vytváranie',\n    creatingNewLabel: 'Vytváranie nového {{label}}',\n    currentlyEditing:\n      'práve upravuje tento dokument. Ak prevezmete kontrolu, budú zablokovaní z pokračovania v úpravách a môžu tiež stratiť neuložené zmeny.',\n    custom: 'Vlastný',\n    dark: 'Tmavý',\n    dashboard: 'Nástenka',\n    delete: 'Odstrániť',\n    deleted: 'Vymazané',\n    deletedAt: 'Vymazané dňa',\n    deletedCountSuccessfully: 'Úspešne zmazané {{count}} {{label}}.',\n    deletedSuccessfully: 'Úspešne odstránené.',\n    deletePermanently: 'Preskočiť kôš a odstrániť natrvalo',\n    deleting: 'Odstraňovanie...',\n    depth: 'Hĺbka',\n    descending: 'Zostupne',\n    deselectAllRows: 'Zrušiť výber všetkých riadkov',\n    document: 'Dokument',\n    documentIsTrashed: 'Táto {{label}} je v koši a je iba na čítanie.',\n    documentLocked: 'Dokument je zamknutý',\n    documents: 'Dokumenty',\n    duplicate: 'Duplikovať',\n    duplicateWithoutSaving: 'Duplikovať bez uloženia zmien',\n    edit: 'Upraviť',\n    editAll: 'Upraviť všetko',\n    editedSince: 'Upravené od',\n    editing: 'Úpravy',\n    editingLabel_many: 'Úprava {{count}} {{label}}',\n    editingLabel_one: 'Úprava {{count}} {{label}}',\n    editingLabel_other: 'Úprava {{count}} {{label}}',\n    editingTakenOver: 'Úpravy prevzaté',\n    editLabel: 'Upraviť {{label}}',\n    email: 'E-mail',\n    emailAddress: 'E-mailová adresa',\n    emptyTrash: 'Vyprázdniť koš',\n    emptyTrashLabel: 'Vyprázdniť koš {{label}}',\n    enterAValue: 'Zadajte hodnotu',\n    error: 'Chyba',\n    errors: 'Chyby',\n    exitLivePreview: 'Ukončiť živý náhľad',\n    export: 'Export',\n    fallbackToDefaultLocale: 'Zálohovať do predvoleného jazyka',\n    false: 'Nepravdivé',\n    filter: 'Filter',\n    filters: 'Filtry',\n    filterWhere: 'Filtrovat kde je {{label}}',\n    globals: 'Globalné',\n    goBack: 'Vrátiť sa',\n    groupByLabel: 'Zoskupiť podľa {{label}}',\n    import: 'Dovoz',\n    isEditing: 'upravuje',\n    item: 'položka',\n    items: 'položky',\n    language: 'Jazyk',\n    lastModified: 'Naposledy zmenené',\n    leaveAnyway: 'Presto odísť',\n    leaveWithoutSaving: 'Odísť bez uloženia',\n    light: 'Svetlý',\n    livePreview: 'Náhľad',\n    loading: 'Načítavanie',\n    locale: 'Jazyk',\n    locales: 'Jazyky',\n    menu: 'Menu',\n    moreOptions: 'Viac možností',\n    move: 'Presuňte sa',\n    moveConfirm: 'Chystáte sa presunúť {{count}} {{label}} do <1>{{destination}}</1>. Ste si istý?',\n    moveCount: 'Presuňte {{count}} {{label}}',\n    moveDown: 'Presunúť dolu',\n    moveUp: 'Presunúť hore',\n    moving: 'Pohybujúci sa',\n    movingCount: 'Presunutie {{count}} {{label}}',\n    newPassword: 'Nové heslo',\n    next: 'Ďalej',\n    no: 'Nie',\n    noDateSelected: 'Nie je vybraný dátum',\n    noFiltersSet: 'Nie sú nastavené žiadne filtre',\n    noLabel: '<Žiadny {{label}}>',\n    none: 'Žiadny',\n    noOptions: 'Žiadne možnosti',\n    noResults:\n      'Neboli nájdené žiadne {{label}}. Buď neexistujú žiadne {{label}}, alebo žiadne nespĺňajú filtre, ktoré ste zadali vyššie.',\n    notFound: 'Nenájdené',\n    nothingFound: 'Nič nenájdené',\n    noTrashResults: 'Žiadne {{label}} v koši.',\n    noUpcomingEventsScheduled: 'Nie sú naplánované žiadne nadchádzajúce udalosti.',\n    noValue: 'Žiadna hodnota',\n    of: 'z',\n    only: 'Iba',\n    open: 'Otvoriť',\n    or: 'Alebo',\n    order: 'Poradie',\n    overwriteExistingData: 'Prepísať existujúce pole dát',\n    pageNotFound: 'Stránka nenájdená',\n    password: 'Heslo',\n    pasteField: 'Prilepiť pole',\n    pasteRow: 'Prilepiť riadok',\n    payloadSettings: 'Nastavenia dátového záznamu',\n    permanentlyDelete: 'Trvalo odstrániť',\n    permanentlyDeletedCountSuccessfully: 'Úspešne ste natrvalo odstránili {{count}} {{label}}.',\n    perPage: 'Na stránku: {{limit}}',\n    previous: 'Predchádzajúci',\n    reindex: 'Reindexovať',\n    reindexingAll: 'Znova sa indexujú všetky {{collections}}.',\n    remove: 'Odstrániť',\n    rename: 'Premenovať',\n    reset: 'Resetovať',\n    resetPreferences: 'Obnoviť nastavenia',\n    resetPreferencesDescription: 'Týmto sa všetky vaše nastavenia vrátia na predvolené hodnoty.',\n    resettingPreferences: 'Obnovovanie nastavení.',\n    restore: 'Obnoviť',\n    restoreAsPublished: 'Obnoviť ako publikovanú verziu',\n    restoredCountSuccessfully: 'Úspešne obnovené {{count}} {{label}}.',\n    restoring: 'Obnovovanie...',\n    row: 'Riadok',\n    rows: 'Riadky',\n    save: 'Uložiť',\n    saving: 'Ukladanie...',\n    schedulePublishFor: 'Naplánovať publikovanie pre {{title}}',\n    searchBy: 'Vyhľadať podľa {{label}}',\n    select: 'Vyberte',\n    selectAll: 'Vybrať všetko {{count}} {{label}}',\n    selectAllRows: 'Vybrať všetky riadky',\n    selectedCount: 'Vybrané {{count}} {{label}}',\n    selectLabel: 'Vyberte {{label}}',\n    selectValue: 'Vybrať hodnotu',\n    showAllLabel: 'Zobraziť všetky {{label}}',\n    sorryNotFound: 'Je nám ľúto, ale neexistuje nič, čo by zodpovedalo vášmu požiadavku.',\n    sort: 'Zoradiť',\n    sortByLabelDirection: 'Zoradiť podľa {{label}} {{direction}}',\n    stayOnThisPage: 'Zostať na tejto stránke',\n    submissionSuccessful: 'Odoslanie úspešné.',\n    submit: 'Odoslať',\n    submitting: 'Odosielanie...',\n    success: 'Úspech',\n    successfullyCreated: '{{label}} úspešne vytvorené.',\n    successfullyDuplicated: '{{label}} úspešne duplikované.',\n    successfullyReindexed:\n      'Úspešne bolo reindexovaných {{count}} z {{total}} dokumentov z kolekcií {{collections}}.',\n    takeOver: 'Prevziať',\n    thisLanguage: 'Slovenčina',\n    time: 'Čas',\n    timezone: 'Časové pásmo',\n    titleDeleted: '{{label}} \"{{title}}\" úspešne zmazané.',\n    titleRestored: '{{label}} \"{{title}}\" úspešne obnovený.',\n    titleTrashed: '{{label}} \"{{title}}\" presunuté do koša.',\n    trash: 'Koš',\n    trashedCountSuccessfully: '{{count}} {{label}} presunuté do koša.',\n    true: 'Pravda',\n    unauthorized: 'Neoprávnený prístup',\n    unsavedChanges: 'Máte neuložené zmeny. Uložte alebo zahoďte pred pokračovaním.',\n    unsavedChangesDuplicate: 'Máte neuložené zmeny. Chceli by ste pokračovať v duplikovaní?',\n    untitled: 'Bez názvu',\n    upcomingEvents: 'Nadchádzajúce udalosti',\n    updatedAt: 'Aktualizované v',\n    updatedCountSuccessfully: 'Úspešne aktualizované {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Úspešne aktualizované {{label}}.',\n    updatedSuccessfully: 'Úspešne aktualizované.',\n    updateForEveryone: 'Aktualizácia pre všetkých',\n    updating: 'Aktualizácia',\n    uploading: 'Nahrávanie',\n    uploadingBulk: 'Nahrávanie {{current}} z {{total}}',\n    user: 'Používateľ',\n    username: 'Používateľské meno',\n    users: 'Používatelia',\n    value: 'Hodnota',\n    viewing: 'Prezeranie',\n    viewReadOnly: 'Zobraziť iba na čítanie',\n    welcome: 'Vitajte',\n    yes: 'Áno',\n  },\n  localization: {\n    cannotCopySameLocale: 'Nemožno kopírovať do rovnakej lokalizácie.',\n    copyFrom: 'Kopírovať z',\n    copyFromTo: 'Kopírovanie z {{from}} do {{to}}',\n    copyTo: 'Kopírovať do',\n    copyToLocale: 'Kopírovať do lokalizácie',\n    localeToPublish: 'Miesto na publikovanie',\n    selectLocaleToCopy: 'Vyberte miestny systém na kopírovanie',\n  },\n  operators: {\n    contains: 'obsahuje',\n    equals: 'rovná sa',\n    exists: 'existuje',\n    intersects: 'pretína sa',\n    isGreaterThan: 'je väčšie ako',\n    isGreaterThanOrEqualTo: 'je väčšie alebo rovné',\n    isIn: 'je v',\n    isLessThan: 'je menšie ako',\n    isLessThanOrEqualTo: 'je menšie alebo rovné',\n    isLike: 'je ako',\n    isNotEqualTo: 'nie je rovné',\n    isNotIn: 'nie je v',\n    isNotLike: 'nie je ako',\n    near: 'blízko',\n    within: 'vnútri',\n  },\n  upload: {\n    addFile: 'Pridať súbor',\n    addFiles: 'Pridať súbory',\n    bulkUpload: 'Hromadné nahranie',\n    crop: 'Orezať',\n    cropToolDescription:\n      'Potiahnite rohy vybranej oblasti, nakreslite novú oblasť alebo upravte hodnoty nižšie.',\n    download: 'Stiahnuť',\n    dragAndDrop: 'Potiahnite a pusťte súbor',\n    dragAndDropHere: 'alebo sem potiahnite a pusťte súbor',\n    editImage: 'Upraviť obrázok',\n    fileName: 'Názov súboru',\n    fileSize: 'Veľkosť súboru',\n    filesToUpload: 'Súbory na nahranie',\n    fileToUpload: 'Súbor na nahranie',\n    focalPoint: 'Stredobod',\n    focalPointDescription:\n      'Potiahnite bod stredobodu priamo na náhľad alebo upravte hodnoty nižšie.',\n    height: 'Výška',\n    lessInfo: 'Menej informácií',\n    moreInfo: 'Viac informácií',\n    noFile: 'Žiadny súbor',\n    pasteURL: 'Vložiť URL',\n    previewSizes: 'Náhľady veľkostí',\n    selectCollectionToBrowse: 'Vyberte kolekciu na prezeranie',\n    selectFile: 'Vyberte súbor',\n    setCropArea: 'Nastaviť oblasť orezania',\n    setFocalPoint: 'Nastaviť stredobod',\n    sizes: 'Veľkosti',\n    sizesFor: 'Veľkosti pre {{label}}',\n    width: 'Šírka',\n  },\n  validation: {\n    emailAddress: 'Zadajte prosím platnú e-mailovú adresu.',\n    enterNumber: 'Zadajte prosím platné číslo.',\n    fieldHasNo: 'Toto pole nemá {{label}}',\n    greaterThanMax: '{{value}} je vyššie ako maximálne povolené {{label}} {{max}}.',\n    invalidInput: 'Toto pole má neplatný vstup.',\n    invalidSelection: 'Toto pole má neplatný výber.',\n    invalidSelections: 'Toto pole má nasledujúce neplatné výbery:',\n    lessThanMin: '{{value}} je nižšie ako minimálne povolené {{label}} {{min}}.',\n    limitReached: 'Dosiahnutý limit, môžu byť pridané len {{max}} položky.',\n    longerThanMin: 'Táto hodnota musí byť dlhšia ako minimálna dĺžka {{minLength}} znakov.',\n    notValidDate: '\"{{value}}\" nie je platný dátum.',\n    required: 'Toto pole je povinné.',\n    requiresAtLeast: 'Toto pole vyžaduje aspoň {{count}} {{label}}.',\n    requiresNoMoreThan: 'Toto pole vyžaduje nie viac ako {{count}} {{label}}.',\n    requiresTwoNumbers: 'Toto pole vyžaduje dve čísla.',\n    shorterThanMax: 'Táto hodnota musí byť kratšia ako maximálna dĺžka {{maxLength}} znakov.',\n    timezoneRequired: 'Je potrebné uviesť časové pásmo.',\n    trueOrFalse: 'Toto pole môže byť rovné iba true alebo false.',\n    username:\n      'Prosím, zadajte platné používateľské meno. Môže obsahovať písmená, čísla, pomlčky, bodky a podčiarknutia.',\n    validUploadID: 'Toto pole nie je platné ID pre odoslanie.',\n  },\n  version: {\n    type: 'Typ',\n    aboutToPublishSelection: 'Chystáte sa publikovať všetky {{label}} vo výbere. Ste si istý?',\n    aboutToRestore:\n      'Chystáte sa obnoviť tento {{label}} dokument do stavu, v akom bol {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Chystáte sa obnoviť globálne {{label}} do stavu, v akom bol {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Chystáte sa vrátiť zmeny tohto dokumentu do jeho publikovaného stavu. Ste si istý?',\n    aboutToUnpublish: 'Chystáte sa zrušiť publikovanie tohto dokumentu. Ste si istý?',\n    aboutToUnpublishSelection:\n      'Chystáte sa zrušiť publikovanie všetkých {{label}} vo výbere. Ste si istý?',\n    autosave: 'Automatické uloženie',\n    autosavedSuccessfully: 'Úspešne uložené automaticky.',\n    autosavedVersion: 'Verzia automatického uloženia',\n    changed: 'Zmenené',\n    changedFieldsCount_one: '{{count}} zmenené pole',\n    changedFieldsCount_other: '{{count}} zmenených polí',\n    compareVersion: 'Porovnať verziu s:',\n    compareVersions: 'Porovnať verzie',\n    comparingAgainst: 'Porovnávajúc s',\n    confirmPublish: 'Potvrdiť publikovanie',\n    confirmRevertToSaved: 'Potvrdiť vrátenie k uloženému',\n    confirmUnpublish: 'Potvrdiť zrušenie publikovania',\n    confirmVersionRestoration: 'Potvrdiť obnovenie verzie',\n    currentDocumentStatus: 'Súčasný {{docStatus}} dokument',\n    currentDraft: 'Aktuálny koncept',\n    currentlyPublished: 'Aktuálne publikované',\n    currentlyViewing: 'Práve prezeráte',\n    currentPublishedVersion: 'Aktuálne publikovaná verzia',\n    draft: 'Návrh',\n    draftSavedSuccessfully: 'Návrh úspešne uložený.',\n    lastSavedAgo: 'Naposledy uložené pred {{distance}}',\n    modifiedOnly: 'Iba upravené',\n    moreVersions: 'Viac verzií...',\n    noFurtherVersionsFound: 'Nenájdené ďalšie verzie',\n    noRowsFound: 'Nenájdené {{label}}',\n    noRowsSelected: 'Nie je vybraté žiadne {{označenie}}',\n    preview: 'Náhľad',\n    previouslyDraft: 'Predtým Koncept',\n    previouslyPublished: 'Predtým publikované',\n    previousVersion: 'Predchádzajúca verzia',\n    problemRestoringVersion: 'Pri obnovovaní tejto verzie došlo k problému',\n    publish: 'Publikovať',\n    publishAllLocales: 'Publikujte všetky lokality',\n    publishChanges: 'Publikovať zmeny',\n    published: 'Publikované',\n    publishIn: 'Publikujte v {{locale}}',\n    publishing: 'Publikovanie',\n    restoreAsDraft: 'Obnoviť ako koncept',\n    restoredSuccessfully: 'Úspešne obnovené.',\n    restoreThisVersion: 'Obnoviť túto verziu',\n    restoring: 'Obnovovanie...',\n    reverting: 'Vracanie...',\n    revertToPublished: 'Vrátiť sa k publikovanému',\n    saveDraft: 'Uložiť návrh',\n    scheduledSuccessfully: 'Úspešne naplánované.',\n    schedulePublish: 'Naplánovať publikovanie',\n    selectLocales: 'Vybrať lokálne verzie na zobrazenie',\n    selectVersionToCompare: 'Vybrať verziu na porovnanie',\n    showingVersionsFor: 'Zobrazujú sa verzie pre:',\n    showLocales: 'Zobraziť lokálne verzie:',\n    specificVersion: 'Špecifická verzia',\n    status: 'Stav',\n    unpublish: 'Zrušiť publikovanie',\n    unpublishing: 'Zrušujem publikovanie...',\n    version: 'Verzia',\n    versionAgo: 'pred {{distance}}',\n    versionCount_many: '{{count}} verzií nájdených',\n    versionCount_none: 'Žiadne verzie nenájdené',\n    versionCount_one: '{{count}} verzia nájdená',\n    versionCount_other: '{{count}} verzií nájdených',\n    versionCreatedOn: '{{version}} vytvorená:',\n    versionID: 'ID verzie',\n    versions: 'Verzie',\n    viewingVersion: 'Zobrazujem verziu pre {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Zobrazujem verziu pre globálne {{entityLabel}}',\n    viewingVersions: 'Zobrazujem verzie pre {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Zobrazujem verzie pre globálne {{entityLabel}}',\n  },\n}\n\nexport const sk: Language = {\n  dateFNSKey: 'sk',\n  translations: skTranslations,\n}\n"], "names": ["skTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "sk", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBACE;QACFC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YACE;QACFC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}