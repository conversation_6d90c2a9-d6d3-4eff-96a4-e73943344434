{"version": 3, "sources": ["../../src/languages/id.ts"], "sourcesContent": ["import { title } from 'process'\n\nimport type { Language } from '../types.js'\n\nexport const idTranslations = {\n  authentication: {\n    account: 'Akun',\n    accountOfCurrentUser: 'Akun pengguna saat ini',\n    accountVerified: 'Akun berhasil diverifikasi.',\n    alreadyActivated: 'Sudah Diaktifkan',\n    alreadyLoggedIn: 'Sudah masuk',\n    apiKey: 'API Key',\n    authenticated: 'Terautentikasi',\n    backToLogin: 'Ke<PERSON><PERSON> ke halaman masuk',\n    beginCreateFirstUser: 'Untuk memulai, buat pengguna pertama Anda.',\n    changePassword: 'Ubah Kata Sandi',\n    checkYourEmailForPasswordReset:\n      'Jika alamat email dikaitkan dengan sebuah akun, Anda akan segera menerima instruksi untuk mengatur ulang kata sandi Anda. Silakan periksa folder spam atau junk mail Anda jika Anda tidak melihat email di kotak masuk Anda.',\n    confirmGeneration: 'Konfirmasi Pembuatan',\n    confirmPassword: 'Konfirmasi Kata Sandi',\n    createFirstUser: 'Buat pengguna pertama',\n    emailNotValid: 'Email yang diberikan tidak valid',\n    emailOrUsername: 'Email atau Nama Pengguna',\n    emailSent: 'Email Terkirim',\n    emailVerified: 'Email berhasil diverifikasi.',\n    enableAPIKey: 'Aktifkan API Key',\n    failedToUnlock: 'Gagal membuka kunci',\n    forceUnlock: 'Paksa Buka Kunci',\n    forgotPassword: 'Lupa Kata Sandi',\n    forgotPasswordEmailInstructions:\n      'Silakan masukkan email Anda di bawah ini. Anda akan menerima pesan email dengan instruksi tentang cara mengatur ulang kata sandi Anda.',\n    forgotPasswordUsernameInstructions:\n      'Silakan masukkan nama pengguna Anda di bawah ini. Instruksi tentang cara mengatur ulang kata sandi Anda akan dikirim ke alamat email yang terkait dengan nama pengguna Anda.',\n    usernameNotValid: 'Nama pengguna yang diberikan tidak valid',\n\n    forgotPasswordQuestion: 'Lupa kata sandi?',\n    generate: 'Buat',\n    generateNewAPIKey: 'Buat kunci API baru',\n    generatingNewAPIKeyWillInvalidate:\n      'Membuat API Key baru akan <1>membatalkan</1> kunci sebelumnya. Apakah Anda yakin ingin melanjutkan?',\n    lockUntil: 'Kunci Hingga',\n    logBackIn: 'Masuk kembali',\n    loggedIn: 'Untuk masuk dengan pengguna lain, Anda harus <0>keluar</0> terlebih dahulu.',\n    loggedInChangePassword:\n      'Untuk mengubah kata sandi Anda, buka <0>akun</0> Anda dan edit kata sandi Anda di sana.',\n    loggedOutInactivity: 'Anda telah dikeluarkan karena tidak ada aktivitas.',\n    loggedOutSuccessfully: 'Anda telah berhasil keluar.',\n    loggingOut: 'Mengeluarkan...',\n    login: 'Masuk',\n    loginAttempts: 'Upaya Masuk',\n    loginUser: 'Masuk pengguna',\n    loginWithAnotherUser:\n      'Untuk masuk dengan pengguna lain, Anda harus <0>keluar</0> terlebih dahulu.',\n    logOut: 'Keluar',\n    logout: 'Keluar',\n    logoutSuccessful: 'Berhasil keluar.',\n    logoutUser: 'Keluar pengguna',\n    newAccountCreated:\n      'Akun baru telah dibuat untuk Anda agar dapat mengakses <a href=\"{{serverURL}}\">{{serverURL}}</a> Silakan klik tautan berikut atau tempel URL di bawah ini ke browser Anda untuk memverifikasi email Anda: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Setelah memverifikasi email, Anda akan dapat masuk dengan sukses.',\n    newAPIKeyGenerated: 'API Key Baru Telah Dibuat.',\n    newPassword: 'Kata Sandi Baru',\n    passed: 'Autentikasi Lulus',\n    passwordResetSuccessfully: 'Kata sandi berhasil diatur ulang.',\n    resetPassword: 'Atur Ulang Kata Sandi',\n    resetPasswordExpiration: 'Masa Berlaku Token Atur Ulang Kata Sandi',\n    resetPasswordToken: 'Token Atur Ulang Kata Sandi',\n    resetYourPassword: 'Atur Ulang Kata Sandi Anda',\n    stayLoggedIn: 'Tetap masuk',\n    successfullyRegisteredFirstUser: 'Berhasil mendaftarkan pengguna pertama.',\n    successfullyUnlocked: 'Berhasil dibuka kuncinya',\n    tokenRefreshSuccessful: 'Penyegaran token berhasil.',\n    unableToVerify: 'Tidak Dapat Memverifikasi',\n    username: 'Nama Pengguna',\n    verified: 'Terverifikasi',\n    verifiedSuccessfully: 'Berhasil Diverifikasi',\n    verify: 'Verifikasi',\n    verifyUser: 'Verifikasi Pengguna',\n    verifyYourEmail: 'Verifikasi email Anda',\n    youAreInactive:\n      'Anda sudah beberapa saat tidak aktif dan akan segera dikeluarkan secara otomatis demi keamanan Anda. Apakah Anda ingin tetap masuk?',\n    youAreReceivingResetPassword:\n      'Anda menerima ini karena Anda (atau orang lain) telah meminta pengaturan ulang kata sandi untuk akun Anda. Silakan klik tautan berikut, atau tempel ini ke browser Anda untuk menyelesaikan proses:',\n    youDidNotRequestPassword:\n      'Jika Anda tidak meminta ini, harap abaikan email ini dan kata sandi Anda akan tetap tidak berubah.',\n  },\n  error: {\n    accountAlreadyActivated: 'Akun ini sudah diaktifkan.',\n    autosaving: 'Terjadi masalah saat menyimpan otomatis dokumen ini.',\n    correctInvalidFields: 'Harap perbaiki isian yang tidak valid.',\n    deletingFile: 'Terjadi kesalahan saat menghapus file.',\n    deletingTitle:\n      'Terjadi kesalahan saat menghapus {{title}}. Harap periksa koneksi Anda dan coba lagi.',\n    documentNotFound:\n      'Dokumen dengan ID {{id}} tidak dapat ditemukan. Mungkin telah dihapus atau tidak pernah ada, atau Anda mungkin tidak memiliki akses ke sana.',\n    emailOrPasswordIncorrect: 'Email atau kata sandi yang diberikan salah.',\n    followingFieldsInvalid_one: 'Isian berikut tidak valid:',\n    followingFieldsInvalid_other: 'Isian-isian berikut tidak valid:',\n    incorrectCollection: 'Koleksi Salah',\n    insufficientClipboardPermissions:\n      'Akses papan klip ditolak. Silakan periksa izin papan klip Anda.',\n    invalidClipboardData: 'Data papan klip tidak valid.',\n    invalidFileType: 'Jenis file tidak valid',\n    invalidFileTypeValue: 'Jenis file tidak valid: {{value}}',\n    invalidRequestArgs: 'Argumen yang diteruskan dalam permintaan tidak valid: {{args}}',\n    loadingDocument: 'Terjadi masalah saat memuat dokumen dengan ID {{id}}.',\n    localesNotSaved_one: 'Lokal berikut tidak dapat disimpan:',\n    localesNotSaved_other: 'Lokal-lokal berikut tidak dapat disimpan:',\n    logoutFailed: 'Gagal keluar.',\n    missingEmail: 'Email tidak ada.',\n    missingIDOfDocument: 'ID dokumen yang akan diperbarui tidak ada.',\n    missingIDOfVersion: 'ID versi tidak ada.',\n    missingRequiredData: 'Data yang diperlukan tidak ada.',\n    noFilesUploaded: 'Tidak ada file yang diunggah.',\n    noMatchedField: 'Tidak ada isian yang cocok ditemukan untuk \"{{label}}\"',\n    notAllowedToAccessPage: 'Anda tidak diizinkan mengakses halaman ini.',\n    notAllowedToPerformAction: 'Anda tidak diizinkan melakukan tindakan ini.',\n    notFound: 'Data yang diminta tidak ditemukan.',\n    noUser: 'Tidak Ada Pengguna',\n    previewing: 'Terjadi masalah saat mempratinjau dokumen ini.',\n    problemUploadingFile: 'Terjadi masalah saat mengunggah file.',\n    restoringTitle:\n      'Terjadi kesalahan saat memulihkan {{title}}. Harap periksa koneksi Anda dan coba lagi.',\n    tokenInvalidOrExpired: 'Token tidak valid atau telah kedaluwarsa.',\n    tokenNotProvided: 'Token tidak disediakan.',\n    unableToCopy: 'Tidak dapat menyalin.',\n    unableToDeleteCount: 'Tidak dapat menghapus {{count}} dari {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Kesalahan mengindeks ulang koleksi {{collection}}. Operasi dibatalkan.',\n    unableToUpdateCount: 'Tidak dapat memperbarui {{count}} dari {{total}} {{label}}.',\n    unauthorized: 'Tidak sah, Anda harus masuk untuk membuat permintaan ini.',\n    unauthorizedAdmin: 'Tidak sah, pengguna ini tidak memiliki akses ke panel admin.',\n    unknown: 'Terjadi kesalahan yang tidak diketahui.',\n    unPublishingDocument: 'Terjadi masalah saat membatalkan publikasi dokumen ini.',\n    unspecific: 'Terjadi kesalahan.',\n    unverifiedEmail: 'Harap verifikasi email Anda sebelum masuk.',\n    userEmailAlreadyRegistered: 'Pengguna dengan email yang diberikan sudah terdaftar.',\n    userLocked: 'Pengguna ini terkunci karena terlalu banyak upaya masuk yang gagal.',\n    usernameAlreadyRegistered: 'Pengguna dengan nama pengguna yang diberikan sudah terdaftar.',\n    usernameOrPasswordIncorrect: 'Nama pengguna atau kata sandi yang diberikan salah.',\n    valueMustBeUnique: 'Nilai harus unik',\n    verificationTokenInvalid: 'Token verifikasi tidak valid.',\n  },\n  fields: {\n    addLabel: 'Tambah {{label}}',\n    addLink: 'Tambah Tautan',\n    addNew: 'Tambah baru',\n    addNewLabel: 'Tambah {{label}} baru',\n    addRelationship: 'Tambah Hubungan',\n    addUpload: 'Tambah Unggahan',\n    block: 'blok',\n    blocks: 'blok',\n    blockType: 'Tipe Blok',\n    chooseBetweenCustomTextOrDocument:\n      'Pilih antara memasukkan URL teks kustom atau menautkan ke dokumen lain.',\n    chooseDocumentToLink: 'Pilih dokumen untuk ditautkan',\n    chooseFromExisting: 'Pilih dari yang sudah ada',\n    chooseLabel: 'Pilih {{label}}',\n    collapseAll: 'Ciutkan Semua',\n    customURL: 'URL Kustom',\n    editLabelData: 'Edit data {{label}}',\n    editLink: 'Edit Tautan',\n    editRelationship: 'Edit Hubungan',\n    enterURL: 'Masukkan URL',\n    internalLink: 'Tautan Internal',\n    itemsAndMore: '{{items}} dan {{count}} lainnya',\n    labelRelationship: 'Hubungan {{label}}',\n    latitude: 'Lintang',\n    linkedTo: 'Tertaut ke <0>{{label}}</0>',\n    linkType: 'Jenis Tautan',\n    longitude: 'Bujur',\n    newLabel: '{{label}} Baru',\n    openInNewTab: 'Buka di tab baru',\n    passwordsDoNotMatch: 'Kata sandi tidak cocok.',\n    relatedDocument: 'Dokumen Terkait',\n    relationTo: 'Hubungan Ke',\n    removeRelationship: 'Hapus Hubungan',\n    removeUpload: 'Hapus Unggahan',\n    saveChanges: 'Simpan perubahan',\n    searchForBlock: 'Cari blok',\n    selectExistingLabel: 'Pilih {{label}} yang ada',\n    selectFieldsToEdit: 'Pilih isian untuk diedit',\n    showAll: 'Tampilkan Semua',\n    swapRelationship: 'Tukar Hubungan',\n    swapUpload: 'Tukar Unggahan',\n    textToDisplay: 'Teks untuk ditampilkan',\n    toggleBlock: 'Beralih blok',\n    uploadNewLabel: 'Unggah {{label}} baru',\n  },\n  folder: {\n    browseByFolder: 'Jelajahi berdasarkan Folder',\n    byFolder: 'Berdasarkan Folder',\n    deleteFolder: 'Hapus Folder',\n    folderName: 'Nama Folder',\n    folders: 'Folder',\n    folderTypeDescription: 'Pilih jenis dokumen koleksi yang diizinkan di folder ini.',\n    itemHasBeenMoved: '{{title}} telah dipindahkan ke {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} telah dipindahkan ke folder root',\n    itemsMovedToFolder: '{{title}} dipindahkan ke {{folderName}}',\n    itemsMovedToRoot: '{{title}} dipindahkan ke folder root',\n    moveFolder: 'Pindahkan Folder',\n    moveItemsToFolderConfirmation:\n      'Anda akan memindahkan <1>{{count}} {{label}}</1> ke <2>{{toFolder}}</2>. Apakah Anda yakin?',\n    moveItemsToRootConfirmation:\n      'Anda akan memindahkan <1>{{count}} {{label}}</1> ke folder root. Apakah Anda yakin?',\n    moveItemToFolderConfirmation:\n      'Anda akan memindahkan <1>{{title}}</1> ke <2>{{toFolder}}</2>. Apakah Anda yakin?',\n    moveItemToRootConfirmation:\n      'Anda akan memindahkan <1>{{title}}</1> ke folder root. Apakah Anda yakin?',\n    movingFromFolder: 'Memindahkan {{title}} dari {{fromFolder}}',\n    newFolder: 'Folder Baru',\n    noFolder: 'Tidak Ada Folder',\n    renameFolder: 'Ganti Nama Folder',\n    searchByNameInFolder: 'Cari berdasarkan Nama di {{folderName}}',\n    selectFolderForItem: 'Pilih folder untuk {{title}}',\n  },\n  general: {\n    name: 'Nama',\n    aboutToDelete: 'Anda akan menghapus {{label}} <1>{{title}}</1>. Apakah Anda yakin?',\n    aboutToDeleteCount_many: 'Anda akan menghapus {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Anda akan menghapus {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Anda akan menghapus {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Anda akan menghapus secara permanen {{label}} <1>{{title}}</1>. Apakah Anda yakin?',\n    aboutToPermanentlyDeleteTrash:\n      'Anda akan menghapus secara permanen <0>{{count}}</0> <1>{{label}}</1> dari tempat sampah. Apakah Anda yakin?',\n    aboutToRestore: 'Anda akan memulihkan {{label}} <1>{{title}}</1>. Apakah Anda yakin?',\n    aboutToRestoreAsDraft:\n      'Anda akan memulihkan {{label}} <1>{{title}}</1> sebagai draf. Apakah Anda yakin?',\n    aboutToRestoreAsDraftCount: 'Anda akan memulihkan {{count}} {{label}} sebagai draf',\n    aboutToRestoreCount: 'Anda akan memulihkan {{count}} {{label}}',\n    aboutToTrash:\n      'Anda akan memindahkan {{label}} <1>{{title}}</1> ke tempat sampah. Apakah Anda yakin?',\n    aboutToTrashCount: 'Anda akan memindahkan {{count}} {{label}} ke tempat sampah',\n    addBelow: 'Tambah di Bawah',\n    addFilter: 'Tambah Filter',\n    adminTheme: 'Tema Admin',\n    all: 'Semua',\n    allCollections: 'Semua Koleksi',\n    allLocales: 'Semua lokal',\n    and: 'Dan',\n    anotherUser: 'Pengguna lain',\n    anotherUserTakenOver: 'Pengguna lain telah mengambil alih pengeditan dokumen ini.',\n    applyChanges: 'Terapkan Perubahan',\n    ascending: 'Naik',\n    automatic: 'Otomatis',\n    backToDashboard: 'Kembali ke Dasbor',\n    cancel: 'Batal',\n    changesNotSaved:\n      'Perubahan Anda belum disimpan. Jika Anda pergi sekarang, Anda akan kehilangan perubahan Anda.',\n    clear: 'Hapus',\n    clearAll: 'Hapus Semua',\n    close: 'Tutup',\n    collapse: 'Ciutkan',\n    collections: 'Koleksi',\n    columns: 'Kolom',\n    columnToSort: 'Kolom untuk Diurutkan',\n    confirm: 'Konfirmasi',\n    confirmCopy: 'Konfirmasi salin',\n    confirmDeletion: 'Konfirmasi penghapusan',\n    confirmDuplication: 'Konfirmasi duplikasi',\n    confirmMove: 'Konfirmasi pindah',\n    confirmReindex: 'Indeks ulang semua {{collections}}?',\n    confirmReindexAll: 'Indeks ulang semua koleksi?',\n    confirmReindexDescription:\n      'Ini akan menghapus indeks yang ada dan mengindeks ulang dokumen di koleksi {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Ini akan menghapus indeks yang ada dan mengindeks ulang dokumen di semua koleksi.',\n    confirmRestoration: 'Konfirmasi pemulihan',\n    copied: 'Disalin',\n    copy: 'Salin',\n    copyField: 'Salin Isian',\n    copying: 'Menyalin',\n    copyRow: 'Salin Baris',\n    copyWarning:\n      'Anda akan menimpa {{to}} dengan {{from}} untuk {{label}} {{title}}. Apakah Anda yakin?',\n    create: 'Buat',\n    created: 'Dibuat',\n    createdAt: 'Dibuat Pada',\n    createNew: 'Buat Baru',\n    createNewLabel: 'Buat {{label}} baru',\n    creating: 'Membuat',\n    creatingNewLabel: 'Membuat {{label}} baru',\n    currentlyEditing:\n      'sedang mengedit dokumen ini. Jika Anda mengambil alih, mereka akan diblokir untuk melanjutkan pengeditan, dan mungkin juga kehilangan perubahan yang belum disimpan.',\n    custom: 'Kustom',\n    dark: 'Gelap',\n    dashboard: 'Dasbor',\n    delete: 'Hapus',\n    deleted: 'Dihapus',\n    deletedAt: 'Dihapus Pada',\n    deletedCountSuccessfully: 'Berhasil menghapus {{count}} {{label}}.',\n    deletedSuccessfully: 'Berhasil dihapus.',\n    deletePermanently: 'Lewati tempat sampah dan hapus secara permanen',\n    deleting: 'Menghapus...',\n    depth: 'Kedalaman',\n    descending: 'Turun',\n    deselectAllRows: 'Batal pilih semua baris',\n    document: 'Dokumen',\n    documentIsTrashed: '{{label}} ini ada di tempat sampah dan bersifat hanya-baca.',\n    documentLocked: 'Dokumen terkunci',\n    documents: 'Dokumen',\n    duplicate: 'Duplikat',\n    duplicateWithoutSaving: 'Duplikat tanpa menyimpan perubahan',\n    edit: 'Edit',\n    editAll: 'Edit semua',\n    editedSince: 'Diedit sejak',\n    editing: 'Mengedit',\n    editingLabel_many: 'Mengedit {{count}} {{label}}',\n    editingLabel_one: 'Mengedit {{count}} {{label}}',\n    editingLabel_other: 'Mengedit {{count}} {{label}}',\n    editingTakenOver: 'Pengeditan diambil alih',\n    editLabel: 'Edit {{label}}',\n    email: 'Email',\n    emailAddress: 'Alamat Email',\n    emptyTrash: 'Kosongkan tempat sampah',\n    emptyTrashLabel: 'Kosongkan tempat sampah {{label}}',\n    enterAValue: 'Masukkan nilai',\n    error: 'Kesalahan',\n    errors: 'Kesalahan',\n    exitLivePreview: 'Keluar dari Pratinjau Langsung',\n    export: 'Ekspor',\n    fallbackToDefaultLocale: 'Kembali ke lokal default',\n    false: 'Salah',\n    filter: 'Filter',\n    filters: 'Filter',\n    filterWhere: 'Filter {{label}} di mana',\n    globals: 'Global',\n    goBack: 'Kembali',\n    groupByLabel: 'Kelompokkan berdasarkan {{label}}',\n    import: 'Impor',\n    isEditing: 'sedang mengedit',\n    item: 'item',\n    items: 'item',\n    language: 'Bahasa',\n    lastModified: 'Terakhir Diubah',\n    leaveAnyway: 'Tetap pergi',\n    leaveWithoutSaving: 'Pergi tanpa menyimpan',\n    light: 'Terang',\n    livePreview: 'Pratinjau Langsung',\n    loading: 'Memuat',\n    locale: 'Lokal',\n    locales: 'Lokal',\n    menu: 'Menu',\n    moreOptions: 'Opsi lainnya',\n    move: 'Pindah',\n    moveConfirm:\n      'Anda akan memindahkan {{count}} {{label}} ke <1>{{destination}}</1>. Apakah Anda yakin?',\n    moveCount: 'Pindahkan {{count}} {{label}}',\n    moveDown: 'Pindah ke Bawah',\n    moveUp: 'Pindah ke Atas',\n    moving: 'Memindahkan',\n    movingCount: 'Memindahkan {{count}} {{label}}',\n    newPassword: 'Kata Sandi Baru',\n    next: 'Berikutnya',\n    no: 'Tidak',\n    noDateSelected: 'Tidak ada tanggal yang dipilih',\n    noFiltersSet: 'Tidak ada filter yang diatur',\n    noLabel: '<Tidak ada {{label}}>',\n    none: 'Tidak ada',\n    noOptions: 'Tidak ada opsi',\n    noResults:\n      'Tidak ada {{label}} yang ditemukan. Entah belum ada {{label}} atau tidak ada yang cocok dengan filter yang Anda tentukan di atas.',\n    notFound: 'Tidak Ditemukan',\n    nothingFound: 'Tidak ada yang ditemukan',\n    noTrashResults: 'Tidak ada {{label}} di tempat sampah.',\n    noUpcomingEventsScheduled: 'Tidak ada acara mendatang yang dijadwalkan.',\n    noValue: 'Tidak ada nilai',\n    of: 'dari',\n    only: 'Hanya',\n    open: 'Buka',\n    or: 'Atau',\n    order: 'Urutan',\n    overwriteExistingData: 'Timpa data isian yang ada',\n    pageNotFound: 'Halaman tidak ditemukan',\n    password: 'Kata Sandi',\n    pasteField: 'Tempel Isian',\n    pasteRow: 'Tempel Baris',\n    payloadSettings: 'Pengaturan Payload',\n    permanentlyDelete: 'Hapus Secara Permanen',\n    permanentlyDeletedCountSuccessfully: 'Berhasil menghapus secara permanen {{count}} {{label}}.',\n    perPage: 'Per Halaman: {{limit}}',\n    previous: 'Sebelumnya',\n    reindex: 'Indeks Ulang',\n    reindexingAll: 'Mengindeks ulang semua {{collections}}.',\n    remove: 'Hapus',\n    rename: 'Ganti Nama',\n    reset: 'Atur Ulang',\n    resetPreferences: 'Atur Ulang Preferensi',\n    resetPreferencesDescription:\n      'Ini akan mengatur ulang semua preferensi Anda ke pengaturan default.',\n    resettingPreferences: 'Mengatur Ulang Preferensi.',\n    restore: 'Pulihkan',\n    restoreAsPublished: 'Pulihkan sebagai versi yang diterbitkan',\n    restoredCountSuccessfully: 'Berhasil memulihkan {{count}} {{label}}.',\n    restoring: 'Memulihkan...',\n    row: 'Baris',\n    rows: 'Baris',\n    save: 'Simpan',\n    saving: 'Menyimpan...',\n    schedulePublishFor: 'Jadwalkan publikasi untuk {{title}}',\n    searchBy: 'Cari berdasarkan {{label}}',\n    select: 'Pilih',\n    selectAll: 'Pilih semua {{count}} {{label}}',\n    selectAllRows: 'Pilih semua baris',\n    selectedCount: '{{count}} {{label}} dipilih',\n    selectLabel: 'Pilih {{label}}',\n    selectValue: 'Pilih nilai',\n    showAllLabel: 'Tampilkan semua {{label}}',\n    sorryNotFound: 'Maaf—tidak ada yang sesuai dengan permintaan Anda.',\n    sort: 'Urutkan',\n    sortByLabelDirection: 'Urutkan berdasarkan {{label}} {{direction}}',\n    stayOnThisPage: 'Tetap di halaman ini',\n    submissionSuccessful: 'Pengiriman Berhasil.',\n    submit: 'Kirim',\n    submitting: 'Mengirim...',\n    success: 'Sukses',\n    successfullyCreated: '{{label}} berhasil dibuat.',\n    successfullyDuplicated: '{{label}} berhasil diduplikasi.',\n    successfullyReindexed:\n      'Berhasil mengindeks ulang {{count}} dari {{total}} dokumen dari {{collections}}',\n    takeOver: 'Ambil alih',\n    thisLanguage: 'Bahasa Indonesia',\n    time: 'Waktu',\n    timezone: 'Zona Waktu',\n    titleDeleted: '{{label}} \"{{title}}\" berhasil dihapus.',\n    titleRestored: '{{label}} \"{{title}}\" berhasil dipulihkan.',\n    titleTrashed: '{{label}} \"{{title}}\" dipindahkan ke tempat sampah.',\n    trash: 'Tempat Sampah',\n    trashedCountSuccessfully: '{{count}} {{label}} dipindahkan ke tempat sampah.',\n    true: 'Benar',\n    unauthorized: 'Tidak Sah',\n    unsavedChanges:\n      'Anda memiliki perubahan yang belum disimpan. Simpan atau buang sebelum melanjutkan.',\n    unsavedChangesDuplicate:\n      'Anda memiliki perubahan yang belum disimpan. Apakah Anda ingin melanjutkan untuk menduplikasi?',\n    untitled: 'Tanpa Judul',\n    upcomingEvents: 'Acara Mendatang',\n    updatedAt: 'Diperbarui Pada',\n    updatedCountSuccessfully: 'Berhasil memperbarui {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Berhasil memperbarui {{label}}.',\n    updatedSuccessfully: 'Berhasil diperbarui.',\n    updateForEveryone: 'Perbarui untuk semua orang',\n    updating: 'Memperbarui',\n    uploading: 'Mengunggah',\n    uploadingBulk: 'Mengunggah {{current}} dari {{total}}',\n    user: 'Pengguna',\n    username: 'Nama Pengguna',\n    users: 'Pengguna',\n    value: 'Nilai',\n    viewing: 'Melihat',\n    viewReadOnly: 'Lihat hanya-baca',\n    welcome: 'Selamat Datang',\n    yes: 'Ya',\n  },\n  localization: {\n    cannotCopySameLocale: 'Tidak dapat menyalin ke lokal yang sama',\n    copyFrom: 'Salin dari',\n    copyFromTo: 'Menyalin dari {{from}} ke {{to}}',\n    copyTo: 'Salin ke',\n    copyToLocale: 'Salin ke lokal',\n    localeToPublish: 'Lokal untuk dipublikasikan',\n    selectLocaleToCopy: 'Pilih lokal untuk disalin',\n  },\n  operators: {\n    contains: 'mengandung',\n    equals: 'sama dengan',\n    exists: 'ada',\n    intersects: 'bersinggungan',\n    isGreaterThan: 'lebih besar dari',\n    isGreaterThanOrEqualTo: 'lebih besar dari atau sama dengan',\n    isIn: 'berada di dalam',\n    isLessThan: 'lebih kecil dari',\n    isLessThanOrEqualTo: 'lebih kecil dari atau sama dengan',\n    isLike: 'seperti',\n    isNotEqualTo: 'tidak sama dengan',\n    isNotIn: 'tidak berada di dalam',\n    isNotLike: 'tidak seperti',\n    near: 'dekat',\n    within: 'di dalam',\n  },\n  upload: {\n    addFile: 'Tambah file',\n    addFiles: 'Tambah file',\n    bulkUpload: 'Unggah Massal',\n    crop: 'Pangkas',\n    cropToolDescription:\n      'Seret sudut area yang dipilih, gambar area baru atau sesuaikan nilai di bawah ini.',\n    download: 'Unduh',\n    dragAndDrop: 'Seret dan lepas file',\n    dragAndDropHere: 'atau seret dan lepas file di sini',\n    editImage: 'Edit Gambar',\n    fileName: 'Nama File',\n    fileSize: 'Ukuran File',\n    filesToUpload: 'File untuk Diunggah',\n    fileToUpload: 'File untuk Diunggah',\n    focalPoint: 'Titik Fokus',\n    focalPointDescription:\n      'Seret titik fokus langsung pada pratinjau atau sesuaikan nilai di bawah ini.',\n    height: 'Tinggi',\n    lessInfo: 'Info lebih sedikit',\n    moreInfo: 'Info lebih lanjut',\n    noFile: 'Tidak ada file',\n    pasteURL: 'Tempel URL',\n    previewSizes: 'Ukuran Pratinjau',\n    selectCollectionToBrowse: 'Pilih Koleksi untuk Dijelajahi',\n    selectFile: 'Pilih file',\n    setCropArea: 'Atur area pangkas',\n    setFocalPoint: 'Atur titik fokus',\n    sizes: 'Ukuran',\n    sizesFor: 'Ukuran untuk {{label}}',\n    width: 'Lebar',\n  },\n  validation: {\n    emailAddress: 'Harap masukkan alamat email yang valid.',\n    enterNumber: 'Harap masukkan nomor yang valid.',\n    fieldHasNo: 'Isian ini tidak memiliki {{label}}',\n    greaterThanMax: '{{value}} lebih besar dari {{label}} maksimum yang diizinkan yaitu {{max}}.',\n    invalidInput: 'Isian ini memiliki masukan yang tidak valid.',\n    invalidSelection: 'Isian ini memiliki pilihan yang tidak valid.',\n    invalidSelections: 'Isian ini memiliki pilihan tidak valid berikut:',\n    lessThanMin: '{{value}} lebih kecil dari {{label}} minimum yang diizinkan yaitu {{min}}.',\n    limitReached: 'Batas tercapai, hanya {{max}} item yang dapat ditambahkan.',\n    longerThanMin: 'Nilai ini harus lebih panjang dari panjang minimum {{minLength}} karakter.',\n    notValidDate: '\"{{value}}\" bukan tanggal yang valid.',\n    required: 'Isian ini wajib diisi.',\n    requiresAtLeast: 'Isian ini membutuhkan setidaknya {{count}} {{label}}.',\n    requiresNoMoreThan: 'Isian ini membutuhkan tidak lebih dari {{count}} {{label}}.',\n    requiresTwoNumbers: 'Isian ini membutuhkan dua angka.',\n    shorterThanMax: 'Nilai ini harus lebih pendek dari panjang maksimum {{maxLength}} karakter.',\n    timezoneRequired: 'Zona waktu diperlukan.',\n    trueOrFalse: 'Isian ini hanya bisa sama dengan benar atau salah.',\n    username:\n      'Harap masukkan nama pengguna yang valid. Dapat berisi huruf, angka, tanda hubung, titik, dan garis bawah.',\n    validUploadID: 'Isian ini bukan ID unggahan yang valid.',\n  },\n  version: {\n    type: 'Tipe',\n    aboutToPublishSelection:\n      'Anda akan mempublikasikan semua {{label}} dalam pilihan. Apakah Anda yakin?',\n    aboutToRestore: 'Anda akan memulihkan dokumen {{label}} ini ke keadaan pada {{versionDate}}.',\n    aboutToRestoreGlobal: 'Anda akan memulihkan global {{label}} ke keadaan pada {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Anda akan mengembalikan perubahan dokumen ini ke keadaan yang dipublikasikan. Apakah Anda yakin?',\n    aboutToUnpublish: 'Anda akan membatalkan publikasi dokumen ini. Apakah Anda yakin?',\n    aboutToUnpublishSelection:\n      'Anda akan membatalkan publikasi semua {{label}} dalam pilihan. Apakah Anda yakin?',\n    autosave: 'Simpan Otomatis',\n    autosavedSuccessfully: 'Berhasil disimpan otomatis.',\n    autosavedVersion: 'Versi simpan otomatis',\n    changed: 'Berubah',\n    changedFieldsCount_one: '{{count}} Isian berubah',\n    changedFieldsCount_other: '{{count}} Isian berubah',\n    compareVersion: 'Bandingkan versi dengan:',\n    compareVersions: 'Bandingkan Versi',\n    comparingAgainst: 'Membandingkan dengan',\n    confirmPublish: 'Konfirmasi publikasi',\n    confirmRevertToSaved: 'Konfirmasi kembali ke yang tersimpan',\n    confirmUnpublish: 'Konfirmasi pembatalan publikasi',\n    confirmVersionRestoration: 'Konfirmasi Pemulihan Versi',\n    currentDocumentStatus: 'Dokumen {{docStatus}} saat ini',\n    currentDraft: 'Draf Saat Ini',\n    currentlyPublished: 'Sedang Dipublikasikan',\n    currentlyViewing: 'Sedang melihat',\n    currentPublishedVersion: 'Versi Terbitan Saat Ini',\n    draft: 'Draf',\n    draftSavedSuccessfully: 'Draf berhasil disimpan.',\n    lastSavedAgo: 'Terakhir disimpan {{distance}} yang lalu',\n    modifiedOnly: 'Hanya yang diubah',\n    moreVersions: 'Versi lainnya...',\n    noFurtherVersionsFound: 'Tidak ada versi lebih lanjut yang ditemukan',\n    noRowsFound: 'Tidak ada {{label}} yang ditemukan',\n    noRowsSelected: 'Tidak ada {{label}} yang dipilih',\n    preview: 'Pratinjau',\n    previouslyDraft: 'Sebelumnya Draf',\n    previouslyPublished: 'Sebelumnya Dipublikasikan',\n    previousVersion: 'Versi Sebelumnya',\n    problemRestoringVersion: 'Terjadi masalah saat memulihkan versi ini',\n    publish: 'Publikasikan',\n    publishAllLocales: 'Publikasikan semua lokal',\n    publishChanges: 'Publikasikan perubahan',\n    published: 'Diterbitkan',\n    publishIn: 'Publikasikan di {{locale}}',\n    publishing: 'Mempublikasikan',\n    restoreAsDraft: 'Pulihkan sebagai draf',\n    restoredSuccessfully: 'Berhasil dipulihkan.',\n    restoreThisVersion: 'Pulihkan versi ini',\n    restoring: 'Memulihkan...',\n    reverting: 'Mengembalikan...',\n    revertToPublished: 'Kembali ke yang dipublikasikan',\n    saveDraft: 'Simpan Draf',\n    scheduledSuccessfully: 'Berhasil dijadwalkan.',\n    schedulePublish: 'Jadwalkan Publikasi',\n    selectLocales: 'Pilih lokal untuk ditampilkan',\n    selectVersionToCompare: 'Pilih versi untuk dibandingkan',\n    showingVersionsFor: 'Menampilkan versi untuk:',\n    showLocales: 'Tampilkan lokal:',\n    specificVersion: 'Versi Spesifik',\n    status: 'Status',\n    unpublish: 'Batalkan Publikasi',\n    unpublishing: 'Membatalkan publikasi...',\n    version: 'Versi',\n    versionAgo: '{{distance}} yang lalu',\n    versionCount_many: '{{count}} versi ditemukan',\n    versionCount_none: 'Tidak ada versi yang ditemukan',\n    versionCount_one: '{{count}} versi ditemukan',\n    versionCount_other: '{{count}} versi ditemukan',\n    versionCreatedOn: '{{version}} dibuat pada:',\n    versionID: 'ID Versi',\n    versions: 'Versi',\n    viewingVersion: 'Melihat versi untuk {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Melihat versi untuk global {{entityLabel}}',\n    viewingVersions: 'Melihat versi untuk {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Melihat versi untuk global {{entityLabel}}',\n  },\n}\n\nexport const id: Language = {\n  dateFNSKey: 'id',\n  translations: idTranslations,\n}\n"], "names": ["idTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordUsernameInstructions", "usernameNotValid", "forgotPasswordQuestion", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "id", "dateFNS<PERSON>ey", "translations"], "mappings": "AAIA,OAAO,MAAMA,iBAAiB;IAC5BC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,oCACE;QACFC,kBAAkB;QAElBC,wBAAwB;QACxBC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbvQ,aAAa;QACbwQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBACE;QACFC,yBACE;QACFC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNjV,UAAU;QACVkV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb9Z,UACE;QACF+Z,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}