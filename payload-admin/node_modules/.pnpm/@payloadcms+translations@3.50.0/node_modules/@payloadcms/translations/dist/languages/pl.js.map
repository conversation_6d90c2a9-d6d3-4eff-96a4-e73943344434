{"version": 3, "sources": ["../../src/languages/pl.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const plTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Konto',\n    accountOfCurrentUser: 'Konto bieżącego użytkownika',\n    accountVerified: 'Konto zweryfikowane pomyślnie.',\n    alreadyActivated: '<PERSON><PERSON> aktywowano',\n    alreadyLoggedIn: '<PERSON><PERSON> zalog<PERSON>',\n    apiKey: 'Klucz API',\n    authenticated: 'Uwierzytelniony',\n    backToLogin: 'Powrót do logowania',\n    beginCreateFirstUser: '<PERSON><PERSON>, utwórz pierwszego użytkownika',\n    changePassword: '<PERSON><PERSON><PERSON> hasło',\n    checkYourEmailForPasswordReset:\n      'Jeśli adres e-mail jest powiązany z kontem, wkr<PERSON>tce otrzymasz instrukcje dotyczące zresetowania hasła. Sprawdź folder ze spamem lub niechcianą pocztą, jeśli nie widzisz e-maila w swojej skrzynce odbiorczej.',\n    confirmGeneration: 'Potwierdź wygenerowanie',\n    confirmPassword: 'Potwi<PERSON><PERSON> hasło',\n    createFirstUser: 'Utwórz pierwszego użytkownika',\n    emailNotValid: 'Podany email jest nieprawidłowy',\n    emailOrUsername: 'Email lub Nazwa użytkownika',\n    emailSent: 'Wysłano email',\n    emailVerified: 'Email zweryfikowany pomyślnie.',\n    enableAPIKey: 'Aktywuj klucz API',\n    failedToUnlock: 'Nie udało się odblokować',\n    forceUnlock: 'Wymuś odblokowanie',\n    forgotPassword: 'Zresetuj hasło',\n    forgotPasswordEmailInstructions:\n      'Proszę podaj swój email. Otrzymasz wiadomość z instrukcjami, jak zresetować hasło.',\n    forgotPasswordQuestion: 'Nie pamiętasz hasła?',\n    forgotPasswordUsernameInstructions:\n      'Proszę wpisać poniżej swoją nazwę użytkownika. Instrukcje dotyczące resetowania hasła zostaną wysłane na adres e-mail powiązany z Twoją nazwą użytkownika.',\n    generate: 'Wygeneruj',\n    generateNewAPIKey: 'Wygeneruj nowy klucz API',\n    generatingNewAPIKeyWillInvalidate:\n      'Wygenerowanie nowego klucza API <1>unieważni</1> poprzedni klucz. Czy na pewno chcesz kontynuować?',\n    lockUntil: 'Zablokuj do',\n    logBackIn: 'Zaloguj się ponownie',\n    loggedIn: 'Aby zalogować się na inne konto, najpierw się <0>wyloguj</0>.',\n    loggedInChangePassword:\n      'Aby zmienić hasło, przejdź do swojego <0>konta</0> i tam edytuj swoje hasło.',\n    loggedOutInactivity: 'Zostałeś wylogowany z powodu braku aktywności.',\n    loggedOutSuccessfully: 'Zostałeś pomyślnie wylogowany.',\n    loggingOut: 'Wylogowywanie...',\n    login: 'Zaloguj',\n    loginAttempts: 'Próby logowania',\n    loginUser: 'Zaloguj użytkownika',\n    loginWithAnotherUser: 'Aby zalogować się na inne konto, najpierw się <0>wyloguj</0>.',\n    logOut: 'Wyloguj',\n    logout: 'Wyloguj',\n    logoutSuccessful: 'Wylogowanie powiodło się.',\n    logoutUser: 'Wyloguj użytkownika',\n    newAccountCreated:\n      'Właśnie utworzono nowe konto, w celu uzyskania dostępu do <a href=\"{{serverURL}}\">{{serverURL}}</a>. Kliknij poniższy link lub wklej go do przeglądarki, aby zweryfikować swój adres email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a>.<br> Po zweryfikowaniu adresu email będziesz mógł się pomyślnie zalogować.',\n    newAPIKeyGenerated: 'Wygenerowano nowy klucz API.',\n    newPassword: 'Nowe hasło',\n    passed: 'Uwierzytelnienie zakończone sukcesem',\n    passwordResetSuccessfully: 'Hasło zostało pomyślnie zresetowane.',\n    resetPassword: 'Zresetuj hasło',\n    resetPasswordExpiration: 'Zresetuj czas wygaśnięcia hasła',\n    resetPasswordToken: 'Zresetuj token hasła',\n    resetYourPassword: 'Zresetuj swoje hasło',\n    stayLoggedIn: 'Pozostań zalogowany',\n    successfullyRegisteredFirstUser: 'Pomyślnie zarejestrowano pierwszego użytkownika.',\n    successfullyUnlocked: 'Pomyślnie odblokowano',\n    tokenRefreshSuccessful: 'Odświeżenie tokenu powiodło się.',\n    unableToVerify: 'Nie można zweryfikować',\n    username: 'Nazwa użytkownika',\n    usernameNotValid: 'Podana nazwa użytkownika nie jest prawidłowa.',\n    verified: 'Zweryfikowano',\n    verifiedSuccessfully: 'Pomyślnie zweryfikowany',\n    verify: 'Zweryfikuj',\n    verifyUser: 'Zweryfikuj użytkownika',\n    verifyYourEmail: 'Zweryfikuj swój email',\n    youAreInactive:\n      'Nie byłeś aktywny od dłuższego czasu i wkrótce zostaniesz automatycznie wylogowany dla własnego bezpieczeństwa. Czy chcesz pozostać zalogowany?',\n    youAreReceivingResetPassword:\n      'Otrzymałeś tę wiadomość, ponieważ Ty (lub ktoś inny) poprosiłeś o zresetowanie hasła do Twojego konta. Kliknij poniższy link lub wklej go w przeglądarce, aby zakończyć proces:',\n    youDidNotRequestPassword:\n      'Jeśli nie prosiłeś o zmianę hasła, zignoruj tę wiadomość, a Twoje hasło pozostanie niezmienione.',\n  },\n  error: {\n    accountAlreadyActivated: 'To konto zostało już aktywowane.',\n    autosaving: 'Wystąpił problem podczas automatycznego zapisywania tego dokumentu.',\n    correctInvalidFields: 'Popraw nieprawidłowe pola.',\n    deletingFile: '',\n    deletingTitle:\n      'Wystąpił błąd podczas usuwania {{title}}. Proszę, sprawdź swoje połączenie i spróbuj ponownie.',\n    documentNotFound:\n      'Dokument o ID {{id}} nie mógł zostać znaleziony. Mogło zostać usunięte lub nigdy nie istniało, lub może nie masz do niego dostępu.',\n    emailOrPasswordIncorrect: 'Podany adres e-mail lub hasło jest nieprawidłowe.',\n    followingFieldsInvalid_one: 'To pole jest nieprawidłowe:',\n    followingFieldsInvalid_other: 'Następujące pola są nieprawidłowe:',\n    incorrectCollection: 'Nieprawidłowa kolekcja',\n    insufficientClipboardPermissions: 'Odmowa dostępu do schowka. Sprawdź uprawnienia schowka.',\n    invalidClipboardData: 'Nieprawidłowe dane schowka.',\n    invalidFileType: 'Nieprawidłowy typ pliku',\n    invalidFileTypeValue: 'Nieprawidłowy typ pliku: {{value}}',\n    invalidRequestArgs: 'Nieprawidłowe argumenty w żądaniu: {{args}}',\n    loadingDocument: 'Wystapił problem podczas ładowania dokumentu o ID {{id}}.',\n    localesNotSaved_one: 'Następującej lokalizacji nie można było zapisać:',\n    localesNotSaved_other: 'Następujących lokalizacji nie można było zapisać:',\n    logoutFailed: 'Wylogowanie nie powiodło się.',\n    missingEmail: 'Brak adresu email.',\n    missingIDOfDocument: 'Brak ID dokumentu do aktualizacji.',\n    missingIDOfVersion: 'Brak ID wersji',\n    missingRequiredData: 'Brak wymaganych danych.',\n    noFilesUploaded: 'Nie przesłano żadnych plików.',\n    noMatchedField: 'Nie znaleziono pasującego pola dla \"{{label}}\"',\n    notAllowedToAccessPage: 'Nie masz dostępu do tej strony.',\n    notAllowedToPerformAction: 'Nie możesz wykonać tej akcji.',\n    notFound: 'Żądany zasób nie został znaleziony.',\n    noUser: 'Brak użytkownika',\n    previewing: 'Wystąpił problem podczas podglądu tego dokumentu.',\n    problemUploadingFile: 'Wystąpił problem podczas przesyłania pliku.',\n    restoringTitle:\n      'Wystąpił błąd podczas przywracania {{title}}. Sprawdź swoje połączenie i spróbuj ponownie.',\n    tokenInvalidOrExpired: 'Token jest nieprawidłowy lub wygasł.',\n    tokenNotProvided: 'Token nie został dostarczony.',\n    unableToCopy: 'Nie można skopiować.',\n    unableToDeleteCount: 'Nie można usunąć {{count}} z {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Błąd podczas ponownego indeksowania kolekcji {{collection}}. Operacja została przerwana.',\n    unableToUpdateCount: 'Nie można zaktualizować {{count}} z {{total}} {{label}}.',\n    unauthorized: 'Brak dostępu, musisz być zalogowany.',\n    unauthorizedAdmin: 'Brak dostępu, ten użytkownik nie ma dostępu do panelu administracyjnego.',\n    unknown: 'Wystąpił nieznany błąd.',\n    unPublishingDocument: 'Wystąpił problem podczas cofania publikacji tego dokumentu.',\n    unspecific: 'Wystąpił błąd',\n    unverifiedEmail: 'Proszę zweryfikować swój e-mail przed zalogowaniem się.',\n    userEmailAlreadyRegistered: 'Użytkownik o podanym adresie e-mail jest już zarejestrowany.',\n    userLocked: 'Ten użytkownik został zablokowany z powodu zbyt wielu nieudanych prób logowania.',\n    usernameAlreadyRegistered: 'Użytkownik o podanej nazwie użytkownika jest już zarejestrowany.',\n    usernameOrPasswordIncorrect: 'Podana nazwa użytkownika lub hasło jest nieprawidłowe.',\n    valueMustBeUnique: 'Wartość musi być unikalna',\n    verificationTokenInvalid: 'Token weryfikacyjny jest nieprawidłowy.',\n  },\n  fields: {\n    addLabel: 'Dodaj {{label}}',\n    addLink: 'Dodaj Link',\n    addNew: 'Dodaj nowy',\n    addNewLabel: 'Dodaj nowy {{label}}',\n    addRelationship: 'Dodaj Relację',\n    addUpload: 'Dodaj ładowanie',\n    block: 'Blok',\n    blocks: 'Bloki',\n    blockType: 'Typ Bloku',\n    chooseBetweenCustomTextOrDocument:\n      'Wybierz między wprowadzeniem niestandardowego tekstowego adresu URL a linkiem do innego dokumentu.',\n    chooseDocumentToLink: 'Wybierz dokument, do którego chcesz utworzyć łącze',\n    chooseFromExisting: 'Wybierz z istniejących',\n    chooseLabel: 'Wybierz {{label}}',\n    collapseAll: 'Zwiń wszystko',\n    customURL: 'Niestandardowy adres URL',\n    editLabelData: 'Edytuj dane {{label}}',\n    editLink: 'Edytuj Link',\n    editRelationship: 'Edytuj Relację',\n    enterURL: 'Wpisz adres URL',\n    internalLink: 'Link wewnętrzny',\n    itemsAndMore: '{{items}} i {{count}} więcej',\n    labelRelationship: 'Relacja {{label}}',\n    latitude: 'Szerokość',\n    linkedTo: 'Połączony z <0>{{label}}</0>',\n    linkType: 'Typ łącza',\n    longitude: 'Długość geograficzna',\n    newLabel: 'Nowy {{label}}',\n    openInNewTab: 'Otwórz w nowej karcie',\n    passwordsDoNotMatch: 'Hasła nie pasują',\n    relatedDocument: 'Powiązany dokument',\n    relationTo: 'Powiązany z',\n    removeRelationship: 'Usuń Relację',\n    removeUpload: 'Usuń Wrzucone',\n    saveChanges: 'Zapisz zmiany',\n    searchForBlock: 'Szukaj bloku',\n    selectExistingLabel: 'Wybierz istniejący {{label}}',\n    selectFieldsToEdit: 'Wybierz pola do edycji',\n    showAll: 'Pokaż wszystkie',\n    swapRelationship: 'Zamiana Relacji',\n    swapUpload: 'Zamień Wrzucone',\n    textToDisplay: 'Tekst do wyświetlenia',\n    toggleBlock: 'Przełącz blok',\n    uploadNewLabel: 'Wrzuć nowy {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Przeglądaj według folderu',\n    byFolder: 'Według Folderu',\n    deleteFolder: 'Usuń folder',\n    folderName: 'Nazwa folderu',\n    folders: 'Foldery',\n    folderTypeDescription:\n      'Wybierz, które typy dokumentów z kolekcji powinny być dozwolone w tym folderze.',\n    itemHasBeenMoved: '{{title}} został przeniesiony do {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} został przeniesiony do folderu głównego',\n    itemsMovedToFolder: '{{title}} przeniesiono do {{folderName}}',\n    itemsMovedToRoot: '{{title}} został przeniesiony do folderu głównego',\n    moveFolder: 'Przenieś folder',\n    moveItemsToFolderConfirmation:\n      'Zamierzasz przenieść <1>{{count}} {{label}}</1> do <2>{{toFolder}}</2>. Czy jesteś pewien?',\n    moveItemsToRootConfirmation:\n      'Zamierzasz przenieść <1>{{count}} {{label}}</1> do folderu głównego. Czy jesteś pewien?',\n    moveItemToFolderConfirmation:\n      'Zamierzasz przenieść <1>{{title}}</1> do <2>{{toFolder}}</2>. Czy jesteś pewien?',\n    moveItemToRootConfirmation:\n      'Zamierzasz przenieść <1>{{title}}</1> do folderu głównego. Jesteś pewien?',\n    movingFromFolder: 'Przenoszenie {{title}} z {{fromFolder}}',\n    newFolder: 'Nowy folder',\n    noFolder: 'Brak folderu',\n    renameFolder: 'Zmień nazwę folderu',\n    searchByNameInFolder: 'Szukaj według nazwy w {{folderName}}',\n    selectFolderForItem: 'Wybierz folder dla {{title}}',\n  },\n  general: {\n    name: 'Nazwa',\n    aboutToDelete: 'Zamierzasz usunąć {{label}} <1>{{title}}</1>. Jesteś pewien?',\n    aboutToDeleteCount_many: 'Zamierzasz usunąć {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Zamierzasz usunąć {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Zamierzasz usunąć {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Zamierzasz na stałe usunąć {{label}} <1>{{title}}</1>. Czy jesteś pewien?',\n    aboutToPermanentlyDeleteTrash:\n      'Zamierzasz na stałe usunąć <0>{{count}}</0> <1>{{label}}</1> z kosza. Czy jesteś pewny?',\n    aboutToRestore: 'Zamierzasz przywrócić {{label}} <1>{{title}}</1>. Czy jesteś pewny?',\n    aboutToRestoreAsDraft:\n      'Zamierzasz przywrócić {{label}} <1>{{title}}</1> jako szkic. Czy jesteś pewien?',\n    aboutToRestoreAsDraftCount: 'Za chwilę przywrócisz {{count}} {{label}} jako szkic',\n    aboutToRestoreCount: 'Za chwilę przywrócisz {{count}} {{label}}',\n    aboutToTrash: 'Zamierzasz przenieść {{label}} <1>{{title}}</1> do kosza. Czy jesteś pewien?',\n    aboutToTrashCount: 'Zamierzasz przenieść {{count}} {{label}} do kosza.',\n    addBelow: 'Dodaj poniżej',\n    addFilter: 'Dodaj filtr',\n    adminTheme: 'Motyw administratora',\n    all: 'Wszystko',\n    allCollections: 'Wszystkie kolekcje',\n    allLocales: 'Wszystkie lokalizacje',\n    and: 'i',\n    anotherUser: 'Inny użytkownik',\n    anotherUserTakenOver: 'Inny użytkownik przejął edycję tego dokumentu.',\n    applyChanges: 'Zastosuj zmiany',\n    ascending: 'Rosnąco',\n    automatic: 'Automatyczny',\n    backToDashboard: 'Powrót do panelu',\n    cancel: 'Anuluj',\n    changesNotSaved:\n      'Twoje zmiany nie zostały zapisane. Jeśli teraz wyjdziesz, stracisz swoje zmiany.',\n    clear: 'Jasne',\n    clearAll: 'Wyczyść wszystko',\n    close: 'Zamknij',\n    collapse: 'Zwiń',\n    collections: 'Kolekcje',\n    columns: 'Kolumny',\n    columnToSort: 'Kolumna sortowania',\n    confirm: 'Potwierdź',\n    confirmCopy: 'Potwierdź kopię',\n    confirmDeletion: 'Potwierdź usunięcie',\n    confirmDuplication: 'Potwierdź duplikację',\n    confirmMove: 'Potwierdź przeniesienie',\n    confirmReindex: 'Ponownie zaindeksować wszystkie {{collections}}?',\n    confirmReindexAll: 'Ponownie zaindeksować wszystkie kolekcje?',\n    confirmReindexDescription:\n      'Spowoduje to usunięcie istniejących indeksów i ponowne zaindeksowanie dokumentów w kolekcjach {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Spowoduje to usunięcie istniejących indeksów i ponowne zaindeksowanie dokumentów we wszystkich kolekcjach.',\n    confirmRestoration: 'Potwierdź przywrócenie',\n    copied: 'Skopiowano',\n    copy: 'Skopiuj',\n    copyField: 'Kopiuj pole',\n    copying: 'Kopiowanie',\n    copyRow: 'Kopiuj wiersz',\n    copyWarning:\n      'Zamierzasz nadpisać {{to}} na {{from}} dla {{label}} {{title}}. Czy jesteś pewny?',\n    create: 'Stwórz',\n    created: 'Utworzono',\n    createdAt: 'Data utworzenia',\n    createNew: 'Stwórz nowy',\n    createNewLabel: 'Stwórz nowy {{label}}',\n    creating: 'Tworzenie',\n    creatingNewLabel: 'Tworzenie nowego {{label}}',\n    currentlyEditing:\n      'obecnie edytuje ten dokument. Jeśli przejmiesz kontrolę, zostaną zablokowani przed dalszą edycją i mogą również utracić niezapisane zmiany.',\n    custom: 'Niestandardowy',\n    dark: 'Ciemny',\n    dashboard: 'Panel',\n    delete: 'Usuń',\n    deleted: 'Usunięte',\n    deletedAt: 'Usunięto o',\n    deletedCountSuccessfully: 'Pomyślnie usunięto {{count}} {{label}}.',\n    deletedSuccessfully: 'Pomyślnie usunięto.',\n    deletePermanently: 'Pomiń kosz i usuń na stałe',\n    deleting: 'Usuwanie...',\n    depth: 'Głębokość',\n    descending: 'Malejąco',\n    deselectAllRows: 'Odznacz wszystkie wiersze',\n    document: 'Dokument',\n    documentIsTrashed: 'To {{label}} jest w koszu i jest tylko do odczytu.',\n    documentLocked: 'Dokument zablokowany',\n    documents: 'Dokumenty',\n    duplicate: 'Zduplikuj',\n    duplicateWithoutSaving: 'Zduplikuj bez zapisywania zmian',\n    edit: 'Edytuj',\n    editAll: 'Edytuj wszystko',\n    editedSince: 'Edytowano od',\n    editing: 'Edycja',\n    editingLabel_many: 'Edytowanie {{count}} {{label}}',\n    editingLabel_one: 'Edytowanie {{count}} {{label}}',\n    editingLabel_other: 'Edytowanie {{count}} {{label}}',\n    editingTakenOver: 'Edycja przejęta',\n    editLabel: 'Edytuj {{label}}',\n    email: 'Email',\n    emailAddress: 'Adres email',\n    emptyTrash: 'Opróżnij kosz',\n    emptyTrashLabel: 'Opróżnij śmieci {{label}}',\n    enterAValue: 'Wpisz wartość',\n    error: 'Błąd',\n    errors: 'Błędy',\n    exitLivePreview: 'Wyjdź z Podglądu na Żywo',\n    export: 'Eksport',\n    fallbackToDefaultLocale: 'Powrót do domyślnych ustawień regionalnych',\n    false: 'Fałszywe',\n    filter: 'Filtr',\n    filters: 'Filtry',\n    filterWhere: 'Filtruj gdzie',\n    globals: 'Globalne',\n    goBack: 'Wróć',\n    groupByLabel: 'Grupuj według {{label}}',\n    import: 'Import',\n    isEditing: 'edytuje',\n    item: 'przedmiot',\n    items: 'przedmioty',\n    language: 'Język',\n    lastModified: 'Ostatnio zmodyfikowany',\n    leaveAnyway: 'Wyjdź mimo to',\n    leaveWithoutSaving: 'Wyjdź bez zapisywania',\n    light: 'Jasny',\n    livePreview: 'Podgląd',\n    loading: 'Ładowanie',\n    locale: 'Ustawienia regionalne',\n    locales: 'Ustawienia regionalne',\n    menu: 'Menu',\n    moreOptions: 'Więcej opcji',\n    move: 'Przesuń',\n    moveConfirm:\n      'Zamierzasz przenieść {{count}} {{label}} do <1>{{destination}}</1>. Czy na pewno?',\n    moveCount: 'Przenieś {{count}} {{label}}',\n    moveDown: 'Przesuń niżej',\n    moveUp: 'Przesuń wyżej',\n    moving: 'Przeprowadzka',\n    movingCount: 'Przenoszenie {{count}} {{label}}',\n    newPassword: 'Nowe hasło',\n    next: 'Następny',\n    no: 'Nie',\n    noDateSelected: 'Nie wybrano daty',\n    noFiltersSet: 'Brak ustawionych filtrów',\n    noLabel: '<Bez {{label}}>',\n    none: 'Nic',\n    noOptions: 'Brak opcji',\n    noResults:\n      'Nie znaleziono {{label}}. Być może {{label}} jeszcze nie istnieje, albo żaden nie pasuje do filtrów określonych powyżej.',\n    notFound: 'Nie znaleziono',\n    nothingFound: 'Nic nie znaleziono',\n    noTrashResults: 'Brak {{label}} w koszu.',\n    noUpcomingEventsScheduled: 'Nie zaplanowano żadnych nadchodzących wydarzeń.',\n    noValue: 'Brak wartości',\n    of: 'z',\n    only: 'Tylko',\n    open: 'Otwórz',\n    or: 'lub',\n    order: 'Kolejność',\n    overwriteExistingData: 'Nadpisz istniejące dane pola',\n    pageNotFound: 'Strona nie znaleziona',\n    password: 'Hasło',\n    pasteField: 'Wklej pole',\n    pasteRow: 'Wklej wiersz',\n    payloadSettings: 'Ustawienia Payload',\n    permanentlyDelete: 'Trwale Usuń',\n    permanentlyDeletedCountSuccessfully: 'Trwale usunięto {{count}} {{label}} pomyślnie.',\n    perPage: 'Na stronę: {{limit}}',\n    previous: 'Poprzedni',\n    reindex: 'Ponowne indeksowanie',\n    reindexingAll: 'Ponowne indeksowanie wszystkich {{collections}}.',\n    remove: 'Usuń',\n    rename: 'Zmień nazwę',\n    reset: 'Zresetuj',\n    resetPreferences: 'Zresetuj preferencje',\n    resetPreferencesDescription: 'To zresetuje wszystkie Twoje preferencje do ustawień domyślnych.',\n    resettingPreferences: 'Resetowanie preferencji.',\n    restore: 'Przywróć',\n    restoreAsPublished: 'Przywróć jako opublikowaną wersję',\n    restoredCountSuccessfully: 'Pomyślnie przywrócono {{count}} {{label}}.',\n    restoring: 'Przywracanie...',\n    row: 'Wiersz',\n    rows: 'Wiersze',\n    save: 'Zapisz',\n    saving: 'Zapisywanie...',\n    schedulePublishFor: 'Zaplanuj publikację dla {{title}}',\n    searchBy: 'Szukaj według',\n    select: 'Wybierz',\n    selectAll: 'Wybierz wszystkie {{count}} {{label}}',\n    selectAllRows: 'Wybierz wszystkie wiersze',\n    selectedCount: 'Wybrano {{count}} {{label}}',\n    selectLabel: 'Wybierz {{label}}',\n    selectValue: 'Wybierz wartość',\n    showAllLabel: 'Pokaż wszystkie {{label}}',\n    sorryNotFound: 'Przepraszamy — nie ma nic, co odpowiadałoby twojemu zapytaniu.',\n    sort: 'Sortuj',\n    sortByLabelDirection: 'Sortuj według {{label}} {{direction}}',\n    stayOnThisPage: 'Pozostań na stronie',\n    submissionSuccessful: 'Zgłoszenie zakończone powodzeniem.',\n    submit: 'Zatwierdź',\n    submitting: 'Przesyłanie...',\n    success: 'Sukces',\n    successfullyCreated: 'Pomyślnie utworzono {{label}}.',\n    successfullyDuplicated: 'Pomyślnie zduplikowano {{label}}',\n    successfullyReindexed:\n      'Pomyślnie ponownie zindeksowano {{count}} z {{total}} dokumentów z kolekcji {{collections}}.',\n    takeOver: 'Przejąć',\n    thisLanguage: 'Polski',\n    time: 'Czas',\n    timezone: 'Strefa czasowa',\n    titleDeleted: 'Pomyślnie usunięto {{label}} {{title}}',\n    titleRestored: 'Etykieta \"{{title}}\" została pomyślnie przywrócona.',\n    titleTrashed: '{{label}} \"{{title}}\" przeniesiony do kosza.',\n    trash: 'Śmieci',\n    trashedCountSuccessfully: '{{count}} {{label}} przeniesiono do kosza.',\n    true: 'Prawda',\n    unauthorized: 'Brak autoryzacji',\n    unsavedChanges: 'Masz niezapisane zmiany. Zapisz lub odrzuć, zanim kontynuujesz.',\n    unsavedChangesDuplicate: 'Masz niezapisane zmiany. Czy chcesz kontynuować duplikowanie?',\n    untitled: 'Bez nazwy',\n    upcomingEvents: 'Nadchodzące Wydarzenia',\n    updatedAt: 'Data edycji',\n    updatedCountSuccessfully: 'Pomyślnie zaktualizowano {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Pomyślnie zaktualizowano {{label}}.',\n    updatedSuccessfully: 'Aktualizacja zakończona sukcesem.',\n    updateForEveryone: 'Aktualizacja dla wszystkich',\n    updating: 'Aktualizacja',\n    uploading: 'Przesyłanie',\n    uploadingBulk: 'Przesyłanie {{current}} z {{total}}',\n    user: 'użytkownik',\n    username: 'Nazwa użytkownika',\n    users: 'użytkownicy',\n    value: 'Wartość',\n    viewing: 'Podgląd',\n    viewReadOnly: 'Widok tylko do odczytu',\n    welcome: 'Witaj',\n    yes: 'Tak',\n  },\n  localization: {\n    cannotCopySameLocale: 'Nie można skopiować do tego samego miejsca.',\n    copyFrom: 'Kopiuj z',\n    copyFromTo: 'Kopiowanie z {{from}} do {{to}}',\n    copyTo: 'Kopiuj do',\n    copyToLocale: 'Kopiuj do lokalizacji',\n    localeToPublish: 'Publikować lokalnie',\n    selectLocaleToCopy: 'Wybierz lokalizację do skopiowania',\n  },\n  operators: {\n    contains: 'zawiera',\n    equals: 'równe',\n    exists: 'istnieje',\n    intersects: 'przecina się',\n    isGreaterThan: 'jest większy niż',\n    isGreaterThanOrEqualTo: 'jest większe lub równe',\n    isIn: 'jest w',\n    isLessThan: 'jest mniejsze niż',\n    isLessThanOrEqualTo: 'jest mniejsze lub równe',\n    isLike: 'jest jak',\n    isNotEqualTo: 'nie jest równe',\n    isNotIn: 'nie ma go w',\n    isNotLike: 'nie jest jak',\n    near: 'blisko',\n    within: 'w ciągu',\n  },\n  upload: {\n    addFile: 'Dodaj plik',\n    addFiles: 'Dodaj pliki',\n    bulkUpload: 'Załaduj masowo',\n    crop: 'Przytnij',\n    cropToolDescription:\n      'Przeciągnij narożniki wybranego obszaru, narysuj nowy obszar lub dostosuj poniższe wartości.',\n    download: 'Pobierz',\n    dragAndDrop: 'Przeciągnij i upuść plik',\n    dragAndDropHere: 'lub złap i upuść plik tutaj',\n    editImage: 'Edytuj obraz',\n    fileName: 'Nazwa pliku',\n    fileSize: 'Rozmiar pliku',\n    filesToUpload: 'Pliki do przesłania',\n    fileToUpload: 'Plik do przesłania',\n    focalPoint: 'Punkt centralny',\n    focalPointDescription:\n      'Przeciągnij punkt centralny bezpośrednio na podglądzie lub dostosuj wartości poniżej.',\n    height: 'Wysokość',\n    lessInfo: 'Mniej informacji',\n    moreInfo: 'Więcej informacji',\n    noFile: 'Brak pliku',\n    pasteURL: 'Wklej URL',\n    previewSizes: 'Rozmiary podglądu',\n    selectCollectionToBrowse: 'Wybierz kolekcję aby przejrzeć',\n    selectFile: 'Wybierz plik',\n    setCropArea: 'Ustaw obszar kadrowania',\n    setFocalPoint: 'Ustawić punkt ogniskowy',\n    sizes: 'Rozmiary',\n    sizesFor: 'Rozmiary dla {{label}}',\n    width: 'Szerokość',\n  },\n  validation: {\n    emailAddress: 'Wprowadź poprawny adres email.',\n    enterNumber: 'Wprowadź poprawny numer telefonu.',\n    fieldHasNo: 'To pole nie posiada {{label}}',\n    greaterThanMax: '{{value}} jest większe niż maksymalnie dozwolony {{label}} wynoszący {{max}}.',\n    invalidInput: 'To pole zawiera nieprawidłowe dane.',\n    invalidSelection: 'To pole ma nieprawidłowy wybór.',\n    invalidSelections: 'To pole zawiera następujące, nieprawidłowe wybory:',\n    lessThanMin: '{{value}} jest mniejsze niż minimalnie dozwolony {{label}} wynoszący {{min}}.',\n    limitReached: 'Osiągnięto limit, można dodać tylko {{max}} elementów.',\n    longerThanMin: 'Ta wartość musi być dłuższa niż minimalna długość znaków: {{minLength}}.',\n    notValidDate: '\"{{value}}\" nie jest prawidłową datą.',\n    required: 'To pole jest wymagane.',\n    requiresAtLeast: 'To pole wymaga co najmniej {{count}} {{label}}.',\n    requiresNoMoreThan: 'To pole może posiadać co najmniej {{count}} {{label}}.',\n    requiresTwoNumbers: 'To pole wymaga dwóch liczb.',\n    shorterThanMax: 'Ta wartość musi być krótsza niż maksymalna długość znaków: {{maxLength}}.',\n    timezoneRequired: 'Wymagana jest strefa czasowa.',\n    trueOrFalse: \"To pole może mieć wartość tylko 'true' lub 'false'.\",\n    username:\n      'Proszę wprowadzić prawidłową nazwę użytkownika. Może zawierać litery, cyfry, myślniki, kropki i podkreślniki.',\n    validUploadID: 'To pole nie jest prawidłowym identyfikatorem przesyłania.',\n  },\n  version: {\n    type: 'Typ',\n    aboutToPublishSelection:\n      'Za chwilę opublikujesz wszystkie {{label}} w zaznaczeniu. Jesteś pewny?',\n    aboutToRestore:\n      'Zamierzasz przywrócić dokument {{label}} do stanu, w jakim znajdował się w dniu {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Zamierzasz przywrócić globalny rekord {{label}} do stanu, w którym znajdował się w dniu {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Zamierzasz przywrócić zmiany w tym dokumencie do stanu opublikowanego. Jesteś pewien?',\n    aboutToUnpublish: 'Zamierzasz cofnąć publikację tego dokumentu. Jesteś pewien?',\n    aboutToUnpublishSelection:\n      'Zamierzasz cofnąć publikację wszystkich {{label}} w zaznaczeniu. Jesteś pewny?',\n    autosave: 'Autozapis',\n    autosavedSuccessfully: 'Pomyślnie zapisano automatycznie.',\n    autosavedVersion: 'Wersja zapisana automatycznie',\n    changed: 'Zmieniono',\n    changedFieldsCount_one: '{{count}} zmienione pole',\n    changedFieldsCount_other: '{{count}} zmienione pola',\n    compareVersion: 'Porównaj wersję z:',\n    compareVersions: 'Porównaj Wersje',\n    comparingAgainst: 'Porównując do',\n    confirmPublish: 'Potwierdź publikację',\n    confirmRevertToSaved: 'Potwierdź powrót do zapisanego',\n    confirmUnpublish: 'Potwierdź cofnięcie publikacji',\n    confirmVersionRestoration: 'Potwierdź przywrócenie wersji',\n    currentDocumentStatus: 'Bieżący status {{docStatus}} dokumentu',\n    currentDraft: 'Aktualna wersja robocza',\n    currentlyPublished: 'Obecnie opublikowane',\n    currentlyViewing: 'Obecnie przeglądasz',\n    currentPublishedVersion: 'Aktualna Opublikowana Wersja',\n    draft: 'Szkic',\n    draftSavedSuccessfully: 'Wersja robocza została pomyślnie zapisana.',\n    lastSavedAgo: 'Ostatnio zapisane {{distance}} temu',\n    modifiedOnly: 'Tylko zmodyfikowany',\n    moreVersions: 'Więcej wersji...',\n    noFurtherVersionsFound: 'Nie znaleziono dalszych wersji',\n    noRowsFound: 'Nie znaleziono {{label}}',\n    noRowsSelected: 'Nie wybrano {{etykieta}}',\n    preview: 'Podgląd',\n    previouslyDraft: 'Poprzednio Szkic',\n    previouslyPublished: 'Wcześniej opublikowane',\n    previousVersion: 'Poprzednia Wersja',\n    problemRestoringVersion: 'Wystąpił problem podczas przywracania tej wersji',\n    publish: 'Publikuj',\n    publishAllLocales: 'Opublikuj wszystkie lokalizacje',\n    publishChanges: 'Opublikuj zmiany',\n    published: 'Opublikowano',\n    publishIn: 'Opublikuj w {{locale}}',\n    publishing: 'Publikacja',\n    restoreAsDraft: 'Przywróć jako szkic',\n    restoredSuccessfully: 'Przywrócono pomyślnie.',\n    restoreThisVersion: 'Przywróć tę wersję',\n    restoring: 'Przywracanie...',\n    reverting: 'Cofanie...',\n    revertToPublished: 'Przywróć do opublikowanego',\n    saveDraft: 'Zapisz szkic',\n    scheduledSuccessfully: 'Zaplanowano pomyślnie.',\n    schedulePublish: 'Zaplanuj publikację',\n    selectLocales: 'Wybierz ustawienia regionalne do wyświetlenia',\n    selectVersionToCompare: 'Wybierz wersję do porównania',\n    showingVersionsFor: 'Wyświetlanie wersji dla:',\n    showLocales: 'Pokaż ustawienia regionalne:',\n    specificVersion: 'Konkretna Wersja',\n    status: 'Status',\n    unpublish: 'Cofnij publikację',\n    unpublishing: 'Cofanie publikacji...',\n    version: 'Wersja',\n    versionAgo: '{{distance}} temu',\n    versionCount_many: 'Znalezionych wersji: {{count}}',\n    versionCount_none: 'Nie znaleziono wersji',\n    versionCount_one: 'Znaleziono {{count}} wersję',\n    versionCount_other: 'Znaleziono {{count}} wersji',\n    versionCreatedOn: 'Wersja {{version}} utworzona:',\n    versionID: 'ID wersji',\n    versions: 'Wersje',\n    viewingVersion: 'Przeglądanie wersji dla {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Przeglądanie wersji dla globalnej kolekcji {{entityLabel}}',\n    viewingVersions: 'Przeglądanie wersji {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Przeglądanie wersji dla globalnej kolekcji {{entityLabel}}',\n  },\n}\n\nexport const pl: Language = {\n  dateFNSKey: 'pl',\n  translations: plTranslations,\n}\n"], "names": ["plTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "pl", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCAAkC;QAClCC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}