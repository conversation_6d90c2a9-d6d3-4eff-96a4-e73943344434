{"version": 3, "sources": ["../../src/languages/th.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const thTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'บัญชี',\n    accountOfCurrentUser: 'บัญชีปัจจุบัน',\n    accountVerified: 'ยืนยันบัญชีสำเร็จแล้ว',\n    alreadyActivated: 'เปิดใช้งานแล้ว',\n    alreadyLoggedIn: 'ลงชื่อเข้าใช้แล้ว',\n    apiKey: 'API Key',\n    authenticated: 'ได้รับการตรวจสอบแล้ว',\n    backToLogin: 'กลับไปหน้าเข้าสู่ระบบ',\n    beginCreateFirstUser: 'สร้างผู้ใช้แรกเพื่อเริ่มใช้งาน',\n    changePassword: 'เปลี่ยนรหัสผ่าน',\n    checkYourEmailForPasswordReset:\n      'ถ้าที่อยู่อีเมลถูกเชื่อมโยงกับบัญชีผู้ใช้งาน คุณจะได้รับคำแนะนำในการเปลี่ยนรหัสผ่านในไม่ช้า กรุณาตรวจสอบโฟลเดอร์สแปมหรือจัด mail ถ้าคุณไม่เห็นอีเมลในกล่องจดหมายขาเข้า',\n    confirmGeneration: 'ยืนยันการสร้าง',\n    confirmPassword: 'ยืนยันรหัสผ่าน',\n    createFirstUser: 'สร้างผู้ใช้แรก',\n    emailNotValid: 'อีเมลไม่ถูกต้อง',\n    emailOrUsername: 'อีเมลหรือชื่อผู้ใช้',\n    emailSent: 'ส่งอีเมลเรียบร้อยแล้ว',\n    emailVerified: 'อีเมลได้รับการยืนยันเรียบร้อยแล้ว',\n    enableAPIKey: 'เปิดใช้ API Key',\n    failedToUnlock: 'ไม่สามารถปลดล็อกได้',\n    forceUnlock: 'ปลดล็อกบัญชี',\n    forgotPassword: 'ลืมรหัสผ่าน',\n    forgotPasswordEmailInstructions:\n      'กรุณาใส่อีเมลของคุณ ระบบจะส่งวิธีการเปลี่ยนรหัสผ่านไปให้คุณทางอีเมล',\n    forgotPasswordQuestion: 'ลืมรหัสผ่าน?',\n    forgotPasswordUsernameInstructions:\n      'กรุณากรอกชื่อผู้ใช้ของคุณที่ด้านล่าง คำแนะนำเกี่ยวกับวิธีการรีเซ็ตรหัสผ่านของคุณจะถูกส่งไปยังที่อยู่อีเมลที่เชื่อมโยงกับชื่อผู้ใช้ของคุณ',\n    generate: 'สร้าง',\n    generateNewAPIKey: 'สร้าง API Key',\n    generatingNewAPIKeyWillInvalidate:\n      'การสร้าง API Key ใหม่จะเป็นการ<1>ยกเลิก</1> API Key เดิม คุณต้องการดำเนินการต่อหรือไม่?',\n    lockUntil: 'ล็อกบัญชีถึง',\n    logBackIn: 'เข้าสู่ระบบอีกครั้ง',\n    loggedIn: 'หากต้องการเข้าสู่ระบบด้วยบัญชีอื่น กรุณา<0>ออกจากระบบ</0>ก่อน',\n    loggedInChangePassword: 'หากต้องการเปลี่ยนรหัสผ่าน กรุณาแก้ไขที่หน้า<0>บัญชี</0>ของคุณ',\n    loggedOutInactivity: 'คุณได้ออกจากระบบเนื่องจากไม่มีการใช้งาน',\n    loggedOutSuccessfully: 'ออกจากระบบเรียบร้อยแล้ว',\n    loggingOut: 'ออกจากระบบ...',\n    login: 'เข้าสู่ระบบ',\n    loginAttempts: 'จำนวนครั้งการเข้าสู่ระบบ',\n    loginUser: 'เข้าสู่ระบบ',\n    loginWithAnotherUser: 'หากต้องการเข้าสู่ระบบด้วยบัญชีอื่น กรุณา<0>ออกจากระบบ</0>ก่อน',\n    logOut: 'ออกจากระบบ',\n    logout: 'ออกจากระบบ',\n    logoutSuccessful: 'ออกจากระบบสำเร็จ',\n    logoutUser: 'ออกจากระบบ',\n    newAccountCreated:\n      'ระบบได้สร้างบัญชีผู้ใช้ให้คุณสำหรับเข้าใช้งาน <a href=\"{{serverURL}}\">{{serverURL}}</a> เรียบร้อยแล้ว กรุณากดลิงก์ด้านล่างเพื่อยืนยันอีเมล หลังจากยืนยันอีเมลเสร็จสิ้น คุณจะสามารถเข้าใช้งานระบบได้',\n    newAPIKeyGenerated: 'สร้าง API Key ใหม่แล้ว',\n    newPassword: 'รหัสผ่านใหม่',\n    passed: 'การยืนยันตัวตนสำเร็จ',\n    passwordResetSuccessfully: 'รีเซ็ตรหัสผ่านเรียบร้อยแล้ว',\n    resetPassword: 'รีเซ็ตรหัสผ่าน',\n    resetPasswordExpiration: 'วันหมดอายุสำหรับการรีเซ็ตรหัสผ่าน',\n    resetPasswordToken: 'Token รีเซ็ตรหัสผ่าน',\n    resetYourPassword: 'รีเซ็ตรหัสผ่านของคุณ',\n    stayLoggedIn: 'เข้าสู่ระบบต่อไป',\n    successfullyRegisteredFirstUser: 'ลงทะเบียนผู้ใช้คนแรกสำเร็จแล้ว',\n    successfullyUnlocked: 'ปลดล็อกบัญชีสำเร็จ',\n    tokenRefreshSuccessful: 'การรีเฟรชโทเค็นสำเร็จ',\n    unableToVerify: 'ไม่สามารถยืนยันบัญชีได้',\n    username: 'ชื่อผู้ใช้',\n    usernameNotValid: 'ชื่อผู้ใช้ที่ให้มาไม่ถูกต้อง',\n    verified: 'ยืนยันบััญชีแล้ว',\n    verifiedSuccessfully: 'ยืนยันบัญชีสำเร็จ',\n    verify: 'ยืนยันบัญชี',\n    verifyUser: 'ยืนยันบัญชี',\n    verifyYourEmail: 'ยืนยันอีเมลของคุณ',\n    youAreInactive:\n      'คุณกำลังจะถูกให้ออกจากระบบในเร็ว ๆ นี้เนื่องจากไม่มีการใช้งานระบบมาสักพักหนึ่ง คุณต้องการเข้าสู่ระบบต่อหรือไม่?',\n    youAreReceivingResetPassword:\n      'คุณได้รับอีเมลนี้เนื่องจากคุณ (หรือคนอื่น) ได้ร้องขอให้รีเซ็ตรหัสผ่านของบัญชีของคุณ กรุณากดลิงก์ด้านล่างเพื่อดำเนินการรีเซ็ตรหัสผ่านต่อ:',\n    youDidNotRequestPassword:\n      'หากคุณไม่ได้ร้องขอให้มีการรีเซ็ตรหัสผ่าน คุณสามารถเพิกเฉยข้อความนี้ได้ โดยรหัสผ่านของคุณจะคงอยู่เช่นเดิม',\n  },\n  error: {\n    accountAlreadyActivated: 'บัญชีนี้ถูกเปิดใช้งานไปแล้ว',\n    autosaving: 'เกิดปัญหาระหว่างการบันทึกเอกสารอัตโนมัติ',\n    correctInvalidFields: 'โปรดแก้ไขช่องที่ไม่ถูกต้อง',\n    deletingFile: 'เกิดปัญหาระหว่างการลบไฟล์',\n    deletingTitle: 'เกิดปัญหาระหว่างการลบ {{title}} โปรดตรวจสอบการเชื่อมต่อของคุณแล้วลองอีกครั้ง',\n    documentNotFound:\n      'ไม่พบเอกสารที่มี ID {{id}} อาจจะถูกลบหรือไม่เคยมีอยู่ หรือคุณอาจไม่มีสิทธิ์เข้าถึง',\n    emailOrPasswordIncorrect: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง',\n    followingFieldsInvalid_one: 'ช่องต่อไปนี้ไม่ถูกต้อง:',\n    followingFieldsInvalid_other: 'ช่องต่อไปนี้ไม่ถูกต้อง:',\n    incorrectCollection: 'Collection ไม่ถูกต้อง',\n    insufficientClipboardPermissions:\n      'การเข้าถึงคลิปบอร์ดถูกปฏิเสธ กรุณาตรวจสอบสิทธิ์การเข้าถึงคลิปบอร์ดของคุณ',\n    invalidClipboardData: 'ข้อมูลคลิปบอร์ดไม่ถูกต้อง',\n    invalidFileType: 'ประเภทของไฟล์ไม่ถูกต้อง',\n    invalidFileTypeValue: 'ประเภทของไฟล์ไม่ถูกต้อง: {{value}}',\n    invalidRequestArgs: 'มีการส่งอาร์กิวเมนต์ที่ไม่ถูกต้องในคำขอ: {{args}}',\n    loadingDocument: 'เกิดปัญหาระหว่างการโหลดเอกสารที่มี ID {{id}}',\n    localesNotSaved_one: 'ไม่สามารถบันทึกกำหนดสถานที่ต่อไปนี้ได้:',\n    localesNotSaved_other: 'ไม่สามารถบันทึกกำหนดสถานที่ต่อไปนี้ได้:',\n    logoutFailed: 'การออกจากระบบล้มเหลว.',\n    missingEmail: 'ไม่พบอีเมล',\n    missingIDOfDocument: 'ไม่พบ ID ของเอกสารที่ต้องการแก้ไข',\n    missingIDOfVersion: 'ไม่พบ ID ของเวอร์ชัน',\n    missingRequiredData: 'ไม่พบข้อมูลที่จำเป็น',\n    noFilesUploaded: 'ไม่มีไฟล์ถูกอัปโหลด',\n    noMatchedField: 'ไม่พบช่อง \"{{label}}\"',\n    notAllowedToAccessPage: 'คุณไม่ได้รับอนุญาตให้เข้าถึงหน้านี้',\n    notAllowedToPerformAction: 'คุณไม่ได้รับอนุญาตให้ดำเนินการสิ่งนี้',\n    notFound: 'ไม่พบหน้าที่คุณต้องการ',\n    noUser: 'ไม่พบผู้ใช้',\n    previewing: 'เกิดปัญหาระหว่างการแสดงตัวอย่างเอกสาร',\n    problemUploadingFile: 'เกิดปัญหาระหว่างการอัปโหลดไฟล์',\n    restoringTitle:\n      'เกิดข้อผิดพลาดขณะกำลังคืนค่า {{title}} กรุณาตรวจสอบการเชื่อมต่อของคุณและลองอีกครั้ง',\n    tokenInvalidOrExpired: 'Token ไม่ถูกต้องหรือหมดอายุ',\n    tokenNotProvided: 'ไม่ได้รับโทเค็น',\n    unableToCopy: 'ไม่สามารถคัดลอกได้',\n    unableToDeleteCount: 'ไม่สามารถลบ {{count}} จาก {{total}} {{label}}',\n    unableToReindexCollection:\n      'เกิดข้อผิดพลาดในการจัดทำดัชนีใหม่ของคอลเลกชัน {{collection}}. การดำเนินการถูกยกเลิก',\n    unableToUpdateCount: 'ไม่สามารถอัปเดต {{count}} จาก {{total}} {{label}}',\n    unauthorized: 'คุณไม่ได้รับอนุญาต กรุณาเข้าสู่ระบบเพื่อทำคำขอนี้',\n    unauthorizedAdmin: 'คุณไม่ได้รับอนุญาตให้เข้าถึงแผงผู้ดูแล',\n    unknown: 'เกิดปัญหาบางอย่างที่ไม่ทราบสาเหตุ',\n    unPublishingDocument: 'เกิดปัญหาระหว่างการยกเลิกการเผยแพร่เอกสารนี้',\n    unspecific: 'เกิดปัญหาบางอย่าง',\n    unverifiedEmail: 'กรุณายืนยันอีเมลของคุณก่อนเข้าสู่ระบบ',\n    userEmailAlreadyRegistered: 'ผู้ใช้ที่มีอีเมลดังกล่าวได้ลงทะเบียนแล้ว',\n    userLocked: 'บัญชีนี้ถูกล็อกเนื่องจากมีการพยายามเข้าสู่ระบบมากเกินไป',\n    usernameAlreadyRegistered: 'ผู้ใช้ที่มีชื่อผู้ใช้ที่ระบุไว้แล้วถูกลงทะเบียนเอาไว้แล้ว',\n    usernameOrPasswordIncorrect: 'ชื่อผู้ใช้หรือรหัสผ่านที่คุณให้มาไม่ถูกต้อง',\n    valueMustBeUnique: 'ค่าต้องไม่ซ้ำกับเอกสารอื่น',\n    verificationTokenInvalid: 'Token ยืนยันตัวตนไม่ถูกต้อง',\n  },\n  fields: {\n    addLabel: 'เพิ่ม {{label}}',\n    addLink: 'เพิ่มลิงค์',\n    addNew: 'เพิ่ม',\n    addNewLabel: 'เพิ่ม {{label}} ใหม่',\n    addRelationship: 'เพิ่มความสัมพันธ์',\n    addUpload: 'เพิ่มการอัปโหลด',\n    block: 'Block',\n    blocks: 'Blocks',\n    blockType: 'ประเภท Block',\n    chooseBetweenCustomTextOrDocument: 'เลือกระหว่างกำหนด URL เองหรือเชื่อมไปยังเอกสารอื่น',\n    chooseDocumentToLink: 'เลือกเอกสารที่จะเชื่อมโยง',\n    chooseFromExisting: 'เลือกจากที่มีอยู่',\n    chooseLabel: 'เลือก {{label}}',\n    collapseAll: 'ยุบทั้งหมด',\n    customURL: 'URL ที่กำหนดเอง',\n    editLabelData: 'แก้ไขข้อมูล {{label}}',\n    editLink: 'แก้ไขลิงก์',\n    editRelationship: 'แก้ไขความสัมพันธ์',\n    enterURL: 'ระบุ URL',\n    internalLink: 'ลิงก์ภายใน',\n    itemsAndMore: '{{items}} และเพิ่มเติมอีก {{count}}',\n    labelRelationship: 'ความสัมพันธ์กับ {{label}}',\n    latitude: 'ละติจูด',\n    linkedTo: 'เชื่อมกับ <0>{{label}}</0> สำเร็จ',\n    linkType: 'ประเภทของลิงก์',\n    longitude: 'ลองติจูด',\n    newLabel: '{{label}} ใหม่',\n    openInNewTab: 'เปิดในแท็บใหม่',\n    passwordsDoNotMatch: 'รหัสผ่านไม่ตรงกัน',\n    relatedDocument: 'เอกสารที่เกี่ยวข้อง',\n    relationTo: 'เชื่อมกับ',\n    removeRelationship: 'ลบความสัมพันธ์',\n    removeUpload: 'ลบอัปโหลด',\n    saveChanges: 'บันทึก',\n    searchForBlock: 'ค้นหา Block',\n    selectExistingLabel: 'เลือก {{label}} ที่มีอยู่',\n    selectFieldsToEdit: 'เลือกช่องที่จะแก้ไข',\n    showAll: 'แสดงทั้งหมด',\n    swapRelationship: 'สลับความสัมพันธ์',\n    swapUpload: 'สลับอัปโหลด',\n    textToDisplay: 'ข้อความสำหรับแสดงผล',\n    toggleBlock: 'เปิด/ปิด Block',\n    uploadNewLabel: 'อัปโหลด {{label}} ใหม่',\n  },\n  folder: {\n    browseByFolder: 'เรียกดูตามโฟลเดอร์',\n    byFolder: 'ตามโฟลเดอร์',\n    deleteFolder: 'ลบโฟลเดอร์',\n    folderName: 'ชื่อโฟลเดอร์',\n    folders: 'โฟลเดอร์',\n    folderTypeDescription: 'เลือกประเภทของเอกสารคอลเลกชันที่ควรอนุญาตในโฟลเดอร์นี้',\n    itemHasBeenMoved: '{{title}} ได้ถูกย้ายไปที่ {{folderName}}',\n    itemHasBeenMovedToRoot: '\"{{title}}\" ได้ถูกย้ายไปยังโฟลเดอร์ราก',\n    itemsMovedToFolder: '{{title}} ถูกย้ายไปยัง {{folderName}}',\n    itemsMovedToRoot: '{{title}} ถูกย้ายไปยังโฟลเดอร์ราก',\n    moveFolder: 'ย้ายโฟลเดอร์',\n    moveItemsToFolderConfirmation:\n      'คุณกำลังจะย้าย <1>{{count}} {{label}}</1> ไปยัง <2>{{toFolder}}</2> แน่ใจไหม?',\n    moveItemsToRootConfirmation:\n      'คุณกำลังจะย้าย <1>{{count}} {{label}}</1> ไปยังโฟลเดอร์ราก คุณแน่ใจหรือไม่?',\n    moveItemToFolderConfirmation:\n      'คุณกำลังจะย้าย <1>{{title}}</1> ไปยัง <2>{{toFolder}}</2> คุณแน่ใจหรือไม่?',\n    moveItemToRootConfirmation: 'คุณกำลังจะย้าย <1>{{title}}</1> ไปยังโฟลเดอร์ราก คุณแน่ใจหรือไม่?',\n    movingFromFolder: 'ย้าย {{title}} จาก {{fromFolder}}',\n    newFolder: 'โฟลเดอร์ใหม่',\n    noFolder: 'ไม่มีโฟลเดอร์',\n    renameFolder: 'เปลี่ยนชื่อโฟลเดอร์',\n    searchByNameInFolder: 'ค้นหาด้วยชื่อใน {{folderName}}',\n    selectFolderForItem: 'เลือกโฟลเดอร์สำหรับ {{title}}',\n  },\n  general: {\n    name: 'ชื่อ',\n    aboutToDelete: 'คุณกำลังจะลบ {{label}} <1>{{title}}</1> ต้องการดำเนินการต่อหรือไม่?',\n    aboutToDeleteCount_many: 'คุณกำลังจะลบ {{count}} {{label}}',\n    aboutToDeleteCount_one: 'คุณกำลังจะลบ {{count}} {{label}}',\n    aboutToDeleteCount_other: 'คุณกำลังจะลบ {{count}} {{label}}',\n    aboutToPermanentlyDelete: 'คุณกำลังจะลบ {{label}} <1>{{title}}</1> อย่างถาวร คุณแน่ใจหรือไม่?',\n    aboutToPermanentlyDeleteTrash:\n      'คุณกำลังจะลบ <0>{{count}}</0> <1>{{label}}</1> อย่างถาวรจากถังขยะ คุณแน่ใจหรือไม่?',\n    aboutToRestore: 'คุณกำลังจะกู้คืน {{label}} <1>{{title}}</1> คุณแน่ใจไหม?',\n    aboutToRestoreAsDraft: 'คุณกำลังจะกู้คืน {{label}} <1>{{title}}</1> เป็นร่างฉบับ คุณแน่ใจไหม?',\n    aboutToRestoreAsDraftCount: 'คุณกำลังจะกู้คืน {{count}} {{label}} เป็นร่าง',\n    aboutToRestoreCount: 'คุณกำลังจะกู้คืน {{count}} {{label}}',\n    aboutToTrash: 'คุณกำลังจะย้าย {{label}} <1>{{title}}</1> ไปยังถังขยะ คุณแน่ใจไหม?',\n    aboutToTrashCount: 'คุณกำลังจะย้าย {{count}} {{label}} ไปที่ถังขยะ',\n    addBelow: 'เพิ่มด้านล่าง',\n    addFilter: 'เพิ่มการกรอง',\n    adminTheme: 'ธีมผู้ดูแลระบบ',\n    all: 'ทั้งหมด',\n    allCollections: 'คอลเลกชันทั้งหมด',\n    allLocales: 'ทุกสถานที่',\n    and: 'และ',\n    anotherUser: 'ผู้ใช้อื่น',\n    anotherUserTakenOver: 'ผู้ใช้อื่นเข้าครอบครองการแก้ไขเอกสารนี้แล้ว',\n    applyChanges: 'ใช้การเปลี่ยนแปลง',\n    ascending: 'น้อยไปมาก',\n    automatic: 'อัตโนมัติ',\n    backToDashboard: 'กลับไปหน้าแดชบอร์ด',\n    cancel: 'ยกเลิก',\n    changesNotSaved: 'การเปลี่ยนแปลงยังไม่ได้ถูกบันทึก ถ้าคุณออกตอนนี้ สิ่งที่แก้ไขไว้จะหายไป',\n    clear:\n      'ให้เคารพความหมายของข้อความต้นฉบับภายในบริบทของ Payload นี่คือรายการของคำที่มักใช้ใน Payload ที่มีความหมายที่เฉพาะเจาะจงมาก:\\n    - Collection: Collection คือกลุ่มของเอกสารที่มีโครงสร้างและจุดประสงค์ท',\n    clearAll: 'ล้างทั้งหมด',\n    close: 'ปิด',\n    collapse: 'ยุบ',\n    collections: 'Collections',\n    columns: 'คอลัมน์',\n    columnToSort: 'คอลัมน์ที่ต้องการเรียง',\n    confirm: 'ยืนยัน',\n    confirmCopy: 'ยืนยันสำเนา',\n    confirmDeletion: 'ยืนยันการลบ',\n    confirmDuplication: 'ยืนยันการสำเนา',\n    confirmMove: 'ยืนยันการย้าย',\n    confirmReindex: 'ทำการจัดทำดัชนีใหม่ทั้งหมดใน {{collections}}?',\n    confirmReindexAll: 'ทำการจัดทำดัชนีใหม่ทั้งหมดในทุกคอลเลกชัน?',\n    confirmReindexDescription:\n      'การดำเนินการนี้จะลบดัชนีที่มีอยู่และทำการจัดทำดัชนีใหม่ในเอกสารของคอลเลกชัน {{collections}}.',\n    confirmReindexDescriptionAll:\n      'การดำเนินการนี้จะลบดัชนีที่มีอยู่และทำการจัดทำดัชนีใหม่ในเอกสารของทุกคอลเลกชัน.',\n    confirmRestoration: 'ยืนยันการคืนค่าให้ครบถ้วน',\n    copied: 'คัดลอกแล้ว',\n    copy: 'คัดลอก',\n    copyField: 'คัดลอกฟิลด์',\n    copying: 'การคัดลอก',\n    copyRow: 'คัดลอกแถว',\n    copyWarning:\n      'คุณกำลังจะเขียนทับ {{to}} ด้วย {{from}} สำหรับ {{label}} {{title}}. คุณแน่ใจหรือไม่?',\n    create: 'สร้าง',\n    created: 'ถูกสร้างเมื่อ',\n    createdAt: 'สร้างเมื่อ',\n    createNew: 'สร้างใหม่',\n    createNewLabel: 'สร้าง {{label}} ใหม่',\n    creating: 'กำลังสร้าง',\n    creatingNewLabel: 'กำลังสร้าง {{label}} ใหม่',\n    currentlyEditing:\n      'กำลังแก้ไขเอกสารนี้อยู่ในขณะนี้ หากคุณเข้าครอบครอง พวกเขาจะถูกบล็อกจากการแก้ไขต่อไป และอาจสูญเสียการเปลี่ยนแปลงที่ไม่ได้บันทึก',\n    custom: 'ที่ทำขึ้นเฉพาะ',\n    dark: 'มืด',\n    dashboard: 'แดชบอร์ด',\n    delete: 'ลบ',\n    deleted: 'ถูกลบ',\n    deletedAt: 'ถูกลบที่',\n    deletedCountSuccessfully: 'Deleted {{count}} {{label}} successfully.',\n    deletedSuccessfully: 'ลบสำเร็จ',\n    deletePermanently: 'ข้ามถังขยะและลบอย่างถาวร',\n    deleting: 'กำลังลบ...',\n    depth: 'ความลึก',\n    descending: 'มากไปน้อย',\n    deselectAllRows: 'ยกเลิกการเลือกทุกแถว',\n    document: 'เอกสาร',\n    documentIsTrashed: 'ป้ายนี้ {{label}} ถูกทำให้เป็นขยะและอ่านอย่างเดียว',\n    documentLocked: 'เอกสารถูกล็อค',\n    documents: 'เอกสาร',\n    duplicate: 'สำเนา',\n    duplicateWithoutSaving: 'สำเนาโดยไม่บันทึกการแก้ไข',\n    edit: 'แก้ไข',\n    editAll: 'แก้ไขทั้งหมด',\n    editedSince: 'แก้ไขตั้งแต่',\n    editing: 'แก้ไข',\n    editingLabel_many: 'กำลังแก้ไข {{count}} {{label}}',\n    editingLabel_one: 'กำลังแก้ไข {{count}} {{label}}',\n    editingLabel_other: 'กำลังแก้ไข {{count}} {{label}}',\n    editingTakenOver: 'การแก้ไขถูกครอบครอง',\n    editLabel: 'แก้ไข {{label}}',\n    email: 'อีเมล',\n    emailAddress: 'อีเมล',\n    emptyTrash: 'ลบถังขยะ',\n    emptyTrashLabel: 'ลบ {{label}} ที่อยู่ในถังขยะ',\n    enterAValue: 'ระบุค่า',\n    error: 'ข้อผิดพลาด',\n    errors: 'ข้อผิดพลาด',\n    exitLivePreview: 'ออกจากการแสดงตัวอย่างสด',\n    export: 'ส่งออก',\n    fallbackToDefaultLocale: 'สำรองไปยังตำแหน่งที่ตั้งเริ่มต้น',\n    false: 'เท็จ',\n    filter: 'กรอง',\n    filters: 'กรอง',\n    filterWhere: 'กรอง {{label}} เฉพาะ',\n    globals: 'Globals',\n    goBack: 'กลับไป',\n    groupByLabel: 'จัดกลุ่มตาม {{label}}',\n    import: 'นำเข้า',\n    isEditing: 'กำลังแก้ไข',\n    item: 'รายการ',\n    items: 'รายการ',\n    language: 'ภาษา',\n    lastModified: 'แก้ไขล่าสุดเมื่อ',\n    leaveAnyway: 'ออกจากหน้านี้',\n    leaveWithoutSaving: 'ออกโดยไม่บันทึก',\n    light: 'สว่าง',\n    livePreview: 'แสดงตัวอย่าง',\n    loading: 'กำลังโหลด',\n    locale: 'ตำแหน่งที่ตั้ง',\n    locales: 'ภาษา',\n    menu: 'เมนู',\n    moreOptions: 'ตัวเลือกเพิ่มเติม',\n    move: 'ย้าย',\n    moveConfirm: 'คุณกำลังจะย้าย {{count}} {{label}} ไปที่ <1>{{destination}}</1> แน่ใจไหม?',\n    moveCount: 'ย้าย {{count}} {{label}}',\n    moveDown: 'ขยับขึ้น',\n    moveUp: 'ขยับลง',\n    moving: 'การย้ายที่อยู่',\n    movingCount: 'ย้าย {{count}} {{label}}',\n    newPassword: 'รหัสผ่านใหม่',\n    next: 'ถัดไป',\n    no: 'ไม่',\n    noDateSelected: 'ไม่ได้เลือกวันที่',\n    noFiltersSet: 'ไม่มีการกรอง',\n    noLabel: '<ไม่มี {{label}}>',\n    none: 'ไม่มี',\n    noOptions: 'ไม่มีตัวเลือก',\n    noResults:\n      'ไม่พบ {{label}} เนื่องจากยังไม่มี {{label}} หรือไม่มี {{label}} ใดตรงกับการกรองด้านบน',\n    notFound: 'ไม่พบ',\n    nothingFound: 'ไม่พบสิ่งใด',\n    noTrashResults: 'ไม่มี {{label}} ในถังขยะ.',\n    noUpcomingEventsScheduled: 'ไม่มีกิจกรรมที่จะมาถึงถูกกำหนดไว้',\n    noValue: 'ไม่มีค่า',\n    of: 'จาก',\n    only: 'เท่านั้น',\n    open: 'เปิด',\n    or: 'หรือ',\n    order: 'เรียงตาม',\n    overwriteExistingData: 'เขียนทับข้อมูลในฟิลด์ที่มีอยู่แล้ว',\n    pageNotFound: 'ไม่พบหน้าที่ต้องการ',\n    password: 'รหัสผ่าน',\n    pasteField: 'วางฟิลด์',\n    pasteRow: 'วางแถว',\n    payloadSettings: 'การตั้งค่า Payload',\n    permanentlyDelete: 'ลบถาวร',\n    permanentlyDeletedCountSuccessfully: 'ลบ {{label}} {{count}} รายการอย่างถาวรสำเร็จแล้ว',\n    perPage: 'จำนวนต่อหน้า: {{limit}}',\n    previous: 'ก่อนหน้านี้',\n    reindex: 'จัดทำดัชนีใหม่',\n    reindexingAll: 'กำลังทำการจัดทำดัชนีใหม่ทั้งหมดใน {{collections}}.',\n    remove: 'ลบ',\n    rename: 'เปลี่ยนชื่อ',\n    reset: 'รีเซ็ต',\n    resetPreferences: 'รีเซ็ตการตั้งค่า',\n    resetPreferencesDescription: 'การกระทำนี้จะรีเซ็ตการตั้งค่าทั้งหมดของคุณเป็นค่าเริ่มต้น',\n    resettingPreferences: 'กำลังรีเซ็ตการตั้งค่า',\n    restore: 'กู้คืน',\n    restoreAsPublished: 'เรียกคืนเป็นเวอร์ชันที่เผยแพร่',\n    restoredCountSuccessfully: 'ได้ทำการกู้คืน {{count}} {{label}} สำเร็จแล้ว',\n    restoring:\n      'สนับสนุนความหมายของข้อความต้นฉบับในบริบทของ Payload นี่คือรายการของคำที่เกี่ยวข้องกับ Payload ที่มีความหมายเฉพาะเจาะจง:\\n    - Collection: Collection เป็นกลุ่มของเอกสารที่มีโครงสร้างและจุดประสงค์ที่เหมือน',\n    row: 'แถว',\n    rows: 'แถว',\n    save: 'บันทึก',\n    saving: 'กำลังบันทึก...',\n    schedulePublishFor: 'ตั้งเวลาเผยแพร่สำหรับ {{title}}',\n    searchBy: 'ค้นหาด้วย {{label}}',\n    select: 'เลือก',\n    selectAll: 'เลือกทั้งหมด {{count}} {{label}}',\n    selectAllRows: 'เลือกทุกแถว',\n    selectedCount: 'เลือก {{count}} {{label}} แล้ว',\n    selectLabel: 'เลือก {{label}}',\n    selectValue: 'เลือกค่า',\n    showAllLabel: 'แสดง {{label}} ทั้งหมด',\n    sorryNotFound: 'ขออภัย ไม่สามารถทำตามคำขอของคุณได้',\n    sort: 'เรียง',\n    sortByLabelDirection: 'เรียงลำดับตาม {{label}} {{direction}}',\n    stayOnThisPage: 'อยู่หน้านี้ต่อ',\n    submissionSuccessful: 'ส่งสำเร็จ',\n    submit: 'ส่ง',\n    submitting: 'ส่ง...',\n    success: 'ความสำเร็จ',\n    successfullyCreated: 'สร้าง {{label}} สำเร็จ',\n    successfullyDuplicated: 'สำเนา {{label}} สำเร็จ',\n    successfullyReindexed:\n      'จัดทำดัชนีใหม่สำเร็จ {{count}} จาก {{total}} เอกสารจากคอลเลกชัน {{collections}}',\n    takeOver: 'เข้ายึด',\n    thisLanguage: 'ไทย',\n    time: 'เวลา',\n    timezone: 'เขตเวลา',\n    titleDeleted: 'ลบ {{label}} \"{{title}}\" สำเร็จ',\n    titleRestored: '{{label}} \"{{title}}\" ถูกกู้คืนสำเร็จแล้ว.',\n    titleTrashed: '{{label}} \"{{title}}\" ถูกย้ายไปถังขยะ',\n    trash: 'ถังขยะ',\n    trashedCountSuccessfully: '{{count}} {{label}} ถูกย้ายไปยังถังขยะ',\n    true: 'จริง',\n    unauthorized: 'ไม่ได้รับอนุญาต',\n    unsavedChanges: 'คุณมีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก บันทึกหรือทิ้งก่อนที่จะดำเนินการต่อ',\n    unsavedChangesDuplicate: 'คุณมีการแก้ไขที่ยังไม่ถูกบันทึก คุณต้องการทำสำเนาต่อหรือไม่?',\n    untitled: 'ไม่มีชื่อ',\n    upcomingEvents: 'กิจกรรมที่จะถึง',\n    updatedAt: 'แก้ไขเมื่อ',\n    updatedCountSuccessfully: 'อัปเดต {{count}} {{label}} เรียบร้อยแล้ว',\n    updatedLabelSuccessfully: 'อัปเดต {{label}} สำเร็จแล้ว',\n    updatedSuccessfully: 'แก้ไขสำเร็จ',\n    updateForEveryone: 'อัปเดตสำหรับทุกคน',\n    updating: 'กำลังอัปเดต',\n    uploading: 'กำลังอัปโหลด',\n    uploadingBulk: 'อัปโหลด {{current}} จาก {{total}}',\n    user: 'ผู้ใช้',\n    username: 'ชื่อผู้ใช้',\n    users: 'ผู้ใช้',\n    value: 'ค่า',\n    viewing: 'การดู',\n    viewReadOnly: 'ดูในโหมดอ่านอย่างเดียว',\n    welcome: 'ยินดีต้อนรับ',\n    yes: 'ใช่',\n  },\n  localization: {\n    cannotCopySameLocale: 'ไม่สามารถคัดลอกไปยังตำแหน่งที่ตั้งเดียวกัน',\n    copyFrom: 'คัดลอกจาก',\n    copyFromTo: 'คัดลอกจาก {{from}} ไปยัง {{to}}',\n    copyTo: 'คัดลอกไปที่',\n    copyToLocale: 'คัดลอกไปยังสถานที่',\n    localeToPublish: 'เผยแพร่ในสถานที่',\n    selectLocaleToCopy: 'เลือกสถานที่ท้องถิ่นเพื่อคัดลอก',\n  },\n  operators: {\n    contains: 'มี',\n    equals: 'เท่ากับ',\n    exists: 'มีอยู่',\n    intersects: 'ตัดกัน',\n    isGreaterThan: 'มากกว่า',\n    isGreaterThanOrEqualTo: 'มากกว่าหรือเท่ากับ',\n    isIn: 'อยู่ใน',\n    isLessThan: 'น้อยกว่า',\n    isLessThanOrEqualTo: 'น้อยกว่าหรือเท่ากับ',\n    isLike: 'เหมือน',\n    isNotEqualTo: 'ไม่เท่ากับ',\n    isNotIn: 'ไม่ได้อยู่ใน',\n    isNotLike: 'ไม่เหมือน',\n    near: 'ใกล้',\n    within: 'ภายใน',\n  },\n  upload: {\n    addFile: 'เพิ่มไฟล์',\n    addFiles: 'เพิ่มไฟล์',\n    bulkUpload: 'อัปโหลดจำนวนมาก',\n    crop: 'พืชผล',\n    cropToolDescription: 'ลากมุมของพื้นที่ที่เลือก, วาดพื้นที่ใหม่หรือปรับค่าด้านล่าง',\n    download: 'ดาวน์โหลด',\n    dragAndDrop: 'ลากและวางไฟล์',\n    dragAndDropHere: 'หรือลากและวางไฟล์ที่นี่',\n    editImage: 'แก้ไขรูปภาพ',\n    fileName: 'ชื่อไฟล์',\n    fileSize: 'ขนาดไฟล์',\n    filesToUpload: 'อัปโหลดไฟล์',\n    fileToUpload: 'อัปโหลดไฟล์',\n    focalPoint: 'จุดสนใจ',\n    focalPointDescription: 'ลากจุดโฟกัสตรงบนภาพตัวอย่างหรือปรับค่าที่อยู่ด้านล่าง',\n    height: 'ความสูง',\n    lessInfo: 'ซ่อนข้อมูล',\n    moreInfo: 'แสดงข้อมูล',\n    noFile: 'ไม่มีไฟล์',\n    pasteURL: 'วาง URL',\n    previewSizes: 'ขนาดตัวอย่าง',\n    selectCollectionToBrowse: 'เลือก Collection ที่ต้องการค้นหา',\n    selectFile: 'เลือกไฟล์',\n    setCropArea: 'ตั้งค่าพื้นที่การครอบตัด',\n    setFocalPoint: 'ตั้งจุดโฟกัส',\n    sizes: 'ขนาด',\n    sizesFor: 'ขนาดสำหรับ {{label}}',\n    width: 'ความกว้าง',\n  },\n  validation: {\n    emailAddress: 'กรุณาระบุอีเมลที่ถูกต้อง',\n    enterNumber: 'กรุณาระบุตัวเลขที่ถูกต้อง',\n    fieldHasNo: 'ช่องนี้ไม่มี {{label}}',\n    greaterThanMax: '{{value}} มากกว่าค่าสูงสุดที่อนุญาตของ {{label}} ซึ่งคือ {{max}}.',\n    invalidInput: 'ข้อมูลไม่ถูกต้อง',\n    invalidSelection: 'ค่าที่เลือกไม่ถูกต้อง',\n    invalidSelections: 'ค่าที่เลือกไม่ถูกต้องดังนี้:',\n    lessThanMin: '{{value}} น้อยกว่าค่าต่ำสุดที่อนุญาตของ {{label}} ซึ่งคือ {{min}}.',\n    limitReached: 'ถึงขีดจำกัดแล้ว, สามารถเพิ่มไอเทมได้เพียง {{max}} ไอเทมเท่านั้น',\n    longerThanMin: 'ค่าต้องมีความยาวมากกว่า {{minLength}} ตัวอักษร',\n    notValidDate: 'วันที่ \"{{value}}\" ไม่ถูกต้อง',\n    required: 'จำเป็นต้องระบุค่า',\n    requiresAtLeast: 'ต้องมีอย่างน้อย {{count}} {{label}}',\n    requiresNoMoreThan: 'ห้ามมีเกิน {{count}} {{label}}',\n    requiresTwoNumbers: 'ต้องมีตัวเลข 2 ค่า',\n    shorterThanMax: 'ค่าต้องมีความยาวน้อยกว่า {{maxLength}} ตัวอักษร',\n    timezoneRequired: 'ต้องการเขตเวลา',\n    trueOrFalse: 'เป็นได้แค่ \"ใช่\" หรือ \"ไม่ใช่\"',\n    username: 'กรุณาใส่ชื่อผู้ใช้ที่ถูกต้อง สามารถมีตัวอักษร ตัวเลข ขีดกลาง จุด และขีดล่าง',\n    validUploadID: 'ไม่ใช่ ID ของการอัปโหลดที่ถูกต้อง',\n  },\n  version: {\n    type: 'ประเภท',\n    aboutToPublishSelection: 'คุณกำลังจะเผยแพร่ {{label}} ทั้งหมดในส่วนที่เลือก คุณแน่ใจไหม?',\n    aboutToRestore:\n      'คุณกำลังจะคืนค่าเอกสาร {{label}} นี้กลับไปอยู่ในเวอร์ชันเมื่อวันที่ {{versionDate}}',\n    aboutToRestoreGlobal:\n      'คุณกำลังจะคืนค่า global {{label}} กลับไปอยู่ในเวอร์ชันเมื่อวันที่ {{versionDate}}.',\n    aboutToRevertToPublished:\n      'คุณกำลังจะย้อนการเปลี่ยนแปลงของเอกสารนี้ไปยังเวอร์ชันที่เผยแพร่อยู่ คุณต้องการดำเนินการต่อหรือไม่?',\n    aboutToUnpublish: 'คุณกำลังจะยกเลิกเผยแพร่เอกสารนี้ คุณต้องการดำเนินการต่อหรือไม่?',\n    aboutToUnpublishSelection: 'คุณกำลังจะเลิกเผยแพร่ {{label}} ทั้งหมดในส่วนที่เลือก คุณแน่ใจไหม?',\n    autosave: 'บันทึกอัตโนมัติ',\n    autosavedSuccessfully: 'บันทึกอัตโนมัติสำเร็จ',\n    autosavedVersion: 'เวอร์ชันบันทึกอัตโนมัติ',\n    changed: 'มีการแก้ไข',\n    changedFieldsCount_one: '{{count}} เปลี่ยนฟิลด์',\n    changedFieldsCount_other: '{{count}} ฟิลด์ที่มีการเปลี่ยนแปลง',\n    compareVersion: 'เปรียบเทียบเวอร์ชันกับ:',\n    compareVersions: 'เปรียบเทียบรุ่น',\n    comparingAgainst: 'เปรียบเทียบกับ',\n    confirmPublish: 'ยืนยันการเผยแพร่',\n    confirmRevertToSaved: 'ยืนยันย้อนการแก้ไข',\n    confirmUnpublish: 'ยืนยันการยกเลิกการเผยแพร่',\n    confirmVersionRestoration: 'ยืนยันการกู้คืนเวอร์ชัน',\n    currentDocumentStatus: 'เอกสารปัจจุบัน',\n    currentDraft: 'ร่างปัจจุบัน',\n    currentlyPublished: 'ปัจจุบันได้รับการเผยแพร่',\n    currentlyViewing: 'กำลังดูอยู่ในขณะนี้',\n    currentPublishedVersion: 'เวอร์ชันที่เผยแพร่ในปัจจุบัน',\n    draft: 'ฉบับร่าง',\n    draftSavedSuccessfully: 'บันทึกร่างสำเร็จ',\n    lastSavedAgo: 'บันทึกครั้งล่าสุด {{distance}} ที่ผ่านมา',\n    modifiedOnly: 'แก้ไขเท่านั้น',\n    moreVersions: 'เพิ่มเวอร์ชั่น...',\n    noFurtherVersionsFound: 'ไม่พบเวอร์ชันอื่น ๆ',\n    noRowsFound: 'ไม่พบ {{label}}',\n    noRowsSelected: 'ไม่มี {{label}} ที่ถูกเลือก',\n    preview: 'ตัวอย่าง',\n    previouslyDraft: 'ก่อนหน้านี้เป็นร่าง',\n    previouslyPublished: 'เผยแพร่ก่อนหน้านี้',\n    previousVersion: 'เวอร์ชันก่อนหน้านี้',\n    problemRestoringVersion: 'เกิดปัญหาระหว่างการกู้คืนเวอร์ชันนี้',\n    publish: 'เผยแพร่',\n    publishAllLocales: 'เผยแพร่ทุกสถานที่',\n    publishChanges: 'เผยแพร่การแก้ไข',\n    published: 'เผยแพร่แล้ว',\n    publishIn: 'เผยแพร่ใน {{locale}}',\n    publishing: 'การเผยแพร่',\n    restoreAsDraft: 'เรียกคืนเป็นร่าง',\n    restoredSuccessfully: 'กู้คืนเวอร์ชันสำเร็จ',\n    restoreThisVersion: 'กู้คืนเวอร์ชันนี้',\n    restoring: 'กำลังกู้คืน...',\n    reverting: 'กำลังย้อน...',\n    revertToPublished: 'ย้อนกลับไปเวอร์ชันที่เผยแพร่อยู่',\n    saveDraft: 'บันทึกร่าง',\n    scheduledSuccessfully: 'ได้ทำการจัดตารางเรียบร้อยแล้ว',\n    schedulePublish: 'ตั้งเวลาเผยแพร่',\n    selectLocales: 'เลือกภาษาที่ต้องการแสดง',\n    selectVersionToCompare: 'เลือกเวอร์ชันที่ต้องการเปรียบเทียบ',\n    showingVersionsFor: 'กำลังแสดงเวอร์ชันของ:',\n    showLocales: 'แสดงภาษา:',\n    specificVersion: 'เวอร์ชันเฉพาะ',\n    status: 'สถานะ',\n    unpublish: 'หยุดเผยแพร่',\n    unpublishing: 'กำลังหยุดการเผยแพร่...',\n    version: 'เวอร์ชัน',\n    versionAgo: '{{distance}} ที่แล้ว',\n    versionCount_many: 'พบ {{count}} เวอร์ชัน',\n    versionCount_none: 'ไม่พบเวอร์ชันอื่น',\n    versionCount_one: 'พบ {{count}} เวอร์ชัน',\n    versionCount_other: 'พบ {{count}} เวอร์ชัน',\n    versionCreatedOn: '{{version}} ถูกสร้างเมื่อ:',\n    versionID: 'ID ของเวอร์ชัน',\n    versions: 'เวอร์ชัน',\n    viewingVersion: 'กำลังดูเวอร์ชันของ {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'กำลังดูเวอร์ชันของ global {{entityLabel}}',\n    viewingVersions: 'กำลังดูเวอร์ชันของ {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'กำลังดูเวอร์ชันของ global {{entityLabel}}',\n  },\n}\n\nexport const th: Language = {\n  dateFNSKey: 'th',\n  translations: thTranslations,\n}\n"], "names": ["thTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "th", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eAAe;QACfC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCAAmC;QACnCC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,+BACE;QACFC,gBAAgB;QAChBC,uBAAuB;QACvBC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OACE;QACFC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBAAqB;QACrBC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UAAU;QACVga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}