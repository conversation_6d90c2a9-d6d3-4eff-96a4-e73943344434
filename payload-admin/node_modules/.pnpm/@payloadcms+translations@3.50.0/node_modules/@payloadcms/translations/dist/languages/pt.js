export const ptTranslations = {
    authentication: {
        account: 'Conta',
        accountOfCurrentUser: 'Conta do usuário ativo',
        accountVerified: 'Conta verificada com sucesso.',
        alreadyActivated: 'Conta já ativada',
        alreadyLoggedIn: 'Login já realizado',
        apiKey: 'Chave da <PERSON>',
        authenticated: 'Autenticado',
        backToLogin: 'Voltar para login',
        beginCreateFirstUser: 'Para começar, crie seu primeiro usuário.',
        changePassword: 'Mudar senha',
        checkYourEmailForPasswordReset: 'Se o endereço de email estiver associado a uma conta, você receberá instruções para redefinir sua senha em breve. Por favor, verifique sua pasta de spam ou lixo eletrônico se você não vir o email na sua caixa de entrada.',
        confirmGeneration: 'Confirmar Gera<PERSON>',
        confirmPassword: 'Confirmar <PERSON><PERSON>',
        createFirstUser: 'Criar primeiro usuário',
        emailNotValid: 'O email fornecido não é válido',
        emailOrUsername: 'Email ou Nome de Usuário',
        emailSent: 'Email Enviado',
        emailVerified: 'Email verificado com sucesso.',
        enableAPIKey: 'Habilitar Chave API',
        failedToUnlock: 'Falha ao desbloquear',
        forceUnlock: 'Forçar Desbloqueio',
        forgotPassword: 'Esqueci a senha',
        forgotPasswordEmailInstructions: 'Por favor, preencha seu email abaixo. Você receberá um email com instruções para gerar uma nova senha',
        forgotPasswordQuestion: 'Esqueceu a senha?',
        forgotPasswordUsernameInstructions: 'Digite seu nome de usuário abaixo. Instruções sobre como redefinir sua senha serão enviadas para o endereço de e-mail associado ao seu nome de usuário.',
        generate: 'Gerar',
        generateNewAPIKey: 'Gerar nova chave API',
        generatingNewAPIKeyWillInvalidate: 'Gerar uma nova chave API <1>invalidará</1> a chave anterior. Você tem certeza que deseja prosseguir?',
        lockUntil: 'Bloquear Até',
        logBackIn: 'Fazer login novamente',
        loggedIn: 'Para fazer login como outro usuário, você deve fazer o <0>log out</0> antes.',
        loggedInChangePassword: 'Para mudar a sua senha, acesse a sua <0>conta</0> e edite sua senha lá.',
        loggedOutInactivity: 'Você foi desconectado devido a inatividade.',
        loggedOutSuccessfully: 'Log out efetuado com sucesso.',
        loggingOut: 'Saindo...',
        login: 'Login',
        loginAttempts: 'Tentativas de Login',
        loginUser: 'Iniciar sessão',
        loginWithAnotherUser: 'Para fazer login como outro usuário, você deve fazer o <0>log out</0> antes.',
        logOut: 'Log out',
        logout: 'Logout',
        logoutSuccessful: 'Logout bem sucedido.',
        logoutUser: 'Encerrar sessão',
        newAccountCreated: 'Uma nova conta acaba de ser criada para que você possa acessar <a href="{{serverURL}}">{{serverURL}}</a> Por favor, clique no link a seguir ou cole a URL abaixo no seu navegador para verificar seu email: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Após a verificação de email, você será capaz de fazer o login.',
        newAPIKeyGenerated: 'Nova Chave API Gerada.',
        newPassword: 'Nova Senha',
        passed: 'Autenticação Aprovada',
        passwordResetSuccessfully: 'Redefinição de senha realizada com sucesso.',
        resetPassword: 'Redefinir Senha',
        resetPasswordExpiration: 'Tempo Limite para Redefinição de Senha',
        resetPasswordToken: 'Token para Redefinição de Senha',
        resetYourPassword: 'Redefinir Sua Senha',
        stayLoggedIn: 'Manter sessão ativa',
        successfullyRegisteredFirstUser: 'Primeiro usuário registrado com sucesso.',
        successfullyUnlocked: 'Desbloqueado com sucesso',
        tokenRefreshSuccessful: 'Atualização do token bem-sucedida.',
        unableToVerify: 'Não foi possível verificar',
        username: 'Nome de usuário',
        usernameNotValid: 'O nome de usuário fornecido não é válido',
        verified: 'Verificado',
        verifiedSuccessfully: 'Verificado com Sucesso',
        verify: 'Verificar',
        verifyUser: 'Verificar Usuário',
        verifyYourEmail: 'Verifique seu email',
        youAreInactive: 'Você não está ativo há algum tempo e sua sessão será automaticamente finalizada em breve, para sua própria segurança. Você gostaria de manter a sessão ativa?',
        youAreReceivingResetPassword: 'Você está recebendo essa mensagem porque você (ou outra pessoa) requisitou a redefinição de senha da sua conta. Por favor, clique no link a seguir ou cole no seu navegador para completar o processo:',
        youDidNotRequestPassword: 'Se você não fez essa requisição, por favor ignore esse email e sua senha permanecerá igual.'
    },
    error: {
        accountAlreadyActivated: 'Essa conta já foi ativada.',
        autosaving: 'Ocorreu um problema ao salvar automaticamente esse documento.',
        correctInvalidFields: 'Por favor, corrija os campos inválidos.',
        deletingFile: 'Ocorreu um erro ao excluir o arquivo.',
        deletingTitle: 'Ocorreu um erro ao excluir {{title}}. Por favor, verifique sua conexão e tente novamente.',
        documentNotFound: 'O documento com o ID {{id}} não pôde ser encontrado. Ele pode ter sido deletado ou nunca ter existido, ou você pode não ter acesso a ele.',
        emailOrPasswordIncorrect: 'O email ou senha fornecido está incorreto.',
        followingFieldsInvalid_one: 'O campo a seguir está inválido:',
        followingFieldsInvalid_other: 'Os campos a seguir estão inválidos:',
        incorrectCollection: 'Coleção Incorreta',
        insufficientClipboardPermissions: 'Acesso à área de transferência negado. Verifique suas permissões da área de transferência.',
        invalidClipboardData: 'Dados inválidos na área de transferência.',
        invalidFileType: 'Tipo de arquivo inválido',
        invalidFileTypeValue: 'Tipo de arquivo inválido: {{value}}',
        invalidRequestArgs: 'Argumentos inválidos passados na solicitação: {{args}}',
        loadingDocument: 'Ocorreu um problema ao carregar o documento com ID {{id}}.',
        localesNotSaved_one: 'A seguinte configuração regional não pôde ser salva:',
        localesNotSaved_other: 'As seguintes configurações regionais não puderam ser salvas:',
        logoutFailed: 'Falha ao sair.',
        missingEmail: 'Email ausente.',
        missingIDOfDocument: 'ID do documento a ser atualizado ausente.',
        missingIDOfVersion: 'ID da versão ausente.',
        missingRequiredData: 'Dados requeridos ausentes.',
        noFilesUploaded: 'Nenhum arquivo foi carregado.',
        noMatchedField: 'Não foi encontrado nenhum campo correspondente a "{{label}}"',
        notAllowedToAccessPage: 'Você não tem permissão para acessar essa página.',
        notAllowedToPerformAction: 'Você não tem permissão para realizar essa ação.',
        notFound: 'O recurso requisitado não foi encontrado.',
        noUser: 'Nenhum Usuário',
        previewing: 'Ocorreu um problema ao visualizar esse documento.',
        problemUploadingFile: 'Ocorreu um problema ao carregar o arquivo.',
        restoringTitle: 'Ocorreu um erro ao restaurar {{title}}. Por favor, verifique sua conexão e tente novamente.',
        tokenInvalidOrExpired: 'Token expirado ou inválido.',
        tokenNotProvided: 'Token não fornecido.',
        unableToCopy: 'Não é possível copiar.',
        unableToDeleteCount: 'Não é possível excluir {{count}} de {{total}} {{label}}.',
        unableToReindexCollection: 'Erro ao reindexar a coleção {{collection}}. Operação abortada.',
        unableToUpdateCount: 'Não foi possível atualizar {{count}} de {{total}} {{label}}.',
        unauthorized: 'Não autorizado. Você deve estar logado para fazer essa requisição',
        unauthorizedAdmin: 'Não autorizado, esse usuário não tem acesso ao painel de administração.',
        unknown: 'Ocorreu um erro desconhecido.',
        unPublishingDocument: 'Ocorreu um problema ao despublicar esse documento',
        unspecific: 'Ocorreu um erro.',
        unverifiedEmail: 'Por favor, verifique seu e-mail antes de fazer login.',
        userEmailAlreadyRegistered: 'Um usuário com o email fornecido já está registrado.',
        userLocked: 'Esse usuário está bloqueado devido a muitas tentativas de login malsucedidas.',
        usernameAlreadyRegistered: 'Um usuário com o nome de usuário fornecido já está registrado.',
        usernameOrPasswordIncorrect: 'O nome de usuário ou senha fornecidos estão incorretos.',
        valueMustBeUnique: 'Valor deve ser único',
        verificationTokenInvalid: 'Token de verificação inválido.'
    },
    fields: {
        addLabel: 'Adicionar {{label}}',
        addLink: 'Adicionar Link',
        addNew: 'Adicionar novo',
        addNewLabel: 'Adicionar novo {{label}}',
        addRelationship: 'Adicionar Relação',
        addUpload: 'Adicionar Upload',
        block: 'bloco',
        blocks: 'blocos',
        blockType: 'Tipo de bloco',
        chooseBetweenCustomTextOrDocument: 'Escolha entre inserir um URL de texto personalizado ou vincular a outro documento.',
        chooseDocumentToLink: 'Escolha um documento para vincular',
        chooseFromExisting: 'Escolher entre os existentes',
        chooseLabel: 'Escolher {{label}}',
        collapseAll: 'Recolher todos',
        customURL: 'URL personalizado',
        editLabelData: 'Editar dados de {{label}}',
        editLink: 'Editar Link',
        editRelationship: 'Editar Relacionamento',
        enterURL: 'Insira um URL',
        internalLink: 'Link Interno',
        itemsAndMore: '{{items}} e mais {{count}}',
        labelRelationship: 'Relacionado a {{label}}',
        latitude: 'Latitude',
        linkedTo: 'Ligado a <0>{{label}}</0>',
        linkType: 'Tipo de link',
        longitude: 'Longitude',
        newLabel: 'Novo(a) {{label}}',
        openInNewTab: 'Abrir em nova aba',
        passwordsDoNotMatch: 'Senhas não coincidem.',
        relatedDocument: 'Documento Relacionado',
        relationTo: 'Relacionado a',
        removeRelationship: 'Remover Relacionamento',
        removeUpload: 'Remover Upload',
        saveChanges: 'Salvar alterações',
        searchForBlock: 'Procurar bloco',
        selectExistingLabel: 'Selecionar {{label}} existente',
        selectFieldsToEdit: 'Selecione os campos para editar',
        showAll: 'Mostrar Tudo',
        swapRelationship: 'Relação de Troca',
        swapUpload: 'Substituir Upload',
        textToDisplay: 'Texto a ser exibido',
        toggleBlock: 'Alternar bloco',
        uploadNewLabel: 'Carregar novo(a) {{label}}'
    },
    folder: {
        browseByFolder: 'Navegar por Pasta',
        byFolder: 'Por Pasta',
        deleteFolder: 'Apagar Pasta',
        folderName: 'Nome da Pasta',
        folders: 'Pastas',
        folderTypeDescription: 'Selecione qual tipo de documentos da coleção devem ser permitidos nesta pasta.',
        itemHasBeenMoved: '{{title}} foi movido para {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} foi movido para a pasta raiz',
        itemsMovedToFolder: '{{title}} movido para {{folderName}}',
        itemsMovedToRoot: '{{title}} foi movido para a pasta raiz',
        moveFolder: 'Mover Pasta',
        moveItemsToFolderConfirmation: 'Você está prestes a mover <1>{{count}} {{label}}</1> para <2>{{toFolder}}</2>. Tem certeza?',
        moveItemsToRootConfirmation: 'Você está prestes a mover <1>{{count}} {{label}}</1> para a pasta raiz. Tem certeza?',
        moveItemToFolderConfirmation: 'Você está prestes a mover <1>{{title}}</1> para <2>{{toFolder}}</2>. Tem certeza?',
        moveItemToRootConfirmation: 'Você está prestes a mover <1>{{title}}</1> para a pasta raiz. Tem certeza disso?',
        movingFromFolder: 'Movendo {{title}} de {{fromFolder}}',
        newFolder: 'Nova Pasta',
        noFolder: 'Sem Pasta',
        renameFolder: 'Renomear Pasta',
        searchByNameInFolder: 'Pesquisar por Nome em {{folderName}}',
        selectFolderForItem: 'Selecione a pasta para {{title}}'
    },
    general: {
        name: 'Nome',
        aboutToDelete: 'Você está prestes a excluir o/a {{label}} <1>{{title}}</1>. Tem certeza?',
        aboutToDeleteCount_many: 'Você está prestes a deletar {{count}} {{label}}',
        aboutToDeleteCount_one: 'Você está prestes a deletar {{count}} {{label}}',
        aboutToDeleteCount_other: 'Você está prestes a deletar {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Está prestes a apagar permanentemente o {{label}} <1>{{title}}</1>. Tem certeza?',
        aboutToPermanentlyDeleteTrash: 'Você está prestes a excluir permanentemente <0>{{count}}</0> <1>{{label}}</1> da lixeira. Você tem certeza?',
        aboutToRestore: 'Está prestes a restaurar o {{label}} <1>{{title}}</1>. Tem certeza?',
        aboutToRestoreAsDraft: 'Está prestes a restaurar o {{label}} <1>{{title}}</1> como um rascunho. Tem certeza?',
        aboutToRestoreAsDraftCount: 'Está prestes a restaurar {{count}} {{label}} como rascunho',
        aboutToRestoreCount: 'Você está prestes a restaurar {{count}} {{label}}',
        aboutToTrash: 'Você está prestes a mover o {{label}} <1>{{title}}</1> para a lixeira. Tem certeza?',
        aboutToTrashCount: 'Estás prestes a mover {{count}} {{label}} para o lixo',
        addBelow: 'Adicionar abaixo',
        addFilter: 'Adicionar Filtro',
        adminTheme: 'Tema do Admin',
        all: 'Todos',
        allCollections: 'Todas as Coleções',
        allLocales: 'Todos os locais',
        and: 'E',
        anotherUser: 'Outro usuário',
        anotherUserTakenOver: 'Outro usuário assumiu a edição deste documento.',
        applyChanges: 'Aplicar alterações',
        ascending: 'Ascendente',
        automatic: 'Automático',
        backToDashboard: 'Voltar para Painel de Controle',
        cancel: 'Cancelar',
        changesNotSaved: 'Suas alterações não foram salvas. Se você sair agora, essas alterações serão perdidas.',
        clear: 'Claro',
        clearAll: 'Limpar Tudo',
        close: 'Fechar',
        collapse: 'Recolher',
        collections: 'Coleções',
        columns: 'Colunas',
        columnToSort: 'Coluna para Ordenar',
        confirm: 'Confirmar',
        confirmCopy: 'Confirme cópia',
        confirmDeletion: 'Confirmar exclusão',
        confirmDuplication: 'Confirmar duplicação',
        confirmMove: 'Confirme a movimentação',
        confirmReindex: 'Reindexar todas as {{collections}}?',
        confirmReindexAll: 'Reindexar todas as coleções?',
        confirmReindexDescription: 'Isso removerá os índices existentes e reindexará os documentos nas coleções {{collections}}.',
        confirmReindexDescriptionAll: 'Isso removerá os índices existentes e reindexará os documentos em todas as coleções.',
        confirmRestoration: 'Confirme a restauração',
        copied: 'Copiado',
        copy: 'Copiar',
        copyField: 'Copiar campo',
        copying: 'Copiando',
        copyRow: 'Copiar linha',
        copyWarning: 'Você está prestes a sobrescrever {{to}} com {{from}} para {{label}} {{title}}. Tem certeza?',
        create: 'Criar',
        created: 'Criado',
        createdAt: 'Criado Em',
        createNew: 'Criar Novo',
        createNewLabel: 'Criar novo(a) {{label}}',
        creating: 'Criando',
        creatingNewLabel: 'Criando novo(a) {{label}}',
        currentlyEditing: 'está editando este documento no momento. Se você assumir, eles serão impedidos de continuar editando e poderão perder alterações não salvas.',
        custom: 'Personalizado',
        dark: 'Escuro',
        dashboard: 'Painel de Controle',
        delete: 'Excluir',
        deleted: 'Excluído',
        deletedAt: 'Excluído Em',
        deletedCountSuccessfully: 'Excluído {{count}} {{label}} com sucesso.',
        deletedSuccessfully: 'Apagado com sucesso.',
        deletePermanently: 'Pular lixeira e excluir permanentemente',
        deleting: 'Excluindo...',
        depth: 'Profundidade',
        descending: 'Decrescente',
        deselectAllRows: 'Desmarcar todas as linhas',
        document: 'Documento',
        documentIsTrashed: 'Este {{label}} está na lixeira e é somente para leitura.',
        documentLocked: 'Documento bloqueado',
        documents: 'Documentos',
        duplicate: 'Duplicar',
        duplicateWithoutSaving: 'Duplicar sem salvar alterações',
        edit: 'Editar',
        editAll: 'Editar todos',
        editedSince: 'Editado desde',
        editing: 'Editando',
        editingLabel_many: 'Editando {{count}} {{label}}',
        editingLabel_one: 'Editando {{count}} {{label}}',
        editingLabel_other: 'Editando {{count}} {{label}}',
        editingTakenOver: 'Edição assumida',
        editLabel: 'Editar {{label}}',
        email: 'Email',
        emailAddress: 'Endereço de Email',
        emptyTrash: 'Esvaziar lixo',
        emptyTrashLabel: 'Esvazie o lixo {{label}}',
        enterAValue: 'Insira um valor',
        error: 'Erro',
        errors: 'Erros',
        exitLivePreview: 'Sair da Visualização ao Vivo',
        export: 'Exportação',
        fallbackToDefaultLocale: 'Recuo para o local padrão',
        false: 'Falso',
        filter: 'Filtro',
        filters: 'Filtros',
        filterWhere: 'Filtrar {{label}} em que',
        globals: 'Globais',
        goBack: 'Voltar',
        groupByLabel: 'Agrupar por {{label}}',
        import: 'Importar',
        isEditing: 'está editando',
        item: 'item',
        items: 'itens',
        language: 'Idioma',
        lastModified: 'Última modificação',
        leaveAnyway: 'Sair mesmo assim',
        leaveWithoutSaving: 'Sair sem salvar',
        light: 'Claro',
        livePreview: 'Pré-visualização',
        loading: 'Carregando',
        locale: 'Local',
        locales: 'Localizações',
        menu: 'Cardápio',
        moreOptions: 'Mais opções',
        move: 'Mova',
        moveConfirm: 'Você está prestes a mover {{count}} {{label}} para <1>{{destination}}</1>. Tem certeza?',
        moveCount: 'Mova {{count}} {{label}}',
        moveDown: 'Mover para Baixo',
        moveUp: 'Mover para Cima',
        moving: 'Mudando',
        movingCount: 'Movendo {{count}} {{label}}',
        newPassword: 'Nova Senha',
        next: 'Próximo',
        no: 'Não',
        noDateSelected: 'Nenhuma data selecionada',
        noFiltersSet: 'Nenhum filtro definido',
        noLabel: '<Nenhum(a) {{label}}>',
        none: 'Nenhum',
        noOptions: 'Sem opções',
        noResults: 'Nenhum {{label}} encontrado. Ou nenhum(a) {{label}} existe ainda, ou nenhum(a) corresponde aos filtros que você especificou acima.',
        notFound: 'Não Encontrado',
        nothingFound: 'Nada encontrado',
        noTrashResults: 'Não há {{label}} no lixo.',
        noUpcomingEventsScheduled: 'Não há eventos futuros agendados.',
        noValue: 'Nenhum valor',
        of: 'de',
        only: 'Apenas',
        open: 'Abrir',
        or: 'Ou',
        order: 'Ordem',
        overwriteExistingData: 'Sobrescrever dados de campo existentes',
        pageNotFound: 'Página não encontrada',
        password: 'Senha',
        pasteField: 'Colar campo',
        pasteRow: 'Colar linha',
        payloadSettings: 'Configurações do Payload',
        permanentlyDelete: 'Excluir Permanentemente',
        permanentlyDeletedCountSuccessfully: 'Apagou permanentemente {{count}} {{label}} com sucesso.',
        perPage: 'Itens por Página: {{limit}}',
        previous: 'Anterior',
        reindex: 'Reindexar',
        reindexingAll: 'Reindexando todas as {{collections}}.',
        remove: 'Remover',
        rename: 'Renomear',
        reset: 'Redefinir',
        resetPreferences: 'Redefinir preferências',
        resetPreferencesDescription: 'Isso redefinirá todas as suas preferências para as configurações padrão.',
        resettingPreferences: 'Redefinindo preferências.',
        restore: 'Restaurar',
        restoreAsPublished: 'Restaurar como versão publicada',
        restoredCountSuccessfully: 'Restaurado {{count}} {{label}} com sucesso.',
        restoring: 'Respeite o significado do texto original dentro do contexto do Payload. Aqui está uma lista de termos comuns do Payload que possuem significados muito específicos:\n    - Collection: Uma coleção é um grupo de documentos que compartilham uma estrutura e propósito comuns. As coleções são usadas para organizar e gerenciar conteúdo no Payload.\n    - Field: Um campo é uma peça específica de dados dentro de um documento em uma coleção. Os campos definem a estrutura e o tipo de dados que podem ser armazenados em um documento.\n    - Document: Um documento é um registro individual dentro de uma coleção. Ele contém dados estruturados de acordo',
        row: 'Linha',
        rows: 'Linhas',
        save: 'Salvar',
        saving: 'Salvando...',
        schedulePublishFor: 'Agendar publicação para {{title}}',
        searchBy: 'Buscar por {{label}}',
        select: 'Selecionar',
        selectAll: 'Selecione tudo {{count}} {{label}}',
        selectAllRows: 'Selecione todas as linhas',
        selectedCount: '{{count}} {{label}} selecionado',
        selectLabel: 'Selecione {{label}}',
        selectValue: 'Selecione um valor',
        showAllLabel: 'Mostre todos {{label}}',
        sorryNotFound: 'Desculpe—não há nada que corresponda à sua requisição.',
        sort: 'Ordenar',
        sortByLabelDirection: 'Ordenar por {{label}} {{direction}}',
        stayOnThisPage: 'Permanecer nessa página',
        submissionSuccessful: 'Envio bem-sucedido.',
        submit: 'Enviar',
        submitting: 'Enviando...',
        success: 'Sucesso',
        successfullyCreated: '{{label}} criado com sucesso.',
        successfullyDuplicated: '{{label}} duplicado com sucesso.',
        successfullyReindexed: 'Reindexação concluída com sucesso de {{count}} de {{total}} documentos das coleções {{collections}}.',
        takeOver: 'Assumir',
        thisLanguage: 'Português',
        time: 'Tempo',
        timezone: 'Fuso horário',
        titleDeleted: '{{label}} {{title}} excluído com sucesso.',
        titleRestored: '{{label}} "{{title}}" restaurado com sucesso.',
        titleTrashed: '{{label}} "{{title}}" movido para a lixeira.',
        trash: 'Lixo',
        trashedCountSuccessfully: '{{count}} {{label}} movido para o lixo.',
        true: 'Verdadeiro',
        unauthorized: 'Não autorizado',
        unsavedChanges: 'Você tem alterações não salvas. Salve ou descarte antes de continuar.',
        unsavedChangesDuplicate: 'Você tem mudanças não salvas. Você gostaria de continuar a duplicar?',
        untitled: 'Sem título',
        upcomingEvents: 'Próximos Eventos',
        updatedAt: 'Atualizado Em',
        updatedCountSuccessfully: 'Atualizado {{count}} {{label}} com sucesso.',
        updatedLabelSuccessfully: '{{label}} atualizado com sucesso.',
        updatedSuccessfully: 'Atualizado com sucesso.',
        updateForEveryone: 'Atualização para todos',
        updating: 'Atualizando',
        uploading: 'Fazendo upload',
        uploadingBulk: 'Carregando {{current}} de {{total}}',
        user: 'usuário',
        username: 'Nome de usuário',
        users: 'usuários',
        value: 'Valor',
        viewing: 'Visualização',
        viewReadOnly: 'Visualizar somente leitura',
        welcome: 'Boas vindas',
        yes: 'Sim'
    },
    localization: {
        cannotCopySameLocale: 'Não é possível copiar para o mesmo local',
        copyFrom: 'Copiar de',
        copyFromTo: 'Copiando de {{from}} para {{to}}',
        copyTo: 'Copiar para',
        copyToLocale: 'Copiar para localidade',
        localeToPublish: 'Local para publicar',
        selectLocaleToCopy: 'Selecione o local para copiar'
    },
    operators: {
        contains: 'contém',
        equals: 'igual',
        exists: 'existe',
        intersects: 'intersecciona',
        isGreaterThan: 'é maior que',
        isGreaterThanOrEqualTo: 'é maior ou igual a',
        isIn: 'está em',
        isLessThan: 'é menor que',
        isLessThanOrEqualTo: 'é menor ou igual a',
        isLike: 'é como',
        isNotEqualTo: 'não é igual a',
        isNotIn: 'não está em',
        isNotLike: 'não é como',
        near: 'perto',
        within: 'dentro'
    },
    upload: {
        addFile: 'Adicionar arquivo',
        addFiles: 'Adicionar Arquivos',
        bulkUpload: 'Upload em Massa',
        crop: 'Cultura',
        cropToolDescription: 'Arraste as bordas da área selecionada, desenhe uma nova área ou ajuste os valores abaixo.',
        download: 'Baixar',
        dragAndDrop: 'Arraste e solte um arquivo',
        dragAndDropHere: 'ou arraste um arquivo aqui',
        editImage: 'Editar imagem',
        fileName: 'Nome do Arquivo',
        fileSize: 'Tamanho do Arquivo',
        filesToUpload: 'Arquivos para Carregar',
        fileToUpload: 'Arquivo para upload',
        focalPoint: 'Ponto Focal',
        focalPointDescription: 'Arraste o ponto focal diretamente na pré-visualização ou ajuste os valores abaixo.',
        height: 'Altura',
        lessInfo: 'Ver menos',
        moreInfo: 'Ver mais',
        noFile: 'Sem arquivo',
        pasteURL: 'Colar URL',
        previewSizes: 'Tamanhos de Pré-visualização',
        selectCollectionToBrowse: 'Selecione uma Coleção para Navegar',
        selectFile: 'Selecione um arquivo',
        setCropArea: 'Definir área de corte',
        setFocalPoint: 'Definir ponto focal',
        sizes: 'Tamanhos',
        sizesFor: 'Tamanhos para {{label}}',
        width: 'Largura'
    },
    validation: {
        emailAddress: 'Por favor, insira um endereço de email válido.',
        enterNumber: 'Por favor, insira um número válido.',
        fieldHasNo: 'Esse campo não contém {{label}}',
        greaterThanMax: '{{value}} é maior que o máximo permitido de {{label}} que é {{max}}.',
        invalidInput: 'Esse campo tem um conteúdo inválido.',
        invalidSelection: 'Esse campo tem uma seleção inválida.',
        invalidSelections: "'Esse campo tem as seguintes seleções inválidas:'",
        lessThanMin: '{{value}} é menor que o mínimo permitido de {{label}} que é {{min}}.',
        limitReached: 'Limite atingido, apenas {{max}} itens podem ser adicionados.',
        longerThanMin: 'Esse valor deve ser maior do que o mínimo de {{minLength}} characters.',
        notValidDate: '"{{value}}" não é uma data válida.',
        required: 'Esse campo é obrigatório.',
        requiresAtLeast: 'Esse campo requer no máximo {{count}} {{label}}.',
        requiresNoMoreThan: 'Esse campo requer pelo menos {{count}} {{label}}.',
        requiresTwoNumbers: 'Esse campo requer dois números.',
        shorterThanMax: 'Esse valor deve ser menor do que o máximo de {{maxLength}} caracteres.',
        timezoneRequired: 'É necessário um fuso horário.',
        trueOrFalse: 'Esse campo pode ser apenas verdadeiro (true) ou falso (false)',
        username: 'Por favor, insira um nome de usuário válido. Pode conter letras, números, hifens, pontos e sublinhados.',
        validUploadID: "'Esse campo não é um ID de upload válido.'"
    },
    version: {
        type: 'Tipo',
        aboutToPublishSelection: 'Você está prestes a publicar todos os {{label}} da seleção. Tem certeza?',
        aboutToRestore: 'Você está prestes a restaurar o documento {{label}} para o estado em que ele se encontrava em {{versionDate}}.',
        aboutToRestoreGlobal: 'Você está prestes a restaurar o Global {{label}} para o estado em que ele se encontrava em {{versionDate}}.',
        aboutToRevertToPublished: 'Você está prestes a reverter as alterações desse documento para seu estado de publicação. Tem certeza?',
        aboutToUnpublish: 'Você está prestes a despublicar esse documento. Tem certeza?',
        aboutToUnpublishSelection: 'Você está prestes a cancelar a publicação de todos os {{label}} na seleção. Tem certeza?',
        autosave: 'Salvamento automático',
        autosavedSuccessfully: 'Salvamento automático com sucesso.',
        autosavedVersion: 'Versão de salvamento automático',
        changed: 'Alterado',
        changedFieldsCount_one: '{{count}} campo alterado',
        changedFieldsCount_other: '{{count}} campos alterados',
        compareVersion: 'Comparar versão com:',
        compareVersions: 'Comparar Versões',
        comparingAgainst: 'Comparando com',
        confirmPublish: 'Confirmar publicação',
        confirmRevertToSaved: 'Confirmar a reversão para o salvo',
        confirmUnpublish: 'Confirmar despublicação',
        confirmVersionRestoration: 'Confirmar Restauração de versão',
        currentDocumentStatus: 'Documento {{docStatus}} atual',
        currentDraft: 'Rascunho Atual',
        currentlyPublished: 'Atualmente Publicado',
        currentlyViewing: 'Atualmente visualizando',
        currentPublishedVersion: 'Versão Publicada Atual',
        draft: 'Rascunho',
        draftSavedSuccessfully: 'Rascunho salvo com sucesso.',
        lastSavedAgo: 'Última gravação há {{distance}}',
        modifiedOnly: 'Modificado apenas',
        moreVersions: 'Mais versões...',
        noFurtherVersionsFound: 'Nenhuma outra versão encontrada',
        noRowsFound: 'Nenhum(a) {{label}} encontrado(a)',
        noRowsSelected: 'Nenhum {{rótulo}} selecionado',
        preview: 'Pré-visualização',
        previouslyDraft: 'Anteriormente um Rascunho',
        previouslyPublished: 'Publicado Anteriormente',
        previousVersion: 'Versão Anterior',
        problemRestoringVersion: 'Ocorreu um problema ao restaurar essa versão',
        publish: 'Publicar',
        publishAllLocales: 'Publicar todas as localidades',
        publishChanges: 'Publicar alterações',
        published: 'Publicado',
        publishIn: 'Publicar em {{locale}}',
        publishing: 'Publicação',
        restoreAsDraft: 'Restaurar como rascunho',
        restoredSuccessfully: 'Restaurado com sucesso.',
        restoreThisVersion: 'Restaurar essa versão',
        restoring: 'Restaurando...',
        reverting: 'Revertendo...',
        revertToPublished: 'Reverter para publicado',
        saveDraft: 'Salvar rascunho',
        scheduledSuccessfully: 'Agendado com sucesso.',
        schedulePublish: 'Agendar Publicação',
        selectLocales: 'Selecione as localizações para exibir',
        selectVersionToCompare: 'Selecione uma versão para comparar',
        showingVersionsFor: 'Mostrando versões para:',
        showLocales: 'Exibir localizações:',
        specificVersion: 'Versão Específica',
        status: 'Status',
        unpublish: 'Despublicar',
        unpublishing: 'Despublicando...',
        version: 'Versão',
        versionAgo: 'há {{distance}}',
        versionCount_many: '{{count}} versões encontradas',
        versionCount_none: 'Nenhuma versão encontrada',
        versionCount_one: '{{count}} versão encontrada',
        versionCount_other: '{{count}} versões encontradas',
        versionCreatedOn: '{{version}} criada em:',
        versionID: 'ID da versão',
        versions: 'Versões',
        viewingVersion: 'Visualizando versão para o/a {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: '`Visualizando versão para o global {{entityLabel}}',
        viewingVersions: 'Visualizando versões para o/a {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: '`Visualizando versões para o global {{entityLabel}}'
    }
};
export const pt = {
    dateFNSKey: 'pt',
    translations: ptTranslations
};

//# sourceMappingURL=pt.js.map