{"version": 3, "sources": ["../../src/languages/bg.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const bgTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Профил',\n    accountOfCurrentUser: 'Профил на текущия потребител',\n    accountVerified: 'Профилът е верифициран успешно.',\n    alreadyActivated: 'Вече активиран',\n    alreadyLoggedIn: 'Вече влязъл',\n    apiKey: 'API ключ',\n    authenticated: 'Удостоверен',\n    backToLogin: 'Обратно към влизане',\n    beginCreateFirstUser: 'За да започнеш, създай първия си потребител',\n    changePassword: 'Промяна на паролата',\n    checkYourEmailForPasswordReset:\n      'Ако имейл адресът е свързан с акаунт, скоро ще получите инструкции за възстановяване на паролата си. Моля, проверете папката си за спам или нежелана поща, ако не виждате имейла във входящата си поща.',\n    confirmGeneration: 'Потвърди създаването',\n    confirmPassword: 'Потвърди парола',\n    createFirstUser: 'Създай първи потребител',\n    emailNotValid: 'Даденият имейл не е валиден',\n    emailOrUsername: 'Имейл или Потребителско име',\n    emailSent: 'Имейлът е изпратен',\n    emailVerified: 'Успешно потвърден имейл.',\n    enableAPIKey: 'Активирай API ключ',\n    failedToUnlock: 'Неуспешно отключване',\n    forceUnlock: 'Принудително отключване',\n    forgotPassword: 'Забравена парола',\n    forgotPasswordEmailInstructions:\n      'Моля, въведи имейла си по-долу. Ще получиш съобщение с насоки как да промениш паролата си.',\n    forgotPasswordQuestion: 'Забравена парола?',\n    forgotPasswordUsernameInstructions:\n      'Моля, въведете вашето потребителско име по-долу. Инструкции как да възстановите паролата си ще бъдат изпратени на имейл адреса, асоцииран с вашето потребителско име.',\n    generate: 'Генерирай',\n    generateNewAPIKey: 'Генерирай нов API ключ',\n    generatingNewAPIKeyWillInvalidate:\n      'Генерирането на нов API ключ ще <1>анулира</1> предишния. Сигурен ли си, че искаш да продължиш?',\n    lockUntil: 'Заключи до',\n    logBackIn: 'Влез обратно',\n    loggedIn: 'За да влезеш с друг потребител, първо трябва да <0>излезеш</0>.',\n    loggedInChangePassword:\n      'За да промениш паролата си, отиди в своя <0>профил</0> и я промени оттам.',\n    loggedOutInactivity: 'Ти беше изкаран поради неактивност.',\n    loggedOutSuccessfully: 'Излезе успешно.',\n    loggingOut: 'Излизане...',\n    login: 'Вход',\n    loginAttempts: 'Опити за вход',\n    loginUser: 'Вкарай потребител',\n    loginWithAnotherUser: 'За да влезеш с друг потребител, първо трябва да <0>излезеш</0>.',\n    logOut: 'Изход',\n    logout: 'Изход',\n    logoutSuccessful: 'Изходът беше успешен.',\n    logoutUser: 'Изкарай потребител',\n    newAccountCreated:\n      'Току-що беше създаден нов профил за достъп до <a href=\"{{serverURL}}\">{{serverURL}}</a> Моля, въведи връзката в браузъра си, за да потвърдиш имейла си: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> След като потвърдиш имейла си, ще можеш да влезеш успешно.',\n    newAPIKeyGenerated: 'Нов API ключ е създаден.',\n    newPassword: 'Нова парола',\n    passed: 'Удостоверението е успешно',\n    passwordResetSuccessfully: 'Паролата е променена успешно.',\n    resetPassword: 'Възстанови парола',\n    resetPasswordExpiration: 'Нулиране на изтичане на паролата',\n    resetPasswordToken: 'Ключ за възстановяване на парола',\n    resetYourPassword: 'Възстанови паролата си',\n    stayLoggedIn: 'Запомни ме',\n    successfullyRegisteredFirstUser: 'Успешна регистрация на първия потребител.',\n    successfullyUnlocked: 'Успешно отключен',\n    tokenRefreshSuccessful: 'Успешно опресняване на токена.',\n    unableToVerify: 'Неуспешно потвърждение',\n    username: 'Потребителско име',\n    usernameNotValid: 'Предоставеното потребителско име не е валидно.',\n    verified: 'Потвърден',\n    verifiedSuccessfully: 'Потвърден успешно',\n    verify: 'Потвърди',\n    verifyUser: 'Потвърди потребител',\n    verifyYourEmail: 'Потвърди имейла си',\n    youAreInactive:\n      'Не си бил активен от известно време и за твоя сигурност ще бъдеш изкаран от системата. Би ли пожелал да останеш вписан?',\n    youAreReceivingResetPassword:\n      'Получаваш това, защото ти (или някой друг) е заявил възстановяване на паролата. Натисни връзката или постави това в браузъра си, за да довършиш процеса:',\n    youDidNotRequestPassword:\n      'Ако не си заявил това, игнорирай този имейл и паролата ти ще остане непроменена.',\n  },\n  error: {\n    accountAlreadyActivated: 'Този профил вече е активиран.',\n    autosaving: 'Имаше проблем в автоматичното запазване на този документ.',\n    correctInvalidFields: 'Моля, поправи некоректните полета.',\n    deletingFile: 'Имаше грешка при изтриването на файла.',\n    deletingTitle:\n      'Имаше проблем при изтриването на {{title}}. Моля провери връзката си и опитай отново.',\n    documentNotFound:\n      'Документът с ID {{id}} не можа да бъде намерен. Възможно е да е бил изтрит или никога да не е съществувал или може би нямате достъп до него.',\n    emailOrPasswordIncorrect: 'Имейлът или паролата не са правилни.',\n    followingFieldsInvalid_one: 'Следното поле е некоректно:',\n    followingFieldsInvalid_other: 'Следните полета са некоректни:',\n    incorrectCollection: 'Грешна колекция',\n    insufficientClipboardPermissions:\n      'Достъпът до клипборда е отказан. Моля, проверете вашите разрешения за клипборда.',\n    invalidClipboardData: 'Невалидни данни в клипборда.',\n    invalidFileType: 'Невалиден тип на файл',\n    invalidFileTypeValue: 'Невалиден тип на файл: {{value}}',\n    invalidRequestArgs: 'Невалидни аргументи в заявката: {{args}}',\n    loadingDocument: 'Имаше проблем при зареждането на документа с идентификатор {{id}}.',\n    localesNotSaved_one: 'Следната локализация не може да бъде запазена:',\n    localesNotSaved_other: 'Следните локализации не могат да бъдат запазени:',\n    logoutFailed: 'Излизането не бе успешно.',\n    missingEmail: 'Липсващ имейл.',\n    missingIDOfDocument: 'Липсващ идентификатор на документа за обновяване.',\n    missingIDOfVersion: 'Липсващ идентификатор на версия.',\n    missingRequiredData: 'Липсва задължителна информация.',\n    noFilesUploaded: 'Никакви файлове не бяха качени.',\n    noMatchedField: 'Поле не беше открито за \"{{label}}\"',\n    notAllowedToAccessPage: 'Нямаш право на достъп до тази страница.',\n    notAllowedToPerformAction: 'Нямаш право да извършиш това действие.',\n    notFound: 'Заявеният ресурс не беше намерен.',\n    noUser: 'Липсващ потребител',\n    previewing: 'Имаше проблем при предварителното разглеждане на документа.',\n    problemUploadingFile: 'Имаше проблем при качването на файла.',\n    restoringTitle:\n      'Възникна грешка при възстановяването на {{title}}. Моля, проверете връзката си и опитайте отново.',\n    tokenInvalidOrExpired: 'Ключът е невалиден или изтекъл.',\n    tokenNotProvided: 'Токенът не е предоставен.',\n    unableToCopy: 'Неуспешно копиране.',\n    unableToDeleteCount: 'Не беше възможно да се изтрият {{count}} от {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Грешка при преиндексиране на колекцията {{collection}}. Операцията е прекратена.',\n    unableToUpdateCount: 'Не беше възможно да се обновят {{count}} от {{total}} {{label}}.',\n    unauthorized: 'Неоторизиран, трябва да влезеш, за да извършиш тази заявка.',\n    unauthorizedAdmin: 'Неоторизиран, трябва да си администратор, за да извършиш тази заявка.',\n    unknown: 'Неизвестна грешка.',\n    unPublishingDocument: 'Имаше проблем при скриването на този документ.',\n    unspecific: 'Грешка.',\n    unverifiedEmail: 'Моля, потвърдете своя имейл, преди да влезете.',\n    userEmailAlreadyRegistered: 'Потребител с дадения имейл вече е регистриран.',\n    userLocked: 'Този потребител има прекалено много невалидни опити за влизане и е заключен.',\n    usernameAlreadyRegistered: 'Потребител със зададеното потребителско име вече е регистриран.',\n    usernameOrPasswordIncorrect: 'Предоставеното потребителско име или парола са неправилни.',\n    valueMustBeUnique: 'Стойността трябва да е уникална',\n    verificationTokenInvalid: 'Ключът за верификация е невалиден.',\n  },\n  fields: {\n    addLabel: 'Добави {{label}}',\n    addLink: 'Добави нова връзка',\n    addNew: 'Добави нов',\n    addNewLabel: 'Добави нов {{label}}',\n    addRelationship: 'Добави отношение',\n    addUpload: 'Качи',\n    block: 'блок',\n    blocks: 'блокове',\n    blockType: 'Тип блок',\n    chooseBetweenCustomTextOrDocument:\n      'Избери между това да въведеш текстова връзка или да свържеш с друг документ.',\n    chooseDocumentToLink: 'Избери документ, с който да свържеш',\n    chooseFromExisting: 'Избери от съществуващите',\n    chooseLabel: 'Избери {{label}}',\n    collapseAll: 'Свий всички',\n    customURL: 'Връзка',\n    editLabelData: 'Редактирай информацията за {{label}}',\n    editLink: 'Редактирай връзка',\n    editRelationship: 'Редактирай отношение',\n    enterURL: 'Въведи връзка',\n    internalLink: 'Вътрешна връзка',\n    itemsAndMore: '{{items}} и {{count}} повече',\n    labelRelationship: '{{label}} връзка',\n    latitude: 'Географска ширина',\n    linkedTo: 'Свързано с <0>{{label}}</0>',\n    linkType: 'Тип на връзката',\n    longitude: 'Географска дължина',\n    newLabel: 'Нов {{label}}',\n    openInNewTab: 'Отвори в нов раздел',\n    passwordsDoNotMatch: 'Паролите не са еднакви.',\n    relatedDocument: 'Свързан документ',\n    relationTo: 'Отношение с',\n    removeRelationship: 'Премахни отношение',\n    removeUpload: 'Премахни качване',\n    saveChanges: 'Запази промените',\n    searchForBlock: 'Търси блок',\n    selectExistingLabel: 'Избери съществуващ {{label}}',\n    selectFieldsToEdit: 'Избери полета за редактиране',\n    showAll: 'Покажи всички',\n    swapRelationship: 'Смени отношение',\n    swapUpload: 'Смени качване',\n    textToDisplay: 'Текст към дисплей',\n    toggleBlock: 'Превключи блок',\n    uploadNewLabel: 'Качи нов {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Прегледай по папки',\n    byFolder: 'По папка',\n    deleteFolder: 'Изтрий папка',\n    folderName: 'Име на папка',\n    folders: 'Папки',\n    folderTypeDescription:\n      'Изберете кой тип документи от колекциите трябва да се допускат в тази папка.',\n    itemHasBeenMoved: '{{title}} е преместен в {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} беше преместено в основната папка',\n    itemsMovedToFolder: '{{title}} беше преместен в {{folderName}}',\n    itemsMovedToRoot: '{{title}} е преместен в основната папка.',\n    moveFolder: 'Премести папка',\n    moveItemsToFolderConfirmation:\n      'Предстои да преместите <1>{{count}} {{label}}</1> в <2>{{toFolder}}</2>. Сигурни ли сте?',\n    moveItemsToRootConfirmation:\n      'Предстои да преместите <1>{{count}} {{label}}</1> в основната папка. Сигурни ли сте?',\n    moveItemToFolderConfirmation:\n      'Предстои да преместите <1>{{title}}</1> в <2>{{toFolder}}</2>. Сигурни ли сте?',\n    moveItemToRootConfirmation:\n      'Предстои да преместите <1>{{title}}</1> в основната папка. Сигурни ли сте?',\n    movingFromFolder: 'Преместване на {{title}} от {{fromFolder}}',\n    newFolder: 'Нова папка',\n    noFolder: 'Няма папка',\n    renameFolder: 'Преименувай папка',\n    searchByNameInFolder: 'Търсене по име в {{folderName}}',\n    selectFolderForItem: 'Изберете папка за {{title}}',\n  },\n  general: {\n    name: 'Име',\n    aboutToDelete: 'На път си да изтриеш {{label}} <1>{{title}}</1>. Сигурен ли си?',\n    aboutToDeleteCount_many: 'На път си да изтриеш {{count}} {{label}}',\n    aboutToDeleteCount_one: 'На път си да изтриеш {{count}} {{label}}',\n    aboutToDeleteCount_other: 'На път си да изтриеш {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Предстои да изтриете завинаги {{label}} <1>{{title}}</1>. Сигурни ли сте?',\n    aboutToPermanentlyDeleteTrash:\n      'Вие се насочвате към перманентно изтриване на <0>{{count}}</0> <1>{{label}}</1> от кошчето. Сигурни ли сте?',\n    aboutToRestore: 'Предстои да възстановите {{label}} <1>{{title}}</1>. Сигурни ли сте?',\n    aboutToRestoreAsDraft:\n      'Предстои да възстановите {{label}} <1>{{title}}</1> като чернова. Сигурни ли сте?',\n    aboutToRestoreAsDraftCount: 'Предстои да възстановите {{count}} {{label}} като чернова',\n    aboutToRestoreCount: 'Предстои да възстановите {{count}} {{label}}',\n    aboutToTrash: 'Предстои да преместите {{label}} <1>{{title}}</1> в кошчето. Сигурни ли сте?',\n    aboutToTrashCount: 'Предстои да преместите {{count}} {{label}} в кошчето',\n    addBelow: 'Добави отдолу',\n    addFilter: 'Добави филтър',\n    adminTheme: 'Цветова тема',\n    all: 'Всички',\n    allCollections: 'Всички колекции',\n    allLocales: 'Всички локации',\n    and: 'И',\n    anotherUser: 'Друг потребител',\n    anotherUserTakenOver: 'Друг потребител пое редактирането на този документ.',\n    applyChanges: 'Приложи промените',\n    ascending: 'Възходящ',\n    automatic: 'Автоматична',\n    backToDashboard: 'Обратно към таблото',\n    cancel: 'Отмени',\n    changesNotSaved: 'Промените ти не са запазени. Ако напуснеш сега, ще ги загубиш.',\n    clear: 'Ясно',\n    clearAll: 'Изчисти всичко',\n    close: 'Затвори',\n    collapse: 'Свий',\n    collections: 'Колекции',\n    columns: 'Колони',\n    columnToSort: 'Колона за сортиране',\n    confirm: 'Потвърди',\n    confirmCopy: 'Потвърди копирането',\n    confirmDeletion: 'Потвърди изтриване',\n    confirmDuplication: 'Потвърди дупликация',\n    confirmMove: 'Потвърждаване на преместване',\n    confirmReindex: 'Да се преиндексират всички {{collections}}?',\n    confirmReindexAll: 'Да се преиндексират всички колекции?',\n    confirmReindexDescription:\n      'Това ще премахне съществуващите индекси и ще преиндексира документите в колекциите {{collections}}.',\n    confirmReindexDescriptionAll:\n      'Това ще премахне съществуващите индекси и ще преиндексира документите във всички колекции.',\n    confirmRestoration: 'Потвърдете възстановяването',\n    copied: 'Копирано',\n    copy: 'Копирай',\n    copyField: 'Копирай поле',\n    copying: 'Копиране',\n    copyRow: 'Копирай ред',\n    copyWarning:\n      'Предстои да презапишете {{to}} с {{from}} за {{label}} {{title}}. Сигурни ли сте?',\n    create: 'Създай',\n    created: 'Създаден',\n    createdAt: 'Създаден на',\n    createNew: 'Създай нов',\n    createNewLabel: 'Създай нов {{label}}',\n    creating: 'Създава се',\n    creatingNewLabel: 'Създаване на нов {{label}}',\n    currentlyEditing:\n      'в момента редактира този документ. Ако поемете управлението, те ще бъдат блокирани от продължаване на редактирането и може да загубят незаписаните промени.',\n    custom: 'Персонализиран',\n    dark: 'Тъмна',\n    dashboard: 'Табло',\n    delete: 'Изтрий',\n    deleted: 'Изтрито',\n    deletedAt: 'Изтрито на',\n    deletedCountSuccessfully: 'Изтрити {{count}} {{label}} успешно.',\n    deletedSuccessfully: 'Изтрито успешно.',\n    deletePermanently: 'Пропуснете кошчето и изтрийте перманентно',\n    deleting: 'Изтриване...',\n    depth: 'Дълбочина',\n    descending: 'Низходящо',\n    deselectAllRows: 'Демаркирай всички редове',\n    document: 'Документ',\n    documentIsTrashed: 'Този {{label}} е изтрит и е само за четене.',\n    documentLocked: 'Документът е заключен',\n    documents: 'Документи',\n    duplicate: 'Дупликирай',\n    duplicateWithoutSaving: 'Дупликирай без да запазваш промените',\n    edit: 'Редактирай',\n    editAll: 'Редактирай всички',\n    editedSince: 'Редактирано от',\n    editing: 'Редактиране',\n    editingLabel_many: 'Редактиране на {{count}} {{label}}',\n    editingLabel_one: 'Редактиране на {{count}} {{label}}',\n    editingLabel_other: 'Редактиране на {{count}} {{label}}',\n    editingTakenOver: 'Редактирането е поето',\n    editLabel: 'Редактирай {{label}}',\n    email: 'Имейл',\n    emailAddress: 'Имейл адрес',\n    emptyTrash: 'Изпразни кошчето',\n    emptyTrashLabel: 'Изпразнете кошчето за {{label}}',\n    enterAValue: 'Въведи стойност',\n    error: 'Грешка',\n    errors: 'Грешки',\n    exitLivePreview: 'Излезте от Визуализация в Реално Време',\n    export: 'Износ',\n    fallbackToDefaultLocale: 'Използвай локализацията, която е по подразбиране',\n    false: 'Невярно',\n    filter: 'Филтрирай',\n    filters: 'Филтри',\n    filterWhere: 'Филтрирай {{label}} където',\n    globals: 'Глобални',\n    goBack: 'Върни се',\n    groupByLabel: 'Групирай по {{label}}',\n    import: 'Внос',\n    isEditing: 'редактира',\n    item: 'артикул',\n    items: 'артикули',\n    language: 'Език',\n    lastModified: 'Последно променено',\n    leaveAnyway: 'Напусни въпреки това',\n    leaveWithoutSaving: 'Напусни без да запазиш',\n    light: 'Светла',\n    livePreview: 'Предварителен преглед',\n    loading: 'Зарежда се',\n    locale: 'Локализация',\n    locales: 'Локализации',\n    menu: 'Меню',\n    moreOptions: 'Повече опции',\n    move: 'Премести',\n    moveConfirm:\n      'Вие сте на път да преместите {{count}} {{label}} към <1>{{destination}}</1>. Сигурни ли сте?',\n    moveCount: 'Преместете {{count}} {{label}}',\n    moveDown: 'Надолу',\n    moveUp: 'Нагоре',\n    moving: 'Преместване',\n    movingCount: 'Преместване на {{count}} {{label}}',\n    newPassword: 'Нова парола',\n    next: 'Следващ',\n    no: 'Не',\n    noDateSelected: 'Не е избрана дата',\n    noFiltersSet: 'Няма зададени филтри',\n    noLabel: '<Няма {{label}}>',\n    none: 'Никакъв',\n    noOptions: 'Няма опции',\n    noResults:\n      '{{label}} не е открит. {{label}} не съществува или никой не отговаря на зададените филтри.',\n    notFound: 'Няма открит',\n    nothingFound: 'Нищо не беше открито',\n    noTrashResults: 'Няма {{label}} в кошчето.',\n    noUpcomingEventsScheduled: 'Няма предстоящи събития.',\n    noValue: 'Няма стойност',\n    of: 'от',\n    only: 'Само',\n    open: 'Отвори',\n    or: 'Или',\n    order: 'Ред',\n    overwriteExistingData: 'Презапишете съществуващите данни в полето',\n    pageNotFound: 'Страницата не беше открита',\n    password: 'Парола',\n    pasteField: 'Постави поле',\n    pasteRow: 'Постави ред',\n    payloadSettings: 'Настройки на Payload',\n    permanentlyDelete: 'Трайно изтриване',\n    permanentlyDeletedCountSuccessfully: 'Успешно изтрити завинаги {{count}} {{label}}.',\n    perPage: 'На страница: {{limit}}',\n    previous: 'Предишен',\n    reindex: 'Преиндексиране',\n    reindexingAll: 'Преиндексиране на всички {{collections}}.',\n    remove: 'Премахни',\n    rename: 'Преименувайте',\n    reset: 'Нулиране',\n    resetPreferences: 'Нулиране на предпочитанията',\n    resetPreferencesDescription:\n      'Това ще нулира всички ваши предпочитания до техните настройки по подразбиране.',\n    resettingPreferences: 'Нулиране на предпочитанията.',\n    restore: 'Възстановяване',\n    restoreAsPublished: 'Възстановете като публикувана версия',\n    restoredCountSuccessfully: 'Успешно възстановени {{count}} {{label}}.',\n    restoring: 'Възстановяване...',\n    row: 'ред',\n    rows: 'Редове',\n    save: 'Запази',\n    saving: 'Запазване...',\n    schedulePublishFor: 'Планирано публикуване за {{title}}',\n    searchBy: 'Търси по {{label}}',\n    select: 'Изберете',\n    selectAll: 'Избери всички {{count}} {{label}}',\n    selectAllRows: 'Избери всички редове',\n    selectedCount: '{{count}} {{label}} избрани',\n    selectLabel: 'Изберете {{label}}',\n    selectValue: 'Избери стойност',\n    showAllLabel: 'Покажи всички {{label}}',\n    sorryNotFound: 'Съжаляваме-няма нищо, което да отговаря на търсенето ти.',\n    sort: 'Сортирай',\n    sortByLabelDirection: 'Сортирай по {{label}} {{direction}}',\n    stayOnThisPage: 'Остани на тази страница',\n    submissionSuccessful: 'Успешно подаване.',\n    submit: 'Подай',\n    submitting: 'Подаване...',\n    success: 'Успех',\n    successfullyCreated: '{{label}} успешно създаден.',\n    successfullyDuplicated: '{{label}} успешно дупликиран.',\n    successfullyReindexed:\n      'Успешно преиндексирани {{count}} от {{total}} документа от {{collections}} колекции.',\n    takeOver: 'Поемане',\n    thisLanguage: 'Български',\n    time: 'Време',\n    timezone: 'Часова зона',\n    titleDeleted: '{{label}} \"{{title}}\" успешно изтрит.',\n    titleRestored: '{{label}} \"{{title}}\" беше успешно възстановено.',\n    titleTrashed: '{{label}} \"{{title}}\" е преместено в кошчето.',\n    trash: 'Боклук',\n    trashedCountSuccessfully: '{{count}} {{label}} преместени в кошчето.',\n    true: 'Вярно',\n    unauthorized: 'Неоторизиран',\n    unsavedChanges: 'Имате незапазени промени. Запазете или отхвърлете преди да продължите.',\n    unsavedChangesDuplicate: 'Имаш незапазени промени. Искаш ли да продължиш да дупликираш?',\n    untitled: 'Неозаглавен',\n    upcomingEvents: 'Предстоящи събития',\n    updatedAt: 'Обновен на',\n    updatedCountSuccessfully: 'Обновени {{count}} {{label}} успешно.',\n    updatedLabelSuccessfully: 'Успешно обновихме {{label}}.',\n    updatedSuccessfully: 'Обновен успешно.',\n    updateForEveryone: 'Актуализация за всички',\n    updating: 'Обновява се',\n    uploading: 'Качва се',\n    uploadingBulk: 'Качване на {{current}} от {{total}}',\n    user: 'Потребител',\n    username: 'Потребителско име',\n    users: 'Потребители',\n    value: 'Стойност',\n    viewing: 'Преглеждане',\n    viewReadOnly: 'Преглед само за четене',\n    welcome: 'Добре дошъл',\n    yes: 'Да',\n  },\n  localization: {\n    cannotCopySameLocale: 'Не може да се копира в същата локация',\n    copyFrom: 'Копирай от',\n    copyFromTo: 'Копиране от {{from}} към {{to}}',\n    copyTo: 'Копирай в',\n    copyToLocale: 'Копирайте в местното',\n    localeToPublish: 'Местоположение за публикуване',\n    selectLocaleToCopy: 'Изберете място за копиране',\n  },\n  operators: {\n    contains: 'съдържа',\n    equals: 'е равно на',\n    exists: 'съществува',\n    intersects: 'пресича',\n    isGreaterThan: 'е по-голямо от',\n    isGreaterThanOrEqualTo: 'е по-голямо от или равно на',\n    isIn: 'е в',\n    isLessThan: 'е по-малко от',\n    isLessThanOrEqualTo: 'е по-малко от или равно на',\n    isLike: 'е като',\n    isNotEqualTo: 'не е равно на',\n    isNotIn: 'не е в',\n    isNotLike: 'не е като',\n    near: 'близко',\n    within: 'в рамките на',\n  },\n  upload: {\n    addFile: 'Добавяне на файл',\n    addFiles: 'Добави файлове',\n    bulkUpload: 'Масово Качване',\n    crop: 'Изрязване',\n    cropToolDescription:\n      'Плъзни ъглите на избраната област, избери нова област или коригирай стойностите по-долу.',\n    download: 'Изтегляне',\n    dragAndDrop: 'Дръпни и пусни файл',\n    dragAndDropHere: 'или дръпни и пусни файла тук',\n    editImage: 'Редактирай изображение',\n    fileName: 'Име на файла',\n    fileSize: 'Големина на файла',\n    filesToUpload: 'Файлове за качване',\n    fileToUpload: 'Файл за качване',\n    focalPoint: 'Фокусна точка',\n    focalPointDescription:\n      'Премести фокусната точка директно върху визуализацията или регулирай стойностите по-долу.',\n    height: 'Височина',\n    lessInfo: 'По-малко информация',\n    moreInfo: 'Повече информация',\n    noFile: 'Няма файл',\n    pasteURL: 'Поставяне на URL',\n    previewSizes: 'Преглед на размери',\n    selectCollectionToBrowse: 'Избери колекция, която да разгледаш',\n    selectFile: 'Избери файл',\n    setCropArea: 'Задай област за изрязване',\n    setFocalPoint: 'Задай фокусна точка',\n    sizes: 'Големини',\n    sizesFor: 'Размери за {{label}}',\n    width: 'Ширина',\n  },\n  validation: {\n    emailAddress: 'Моля, въведи валиден имейл адрес.',\n    enterNumber: 'Моля, въведи валиден номер.',\n    fieldHasNo: 'Това поле няма {{label}}',\n    greaterThanMax: '{{value}} е по-голямо от максимално допустимото {{label}} от {{max}}.',\n    invalidInput: 'Това поле има невалиден вход.',\n    invalidSelection: 'Това поле има невалидна селекция.',\n    invalidSelections: 'Това поле има следните невалидни селекции:',\n    lessThanMin: '{{value}} е по-малко от минимално допустимото {{label}} от {{min}}.',\n    limitReached: 'Достигнат е лимитът, могат да бъдат добавени само {{max}} елемента.',\n    longerThanMin:\n      'Тази стойност трябва да е по-голяма от минималната стойност от {{minLength}} символа.',\n    notValidDate: '\"{{value}}\" не е валидна дата.',\n    required: 'Това поле е задължително.',\n    requiresAtLeast: 'Това поле изисква поне {{count}} {{label}}.',\n    requiresNoMoreThan: 'Това поле изисква не повече от {{count}} {{label}}.',\n    requiresTwoNumbers: 'Това поле изисква 2 числа.',\n    shorterThanMax:\n      'Тази стойност трябва да е по-малка от максималната стойност от {{maxLength}} символа.',\n    timezoneRequired: 'Изисква се часова зона.',\n    trueOrFalse: 'Това поле може да бъде само \"true\" или \"false\".',\n    username:\n      'Моля, въведете валидно потребителско име. Може да съдържа букви, цифри, тирета, точки и долни черти.',\n    validUploadID: 'Това поле не е валиден идентификатор на качването.',\n  },\n  version: {\n    type: 'Тип',\n    aboutToPublishSelection: 'На път си да публикуваш всички избрани {{label}}. Сигурен ли си?',\n    aboutToRestore:\n      'На път си да възстановиш този {{label}} документ до състоянието му от {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'На път си да възстановиш глобалния документ {{label}} до състоянието му от {{versionDate}}.',\n    aboutToRevertToPublished:\n      'На път си да възстановиш промените на този документ до публикуваното му състояние. Сигурен ли си?',\n    aboutToUnpublish: 'На път си да скриеш този документ. Сигурен ли си?',\n    aboutToUnpublishSelection: 'На път си да скриеш всички избрани {{label}}. Сигурен ли си?',\n    autosave: 'Автоматично запазване',\n    autosavedSuccessfully: 'Успешно автоматично запазване.',\n    autosavedVersion: 'Автоматично запазена версия',\n    changed: 'Променен',\n    changedFieldsCount_one: '{{count}} променено поле',\n    changedFieldsCount_other: '{{count}} променени полета',\n    compareVersion: 'Сравни версия с:',\n    compareVersions: 'Сравняване на версии',\n    comparingAgainst: 'Сравнение с',\n    confirmPublish: 'Потвърди публикуване',\n    confirmRevertToSaved: 'Потвърди възстановяване до запазен',\n    confirmUnpublish: 'Потвърди скриване',\n    confirmVersionRestoration: 'Потвърди възстановяване на версия',\n    currentDocumentStatus: 'Сегашен статус на документа: {{docStatus}}',\n    currentDraft: 'Текущ проект',\n    currentlyPublished: 'В момента публикуван',\n    currentlyViewing: 'В момента преглеждате',\n    currentPublishedVersion: 'Текуща публикувана версия',\n    draft: 'Чернова',\n    draftSavedSuccessfully: 'Чернова запазена успешно.',\n    lastSavedAgo: 'последно запазено преди {{distance}}',\n    modifiedOnly: 'Само променени',\n    moreVersions: 'Още версии...',\n    noFurtherVersionsFound: 'Не са открити повече версии',\n    noRowsFound: 'Не е открит {{label}}',\n    noRowsSelected: 'Не е избран {{label}}',\n    preview: 'Предварителен преглед',\n    previouslyDraft: 'Предишно беше Чернова',\n    previouslyPublished: 'Предишно публикувано',\n    previousVersion: 'Предишна версия',\n    problemRestoringVersion: 'Имаше проблем при възстановяването на тази версия',\n    publish: 'Публикувай',\n    publishAllLocales: 'Публикувайте всички локали',\n    publishChanges: 'Публикувай промените',\n    published: 'Публикувано',\n    publishIn: 'Публикувайте в {{locale}}',\n    publishing: 'Публикуване',\n    restoreAsDraft: 'Възстанови като чернова',\n    restoredSuccessfully: 'Успешно възстановяване.',\n    restoreThisVersion: 'Възстанови тази версия',\n    restoring: 'Възстановяване...',\n    reverting: 'Връщане..',\n    revertToPublished: 'Върни се до публикуваното',\n    saveDraft: 'Запази чернова',\n    scheduledSuccessfully: 'Успешно насрочено.',\n    schedulePublish: 'Планирано публикуване',\n    selectLocales: 'Избери локализации за показване',\n    selectVersionToCompare: 'Избери версия за сравняване',\n    showingVersionsFor: 'Показване на версии за:',\n    showLocales: 'Покажи преводи:',\n    specificVersion: 'Специфична версия',\n    status: 'Статус',\n    unpublish: 'Скрий',\n    unpublishing: 'Скриване...',\n    version: 'Версия',\n    versionAgo: 'преди {{distance}}',\n    versionCount_many: '{{count}} открити версии',\n    versionCount_none: 'Няма открити версии',\n    versionCount_one: '{{count}} открита версия',\n    versionCount_other: '{{count}} открити версии',\n    versionCreatedOn: '{{version}} създадена на:',\n    versionID: 'Идентификатор на версията',\n    versions: 'Версии',\n    viewingVersion: 'Гледане на версия за {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Гледане на версия за глобалния документ {{entityLabel}}',\n    viewingVersions: 'Гледане на версии за {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Гледане на версии за глобалния документ {{entityLabel}}',\n  },\n}\n\nexport const bg: Language = {\n  dateFNSKey: 'bg',\n  translations: bgTranslations,\n}\n"], "names": ["bgTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "bg", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eACE;QACFC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBACE;QACFC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}