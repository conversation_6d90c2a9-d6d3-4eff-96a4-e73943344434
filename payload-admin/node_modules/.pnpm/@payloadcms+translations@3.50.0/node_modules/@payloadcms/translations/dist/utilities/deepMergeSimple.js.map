{"version": 3, "sources": ["../../src/utilities/deepMergeSimple.ts"], "sourcesContent": ["/**\n * Very simple, but fast deepMerge implementation. Only deepMerges objects, not arrays and clones everything.\n * Do not use this if your object contains any complex objects like React Components, or if you would like to combine Arrays.\n * If you only have simple objects and need a fast deepMerge, this is the function for you.\n *\n * obj2 takes precedence over obj1 - thus if obj2 has a key that obj1 also has, obj2's value will be used.\n *\n * @param obj1 base object\n * @param obj2 object to merge \"into\" obj1\n */\nexport function deepMergeSimple<T = object>(obj1: object, obj2: object): T {\n  const output = { ...obj1 }\n\n  for (const key in obj2) {\n    if (Object.prototype.hasOwnProperty.call(obj2, key)) {\n      // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n      if (typeof obj2[key] === 'object' && !Array.isArray(obj2[key]) && obj1[key]) {\n        // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n        output[key] = deepMergeSimple(obj1[key], obj2[key])\n      } else {\n        // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n        output[key] = obj2[key]\n      }\n    }\n  }\n\n  return output as T\n}\n"], "names": ["deepMergeSimple", "obj1", "obj2", "output", "key", "Object", "prototype", "hasOwnProperty", "call", "Array", "isArray"], "mappings": "AAAA;;;;;;;;;CASC,GACD,OAAO,SAASA,gBAA4BC,IAAY,EAAEC,IAAY;IACpE,MAAMC,SAAS;QAAE,GAAGF,IAAI;IAAC;IAEzB,IAAK,MAAMG,OAAOF,KAAM;QACtB,IAAIG,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,MAAME,MAAM;YACnD,oFAAoF;YACpF,IAAI,OAAOF,IAAI,CAACE,IAAI,KAAK,YAAY,CAACK,MAAMC,OAAO,CAACR,IAAI,CAACE,IAAI,KAAKH,IAAI,CAACG,IAAI,EAAE;gBAC3E,oFAAoF;gBACpFD,MAAM,CAACC,IAAI,GAAGJ,gBAAgBC,IAAI,CAACG,IAAI,EAAEF,IAAI,CAACE,IAAI;YACpD,OAAO;gBACL,oFAAoF;gBACpFD,MAAM,CAACC,IAAI,GAAGF,IAAI,CAACE,IAAI;YACzB;QACF;IACF;IAEA,OAAOD;AACT"}