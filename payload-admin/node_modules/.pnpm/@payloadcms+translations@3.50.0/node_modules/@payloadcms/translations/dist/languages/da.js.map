{"version": 3, "sources": ["../../src/languages/da.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const daTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Konto',\n    accountOfCurrentUser: 'Den nuværende brugers konto',\n    accountVerified: 'Konto verifieret.',\n    alreadyActivated: 'Allerede aktiveret',\n    alreadyLoggedIn: 'Allerede logget ind',\n    apiKey: 'API nøgle',\n    authenticated: 'Godkendt',\n    backToLogin: 'Tilbage til login',\n    beginCreateFirstUser: 'For at starte, opret en bruger.',\n    changePassword: 'Skift adgangskode',\n    checkYourEmailForPasswordReset:\n      'Hvis e-mailadressen er forbundet med en konto, vil du kort tid modtage instruktioner til at nulstille din adgangskode. Tjek venligst din spam- eller junkmail-mappe, hvis du ikke ser e-mailen i din indbakke.',\n    confirmGeneration: 'Bekræft generering',\n    confirmPassword: 'Bekræft adgangskode',\n    createFirstUser: 'Opret bruger',\n    emailNotValid: 'Ugyldig email',\n    emailOrUsername: 'Email eller brugernavn',\n    emailSent: 'Email sendt',\n    emailVerified: 'Email verificeret.',\n    enableAPIKey: 'Aktiver API nøgle',\n    failedToUnlock: 'Kunne ikke låse op',\n    forceUnlock: 'Tving oplåsning',\n    forgotPassword: 'Glemt adgangskode',\n    forgotPasswordEmailInstructions:\n      'Indtast email nedenunder. Du vil modtage en email med instruktioner i hvordan du nulstiller din adgangskode.',\n    forgotPasswordQuestion: 'Glemt adgangskode?',\n    forgotPasswordUsernameInstructions:\n      'Indtast brugernavn nedenunder. Instruktioner i hvordan du nulstiller din adgangskode vil blive sendt til den email der er tilknyttet brugeren.',\n    generate: 'Generer',\n    generateNewAPIKey: 'Generer ny API nøgle.',\n    generatingNewAPIKeyWillInvalidate:\n      'Generering af en ny API nøgle vil <1>ugyldiggøre</1> den tidligere nøgle. Vil du forsætte?',\n    lockUntil: 'Lås indtil',\n    logBackIn: 'Log på igen',\n    loggedIn: 'For at logge på med en anden bruger, skal du først <0>logge ud</0>.',\n    loggedInChangePassword: 'For at ændre din adgangskode, gå til <0>konto</0> og ændr den der.',\n    loggedOutInactivity: 'Du er blevet logget ud grundet inaktivitet.',\n    loggedOutSuccessfully: 'Du er logget ud.',\n    loggingOut: 'Logger ud...',\n    login: 'Login',\n    loginAttempts: 'Login forsøg',\n    loginUser: 'Login bruger',\n    loginWithAnotherUser: 'For at logge på med en anden bruger, skal du først <0>logge ud</0>.',\n    logOut: 'Log ud',\n    logout: 'Log ud',\n    logoutSuccessful: 'Log ud succesfuldt.',\n    logoutUser: 'Log ud bruger',\n    newAccountCreated:\n      'En ny bruger er blevet oprettet. <a href=\"{{serverURL}}\">{{serverURL}}</a> Klik på linket eller kopier URL for at verificere din email: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Efter verificeringen af din email, kan du logge ind.',\n    newAPIKeyGenerated: 'Ny API nøgle genereret.',\n    newPassword: 'Ny adgangskode',\n    passed: 'Godkendt',\n    passwordResetSuccessfully: 'Adgangskode nulstillet.',\n    resetPassword: 'Nulstil adgangskode',\n    resetPasswordExpiration: 'Nulstil udløbsdato for adgangskoden',\n    resetPasswordToken: 'Nulstil adgangskode token',\n    resetYourPassword: 'Nulstil din adgangskode',\n    stayLoggedIn: 'Forbliv logget ind',\n    successfullyRegisteredFirstUser: 'Bruger registreret.',\n    successfullyUnlocked: 'Låst op',\n    tokenRefreshSuccessful: 'Tokenopdatering blev gennemført med succes.',\n    unableToVerify: 'Kan ikke verificere',\n    username: 'Brugernavn',\n    usernameNotValid: 'Brugernavnet er ugyldigt.',\n    verified: 'Bekræftet',\n    verifiedSuccessfully: 'Bekræftet succesfuldt',\n    verify: 'Bekræft',\n    verifyUser: 'Bekræft bruger',\n    verifyYourEmail: 'Bekræft din email',\n    youAreInactive:\n      'Du har ikke været aktiv i et stykke tid og vil snart blive logget automatisk ud. Vil du forblive logget ind?',\n    youAreReceivingResetPassword:\n      'Du modtager dette, fordi du eller en anden har anmodet om at nulstille adgangskoden til din konto. Klik venligst på følgende link, eller indsæt det i din browser for at fuldføre processen:',\n    youDidNotRequestPassword:\n      'Hvis du ikke har anmodet om dette, skal du blot ignorere denne e-mail, og din adgangskode vil forblive uændret',\n  },\n  error: {\n    accountAlreadyActivated: 'Denne konto er allerede blevet aktiveret.',\n    autosaving: 'Der opstod et problem under autosaving af dette dokument.',\n    correctInvalidFields: 'Venligst korriger ugyldige felter.',\n    deletingFile: 'Der opstod en fejl under sletning af filen.',\n    deletingTitle:\n      'Der opstod en fejl under sletningen {{title}}. Tjek din forbindelse eller prøv igen.',\n    documentNotFound:\n      'Dokumentet med ID {{id}} kunne ikke findes. Det kan være slettet eller har aldrig eksisteret, eller du har muligvis ikke adgang til det.',\n    emailOrPasswordIncorrect: 'Email eller adgangskode er forkert.',\n    followingFieldsInvalid_one: 'Feltet er ugyldigt:',\n    followingFieldsInvalid_other: 'Felterne er ugyldige:',\n    incorrectCollection: 'Forkert samling',\n    insufficientClipboardPermissions:\n      'Adgang til udklipsholder nægtet. Kontroller dine udklipsholderrettigheder.',\n    invalidClipboardData: 'Ugyldige data i udklipsholderen.',\n    invalidFileType: 'Ugyldig filtype',\n    invalidFileTypeValue: 'Ugyldig filtype: {{value}}',\n    invalidRequestArgs: 'Ugyldige argumenter i anmodningen: {{args}}',\n    loadingDocument: 'Der opstod et problem med at loade dokumentet med ID {{id}}.',\n    localesNotSaved_one: 'Følgende lokalitet kunne ikke gemmes:',\n    localesNotSaved_other: 'Følgende lokaliteter kunne ikke gemmes:',\n    logoutFailed: 'Log ud mislykket.',\n    missingEmail: 'Mangler email.',\n    missingIDOfDocument: 'Mangler ID af dokument til opdatering.',\n    missingIDOfVersion: 'ID for version mangler.',\n    missingRequiredData: 'Mangler påkrævet data.',\n    noFilesUploaded: 'Ingen filer blev uploadet.',\n    noMatchedField: 'Ingen matchende felt fundet for \"{{label}}\"',\n    notAllowedToAccessPage: 'Du har ikke adgang til denne side.',\n    notAllowedToPerformAction: 'Du har ikke adgang til denne handling.',\n    notFound: 'Den anmodede ressource blev ikke fundet.',\n    noUser: 'Ingen bruger',\n    previewing: 'Der opstod et problem med at vise dokumentet.',\n    problemUploadingFile: 'Der opstod et problem under uploadingen af filen.',\n    restoringTitle:\n      'Der opstod en fejl under genoprettelsen af {{title}}. Kontroller venligst din forbindelse og prøv igen.',\n    tokenInvalidOrExpired: 'Token er enten ugyldig eller udløbet.',\n    tokenNotProvided: 'Token ikke angivet.',\n    unableToCopy: 'Kan ikke kopiere.',\n    unableToDeleteCount: 'Kunne ikke slette {{count}} mangler {{total}} {{label}}.',\n    unableToReindexCollection:\n      'Fejl ved genindeksering af samling {{collection}}. Operationen blev afbrudt.',\n    unableToUpdateCount: 'Kunne ikke slette {{count}} mangler {{total}} {{label}}.',\n    unauthorized: 'Uautoriseret, log in for at gennemføre handlingen.',\n    unauthorizedAdmin: 'Uautoriseret, denne bruger har ikke adgang til adminpanelet.',\n    unknown: 'En ukendt fejl er opstået.',\n    unPublishingDocument: 'Der opstod et problem med at ophæve udgivelsen af dette dokument.',\n    unspecific: 'En fejl er opstået.',\n    unverifiedEmail: 'Bekræft venligst din e-mail, før du logger ind.',\n    userEmailAlreadyRegistered: 'Email allerede registreret.',\n    userLocked: 'Denne bruger er låst på grund af for mange fejlede loginforsøg',\n    usernameAlreadyRegistered: 'Brugernavn allerede registeret.',\n    usernameOrPasswordIncorrect: 'Bruger navn eller adgangskode er forkert.',\n    valueMustBeUnique: 'Værdien skal være unik',\n    verificationTokenInvalid: 'Verifikationstoken er ugyldigt.',\n  },\n  fields: {\n    addLabel: 'Tilføj {{label}}',\n    addLink: 'Tilføj Link',\n    addNew: 'Tilføj ny',\n    addNewLabel: 'Tilføj ny {{label}}',\n    addRelationship: 'Tilføj forhold',\n    addUpload: 'Tilføj upload',\n    block: 'blok',\n    blocks: 'blokke',\n    blockType: 'Blok type',\n    chooseBetweenCustomTextOrDocument:\n      'Vælg mellem at indtaste en brugerdefineret tekst-URL eller linke til et andet dokument.',\n    chooseDocumentToLink: 'Vælg et dokument at linke til',\n    chooseFromExisting: 'Vælg fra eksiterende',\n    chooseLabel: 'Vælg {{label}}',\n    collapseAll: 'Skjul alt\"',\n    customURL: 'Brugerdefineret URL',\n    editLabelData: 'Rediger {{label}} data',\n    editLink: 'Rediger link',\n    editRelationship: 'Rediger forhold',\n    enterURL: 'Indtast URL',\n    internalLink: 'Intern link',\n    itemsAndMore: '{{items}} og {{count}} mere',\n    labelRelationship: '{{label}} forhold',\n    latitude: 'breddegrad',\n    linkedTo: 'Linket til <0>{{label}}</0>',\n    linkType: 'Link type',\n    longitude: 'Længdegrad',\n    newLabel: 'Ny {{label}}',\n    openInNewTab: 'Åben i ny fane',\n    passwordsDoNotMatch: 'Adgangskoder matcher ikke.',\n    relatedDocument: 'Relateret dokument',\n    relationTo: 'Relateret til',\n    removeRelationship: 'Fjern forhold',\n    removeUpload: 'Fjern upload',\n    saveChanges: 'Gem ændringer',\n    searchForBlock: 'Søg efter blok',\n    selectExistingLabel: 'Vælg eksisterende {{label}}',\n    selectFieldsToEdit: 'Vælg felter at redigere',\n    showAll: 'Vis alle',\n    swapRelationship: 'Byt forhold',\n    swapUpload: 'Byt upload',\n    textToDisplay: 'Tekst der skal vises',\n    toggleBlock: 'Skift blok',\n    uploadNewLabel: 'Upload ny {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Gennemse efter Mappe',\n    byFolder: 'Ved Mappe',\n    deleteFolder: 'Slet mappe',\n    folderName: 'Mappenavn',\n    folders: 'Mapper',\n    folderTypeDescription:\n      'Vælg hvilken type samling af dokumenter der bør være tilladt i denne mappe.',\n    itemHasBeenMoved: '{{title}} er blevet flyttet til {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} er blevet flyttet til rodmappen',\n    itemsMovedToFolder: '{{title}} flyttet til {{folderName}}',\n    itemsMovedToRoot: '{{title}} flyttet til rod-mappen',\n    moveFolder: 'Flyt Mappe',\n    moveItemsToFolderConfirmation:\n      'Du er ved at flytte <1>{{count}} {{label}}</1> til <2>{{toFolder}}</2>. Er du sikker?',\n    moveItemsToRootConfirmation:\n      'Du er ved at flytte <1>{{count}} {{label}}</1> til rodmappen. Er du sikker?',\n    moveItemToFolderConfirmation:\n      'Du er ved at flytte <1>{{title}}</1> til <2>{{toFolder}}</2>. Er du sikker?',\n    moveItemToRootConfirmation: 'Du er ved at flytte <1>{{title}}</1> til rodmappen. Er du sikker?',\n    movingFromFolder: 'Flytter {{title}} fra {{fromFolder}}',\n    newFolder: 'Ny Mappe',\n    noFolder: 'Ingen Mappe',\n    renameFolder: 'Omdøb mappe',\n    searchByNameInFolder: 'Søg efter Navn i {{folderName}}',\n    selectFolderForItem: 'Vælg mappe til {{title}}',\n  },\n  general: {\n    name: 'Navn',\n    aboutToDelete: 'Du er ved at slette {{label}} <1>{{title}}</1>. Er du sikker?',\n    aboutToDeleteCount_many: 'Du er ved at slette {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Du er ved at slette {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Du er ved at slette {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Du er ved at slette {{label}} <1>{{title}}</1> permanent. Er du sikker?',\n    aboutToPermanentlyDeleteTrash:\n      'Du er ved at slette <0>{{count}}</0> <1>{{label}}</1> permanent fra papirkurven. Er du sikker?',\n    aboutToRestore: 'Du er ved at gendanne {{label}} <1>{{title}}</1>. Er du sikker?',\n    aboutToRestoreAsDraft:\n      'Du er ved at gendanne {{label}} <1>{{title}}</1> som et udkast. Er du sikker?',\n    aboutToRestoreAsDraftCount: 'Du er ved at gendanne {{count}} {{label}} som udkast',\n    aboutToRestoreCount: 'Du er ved at gendanne {{count}} {{label}}',\n    aboutToTrash:\n      'Du er ved at flytte {{label}} <1>{{title}}</1> til skraldespanden. Er du sikker?',\n    aboutToTrashCount: 'Du er ved at flytte {{count}} {{label}} til skraldespanden',\n    addBelow: 'Tilføj under',\n    addFilter: 'Tilføj filter',\n    adminTheme: 'Admin tema',\n    all: 'Alle',\n    allCollections: 'Alle samlinger',\n    allLocales: 'Alle lokaliteter',\n    and: 'Og',\n    anotherUser: 'En anden bruger',\n    anotherUserTakenOver: 'En anden bruger har overtaget denne ressource.',\n    applyChanges: 'Tilføj ændringer',\n    ascending: 'Stigende',\n    automatic: 'Automatisk',\n    backToDashboard: 'Tilbage til dashboard',\n    cancel: 'Anuller',\n    changesNotSaved:\n      'Dine ændringer er ikke blevet gemt. Hvis du forlader siden, vil din ændringer gå tabt.',\n    clear: 'Klar',\n    clearAll: 'Ryd alt',\n    close: 'Luk',\n    collapse: 'Skjul',\n    collections: 'Samlinger',\n    columns: 'Kolonner',\n    columnToSort: 'Kolonne at sortere',\n    confirm: 'Bekræft',\n    confirmCopy: 'Bekræft kopi',\n    confirmDeletion: 'Bekræft sletning',\n    confirmDuplication: 'Bekræft duplikering',\n    confirmMove: 'Bekræft flytning',\n    confirmReindex: 'Genindeksér alle {{collections}}?',\n    confirmReindexAll: 'Genindeksér alle samlinger?',\n    confirmReindexDescription:\n      'Dette vil fjerne eksisterende indekser og genindeksere dokumenter i {{collections}}-samlingerne.',\n    confirmReindexDescriptionAll:\n      'Dette vil fjerne eksisterende indekser og genindeksere dokumenter i alle samlinger.',\n    confirmRestoration: 'Bekræft gendannelse',\n    copied: 'Kopieret',\n    copy: 'Kopier',\n    copyField: 'Kopiér felt',\n    copying: 'Kopiering',\n    copyRow: 'Kopiér række',\n    copyWarning:\n      'Du er lige ved at overskrive {{to}} med {{from}} for {{label}} {{title}}. Er du sikker?',\n    create: 'Opret',\n    created: 'Oprettet',\n    createdAt: 'Oprettet til',\n    createNew: 'Opret ny',\n    createNewLabel: 'Opret ny {{label}}',\n    creating: 'Opretter',\n    creatingNewLabel: 'Opretter ny {{label}}',\n    currentlyEditing: 'Du redigerer i øjeblikket',\n    custom: 'Tilpasset',\n    dark: 'Mørk',\n    dashboard: 'Dashboard',\n    delete: 'Slet',\n    deleted: 'Slettet',\n    deletedAt: 'Slettet Ved',\n    deletedCountSuccessfully: 'Slettet {{count}} {{label}}.',\n    deletedSuccessfully: 'Slettet.',\n    deletePermanently: 'Spring affald over og slet permanent',\n    deleting: 'Sletter...',\n    depth: 'Dybde',\n    descending: 'Faldende',\n    deselectAllRows: 'Fjern markering af alle rækker',\n    document: 'Dokument',\n    documentIsTrashed: 'Denne {{label}} er smidt væk og er kun til læsning.',\n    documentLocked: 'Dette dokument er låst',\n    documents: 'Dokumenter',\n    duplicate: 'Duplikér',\n    duplicateWithoutSaving: 'Dupliker uden at gemme ændringer',\n    edit: 'Redigere',\n    editAll: 'Rediger alle',\n    editedSince: 'Dette dokument er blevet redigeret siden du startede',\n    editing: 'Rediger',\n    editingLabel_many: 'Rediger {{count}} {{label}}',\n    editingLabel_one: 'Rediger {{count}} {{label}}',\n    editingLabel_other: 'Rediger {{count}} {{label}}',\n    editingTakenOver: 'En anden bruger har overtaget redigeringen af dette dokument',\n    editLabel: 'Redigere {{label}}',\n    email: 'Email',\n    emailAddress: 'e-mailadresse',\n    emptyTrash: 'Tøm skraldespanden',\n    emptyTrashLabel: 'Tøm {{label}} skraldespanden',\n    enterAValue: 'Indtast en værdi',\n    error: 'Fejl',\n    errors: 'Fejl',\n    exitLivePreview: 'Afslut Live Preview',\n    export: 'Eksport',\n    fallbackToDefaultLocale: 'Tilbagefald til standardlokalitet',\n    false: 'Falsk',\n    filter: 'Filter',\n    filters: 'Filtre',\n    filterWhere: 'Filter {{label}} hvor',\n    globals: 'Globale',\n    goBack: 'Gå tilbage',\n    groupByLabel: 'Gruppér efter {{label}}',\n    import: 'Import',\n    isEditing: 'redigerer',\n    item: 'vare',\n    items: 'elementer',\n    language: 'Sprog',\n    lastModified: 'Sidst ændret',\n    leaveAnyway: 'Forlad alligevel',\n    leaveWithoutSaving: 'Forlad uden at gemme',\n    light: 'Lys',\n    livePreview: 'Live-forhåndsvisning',\n    loading: 'Loader',\n    locale: 'Lokalitet',\n    locales: 'Lokaliteter',\n    menu: 'Menu',\n    moreOptions: 'Flere muligheder',\n    move: 'Flyt',\n    moveConfirm:\n      'Du er ved at flytte {{count}} {{label}} til <1>{{destination}}</1>. Er du sikker?',\n    moveCount: 'Flyt {{count}} {{label}}',\n    moveDown: 'Ryk ned',\n    moveUp: 'Ryk op',\n    moving: 'Flytter',\n    movingCount: 'Flytter {{count}} {{label}}',\n    newPassword: 'Ny adgangskode',\n    next: 'Næste',\n    no: 'Nej',\n    noDateSelected: 'Ingen dato valgt',\n    noFiltersSet: 'Ingen filtre angivet',\n    noLabel: '<Ingen {{label}}>',\n    none: 'Ingen',\n    noOptions: 'Ingen muligheder',\n    noResults:\n      'No {{label}} fundet. Enten findes der endnu ingen {{label}}, eller også matcher ingen af de filtre angivet ovenfor.',\n    notFound: 'Ikke fundet',\n    nothingFound: 'Intet fundet',\n    noTrashResults: 'Ingen {{label}} i papirkurven.',\n    noUpcomingEventsScheduled: 'Ingen kommende begivenheder planlagt.',\n    noValue: 'Ingen værdi',\n    of: 'Af',\n    only: 'kun',\n    open: 'Åben',\n    or: 'Eller',\n    order: 'Rækkefølge',\n    overwriteExistingData: 'Overskriv eksisterende feltdata',\n    pageNotFound: 'Siden blev ikke fundet',\n    password: 'Adgangskode',\n    pasteField: 'Indsæt felt',\n    pasteRow: 'Indsæt række',\n    payloadSettings: 'Payload-indstillinger',\n    permanentlyDelete: 'Permanent Sletning',\n    permanentlyDeletedCountSuccessfully: 'Permanent slettet {{count}} {{label}} succesfuldt.',\n    perPage: 'Per side: {{limit}}',\n    previous: 'Tidligere',\n    reindex: 'Genindekser',\n    reindexingAll: 'Genindekserer alle {{collections}}.',\n    remove: 'Fjern',\n    rename: 'Omdøb',\n    reset: 'Nulstil',\n    resetPreferences: 'Nulstil præferencer',\n    resetPreferencesDescription:\n      'Dette vil nulstille alle dine præferencer til standardindstillingerne.',\n    resettingPreferences: 'Nulstiller præferencer.',\n    restore: 'Gendan',\n    restoreAsPublished: 'Gendan som udgivet version',\n    restoredCountSuccessfully: 'Gendannede {{count}} {{label}} succesfuldt.',\n    restoring:\n      'Respekter betydningen af den originale tekst inden for konteksten Payload. Her er en liste over almindelige Payload-udtryk, der bærer meget specifikke betydninger:\\n    - Samling: En samling er en gruppe af dokumenter, der deler en fælles struktur og formål. Samlinger anvendes til at organisere og administrere indhold i Payload.\\n    - Felt: Et felt er et specifikt stykke data i et dokument i en samling. Felter definerer struktur og type af data, der kan gemmes i et dokument.\\n    - Dokument: Et dokument er en individuel post inden for',\n    row: 'Række',\n    rows: 'Rækker',\n    save: 'Gem',\n    saving: 'Gemmer...',\n    schedulePublishFor: 'Planlæg offentliggørelse for {{title}}',\n    searchBy: 'Søg efter {{label}}',\n    select: 'Vælg',\n    selectAll: 'Vælg alle {{count}} {{label}}',\n    selectAllRows: 'Vælg alle rækker',\n    selectedCount: '{{count}} {{label}} valgt',\n    selectLabel: 'Vælg {{label}}',\n    selectValue: 'Vælg en værdi',\n    showAllLabel: 'Vis alle {{label}}',\n    sorryNotFound: 'Beklager—der er intet, der svarer til din handling.',\n    sort: 'Sorter',\n    sortByLabelDirection: 'Sorter efter {{label}} {{direction}}',\n    stayOnThisPage: 'Forbliv på siden',\n    submissionSuccessful: 'Indsendt.',\n    submit: 'Send',\n    submitting: 'Sender...',\n    success: 'Succes',\n    successfullyCreated: '{{label}} oprettet.',\n    successfullyDuplicated: '{{label}} duplikeret.',\n    successfullyReindexed:\n      '{{count}} ud af {{total}} dokumenter fra {{collections}} samlinger blev genindekseret med succes.',\n    takeOver: 'Overtag',\n    thisLanguage: 'Dansk',\n    time: 'Tid',\n    timezone: 'Tidszone',\n    titleDeleted: '{{label}} \"{{title}}\" slettet.',\n    titleRestored: '{{label}} \"{{title}}\" succesfuldt genoprettet.',\n    titleTrashed: '{{label}} \"{{title}}\" flyttet til papirkurven.',\n    trash: 'Affald',\n    trashedCountSuccessfully: '{{count}} {{label}} flyttet til papirkurven.',\n    true: 'Sandt',\n    unauthorized: 'Uautoriseret',\n    unsavedChanges: 'Du har ikke gemte ændringer. Gem eller kassér før fortsættelse.',\n    unsavedChangesDuplicate: 'Du har ikke-gemte ændringer. Vil du fortsætte med at duplikere?',\n    untitled: 'Uden titel',\n    upcomingEvents: 'Kommende Begivenheder',\n    updatedAt: 'Opdateret ved',\n    updatedCountSuccessfully: 'Opdateret {{count}} {{label}} successfully.',\n    updatedLabelSuccessfully: 'Opdaterede {{label}} med succes.',\n    updatedSuccessfully: 'Opdateret.',\n    updateForEveryone: 'Opdatering for alle',\n    updating: 'Opdaterer',\n    uploading: 'Uploader',\n    uploadingBulk: 'Uploader {{current}} af {{total}}',\n    user: 'Bruger',\n    username: 'Brugernavn',\n    users: 'Brugere',\n    value: 'Værdi',\n    viewing: 'Visning',\n    viewReadOnly: 'Vis kun-læsning',\n    welcome: 'Velkommen',\n    yes: 'Ja',\n  },\n  localization: {\n    cannotCopySameLocale: 'Kan ikke kopiere til den samme lokalitet',\n    copyFrom: 'Kopier fra',\n    copyFromTo: 'Kopierer fra {{from}} til {{to}}',\n    copyTo: 'Kopier til',\n    copyToLocale: 'Kopier til lokal',\n    localeToPublish: 'Offentliggør på lokalitet',\n    selectLocaleToCopy: 'Vælg lokalitet til kopiering',\n  },\n  operators: {\n    contains: 'Indeholder',\n    equals: 'Lig med',\n    exists: 'Eksisterer',\n    intersects: 'Intersekterer',\n    isGreaterThan: 'Er større end',\n    isGreaterThanOrEqualTo: 'Er større end eller lig med',\n    isIn: 'Er i',\n    isLessThan: 'Er mindre end',\n    isLessThanOrEqualTo: 'Er mindre end eller lig med',\n    isLike: 'Ligner',\n    isNotEqualTo: 'Er ikke lig med',\n    isNotIn: 'Er ikke i',\n    isNotLike: 'er ikke som',\n    near: 'Tæt på',\n    within: 'Inden for',\n  },\n  upload: {\n    addFile: 'Tilføj fil',\n    addFiles: 'Tilføj Filer',\n    bulkUpload: 'Masseupload',\n    crop: 'Beskær',\n    cropToolDescription:\n      'Træk i hjørnerne af det valgte område, tegn et nyt område eller juster værdierne nedenfor.',\n    download: 'Download',\n    dragAndDrop: 'Træk og slip en fil',\n    dragAndDropHere: 'Eller træk og slip en fil her',\n    editImage: 'Rediger billede',\n    fileName: 'Filnavn',\n    fileSize: 'Filstørrelse',\n    filesToUpload: 'Filer til upload',\n    fileToUpload: 'Fil til upload',\n    focalPoint: 'Fokuspunkt',\n    focalPointDescription:\n      'Træk fokuspunktet direkte på forhåndsvisningen eller juster værdierne nedenfor.',\n    height: 'Højde',\n    lessInfo: 'Mindre info',\n    moreInfo: 'Mere info',\n    noFile: 'Ingen fil',\n    pasteURL: 'Indsæt URL',\n    previewSizes: 'Forhåndsvisningsstørrelser',\n    selectCollectionToBrowse: 'Vælg en samling for at browse',\n    selectFile: 'Vælg en fil',\n    setCropArea: 'Indstil beskæringsområde',\n    setFocalPoint: 'Indstil fokuspunkt',\n    sizes: 'Størrelse',\n    sizesFor: 'Størrelse for {{label}}',\n    width: 'Bredde',\n  },\n  validation: {\n    emailAddress: 'Indtast venligst en gyldig e-mailadresse.',\n    enterNumber: 'Indtast venligst et gyldigt nummer.',\n    fieldHasNo: 'Dette felt har ingen {{label}}',\n    greaterThanMax: '{{value}} er større end det maksimalt tilladte {{label}} of {{max}}.',\n    invalidInput: 'Dette felt har et ugyldigt indtastning.',\n    invalidSelection: 'Dette felt har en ugyldig valg.',\n    invalidSelections: 'Dette felt har følgende ugyldige valg:',\n    lessThanMin: '{{value}} er mindre end den minimum tilladte {{label}} of {{min}}.',\n    limitReached: 'Grænse nået, kun {{max}} elementer kan tilføjes.',\n    longerThanMin: 'Denne værdi skal være længere end den minimale længde på {{minLength}} tegn.',\n    notValidDate: '\"{{value}}\" er ikke en gyldig dato.',\n    required: 'Dette felt er påkrævet.',\n    requiresAtLeast: 'Dette felt kræver mindst {{count}} {{label}}.',\n    requiresNoMoreThan: 'Dette felt kræver maks {{count}} {{label}}.',\n    requiresTwoNumbers: 'Dette felt kræver to numre.',\n    shorterThanMax: 'Denne værdi skal være kortere end den maksimale længde af {{maxLength}} tegn.',\n    timezoneRequired: 'En tidszone er nødvendig.',\n    trueOrFalse: 'Denne værdi kan kun være lig med sandt eller falsk.',\n    username:\n      'Indtast et brugernavn. Kan indeholde bogstaver, tal, bindestreger, punktum og underscores.',\n    validUploadID: 'Dette felt er ikke en gyldig upload-ID.',\n  },\n  version: {\n    type: 'Type',\n    aboutToPublishSelection:\n      'Du er ved at offentliggøre alt {{label}} i denne sektion. Er du sikker?',\n    aboutToRestore:\n      'Du er ved at gendanne dette {{label}} dokument til den tilstand, det var i den {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Du er ved at gendanne den globale {{label}} til den tilstand, den var i den {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Du er ved at tilbagerulle dette dokuments ændringer til dets offentliggjorte tilstand. Er du sikker?',\n    aboutToUnpublish: 'Du er ved at afpublicere dette dokument. Er du sikker?',\n    aboutToUnpublishSelection:\n      'Du er ved at afpublicere alt {{label}} i denne sektion. Er du sikker?',\n    autosave: 'Autosave',\n    autosavedSuccessfully: 'Autosaved gennemført.',\n    autosavedVersion: 'Autosaved version',\n    changed: 'Ændret',\n    changedFieldsCount_one: '{{count}} ændret felt',\n    changedFieldsCount_other: '{{count}} ændrede felter',\n    compareVersion: 'Sammenlign version med:',\n    compareVersions: 'Sammenlign versioner',\n    comparingAgainst: 'Sammenligner med',\n    confirmPublish: 'Bekræft offentliggørelse',\n    confirmRevertToSaved: 'Bekræft tilbagerulning til gemt',\n    confirmUnpublish: 'Bekræft afpublicering',\n    confirmVersionRestoration: 'Bekræft versionens gendannelse',\n    currentDocumentStatus: 'Nuværende {{docStatus}} dokument',\n    currentDraft: 'Nuværende kladde',\n    currentlyPublished: 'Aktuelt Offentliggjort',\n    currentlyViewing: 'Aktuelt visning',\n    currentPublishedVersion: 'Nuværende offentliggjort version',\n    draft: 'Kladde',\n    draftSavedSuccessfully: 'Kladde gemt.',\n    lastSavedAgo: 'Sidst gemt {{distance}}',\n    modifiedOnly: 'Kun ændret',\n    moreVersions: 'Flere versioner...',\n    noFurtherVersionsFound: 'Ingen yderligere versioner fundet',\n    noRowsFound: 'Ingen {{label}} fundet',\n    noRowsSelected: 'Ingen {{label}} valgt',\n    preview: 'Forhåndsvisning',\n    previouslyDraft: 'Tidligere et udkast',\n    previouslyPublished: 'Tidligere offentliggjort',\n    previousVersion: 'Tidligere version',\n    problemRestoringVersion: 'Der opstod et problem med at gendanne denne version',\n    publish: 'Offentliggør',\n    publishAllLocales: 'Udgiv alle lokalindstillinger',\n    publishChanges: 'Offentliggør ændringer',\n    published: 'Offentliggjort',\n    publishIn: 'Offentliggør i',\n    publishing: 'Offentliggør',\n    restoreAsDraft: 'Gendan som kladde',\n    restoredSuccessfully: 'Gendannet.',\n    restoreThisVersion: 'Gendan denne version',\n    restoring: 'Gendanner...',\n    reverting: 'Tilbageruller...',\n    revertToPublished: 'Tilbagerul til offentliggjort',\n    saveDraft: 'Gem kladde',\n    scheduledSuccessfully: 'Planlagt med succes.',\n    schedulePublish: 'Planlæg offentliggørelse',\n    selectLocales: 'Vælg lokaliteter, der skal vises',\n    selectVersionToCompare: 'Vælg en version til sammenligning',\n    showingVersionsFor: 'Viser versioner for:',\n    showLocales: 'Vis lokaliteter:',\n    specificVersion: 'Specifik Version',\n    status: 'Status',\n    unpublish: 'Afpublicer',\n    unpublishing: 'Afpublicerer...',\n    version: 'Version',\n    versionAgo: '{{distance}} siden',\n    versionCount_many: '{{count}} versioner fundet',\n    versionCount_none: 'Ingen versioner fundet',\n    versionCount_one: '{{count}} version fundet',\n    versionCount_other: '{{count}} version fundet',\n    versionCreatedOn: '{{version}} oprettet den:',\n    versionID: 'Versions-ID',\n    versions: 'Versioner',\n    viewingVersion: 'Se versionen for {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Se version for det globale {{entityLabel}}',\n    viewingVersions: 'Se versioner for {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Se versioner for det global {{entityLabel}}',\n  },\n}\n\nexport const da: Language = {\n  dateFNSKey: 'da',\n  translations: daTranslations,\n}\n"], "names": ["daTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "da", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBAAkB;QAClBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BACE;QACFC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}