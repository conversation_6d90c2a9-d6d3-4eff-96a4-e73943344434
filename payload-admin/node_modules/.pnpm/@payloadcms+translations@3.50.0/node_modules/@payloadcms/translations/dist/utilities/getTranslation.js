export const getTranslation = (label, /**
   * @todo type as I18nClient in 4.0
   */ i18n)=>{
    // If it's a Record, look for translation. If string or React Element, pass through
    if (typeof label === 'object' && !Object.prototype.hasOwnProperty.call(label, '$$typeof')) {
        // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve
        if (label[i18n.language]) {
            // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve
            return label[i18n.language];
        }
        let fallbacks = [];
        if (typeof i18n.fallbackLanguage === 'string') {
            fallbacks = [
                i18n.fallbackLanguage
            ];
        } else if (Array.isArray(i18n.fallbackLanguage)) {
            fallbacks = i18n.fallbackLanguage;
        }
        const fallbackLang = fallbacks.find((language)=>label[language]);
        // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve
        return fallbackLang && label[fallbackLang] ? label[fallbackLang] : label[Object.keys(label)[0]];
    }
    if (typeof label === 'function') {
        return label({
            i18n: i18n,
            t: i18n.t
        });
    }
    // If it's a React Element or string, then we should just pass it through
    return label;
};

//# sourceMappingURL=getTranslation.js.map