{"version": 3, "sources": ["../../src/utilities/getTranslation.ts"], "sourcesContent": ["import type { JSX } from 'react'\n\nimport type { I18n, I18nClient, TFunction } from '../types.js'\n\ntype LabelType =\n  | (() => JSX.Element)\n  | ((args: { i18n: I18nClient; t: TFunction }) => string)\n  | JSX.Element\n  | Record<string, string>\n  | string\n\nexport const getTranslation = <T extends LabelType>(\n  label: T,\n  /**\n   * @todo type as I18nClient in 4.0\n   */\n  i18n: Pick<I18n<any, any>, 'fallbackLanguage' | 'language' | 't'>,\n): T extends JSX.Element ? JSX.Element : string => {\n  // If it's a Record, look for translation. If string or React Element, pass through\n  if (typeof label === 'object' && !Object.prototype.hasOwnProperty.call(label, '$$typeof')) {\n    // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n    if (label[i18n.language]) {\n      // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n      return label[i18n.language]\n    }\n\n    let fallbacks: string[] = []\n    if (typeof i18n.fallbackLanguage === 'string') {\n      fallbacks = [i18n.fallbackLanguage]\n    } else if (Array.isArray(i18n.fallbackLanguage)) {\n      fallbacks = i18n.fallbackLanguage\n    }\n\n    const fallbackLang = fallbacks.find((language) => label[language as keyof typeof label])\n\n    // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n    return fallbackLang && label[fallbackLang] ? label[fallbackLang] : label[Object.keys(label)[0]]\n  }\n\n  if (typeof label === 'function') {\n    return label({ i18n: i18n as I18nClient, t: i18n.t }) as unknown as T extends JSX.Element\n      ? JSX.Element\n      : string\n  }\n\n  // If it's a React Element or string, then we should just pass it through\n  return label as unknown as T extends JSX.Element ? JSX.Element : string\n}\n"], "names": ["getTranslation", "label", "i18n", "Object", "prototype", "hasOwnProperty", "call", "language", "fallbacks", "fallbackLanguage", "Array", "isArray", "fallback<PERSON><PERSON>", "find", "keys", "t"], "mappings": "AAWA,OAAO,MAAMA,iBAAiB,CAC5BC,OACA;;GAEC,GACDC;IAEA,mFAAmF;IACnF,IAAI,OAAOD,UAAU,YAAY,CAACE,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,OAAO,aAAa;QACzF,oFAAoF;QACpF,IAAIA,KAAK,CAACC,KAAKK,QAAQ,CAAC,EAAE;YACxB,oFAAoF;YACpF,OAAON,KAAK,CAACC,KAAKK,QAAQ,CAAC;QAC7B;QAEA,IAAIC,YAAsB,EAAE;QAC5B,IAAI,OAAON,KAAKO,gBAAgB,KAAK,UAAU;YAC7CD,YAAY;gBAACN,KAAKO,gBAAgB;aAAC;QACrC,OAAO,IAAIC,MAAMC,OAAO,CAACT,KAAKO,gBAAgB,GAAG;YAC/CD,YAAYN,KAAKO,gBAAgB;QACnC;QAEA,MAAMG,eAAeJ,UAAUK,IAAI,CAAC,CAACN,WAAaN,KAAK,CAACM,SAA+B;QAEvF,oFAAoF;QACpF,OAAOK,gBAAgBX,KAAK,CAACW,aAAa,GAAGX,KAAK,CAACW,aAAa,GAAGX,KAAK,CAACE,OAAOW,IAAI,CAACb,MAAM,CAAC,EAAE,CAAC;IACjG;IAEA,IAAI,OAAOA,UAAU,YAAY;QAC/B,OAAOA,MAAM;YAAEC,MAAMA;YAAoBa,GAAGb,KAAKa,CAAC;QAAC;IAGrD;IAEA,yEAAyE;IACzE,OAAOd;AACT,EAAC"}