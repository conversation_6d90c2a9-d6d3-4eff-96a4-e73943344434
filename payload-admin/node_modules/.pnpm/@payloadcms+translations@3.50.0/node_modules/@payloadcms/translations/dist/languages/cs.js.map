{"version": 3, "sources": ["../../src/languages/cs.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const csTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Účet',\n    accountOfCurrentUser: 'Účet současného uživatele',\n    accountVerified: 'Účet byl úspěšně ověřen.',\n    alreadyActivated: '<PERSON><PERSON> aktivo<PERSON>',\n    alreadyLoggedIn: '<PERSON><PERSON> přihlášen',\n    apiKey: 'API klíč',\n    authenticated: 'Ověřený',\n    backToLogin: 'Zpět na přihlášení',\n    beginCreateFirstUser: 'Začněte vytvořením svého prvního uživatele.',\n    changePassword: '<PERSON>měnit heslo',\n    checkYourEmailForPasswordReset:\n      'Pokud je e-mailová adresa spojena s účtem, brzy obdržíte pokyny k resetování vašeho hesla. Pokud e-mail nenajdete ve vaš<PERSON> do<PERSON> po<PERSON>t<PERSON>, zkontrolujte prosím složku se spamem nebo nevyžádanou poštou.',\n    confirmGeneration: 'Potvrdit generaci',\n    confirmPassword: 'Potvrdit heslo',\n    createFirstUser: 'Vytvořit prvního uživatele',\n    emailNotValid: 'Zadaný email není platný',\n    emailOrUsername: 'E-mail nebo Uživatelské jméno',\n    emailSent: 'Email odeslán',\n    emailVerified: 'E-mail úspěšně ověřen.',\n    enableAPIKey: 'Povolit API klíč',\n    failedToUnlock: 'Nepodařilo se odemknout',\n    forceUnlock: 'Vynutit odemčení',\n    forgotPassword: 'Zapomněli jste heslo?',\n    forgotPasswordEmailInstructions:\n      'Zadejte svůj email níže. Obdržíte email s instrukcemi, jak resetovat vaše heslo.',\n    forgotPasswordQuestion: 'Zapomněli jste heslo?',\n    forgotPasswordUsernameInstructions:\n      'Zadejte níže své uživatelské jméno. Instrukce, jak obnovit vaše heslo, budou odeslány na e-mailovou adresu spojenou s vaším uživatelským jménem.',\n    generate: 'Generovat',\n    generateNewAPIKey: 'Generovat nový API klíč',\n    generatingNewAPIKeyWillInvalidate:\n      'Vytvoření nového API klíče <1>zneplatní</1> předchozí klíč. Opravdu chcete pokračovat?',\n    lockUntil: 'Uzamknout do',\n    logBackIn: 'Znovu se přihlásit',\n    loggedIn: 'Abyste se mohli přihlásit s jiným uživatelem, nejdříve se <0>odhlaste</0>.',\n    loggedInChangePassword: 'Pro změnu hesla přejděte do svého <0>účtu</0> a zde si heslo upravte.',\n    loggedOutInactivity: 'Byli jste odhlášeni z důvodu neaktivity.',\n    loggedOutSuccessfully: 'Byli jste úspěšně odhlášeni.',\n    loggingOut: 'Odhlášení...',\n    login: 'Přihlásit se',\n    loginAttempts: 'Pokusy o přihlášení',\n    loginUser: 'Přihlásit uživatele',\n    loginWithAnotherUser:\n      'Abyste se mohli přihlásit s jiným uživatelem, nejdříve se <0>odhlaste</0>.',\n    logOut: 'Odhlásit se',\n    logout: 'Odhlásit se',\n    logoutSuccessful: 'Odhlášení úspěšné.',\n    logoutUser: 'Odhlásit uživatele',\n    newAccountCreated:\n      'Pro přístup k <a href=\"{{serverURL}}\">{{serverURL}}</a> byl pro vás vytvořen nový účet. Klepněte na následující odkaz nebo zkopírujte URL do svého prohlížeče pro ověření vašeho emailu: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Po ověření vašeho emailu se budete moci úspěšně přihlásit.',\n    newAPIKeyGenerated: 'Byl vygenerován nový API klíč.',\n    newPassword: 'Nové heslo',\n    passed: 'Ověření prošlo',\n    passwordResetSuccessfully: 'Heslo bylo úspěšně resetováno.',\n    resetPassword: 'Resetovat heslo',\n    resetPasswordExpiration: 'Expirace resetování hesla',\n    resetPasswordToken: 'Token pro resetování hesla',\n    resetYourPassword: 'Resetujte své heslo',\n    stayLoggedIn: 'Zůstat přihlášen',\n    successfullyRegisteredFirstUser: 'Úspěšně zaregistrován první uživatel.',\n    successfullyUnlocked: 'Úspěšně odemčeno',\n    tokenRefreshSuccessful: 'Obnovení tokenu úspěšné.',\n    unableToVerify: 'Nepodařilo se ověřit',\n    username: 'Uživatelské jméno',\n    usernameNotValid: 'Poskytnuté uživatelské jméno není platné.',\n    verified: 'Ověřeno',\n    verifiedSuccessfully: 'Úspěšně ověřeno',\n    verify: 'Ověřit',\n    verifyUser: 'Ověřit uživatele',\n    verifyYourEmail: 'Ověřte svůj email',\n    youAreInactive:\n      'Nějakou dobu jste nebyli aktivní a brzy budete automaticky odhlášeni z bezpečnostních důvodů. Chcete zůstat přihlášen?',\n    youAreReceivingResetPassword:\n      'Tento email obdržíte, protože jste (nebo někdo jiný) požádali o resetování hesla pro váš účet.',\n    youDidNotRequestPassword:\n      'Pokud jste o to nepožádali, ignorujte prosím tento e-mail a vaše heslo zůstane nezměněno.',\n  },\n  error: {\n    accountAlreadyActivated: 'Tento účet již byl aktivován.',\n    autosaving: 'Při automatickém ukládání tohoto dokumentu došlo k chybě.',\n    correctInvalidFields: 'Opravte neplatná pole.',\n    deletingFile: 'Při mazání souboru došlo k chybě.',\n    deletingTitle:\n      'Při mazání {{title}} došlo k chybě. Zkontrolujte své připojení a zkuste to znovu.',\n    documentNotFound:\n      'Dokument s ID {{id}} nebyl nalezen. Mohlo být smazáno nebo nikdy neexistovalo, nebo k němu nemáte přístup.',\n    emailOrPasswordIncorrect: 'Zadaný email nebo heslo není správné.',\n    followingFieldsInvalid_one: 'Následující pole je neplatné:',\n    followingFieldsInvalid_other: 'Následující pole jsou neplatná:',\n    incorrectCollection: 'Nesprávná kolekce',\n    insufficientClipboardPermissions:\n      'Přístup ke schránce byl odepřen. Zkontrolujte oprávnění ke schránce.',\n    invalidClipboardData: 'Neplatná data ve schránce.',\n    invalidFileType: 'Neplatný typ souboru',\n    invalidFileTypeValue: 'Neplatný typ souboru: {{value}}',\n    invalidRequestArgs: 'Neplatné argumenty v požadavku: {{args}}',\n    loadingDocument: 'Při načítání dokumentu s ID {{id}} došlo k chybě.',\n    localesNotSaved_one: 'Následující lokalitu se nepodařilo uložit:',\n    localesNotSaved_other: 'Následující lokality se nepodařilo uložit:',\n    logoutFailed: 'Odhlášení se nezdařilo.',\n    missingEmail: 'Chybějící email.',\n    missingIDOfDocument: 'Chybějící ID dokumentu pro aktualizaci.',\n    missingIDOfVersion: 'Chybějící ID verze.',\n    missingRequiredData: 'Chybějící povinná data.',\n    noFilesUploaded: 'Nebyly nahrány žádné soubory.',\n    noMatchedField: 'Pro \"{{label}}\" nebylo nalezeno žádné odpovídající pole',\n    notAllowedToAccessPage: 'Nemáte povolení přistupovat k této stránce.',\n    notAllowedToPerformAction: 'Nemáte povolení provádět tuto akci.',\n    notFound: 'Požadovaný zdroj nebyl nalezen.',\n    noUser: 'Žádný uživatel',\n    previewing: 'Při náhledu tohoto dokumentu došlo k chybě.',\n    problemUploadingFile: 'Při nahrávání souboru došlo k chybě.',\n    restoringTitle:\n      'Došlo k chybě při obnovování {{title}}. Zkontrolujte prosím své připojení a zkuste to znovu.',\n    tokenInvalidOrExpired: 'Token je neplatný nebo vypršel.',\n    tokenNotProvided: 'Token není poskytnut.',\n    unableToCopy: 'Nelze zkopírovat.',\n    unableToDeleteCount: 'Nelze smazat {{count}} z {{total}} {{label}}',\n    unableToReindexCollection:\n      'Chyba při přeindexování kolekce {{collection}}. Operace byla přerušena.',\n    unableToUpdateCount: 'Nelze aktualizovat {{count}} z {{total}} {{label}}.',\n    unauthorized: 'Neautorizováno, pro zadání tohoto požadavku musíte být přihlášeni.',\n    unauthorizedAdmin: 'Neautorizováno, tento uživatel nemá přístup k administraci.',\n    unknown: 'Došlo k neznámé chybě.',\n    unPublishingDocument: 'Při zrušení publikování tohoto dokumentu došlo k chybě.',\n    unspecific: 'Došlo k chybě.',\n    unverifiedEmail: 'Před přihlášením ověřte svůj e-mail.',\n    userEmailAlreadyRegistered: 'Uživatel s daným e-mailem je již zaregistrován.',\n    userLocked: 'Tento uživatel je uzamčen kvůli příliš mnoha neúspěšným pokusům o přihlášení.',\n    usernameAlreadyRegistered: 'Uživatel se zadaným uživatelským jménem je již zaregistrován.',\n    usernameOrPasswordIncorrect: 'Zadané uživatelské jméno nebo heslo je nesprávné.',\n    valueMustBeUnique: 'Hodnota musí být jedinečná',\n    verificationTokenInvalid: 'Ověřovací token je neplatný.',\n  },\n  fields: {\n    addLabel: 'Přidat {{label}}',\n    addLink: 'Přidat odkaz',\n    addNew: 'Přidat nový',\n    addNewLabel: 'Přidat nový {{label}}',\n    addRelationship: 'Přidat vztah',\n    addUpload: 'Přidat nahrávání',\n    block: 'blok',\n    blocks: 'bloky',\n    blockType: 'Typ bloku',\n    chooseBetweenCustomTextOrDocument:\n      'Zvolte mezi vložením vlastního textového URL nebo odkazováním na jiný dokument.',\n    chooseDocumentToLink: 'Vyberte dokument, na který se chcete odkázat',\n    chooseFromExisting: 'Vybrat z existujících',\n    chooseLabel: 'Vybrat {{label}}',\n    collapseAll: 'Sbalit vše',\n    customURL: 'Vlastní URL',\n    editLabelData: 'Upravit data {{label}}',\n    editLink: 'Upravit odkaz',\n    editRelationship: 'Upravit vztah',\n    enterURL: 'Zadejte URL',\n    internalLink: 'Interní odkaz',\n    itemsAndMore: '{{items}} a {{count}} dalších',\n    labelRelationship: 'Vztah {{label}}',\n    latitude: 'Zeměpisná šířka',\n    linkedTo: 'Odkaz na <0>{{label}}</0>',\n    linkType: 'Typ odkazu',\n    longitude: 'Zeměpisná délka',\n    newLabel: 'Nový {{label}}',\n    openInNewTab: 'Otevřít v nové záložce',\n    passwordsDoNotMatch: 'Hesla se neshodují.',\n    relatedDocument: 'Související dokument',\n    relationTo: 'Vztah k',\n    removeRelationship: 'Odstranit vztah',\n    removeUpload: 'Odstranit nahrání',\n    saveChanges: 'Uložit změny',\n    searchForBlock: 'Hledat blok',\n    selectExistingLabel: 'Vybrat existující {{label}}',\n    selectFieldsToEdit: 'Vyberte pole, která chcete upravit',\n    showAll: 'Zobrazit vše',\n    swapRelationship: 'Zaměnit vztah',\n    swapUpload: 'Vyměnit nahrání',\n    textToDisplay: 'Text k zobrazení',\n    toggleBlock: 'Přepnout blok',\n    uploadNewLabel: 'Nahrát nový {{label}}',\n  },\n  folder: {\n    browseByFolder: 'Procházet podle složek',\n    byFolder: 'Podle složky',\n    deleteFolder: 'Smazat složku',\n    folderName: 'Název složky',\n    folders: 'Složky',\n    folderTypeDescription:\n      'Vyberte, který typ dokumentů ze sbírky by měl být dovolen v této složce.',\n    itemHasBeenMoved: '{{title}} bylo přesunuto do {{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}} byl přesunut do kořenové složky',\n    itemsMovedToFolder: '{{title}} přesunuto do {{folderName}}',\n    itemsMovedToRoot: '{{title}} byl přesunut do kořenové složky',\n    moveFolder: 'Přesunout složku',\n    moveItemsToFolderConfirmation:\n      'Chystáte se přesunout <1>{{count}} {{label}}</1> do <2>{{toFolder}}</2>. Jste si jistý?',\n    moveItemsToRootConfirmation:\n      'Chystáte se přesunout <1>{{count}} {{label}}</1> do kořenové složky. Jste si jisti?',\n    moveItemToFolderConfirmation:\n      'Chystáte se přesunout <1>{{title}}</1> do <2>{{toFolder}}</2>. Jste si jistý?',\n    moveItemToRootConfirmation:\n      'Chystáte se přesunout <1>{{title}}</1> do kořenové složky. Jste si jistý?',\n    movingFromFolder: 'Přesunutí {{title}} z {{fromFolder}}',\n    newFolder: 'Nová složka',\n    noFolder: 'Žádná složka',\n    renameFolder: 'Přejmenovat složku',\n    searchByNameInFolder: 'Vyhledat podle jména v {{folderName}}',\n    selectFolderForItem: 'Vyberte složku pro {{title}}',\n  },\n  general: {\n    name: 'Jméno',\n    aboutToDelete: 'Chystáte se odstranit {{label}} <1>{{title}}</1>. Jste si jisti?',\n    aboutToDeleteCount_many: 'Chystáte se smazat {{count}} {{label}}',\n    aboutToDeleteCount_one: 'Chystáte se smazat {{count}} {{label}}',\n    aboutToDeleteCount_other: 'Chystáte se smazat {{count}} {{label}}',\n    aboutToPermanentlyDelete:\n      'Chystáte se trvale odstranit {{label}} <1>{{title}}</1>. Jste si jistý?',\n    aboutToPermanentlyDeleteTrash:\n      'Chystáte se trvale smazat <0>{{count}}</0> <1>{{label}}</1> z koše. Jste si jistý?',\n    aboutToRestore: 'Chystáte se obnovit {{label}} <1>{{title}}</1>. Jste si jistý?',\n    aboutToRestoreAsDraft:\n      'Chystáte se obnovit {{label}} <1>{{title}}</1> jako koncept. Jste si jistý?',\n    aboutToRestoreAsDraftCount: 'Chystáte se obnovit {{count}} {{label}} jako koncept',\n    aboutToRestoreCount: 'Chystáte se obnovit {{count}} {{label}}',\n    aboutToTrash: 'Chystáte se přesunout {{label}} <1>{{title}}</1> do koše. Jste si jisti?',\n    aboutToTrashCount: 'Chystáte se přesunout {{count}} {{label}} do koše',\n    addBelow: 'Přidat pod',\n    addFilter: 'Přidat filtr',\n    adminTheme: 'Motiv administračního rozhraní',\n    all: 'Všechny',\n    allCollections: 'Všechny kolekce',\n    allLocales: 'Všechny lokalizace',\n    and: 'a',\n    anotherUser: 'Jiný uživatel',\n    anotherUserTakenOver: 'Jiný uživatel převzal úpravy tohoto dokumentu.',\n    applyChanges: 'Použít změny',\n    ascending: 'Vzestupně',\n    automatic: 'Automatický',\n    backToDashboard: 'Zpět na nástěnku',\n    cancel: 'Zrušit',\n    changesNotSaved: 'Vaše změny nebyly uloženy. Pokud teď odejdete, ztratíte své změny.',\n    clear: 'Jasný',\n    clearAll: 'Vymazat vše',\n    close: 'Zavřít',\n    collapse: 'Sbalit',\n    collections: 'Kolekce',\n    columns: 'Sloupce',\n    columnToSort: 'Sloupec k seřazení',\n    confirm: 'Potvrdit',\n    confirmCopy: 'Potvrzení kopie',\n    confirmDeletion: 'Potvrdit odstranění',\n    confirmDuplication: 'Potvrdit duplikaci',\n    confirmMove: 'Potvrdit přesun',\n    confirmReindex: 'Přeindexovat všechny {{collections}}?',\n    confirmReindexAll: 'Přeindexovat všechny kolekce?',\n    confirmReindexDescription:\n      'Tímto budou odstraněny stávající indexy a dokumenty v kolekcích {{collections}} budou znovu zaindexovány.',\n    confirmReindexDescriptionAll:\n      'Tímto budou odstraněny stávající indexy a dokumenty ve všech kolekcích budou znovu zaindexovány.',\n    confirmRestoration: 'Potvrdit obnovení',\n    copied: 'Zkopírováno',\n    copy: 'Kopírovat',\n    copyField: 'Kopírovat pole',\n    copying: 'Kopírování',\n    copyRow: 'Kopírovat řádek',\n    copyWarning: 'Chystáte se přepsat {{to}} s {{from}} pro {{label}} {{title}}. Jste si jistý?',\n    create: 'Vytvořit',\n    created: 'Vytvořeno',\n    createdAt: 'Vytvořeno v',\n    createNew: 'Vytvořit nové',\n    createNewLabel: 'Vytvořit nový {{label}}',\n    creating: 'Vytváření',\n    creatingNewLabel: 'Vytváření nového {{label}}',\n    currentlyEditing:\n      'právě upravuje tento dokument. Pokud převezmete kontrolu, budou zablokováni v pokračování úprav a mohou také přijít o neuložené změny.',\n    custom: 'Vlastní',\n    dark: 'Tmavý',\n    dashboard: 'Nástěnka',\n    delete: 'Odstranit',\n    deleted: 'Smazáno',\n    deletedAt: 'Smazáno dne',\n    deletedCountSuccessfully: 'Úspěšně smazáno {{count}} {{label}}.',\n    deletedSuccessfully: 'Úspěšně odstraněno.',\n    deletePermanently: 'Preskočit koš a smazat trvale',\n    deleting: 'Odstraňování...',\n    depth: 'Hloubka',\n    descending: 'Sestupně',\n    deselectAllRows: 'Zrušte výběr všech řádků',\n    document: 'Dokument',\n    documentIsTrashed: 'Tento {{label}} je v koši a je pouze pro čtení.',\n    documentLocked: 'Dokument je uzamčen',\n    documents: 'Dokumenty',\n    duplicate: 'Duplikovat',\n    duplicateWithoutSaving: 'Duplikovat bez uložení změn',\n    edit: 'Upravit',\n    editAll: 'Upravit vše',\n    editedSince: 'Upraveno od',\n    editing: 'Úprava',\n    editingLabel_many: 'Úprava {{count}} {{label}}',\n    editingLabel_one: 'Úprava {{count}} {{label}}',\n    editingLabel_other: 'Úprava {{count}} {{label}}',\n    editingTakenOver: 'Úpravy byly převzaty',\n    editLabel: 'Upravit {{label}}',\n    email: 'E-mail',\n    emailAddress: 'E-mailová adresa',\n    emptyTrash: 'Vyprázdnit koš',\n    emptyTrashLabel: 'Vyprázdnit {{label}} koš',\n    enterAValue: 'Zadejte hodnotu',\n    error: 'Chyba',\n    errors: 'Chyby',\n    exitLivePreview: 'Opustit živý náhled',\n    export: 'Vývoz',\n    fallbackToDefaultLocale: 'Zpětné přepnutí do výchozího locale',\n    false: 'Nepravda',\n    filter: 'Filtr',\n    filters: 'Filtry',\n    filterWhere: 'Filtrovat {{label}} kde',\n    globals: 'Globální',\n    goBack: 'Vrátit se',\n    groupByLabel: 'Seskupit podle {{label}}',\n    import: 'Import',\n    isEditing: 'upravuje',\n    item: 'položka',\n    items: 'položky',\n    language: 'Jazyk',\n    lastModified: 'Naposledy změněno',\n    leaveAnyway: 'Přesto odejít',\n    leaveWithoutSaving: 'Odejít bez uložení',\n    light: 'Světlé',\n    livePreview: 'Náhled',\n    loading: 'Načítání',\n    locale: 'Místní verze',\n    locales: 'Lokality',\n    menu: 'Jídelní lístek',\n    moreOptions: 'Více možností',\n    move: 'Pohnout',\n    moveConfirm:\n      'Chystáte se přesunout {{count}} {{label}} do <1>{{destination}}</1>. Jste si jisti?',\n    moveCount: 'Přesuňte {{count}} {{label}}',\n    moveDown: 'Posunout dolů',\n    moveUp: 'Posunout nahoru',\n    moving: 'Přesun',\n    movingCount: 'Přesunout {{count}} {{label}}',\n    newPassword: 'Nové heslo',\n    next: 'Další',\n    no: 'Ne',\n    noDateSelected: 'Nebylo vybráno žádné datum',\n    noFiltersSet: 'Nenastaveny žádné filtry',\n    noLabel: '<Žádný {{label}}>',\n    none: 'Žádné',\n    noOptions: 'Žádné možnosti',\n    noResults:\n      'Nebyly nalezeny žádné {{label}}. Buď ještě neexistují žádné {{label}}, nebo žádné nesplňují filtry, které jste zadali výše.',\n    notFound: 'Nenalezeno',\n    nothingFound: 'Nic nenalezeno',\n    noTrashResults: 'Žádný {{label}} v koši.',\n    noUpcomingEventsScheduled: 'Žádné nadcházející události nejsou naplánovány.',\n    noValue: 'Žádná hodnota',\n    of: 'z',\n    only: 'Pouze',\n    open: 'Otevřít',\n    or: 'Nebo',\n    order: 'Pořadí',\n    overwriteExistingData: 'Přepsat existující data pole',\n    pageNotFound: 'Stránka nenalezena',\n    password: 'Heslo',\n    pasteField: 'Vložit pole',\n    pasteRow: 'Vložit řádek',\n    payloadSettings: 'Payload nastavení',\n    permanentlyDelete: 'Trvale smazat',\n    permanentlyDeletedCountSuccessfully: 'Trvale odstraněno {{count}} {{label}} úspěšně.',\n    perPage: 'Na stránku: {{limit}}',\n    previous: 'Předchozí',\n    reindex: 'Přeindexovat',\n    reindexingAll: 'Přeindexování všech {{collections}}.',\n    remove: 'Odstranit',\n    rename: 'Přejmenovat',\n    reset: 'Resetovat',\n    resetPreferences: 'Obnovit nastavení',\n    resetPreferencesDescription: 'Toto obnoví všechna vaše nastavení na výchozí hodnoty.',\n    resettingPreferences: 'Obnovování nastavení.',\n    restore: 'Obnovit',\n    restoreAsPublished: 'Obnovit jako publikovanou verzi',\n    restoredCountSuccessfully: 'Úspěšně obnoveno {{count}} {{label}}.',\n    restoring:\n      'Respektujte význam původního textu v kontextu Payload. Zde je seznam běžných termínů Payload, které nesou velmi specifické významy:\\n    - Collection: Sbírka je skupina dokumentů, které sdílejí společnou strukturu a účel. Sbírky se používají k organizaci a správě obsahu v Payload.\\n    - Field: Field je specifický prvek dat v dokumentu ve sbírce. Field definuje strukturu a typ dat, které mohou',\n    row: 'Řádek',\n    rows: 'Řádky',\n    save: 'Uložit',\n    saving: 'Ukládání...',\n    schedulePublishFor: 'Naplánovat publikaci pro {{title}}',\n    searchBy: 'Vyhledat podle {{label}}',\n    select: 'Vyberte',\n    selectAll: 'Vybrat vše {{count}} {{label}}',\n    selectAllRows: 'Vyberte všechny řádky',\n    selectedCount: 'Vybráno {{count}} {{label}}',\n    selectLabel: 'Vyberte {{label}}',\n    selectValue: 'Vyberte hodnotu',\n    showAllLabel: 'Zobrazit všechny {{label}}',\n    sorryNotFound: 'Je nám líto, ale neexistuje nic, co by odpovídalo vašemu požadavku.',\n    sort: 'Třídit',\n    sortByLabelDirection: 'Seřadit podle {{label}} {{direction}}',\n    stayOnThisPage: 'Zůstat na této stránce',\n    submissionSuccessful: 'Odeslání úspěšné.',\n    submit: 'Odeslat',\n    submitting: 'Odesílání...',\n    success: 'Úspěch',\n    successfullyCreated: '{{label}} úspěšně vytvořeno.',\n    successfullyDuplicated: '{{label}} úspěšně duplikováno.',\n    successfullyReindexed:\n      'Úspěšně přeindexováno {{count}} z {{total}} dokumentů z {{collections}} kolekcí.',\n    takeOver: 'Převzít',\n    thisLanguage: 'Čeština',\n    time: 'Čas',\n    timezone: 'Časové pásmo',\n    titleDeleted: '{{label}} \"{{title}}\" úspěšně smazáno.',\n    titleRestored: '{{label}} \"{{title}}\" úspěšně obnoveno.',\n    titleTrashed: '{{label}} \"{{title}}\" přesunuto do koše.',\n    trash: 'Koš',\n    trashedCountSuccessfully: '{{count}} {{label}} přesunuto do koše.',\n    true: 'Pravda',\n    unauthorized: 'Neoprávněný',\n    unsavedChanges: 'Máte neuložené změny. Uložte nebo zahoďte před pokračováním.',\n    unsavedChangesDuplicate: 'Máte neuložené změny. Chtěli byste pokračovat v duplikování?',\n    untitled: 'Bez názvu',\n    upcomingEvents: 'Nadcházející události',\n    updatedAt: 'Aktualizováno v',\n    updatedCountSuccessfully: 'Úspěšně aktualizováno {{count}} {{label}}.',\n    updatedLabelSuccessfully: 'Úspěšně aktualizovaný {{label}}.',\n    updatedSuccessfully: 'Úspěšně aktualizováno.',\n    updateForEveryone: 'Aktualizace pro všechny',\n    updating: 'Aktualizace',\n    uploading: 'Nahrávání',\n    uploadingBulk: 'Nahrávání {{current}} z {{total}}',\n    user: 'Uživatel',\n    username: 'Uživatelské jméno',\n    users: 'Uživatelé',\n    value: 'Hodnota',\n    viewing: 'Prohlížení',\n    viewReadOnly: 'Zobrazit pouze pro čtení',\n    welcome: 'Vítejte',\n    yes: 'Ano',\n  },\n  localization: {\n    cannotCopySameLocale: 'Nelze kopírovat do stejného umístění',\n    copyFrom: 'Kopírovat z',\n    copyFromTo: 'Kopírování z {{from}} do {{to}}',\n    copyTo: 'Kopírovat do',\n    copyToLocale: 'Kopírovat do lokalizace',\n    localeToPublish: 'Místo k publikování',\n    selectLocaleToCopy: 'Vyberte lokalitu ke kopírování',\n  },\n  operators: {\n    contains: 'obsahuje',\n    equals: 'rovná se',\n    exists: 'existuje',\n    intersects: 'protíná se',\n    isGreaterThan: 'je větší než',\n    isGreaterThanOrEqualTo: 'je větší nebo rovno',\n    isIn: 'je v',\n    isLessThan: 'je menší než',\n    isLessThanOrEqualTo: 'je menší nebo rovno',\n    isLike: 'je jako',\n    isNotEqualTo: 'není rovno',\n    isNotIn: 'není v',\n    isNotLike: 'není jako',\n    near: 'blízko',\n    within: 'uvnitř',\n  },\n  upload: {\n    addFile: 'Přidat soubor',\n    addFiles: 'Přidat soubory',\n    bulkUpload: 'Hromadné nahrání',\n    crop: 'Ořez',\n    cropToolDescription:\n      'Přetáhněte rohy vybrané oblasti, nakreslete novou oblast nebo upravte níže uvedené hodnoty.',\n    download: 'Stáhnout',\n    dragAndDrop: 'Přetáhněte soubor',\n    dragAndDropHere: 'nebo sem přetáhněte soubor',\n    editImage: 'Upravit obrázek',\n    fileName: 'Název souboru',\n    fileSize: 'Velikost souboru',\n    filesToUpload: 'Soubory k nahrání',\n    fileToUpload: 'Soubor k nahrání',\n    focalPoint: 'Středobod',\n    focalPointDescription:\n      'Přetáhněte bod zaměření přímo na náhled nebo upravte níže uvedené hodnoty.',\n    height: 'Výška',\n    lessInfo: 'Méně informací',\n    moreInfo: 'Více informací',\n    noFile: 'Žádný soubor',\n    pasteURL: 'Vložit URL',\n    previewSizes: 'Náhled velikostí',\n    selectCollectionToBrowse: 'Vyberte kolekci pro procházení',\n    selectFile: 'Vyberte soubor',\n    setCropArea: 'Nastavit oblast ořezu',\n    setFocalPoint: 'Nastavit středobod',\n    sizes: 'Velikosti',\n    sizesFor: 'Velikosti pro {{label}}',\n    width: 'Šířka',\n  },\n  validation: {\n    emailAddress: 'Zadejte prosím platnou e-mailovou adresu.',\n    enterNumber: 'Zadejte prosím platné číslo.',\n    fieldHasNo: 'Toto pole nemá {{label}}',\n    greaterThanMax: '{{value}} je vyšší než maximálně povolená {{label}} {{max}}.',\n    invalidInput: 'Toto pole má neplatný vstup.',\n    invalidSelection: 'Toto pole má neplatný výběr.',\n    invalidSelections: 'Toto pole má následující neplatné výběry:',\n    lessThanMin: '{{value}} je nižší než minimálně povolená {{label}} {{min}}.',\n    limitReached: 'Dosáhnutý limit, mohou být přidány pouze {{max}} položky.',\n    longerThanMin: 'Tato hodnota musí být delší než minimální délka {{minLength}} znaků.',\n    notValidDate: '\"{{value}}\" není platné datum.',\n    required: 'Toto pole je povinné.',\n    requiresAtLeast: 'Toto pole vyžaduje alespoň {{count}} {{label}}.',\n    requiresNoMoreThan: 'Toto pole vyžaduje ne více než {{count}} {{label}}.',\n    requiresTwoNumbers: 'Toto pole vyžaduje dvě čísla.',\n    shorterThanMax: 'Tato hodnota musí být kratší než maximální délka {{maxLength}} znaků.',\n    timezoneRequired: 'Je vyžadováno časové pásmo.',\n    trueOrFalse: 'Toto pole může být rovno pouze true nebo false.',\n    username:\n      'Prosím, zadejte platné uživatelské jméno. Může obsahovat písmena, čísla, pomlčky, tečky a podtržítka.',\n    validUploadID: 'Toto pole není platné ID pro odeslání.',\n  },\n  version: {\n    type: 'Typ',\n    aboutToPublishSelection: 'Chystáte se publikovat všechny {{label}} ve výběru. Jsi si jistá?',\n    aboutToRestore:\n      'Chystáte se obnovit tento {{label}} dokument do stavu, v jakém byl {{versionDate}}.',\n    aboutToRestoreGlobal:\n      'Chystáte se obnovit globální {{label}} do stavu, v jakém byl {{versionDate}}.',\n    aboutToRevertToPublished:\n      'Chystáte se vrátit změny tohoto dokumentu do jeho publikovaného stavu. Jste si jisti?',\n    aboutToUnpublish: 'Chystáte se zrušit publikování tohoto dokumentu. Jste si jisti?',\n    aboutToUnpublishSelection:\n      'Chystáte se zrušit publikování všech {{label}} ve výběru. Jsi si jistá?',\n    autosave: 'Automatické uložení',\n    autosavedSuccessfully: 'Úspěšně uloženo automaticky.',\n    autosavedVersion: 'Verze automatického uložení',\n    changed: 'Změněno',\n    changedFieldsCount_one: '{{count}} změněné pole',\n    changedFieldsCount_other: '{{count}} změněná pole',\n    compareVersion: 'Porovnat verzi s:',\n    compareVersions: 'Porovnat verze',\n    comparingAgainst: 'Porovnání s',\n    confirmPublish: 'Potvrďte publikování',\n    confirmRevertToSaved: 'Potvrdit vrácení k uloženému',\n    confirmUnpublish: 'Potvrdit zrušení publikování',\n    confirmVersionRestoration: 'Potvrdit obnovení verze',\n    currentDocumentStatus: 'Současný {{docStatus}} dokument',\n    currentDraft: 'Současný koncept',\n    currentlyPublished: 'Aktuálně publikováno',\n    currentlyViewing: 'Aktuálně prohlížíte',\n    currentPublishedVersion: 'Aktuálně publikovaná verze',\n    draft: 'Koncept',\n    draftSavedSuccessfully: 'Koncept úspěšně uložen.',\n    lastSavedAgo: 'Naposledy uloženo před {{distance}}',\n    modifiedOnly: 'Pouze upraveno',\n    moreVersions: 'Více verzí...',\n    noFurtherVersionsFound: 'Nenalezeny další verze',\n    noRowsFound: 'Nenalezen {{label}}',\n    noRowsSelected: 'Nebyl vybrán žádný {{label}}',\n    preview: 'Náhled',\n    previouslyDraft: 'Dříve Koncept',\n    previouslyPublished: 'Dříve publikováno',\n    previousVersion: 'Předchozí verze',\n    problemRestoringVersion: 'Při obnovování této verze došlo k problému',\n    publish: 'Publikovat',\n    publishAllLocales: 'Publikujte všechny lokalizace',\n    publishChanges: 'Publikovat změny',\n    published: 'Publikováno',\n    publishIn: 'Publikovat v {{locale}}',\n    publishing: 'Publikování',\n    restoreAsDraft: 'Obnovit jako koncept',\n    restoredSuccessfully: 'Úspěšně obnoveno.',\n    restoreThisVersion: 'Obnovit tuto verzi',\n    restoring: 'Obnovování...',\n    reverting: 'Vracení...',\n    revertToPublished: 'Vrátit se k publikovanému',\n    saveDraft: 'Uložit koncept',\n    scheduledSuccessfully: 'Úspěšně naplánováno.',\n    schedulePublish: 'Naplánovat publikaci',\n    selectLocales: 'Vyberte místní verze pro zobrazení',\n    selectVersionToCompare: 'Vyberte verzi pro porovnání',\n    showingVersionsFor: 'Zobrazují se verze pro:',\n    showLocales: 'Zobrazit místní verze:',\n    specificVersion: 'Specifická verze',\n    status: 'Stav',\n    unpublish: 'Zrušit publikování',\n    unpublishing: 'Zrušuji publikování...',\n    version: 'Verze',\n    versionAgo: 'před {{distance}}',\n    versionCount_many: '{{count}} verzí nalezeno',\n    versionCount_none: 'Žádné verze nenalezeny',\n    versionCount_one: '{{count}} verze nalezena',\n    versionCount_other: '{{count}} verzí nalezeno',\n    versionCreatedOn: '{{version}} vytvořena:',\n    versionID: 'ID verze',\n    versions: 'Verze',\n    viewingVersion: 'Zobrazuji verzi pro {{entityLabel}} {{documentTitle}}',\n    viewingVersionGlobal: 'Zobrazuji verzi pro globální {{entityLabel}}',\n    viewingVersions: 'Zobrazuji verze pro {{entityLabel}} {{documentTitle}}',\n    viewingVersionsGlobal: 'Zobrazuji verze pro globální {{entityLabel}}',\n  },\n}\n\nexport const cs: Language = {\n  dateFNSKey: 'cs',\n  translations: csTranslations,\n}\n"], "names": ["csTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "cs", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBAAgB;QAChBC,uBACE;QACFC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBACE;QACF6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}