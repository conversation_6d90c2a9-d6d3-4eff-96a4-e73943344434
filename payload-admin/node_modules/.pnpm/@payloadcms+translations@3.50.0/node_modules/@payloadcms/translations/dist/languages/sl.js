export const slTranslations = {
    authentication: {
        account: '<PERSON><PERSON><PERSON>',
        accountOfCurrentUser: '<PERSON><PERSON><PERSON> trenutnega uporabnika',
        accountVerified: '<PERSON><PERSON><PERSON> uspešno preverjen.',
        alreadyActivated: 'Že aktivirano',
        alreadyLoggedIn: 'Že prijavljeni',
        apiKey: 'API ključ',
        authenticated: 'Avtenticirano',
        backToLogin: 'Nazaj na prijavo',
        beginCreateFirstUser: 'Za začetek ustvarite prvega uporabnika.',
        changePassword: 'Spremeni geslo',
        checkYourEmailForPasswordReset: 'Če je e-poštni naslov povezan z računom, boste kmalu prejeli navodila za ponastavitev gesla. Prosimo, preverite mapo za neželeno pošto ali spam, če e-pošte ne vidite v vašem prejemu.',
        confirmGeneration: 'Potrdi generiranje',
        confirmPassword: '<PERSON>tr<PERSON> geslo',
        createFirstUser: 'Ustvari prvega uporabnika',
        emailNotValid: 'Vneseni e-poštni naslov ni veljaven',
        emailOrUsername: 'E-pošta ali uporabniško ime',
        emailSent: 'E-pošta poslana',
        emailVerified: 'E-pošta uspešno preverjena.',
        enableAPIKey: 'Omogoči API ključ',
        failedToUnlock: 'Odklepanje ni uspelo',
        forceUnlock: 'Prisili odklepanje',
        forgotPassword: 'Pozabljeno geslo',
        forgotPasswordEmailInstructions: 'Vnesite svoj e-poštni naslov. Prejeli boste e-pošto z navodili za ponastavitev gesla.',
        forgotPasswordQuestion: 'Ste pozabili geslo?',
        forgotPasswordUsernameInstructions: 'Vnesite svoje uporabniško ime. Navodila za ponastavitev gesla bodo poslana na e-poštni naslov, povezan z vašim uporabniškim imenom.',
        generate: 'Generiraj',
        generateNewAPIKey: 'Generiraj nov API ključ',
        generatingNewAPIKeyWillInvalidate: 'Generiranje novega API ključa bo <1>razveljavilo</1> prejšnji ključ. Ste prepričani, da želite nadaljevati?',
        lockUntil: 'Zakleni do',
        logBackIn: 'Ponovno se prijavi',
        loggedIn: 'Za prijavo z drugim uporabnikom se morate najprej <0>odjaviti</0>.',
        loggedInChangePassword: 'Za spremembo gesla pojdite na svoj <0>račun</0> in tam uredite svoje geslo.',
        loggedOutInactivity: 'Odjavljeni ste bili zaradi neaktivnosti.',
        loggedOutSuccessfully: 'Uspešno ste se odjavili.',
        loggingOut: 'Odjavljanje...',
        login: 'Prijava',
        loginAttempts: 'Poskusi prijave',
        loginUser: 'Prijavi uporabnika',
        loginWithAnotherUser: 'Za prijavo z drugim uporabnikom se morate najprej <0>odjaviti</0>.',
        logOut: 'Odjava',
        logout: 'Odjava',
        logoutSuccessful: 'Odjava uspešna.',
        logoutUser: 'Odjavi uporabnika',
        newAccountCreated: 'Pravkar je bil ustvarjen nov račun za dostop do <a href="{{serverURL}}">{{serverURL}}</a> Prosimo, kliknite na naslednjo povezavo ali jo prilepite v svoj brskalnik za potrditev e-pošte: <a href="{{verificationURL}}">{{verificationURL}}</a><br> Po potrditvi e-pošte se boste lahko uspešno prijavili.',
        newAPIKeyGenerated: 'Nov API ključ generiran.',
        newPassword: 'Novo geslo',
        passed: 'Avtentikacija uspešna',
        passwordResetSuccessfully: 'Geslo uspešno ponastavljeno.',
        resetPassword: 'Ponastavi geslo',
        resetPasswordExpiration: 'Potek ponastavitve gesla',
        resetPasswordToken: 'Žeton za ponastavitev gesla',
        resetYourPassword: 'Ponastavite svoje geslo',
        stayLoggedIn: 'Ostani prijavljen',
        successfullyRegisteredFirstUser: 'Uspešno registriran prvi uporabnik.',
        successfullyUnlocked: 'Uspešno odklenjeno',
        tokenRefreshSuccessful: 'Osvežitev žetona uspešna.',
        unableToVerify: 'Ni mogoče preveriti',
        username: 'Uporabniško ime',
        usernameNotValid: 'Vneseno uporabniško ime ni veljavno',
        verified: 'Preverjeno',
        verifiedSuccessfully: 'Uspešno preverjeno',
        verify: 'Preveri',
        verifyUser: 'Preveri uporabnika',
        verifyYourEmail: 'Potrdite svojo e-pošto',
        youAreInactive: 'Že nekaj časa niste bili aktivni in boste kmalu samodejno odjavljeni zaradi varnosti. Želite ostati prijavljeni?',
        youAreReceivingResetPassword: 'To sporočilo ste prejeli, ker ste vi (ali nekdo drug) zahtevali ponastavitev gesla za vaš račun. Prosimo, kliknite na naslednjo povezavo ali jo prilepite v svoj brskalnik za dokončanje postopka:',
        youDidNotRequestPassword: 'Če tega niste zahtevali, prezrite to e-pošto in vaše geslo bo ostalo nespremenjeno.'
    },
    error: {
        accountAlreadyActivated: 'Ta račun je že aktiviran.',
        autosaving: 'Pri samodejnem shranjevanju tega dokumenta je prišlo do težave.',
        correctInvalidFields: 'Prosimo, popravite neveljavna polja.',
        deletingFile: 'Pri brisanju datoteke je prišlo do napake.',
        deletingTitle: 'Pri brisanju {{title}} je prišlo do napake. Prosimo, preverite povezavo in poskusite znova.',
        documentNotFound: 'Dokumenta z ID {{id}} ni bilo mogoče najti. Morda je bil izbrisan ali nikoli ni obstajal, ali pa do njega nimate dostopa.',
        emailOrPasswordIncorrect: 'Vnesena e-pošta ali geslo je napačno.',
        followingFieldsInvalid_one: 'Naslednje polje je neveljavno:',
        followingFieldsInvalid_other: 'Naslednja polja so neveljavna:',
        incorrectCollection: 'Napačna zbirka',
        insufficientClipboardPermissions: 'Dostop do odložišča je bil zavrnjen. Preverite dovoljenja za odložišče.',
        invalidClipboardData: 'Neveljavni podatki v odložišču.',
        invalidFileType: 'Neveljaven tip datoteke',
        invalidFileTypeValue: 'Neveljaven tip datoteke: {{value}}',
        invalidRequestArgs: 'V zahtevi so bili poslani neveljavni argumenti: {{args}}',
        loadingDocument: 'Pri nalaganju dokumenta z ID-jem {{id}} je prišlo do težave.',
        localesNotSaved_one: 'Naslednjega jezika ni bilo mogoče shraniti:',
        localesNotSaved_other: 'Naslednjih jezikov ni bilo mogoče shraniti:',
        logoutFailed: 'Odjava ni uspela.',
        missingEmail: 'Manjka e-pošta.',
        missingIDOfDocument: 'Manjka ID dokumenta za posodobitev.',
        missingIDOfVersion: 'Manjka ID različice.',
        missingRequiredData: 'Manjkajo zahtevani podatki.',
        noFilesUploaded: 'Nobena datoteka ni bila naložena.',
        noMatchedField: 'Za "{{label}}" ni bilo najdeno ujemajoče se polje',
        notAllowedToAccessPage: 'Nimate dovoljenja za dostop do te strani.',
        notAllowedToPerformAction: 'Nimate dovoljenja za izvedbo tega dejanja.',
        notFound: 'Zahtevani vir ni bil najden.',
        noUser: 'Ni uporabnika',
        previewing: 'Pri predogledu tega dokumenta je prišlo do težave.',
        problemUploadingFile: 'Pri nalaganju datoteke je prišlo do težave.',
        restoringTitle: 'Pri obnavljanju {{title}} je prišlo do napake. Prosimo, preverite svojo povezavo in poskusite znova.',
        tokenInvalidOrExpired: 'Žeton je neveljaven ali je potekel.',
        tokenNotProvided: 'Žeton ni bil posredovan.',
        unableToCopy: 'Kopiranje ni mogoče.',
        unableToDeleteCount: 'Ni bilo mogoče izbrisati {{count}} od {{total}} {{label}}.',
        unableToReindexCollection: 'Napaka pri reindeksiranju zbirke {{collection}}. Operacija je bila prekinjena.',
        unableToUpdateCount: 'Ni bilo mogoče posodobiti {{count}} od {{total}} {{label}}.',
        unauthorized: 'Neavtorizirano, za to zahtevo morate biti prijavljeni.',
        unauthorizedAdmin: 'Neavtorizirano, ta uporabnik nima dostopa do skrbniškega vmesnika.',
        unknown: 'Prišlo je do neznane napake.',
        unPublishingDocument: 'Pri umiku objave tega dokumenta je prišlo do težave.',
        unspecific: 'Prišlo je do napake.',
        unverifiedEmail: 'Pred prijavo preverite svoj e-poštni naslov.',
        userEmailAlreadyRegistered: 'Uporabnik s tem e-poštnim naslovom je že registriran.',
        userLocked: 'Ta uporabnik je zaklenjen zaradi prevelikega števila neuspešnih poskusov prijave.',
        usernameAlreadyRegistered: 'Uporabnik s tem uporabniškim imenom je že registriran.',
        usernameOrPasswordIncorrect: 'Vneseno uporabniško ime ali geslo je napačno.',
        valueMustBeUnique: 'Vrednost mora biti unikatna',
        verificationTokenInvalid: 'Žeton za preverjanje je neveljaven.'
    },
    fields: {
        addLabel: 'Dodaj {{label}}',
        addLink: 'Dodaj povezavo',
        addNew: 'Dodaj novo',
        addNewLabel: 'Dodaj nov {{label}}',
        addRelationship: 'Dodaj povezavo',
        addUpload: 'Dodaj nalaganje',
        block: 'blok',
        blocks: 'bloki',
        blockType: 'Tip bloka',
        chooseBetweenCustomTextOrDocument: 'Izberite med vnosom URL-ja po meri ali povezavo na drug dokument.',
        chooseDocumentToLink: 'Izberite dokument za povezavo',
        chooseFromExisting: 'Izberite iz obstoječih',
        chooseLabel: 'Izberite {{label}}',
        collapseAll: 'Strni vse',
        customURL: 'URL po meri',
        editLabelData: 'Uredi podatke {{label}}',
        editLink: 'Uredi povezavo',
        editRelationship: 'Uredi povezavo',
        enterURL: 'Vnesite URL',
        internalLink: 'Notranja povezava',
        itemsAndMore: '{{items}} in še {{count}}',
        labelRelationship: '{{label}} povezava',
        latitude: 'Zemljepisna širina',
        linkedTo: 'Povezano z <0>{{label}}</0>',
        linkType: 'Tip povezave',
        longitude: 'Zemljepisna dolžina',
        newLabel: 'Nov {{label}}',
        openInNewTab: 'Odpri v novem zavihku',
        passwordsDoNotMatch: 'Gesli se ne ujemata.',
        relatedDocument: 'Povezan dokument',
        relationTo: 'Povezava z',
        removeRelationship: 'Odstrani povezavo',
        removeUpload: 'Odstrani nalaganje',
        saveChanges: 'Shrani spremembe',
        searchForBlock: 'Išči blok',
        selectExistingLabel: 'Izberi obstoječ {{label}}',
        selectFieldsToEdit: 'Izberi polja za urejanje',
        showAll: 'Pokaži vse',
        swapRelationship: 'Zamenjaj povezavo',
        swapUpload: 'Zamenjaj nalaganje',
        textToDisplay: 'Besedilo za prikaz',
        toggleBlock: 'Preklopi blok',
        uploadNewLabel: 'Naloži nov {{label}}'
    },
    folder: {
        browseByFolder: 'Brskaj po mapi',
        byFolder: 'Po mapi',
        deleteFolder: 'Izbriši mapo',
        folderName: 'Ime mape',
        folders: 'Mape',
        folderTypeDescription: 'Izberite, katere vrste dokumentov zbirke naj bodo dovoljene v tej mapi.',
        itemHasBeenMoved: '{{title}} je bil premaknjen v {{folderName}}',
        itemHasBeenMovedToRoot: '{{title}} je bil premaknjen v korensko mapo.',
        itemsMovedToFolder: '{{title}} premaknjeno v {{folderName}}',
        itemsMovedToRoot: '{{title}} premaknjeno v korensko mapo',
        moveFolder: 'Premakni mapo',
        moveItemsToFolderConfirmation: 'Ravno se pripravljate na premik <1>{{count}} {{label}}</1> v mapo <2>{{toFolder}}</2>. Ste prepričani?',
        moveItemsToRootConfirmation: 'Ravno boste premaknili <1>{{count}} {{label}}</1> v korensko mapo. Ste prepričani?',
        moveItemToFolderConfirmation: 'Pravkar boste premaknili <1>{{title}}</1> v <2>{{toFolder}}</2>. Ste prepričani?',
        moveItemToRootConfirmation: 'Pravkar boste premaknili <1>{{title}}</1> v korensko mapo. Ali ste prepričani?',
        movingFromFolder: 'Premik {{title}} iz {{fromFolder}}',
        newFolder: 'Nova mapa',
        noFolder: 'Brez mape',
        renameFolder: 'Preimenuj Mapo',
        searchByNameInFolder: 'Iskanje po imenu v {{folderName}}',
        selectFolderForItem: 'Izberite mapo za {{title}}'
    },
    general: {
        name: 'Ime',
        aboutToDelete: 'Izbrisali boste {{label}} <1>{{title}}</1>. Ste prepričani?',
        aboutToDeleteCount_many: 'Izbrisali boste {{count}} {{label}}',
        aboutToDeleteCount_one: 'Izbrisali boste {{count}} {{label}}',
        aboutToDeleteCount_other: 'Izbrisali boste {{count}} {{label}}',
        aboutToPermanentlyDelete: 'Ravno boste trajno izbrisali {{label}} <1>{{title}}</1>. Ste prepričani?',
        aboutToPermanentlyDeleteTrash: 'Pravkar boste trajno izbrisali <0>{{count}}</0> <1>{{label}}</1> iz smetnjaka. Ali ste prepričani?',
        aboutToRestore: 'Ravno se odpravljate na obnovitev {{label}} <1>{{title}}</1>. Ste prepričani?',
        aboutToRestoreAsDraft: 'Pravkar boste obnovili {{label}} <1>{{title}}</1> kot osnutek. Ali ste prepričani?',
        aboutToRestoreAsDraftCount: 'Pravkar boste obnovili {{count}} {{label}} kot osnutek.',
        aboutToRestoreCount: 'Pravkar boste obnovili {{count}} {{label}}',
        aboutToTrash: 'Pravkar boste premaknili {{label}} <1>{{title}}</1> v smeti. Ste prepričani?',
        aboutToTrashCount: 'Pravkar boste premaknili {{count}} {{label}} v smeti.',
        addBelow: 'Dodaj spodaj',
        addFilter: 'Dodaj filter',
        adminTheme: 'Tema skrbnika',
        all: 'Vse',
        allCollections: 'Vse Zbirke',
        allLocales: 'Vse lokacije',
        and: 'In',
        anotherUser: 'Drug uporabnik',
        anotherUserTakenOver: 'Drug uporabnik je prevzel urejanje tega dokumenta.',
        applyChanges: 'Uporabi spremembe',
        ascending: 'Naraščajoče',
        automatic: 'Samodejno',
        backToDashboard: 'Nazaj na nadzorno ploščo',
        cancel: 'Prekliči',
        changesNotSaved: 'Vaše spremembe niso shranjene. Če zapustite zdaj, boste izgubili svoje spremembe.',
        clear: 'Čisto',
        clearAll: 'Počisti vse',
        close: 'Zapri',
        collapse: 'Strni',
        collections: 'Zbirke',
        columns: 'Stolpci',
        columnToSort: 'Stolpec za razvrščanje',
        confirm: 'Potrdi',
        confirmCopy: 'Potrdi kopiranje',
        confirmDeletion: 'Potrdi brisanje',
        confirmDuplication: 'Potrdi podvajanje',
        confirmMove: 'Potrdi premik',
        confirmReindex: 'Ponovno indeksirati vse {{collections}}?',
        confirmReindexAll: 'Ponovno indeksirati vse zbirke?',
        confirmReindexDescription: 'To bo odstranilo obstoječe indekse in ponovno indeksiralo dokumente v zbirkah {{collections}}.',
        confirmReindexDescriptionAll: 'To bo odstranilo obstoječe indekse in ponovno indeksiralo dokumente v vseh zbirkah.',
        confirmRestoration: 'Potrdite obnovitev',
        copied: 'Kopirano',
        copy: 'Kopiraj',
        copyField: 'Kopiraj polje',
        copying: 'Kopiranje',
        copyRow: 'Kopiraj vrstico',
        copyWarning: 'Prepisali boste {{to}} z {{from}} za {{label}} {{title}}. Ste prepričani?',
        create: 'Ustvari',
        created: 'Ustvarjeno',
        createdAt: 'Ustvarjeno',
        createNew: 'Ustvari novo',
        createNewLabel: 'Ustvari nov {{label}}',
        creating: 'Ustvarjanje',
        creatingNewLabel: 'Ustvarjanje novega {{label}}',
        currentlyEditing: 'trenutno ureja ta dokument. Če prevzamete, jim bo onemogočeno nadaljnje urejanje in lahko izgubijo neshranjene spremembe.',
        custom: 'Po meri',
        dark: 'Temno',
        dashboard: 'Nadzorna plošča',
        delete: 'Izbriši',
        deleted: 'Izbrisano',
        deletedAt: 'Izbrisano ob',
        deletedCountSuccessfully: 'Uspešno izbrisano {{count}} {{label}}.',
        deletedSuccessfully: 'Uspešno izbrisano.',
        deletePermanently: 'Preskoči smetnjak in trajno izbriši',
        deleting: 'Brisanje...',
        depth: 'Globina',
        descending: 'Padajoče',
        deselectAllRows: 'Odznači vse vrstice',
        document: 'Dokument',
        documentIsTrashed: 'Ta {{label}} je v smetnjaku in je samo za branje.',
        documentLocked: 'Dokument zaklenjen',
        documents: 'Dokumenti',
        duplicate: 'Podvoji',
        duplicateWithoutSaving: 'Podvoji brez shranjevanja sprememb',
        edit: 'Uredi',
        editAll: 'Uredi vse',
        editedSince: 'Urejeno od',
        editing: 'Urejanje',
        editingLabel_many: 'Urejanje {{count}} {{label}}',
        editingLabel_one: 'Urejanje {{count}} {{label}}',
        editingLabel_other: 'Urejanje {{count}} {{label}}',
        editingTakenOver: 'Urejanje prevzeto',
        editLabel: 'Uredi {{label}}',
        email: 'E-pošta',
        emailAddress: 'E-poštni naslov',
        emptyTrash: 'Izprazni koš',
        emptyTrashLabel: 'Izprazni {{label}} smeti',
        enterAValue: 'Vnesite vrednost',
        error: 'Napaka',
        errors: 'Napake',
        exitLivePreview: 'Izhodi iz živega predogleda',
        export: 'Izvoz',
        fallbackToDefaultLocale: 'Uporabi privzeti jezik',
        false: 'Ne',
        filter: 'Filter',
        filters: 'Filtri',
        filterWhere: 'Filtriraj {{label}} kjer',
        globals: 'Globalne nastavitve',
        goBack: 'Nazaj',
        groupByLabel: 'Razvrsti po {{label}}',
        import: 'Uvoz',
        isEditing: 'ureja',
        item: 'predmet',
        items: 'predmeti',
        language: 'Jezik',
        lastModified: 'Zadnja sprememba',
        leaveAnyway: 'Vseeno zapusti',
        leaveWithoutSaving: 'Zapusti brez shranjevanja',
        light: 'Svetlo',
        livePreview: 'Predogled',
        loading: 'Nalaganje',
        locale: 'Jezik',
        locales: 'Jeziki',
        menu: 'Meni',
        moreOptions: 'Več možnosti',
        move: 'Premakni',
        moveConfirm: 'Pravkar boste premaknili {{count}} {{label}} na <1>{{destination}}</1>. Ste prepričani?',
        moveCount: 'Premakni {{count}} {{label}}',
        moveDown: 'Premakni dol',
        moveUp: 'Premakni gor',
        moving: 'Premikanje',
        movingCount: 'Premikanje {{count}} {{label}}',
        newPassword: 'Novo geslo',
        next: 'Naprej',
        no: 'Ne',
        noDateSelected: 'Izbran ni noben datum',
        noFiltersSet: 'Ni nastavljenih filtrov',
        noLabel: '<Brez {{label}}>',
        none: 'Brez',
        noOptions: 'Ni možnosti',
        noResults: 'Ni najdenih {{label}}. Ali {{label}} še ne obstajajo ali pa ne ustrezajo filtrom, ki ste jih določili zgoraj.',
        notFound: 'Ni najdeno',
        nothingFound: 'Nič ni najdeno',
        noTrashResults: 'Ni {{label}} v smetnjaku.',
        noUpcomingEventsScheduled: 'Ni načrtovanih prihajajočih dogodkov.',
        noValue: 'Ni vrednosti',
        of: 'od',
        only: 'Samo',
        open: 'Odpri',
        or: 'Ali',
        order: 'Vrstni red',
        overwriteExistingData: 'Prepišite obstoječe podatke polja',
        pageNotFound: 'Stran ni najdena',
        password: 'Geslo',
        pasteField: 'Prilepi polje',
        pasteRow: 'Prilepi vrstico',
        payloadSettings: 'Nastavitve Payloada',
        permanentlyDelete: 'Trajno Izbrisano',
        permanentlyDeletedCountSuccessfully: 'Uspešno trajno izbrisano {{count}} {{label}}.',
        perPage: 'Na stran: {{limit}}',
        previous: 'Prejšnji',
        reindex: 'Reindeksiraj',
        reindexingAll: 'Ponovno indeksiranje vseh {{collections}}.',
        remove: 'Odstrani',
        rename: 'Preimenuj',
        reset: 'Ponastavi',
        resetPreferences: 'Ponastavi nastavitve',
        resetPreferencesDescription: 'To bo ponastavilo vse vaše nastavitve na privzete vrednosti.',
        resettingPreferences: 'Ponastavitev nastavitve.',
        restore: 'Obnovi',
        restoreAsPublished: 'Obnovi kot objavljeno različico',
        restoredCountSuccessfully: 'Uspešno obnovljeno {{count}} {{label}}.',
        restoring: 'Spoštujte pomen izvirnega besedila znotraj konteksta Payload. Tu je seznam pogostih izrazov Payload, ki imajo zelo specifične pomene:\n    - Zbirka: Zbirka je skupina dokumentov, ki delijo skupno strukturo in namen. Zbirke se uporabljajo za organizacijo in upravljanje vsebine v Payload.\n    - Polje: Polje je določen del podatkov znotraj dokumenta v zbirki. Polja opredeljujejo strukturo in vrsto podatkov, ki jih je mogoče sh',
        row: 'Vrstica',
        rows: 'Vrstice',
        save: 'Shrani',
        saving: 'Shranjevanje...',
        schedulePublishFor: 'Načrtujte objavo za {{naslov}}',
        searchBy: 'Išči po {{label}}',
        select: 'Izberi',
        selectAll: 'Izberi vse {{count}} {{label}}',
        selectAllRows: 'Izberi vse vrstice',
        selectedCount: '{{count}} {{label}} izbranih',
        selectLabel: 'Izberite {{label}}',
        selectValue: 'Izberi vrednost',
        showAllLabel: 'Pokaži vse {{label}}',
        sorryNotFound: 'Oprostite - ničesar ni mogoče najti, kar bi ustrezalo vaši zahtevi.',
        sort: 'Razvrsti',
        sortByLabelDirection: 'Razvrsti po {{label}} {{direction}}',
        stayOnThisPage: 'Ostani na tej strani',
        submissionSuccessful: 'Oddaja uspešna.',
        submit: 'Oddaj',
        submitting: 'Oddajanje...',
        success: 'Uspeh',
        successfullyCreated: '{{label}} uspešno ustvarjen.',
        successfullyDuplicated: '{{label}} uspešno podvojen.',
        successfullyReindexed: 'Uspešno reindeksiranih {{count}} od {{total}} dokumentov iz zbirk {{collections}}.',
        takeOver: 'Prevzemi',
        thisLanguage: 'Slovenščina',
        time: 'Čas',
        timezone: 'Časovni pas',
        titleDeleted: '{{label}} "{{title}}" uspešno izbrisan.',
        titleRestored: 'Oznaka "{{title}}" je bila uspešno obnovljena.',
        titleTrashed: '{{label}} "{{title}}" premaknjeno v smeti.',
        trash: 'Smeti',
        trashedCountSuccessfully: '{{count}} {{label}} premaknjeno v smeti.',
        true: 'Da',
        unauthorized: 'Nepooblaščeno',
        unsavedChanges: 'Neshranjene spremembe',
        unsavedChangesDuplicate: 'Imate neshranjene spremembe. Želite nadaljevati s podvajanjem?',
        untitled: 'Brez naslova',
        upcomingEvents: 'Prihajajoči dogodki',
        updatedAt: 'Posodobljeno',
        updatedCountSuccessfully: 'Uspešno posodobljeno {{count}} {{label}}.',
        updatedLabelSuccessfully: '{{label}} uspešno posodobljen.',
        updatedSuccessfully: 'Uspešno posodobljeno.',
        updateForEveryone: 'Posodobitev za vse',
        updating: 'Posodabljanje',
        uploading: 'Nalaganje',
        uploadingBulk: 'Nalaganje {{current}} od {{total}}',
        user: 'Uporabnik',
        username: 'Uporabniško ime',
        users: 'Uporabniki',
        value: 'Vrednost',
        viewing: 'Ogled',
        viewReadOnly: 'Ogled samo za branje',
        welcome: 'Dobrodošli',
        yes: 'Da'
    },
    localization: {
        cannotCopySameLocale: 'Ni mogoče kopirati v isti jezik',
        copyFrom: 'Kopiraj iz',
        copyFromTo: 'Kopiranje iz {{from}} v {{to}}',
        copyTo: 'Kopiraj v',
        copyToLocale: 'Kopiraj v jezik',
        localeToPublish: 'Lokalno za objavo',
        selectLocaleToCopy: 'Izberite jezik za kopiranje'
    },
    operators: {
        contains: 'vsebuje',
        equals: 'je enako',
        exists: 'obstaja',
        intersects: 'se seka',
        isGreaterThan: 'je večje od',
        isGreaterThanOrEqualTo: 'je večje ali enako',
        isIn: 'je v',
        isLessThan: 'je manjše od',
        isLessThanOrEqualTo: 'je manjše ali enako',
        isLike: 'je podobno',
        isNotEqualTo: 'ni enako',
        isNotIn: 'ni v',
        isNotLike: 'ni podobno',
        near: 'blizu',
        within: 'znotraj'
    },
    upload: {
        addFile: 'Dodaj datoteko',
        addFiles: 'Dodaj datoteke',
        bulkUpload: 'Množično nalaganje',
        crop: 'Obreži',
        cropToolDescription: 'Povlecite kote izbranega območja, narišite novo območje ali prilagodite vrednosti spodaj.',
        download: 'Prenos',
        dragAndDrop: 'Povlecite in spustite datoteko',
        dragAndDropHere: 'ali povlecite in spustite datoteko sem',
        editImage: 'Uredi sliko',
        fileName: 'Ime datoteke',
        fileSize: 'Velikost datoteke',
        filesToUpload: 'Datoteke za nalaganje',
        fileToUpload: 'Datoteka za nalaganje',
        focalPoint: 'Žarišče',
        focalPointDescription: 'Povlecite žarišče neposredno na predogledu ali prilagodite vrednosti spodaj.',
        height: 'Višina',
        lessInfo: 'Manj informacij',
        moreInfo: 'Več informacij',
        noFile: 'Ni datoteke.',
        pasteURL: 'Prilepi URL',
        previewSizes: 'Velikosti predogleda',
        selectCollectionToBrowse: 'Izberite zbirko za brskanje',
        selectFile: 'Izberite datoteko',
        setCropArea: 'Nastavi območje obrezovanja',
        setFocalPoint: 'Nastavi žarišče',
        sizes: 'Velikosti',
        sizesFor: 'Velikosti za {{label}}',
        width: 'Širina'
    },
    validation: {
        emailAddress: 'Vnesite veljaven e-poštni naslov.',
        enterNumber: 'Vnesite veljavno številko.',
        fieldHasNo: 'To polje nima {{label}}',
        greaterThanMax: '{{value}} je večje od največje dovoljene {{label}} {{max}}.',
        invalidInput: 'To polje ima neveljaven vnos.',
        invalidSelection: 'To polje ima neveljavno izbiro.',
        invalidSelections: 'To polje ima naslednje neveljavne izbire:',
        lessThanMin: '{{value}} je manjše od najmanjše dovoljene {{label}} {{min}}.',
        limitReached: 'Dosežena omejitev, dodati je mogoče samo {{max}} elementov.',
        longerThanMin: 'Ta vrednost mora biti daljša od najmanjše dolžine {{minLength}} znakov.',
        notValidDate: '"{{value}}" ni veljaven datum.',
        required: 'To polje je obvezno.',
        requiresAtLeast: 'To polje zahteva vsaj {{count}} {{label}}.',
        requiresNoMoreThan: 'To polje zahteva največ {{count}} {{label}}.',
        requiresTwoNumbers: 'To polje zahteva dve številki.',
        shorterThanMax: 'Ta vrednost mora biti krajša od največje dolžine {{maxLength}} znakov.',
        timezoneRequired: 'Potrebna je časovna cona.',
        trueOrFalse: 'To polje je lahko samo enako true ali false.',
        username: 'Vnesite veljavno uporabniško ime. Lahko vsebuje črke, številke, vezaje, pike in podčrtaje.',
        validUploadID: 'To polje ni veljaven ID nalaganja.'
    },
    version: {
        type: 'Tip',
        aboutToPublishSelection: 'Objavili boste vse {{label}} v izboru. Ste prepričani?',
        aboutToRestore: 'Ta {{label}} dokument boste obnovili v stanje, v katerem je bil {{versionDate}}.',
        aboutToRestoreGlobal: 'Globalni {{label}} boste obnovili v stanje, v katerem je bil {{versionDate}}.',
        aboutToRevertToPublished: 'Spremembe tega dokumenta boste povrnili v objavljeno stanje. Ste prepričani?',
        aboutToUnpublish: 'Ta dokument boste umaknili iz objave. Ste prepričani?',
        aboutToUnpublishSelection: 'Umaknili boste iz objave vse {{label}} v izboru. Ste prepričani?',
        autosave: 'Samodejno shranjevanje',
        autosavedSuccessfully: 'Samodejno shranjeno uspešno.',
        autosavedVersion: 'Samodejno shranjena različica',
        changed: 'Spremenjeno',
        changedFieldsCount_one: '{{count}} spremenjeno polje',
        changedFieldsCount_other: '{{count}} spremenjena polja',
        compareVersion: 'Primerjaj različico z:',
        compareVersions: 'Primerjaj različice',
        comparingAgainst: 'Primerjava z',
        confirmPublish: 'Potrdi objavo',
        confirmRevertToSaved: 'Potrdi vrnitev na shranjeno',
        confirmUnpublish: 'Potrdi umik objave',
        confirmVersionRestoration: 'Potrdi obnovitev različice',
        currentDocumentStatus: 'Trenutni {{docStatus}} dokument',
        currentDraft: 'Trenutni osnutek',
        currentlyPublished: 'Trenutno objavljeno',
        currentlyViewing: 'Trenutno pregledujete',
        currentPublishedVersion: 'Trenutna objavljena različica',
        draft: 'Osnutek',
        draftSavedSuccessfully: 'Osnutek uspešno shranjen.',
        lastSavedAgo: 'Nazadnje shranjeno pred {{distance}}',
        modifiedOnly: 'Samo spremenjeno',
        moreVersions: 'Več različic...',
        noFurtherVersionsFound: 'Ni najdenih nadaljnjih različic',
        noRowsFound: 'Ni najdenih {{label}}',
        noRowsSelected: 'Ni izbranih {{label}}',
        preview: 'Predogled',
        previouslyDraft: 'Prej osnutek',
        previouslyPublished: 'Predhodno objavljeno',
        previousVersion: 'Prejšnja različica',
        problemRestoringVersion: 'Pri obnavljanju te različice je prišlo do težave',
        publish: 'Objavi',
        publishAllLocales: 'Objavi vse jezike',
        publishChanges: 'Objavi spremembe',
        published: 'Objavljeno',
        publishIn: 'Objavi v {{locale}}',
        publishing: 'Objavljanje',
        restoreAsDraft: 'Obnovi kot osnutek',
        restoredSuccessfully: 'Uspešno obnovljeno.',
        restoreThisVersion: 'Obnovi to različico',
        restoring: 'Obnavljanje...',
        reverting: 'Razveljavljanje...',
        revertToPublished: 'Vrni na objavljeno',
        saveDraft: 'Shrani osnutek',
        scheduledSuccessfully: 'Uspešno načrtovano.',
        schedulePublish: 'Razporedi objavo',
        selectLocales: 'Izberite jezike za prikaz',
        selectVersionToCompare: 'Izberite različico za primerjavo',
        showingVersionsFor: 'Prikaz različic za:',
        showLocales: 'Prikaži jezike:',
        specificVersion: 'Specifična različica',
        status: 'Status',
        unpublish: 'Razveljavi objavo',
        unpublishing: 'Razveljavljanje objave...',
        version: 'Različica',
        versionAgo: 'pred {{distance}}',
        versionCount_many: 'Najdenih {{count}} različic',
        versionCount_none: 'Ni najdenih različic',
        versionCount_one: 'Najdena {{count}} različica',
        versionCount_other: 'Najdene {{count}} različice',
        versionCreatedOn: '{{version}} ustvarjena:',
        versionID: 'ID različice',
        versions: 'Različice',
        viewingVersion: 'Ogled različice za {{entityLabel}} {{documentTitle}}',
        viewingVersionGlobal: 'Ogled različice za globalni {{entityLabel}}',
        viewingVersions: 'Ogled različic za {{entityLabel}} {{documentTitle}}',
        viewingVersionsGlobal: 'Ogled različic za globalni {{entityLabel}}'
    }
};
export const sl = {
    dateFNSKey: 'sl-SI',
    translations: slTranslations
};

//# sourceMappingURL=sl.js.map