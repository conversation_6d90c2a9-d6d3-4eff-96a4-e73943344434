{"version": 3, "sources": ["../../src/languages/de.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const deTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: 'Benutzerkonto',\n    accountOfCurrentUser: 'Aktuelles Benutzerkonto',\n    accountVerified: 'Benutzerkonto erfolgreich verifiziert.',\n    alreadyActivated: 'Bereits aktiviert',\n    alreadyLoggedIn: 'Bereits angemeldet',\n    apiKey: 'API-Key',\n    authenticated: 'Authentifiziert',\n    backToLogin: 'Zurück zur Anmeldung',\n    beginCreateFirstUser: 'Erstelle deinen ersten Benutzer, um zu beginnen',\n    changePassword: 'Passwort ändern',\n    checkYourEmailForPasswordReset:\n      'Wenn die E-Mail-Adresse mit einem Benutzerkonto verknüpft ist, erhältst du in Kürze Anweisungen zur Zurücksetzung deines Passworts. Bitte überprüfe deinen Spam-Ordner, wenn du die E-Mail nicht in deinem Posteingang siehst.',\n    confirmGeneration: 'Generierung bestätigen',\n    confirmPassword: 'Passwort bestätigen',\n    createFirstUser: '<PERSON><PERSON> erstellen',\n    emailNotValid: 'Die angegebene E-Mail-Adresse ist ungültig',\n    emailOrUsername: 'E-Mail oder Benutzername',\n    emailSent: 'E-Mail versendet',\n    emailVerified: 'E-Mail erfolgreich verifiziert.',\n    enableAPIKey: 'API-Key aktivieren',\n    failedToUnlock: 'Entsperrung fehlgeschlagen',\n    forceUnlock: 'Entsperrung erzwingen',\n    forgotPassword: 'Passwort vergessen',\n    forgotPasswordEmailInstructions:\n      'Bitte gib deine E-Mail-Adresse an. Du wirst eine E-Mail mit Instruktionen zum Zurücksetzen deines Passworts erhalten.',\n    forgotPasswordQuestion: 'Passwort vergessen?',\n    forgotPasswordUsernameInstructions:\n      'Bitte gib deinen Benutzernamen ein. Anweisungen zum Zurücksetzen deines Passworts werden an die mit deinem Benutzernamen verknüpfte E-Mail-Adresse gesendet.',\n    generate: 'Generieren',\n    generateNewAPIKey: 'Neuen API-Key generieren',\n    generatingNewAPIKeyWillInvalidate:\n      'Wenn du einen neuen Key generierst, wird der vorherige <1>ungültig</1>. Bist du sicher, dass du fortfahren möchtest?',\n    lockUntil: 'Sperren bis',\n    logBackIn: 'Wieder anmelden',\n    loggedIn:\n      'Um dich mit einem anderen Benutzerkonto anzumelden, musst du dich zuerst <0>abmelden</0>.',\n    loggedInChangePassword:\n      'Um dein Passwort zu ändern, gehe zu deinem <0>Benutzerkonto</0> und ändere dort dein Passwort.',\n    loggedOutInactivity: 'Du wurdest aufgrund von Inaktivität abgemeldet.',\n    loggedOutSuccessfully: 'Du wurdest erfolgreich abgemeldet.',\n    loggingOut: 'Abmelden...',\n    login: 'Anmelden',\n    loginAttempts: 'Anmelde-Versuche',\n    loginUser: 'Benutzeranmeldung',\n    loginWithAnotherUser:\n      'Um dich mit einem anderen Benutzer anzumelden, musst du dich zuerst <0>abmelden</0>.',\n    logOut: 'Abmelden',\n    logout: 'Abmelden',\n    logoutSuccessful: 'Abmeldung erfolgreich.',\n    logoutUser: 'Benutzer abmelden',\n    newAccountCreated:\n      'Ein neues Konto wurde gerade für dich auf <a href=\"{{serverURL}}\">{{serverURL}}</a> erstellt. Bitte klicke auf den folgenden Link oder kopiere die URL in deinen Browser, um deine E-Mail-Adresse zu verifizieren: <a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> Nachdem du deine E-Mail-Adresse verifiziert hast, kannst du dich erfolgreich anmelden.',\n    newAPIKeyGenerated: 'Neuer API-Key wurde generiert',\n    newPassword: 'Neues Passwort',\n    passed: 'Authentifizierung erfolgreich',\n    passwordResetSuccessfully: 'Passwort erfolgreich zurückgesetzt.',\n    resetPassword: 'Passwort zurücksetzen',\n    resetPasswordExpiration: 'Passwort-Gültigkeitsdauer zurücksetzen',\n    resetPasswordToken: 'Passwort-Token zurücksetzen',\n    resetYourPassword: 'Dein Passwort zurücksetzen',\n    stayLoggedIn: 'Angemeldet bleiben',\n    successfullyRegisteredFirstUser: 'Ersten Benutzer erfolgreich registriert.',\n    successfullyUnlocked: 'Erfolgreich entsperrt',\n    tokenRefreshSuccessful: 'Token-Aktualisierung erfolgreich.',\n    unableToVerify: 'Konnte nicht verifiziert werden',\n    username: 'Benutzername',\n    usernameNotValid: 'Der angegebene Benutzername ist nicht gültig.',\n    verified: 'Verifiziert',\n    verifiedSuccessfully: 'Erfolgreich verifiziert',\n    verify: 'Verifizieren',\n    verifyUser: 'Benutzer verifizieren',\n    verifyYourEmail: 'Deine E-Mail-Adresse verifizieren',\n    youAreInactive:\n      'Du warst einige Zeit inaktiv und wirst in Kürze zu deiner eigenen Sicherheit abgemeldet. Möchtest du angemeldet bleiben?',\n    youAreReceivingResetPassword:\n      'Du erhältst diese Nachricht, weil du (oder jemand anderes) das Zurücksetzen deines Passworts für dein Benutzerkonto angefordert hat. Bitte klicke auf den folgenden Link, oder kopiere die URL in deinen Browser, um den Prozess abzuschließen:',\n    youDidNotRequestPassword:\n      'Solltest du dies nicht angefordert haben, ignoriere diese E-Mail und dein Passwort bleibt unverändert.',\n  },\n  error: {\n    accountAlreadyActivated: 'Dieses Benutzerkonto wurde bereits aktiviert',\n    autosaving: 'Es gab ein Problem bei der automatischen Speicherung für dieses Dokument',\n    correctInvalidFields: 'Bitte ungültige Felder korrigieren.',\n    deletingFile: 'Beim Löschen der Datei ist ein Fehler aufgetreten.',\n    deletingTitle:\n      'Es gab ein Problem während der Löschung von {{title}}. Bitte überprüfe deine Verbindung und versuche es erneut.',\n    documentNotFound:\n      'Das Dokument mit der ID {{id}} konnte nicht gefunden werden. Es könnte gelöscht oder niemals existiert haben, oder Sie haben möglicherweise keinen Zugang dazu.',\n    emailOrPasswordIncorrect: 'Die E-Mail-Adresse oder das Passwort sind nicht korrekt.',\n    followingFieldsInvalid_one: 'Das folgende Feld ist nicht korrekt:',\n    followingFieldsInvalid_other: 'Die folgenden Felder sind nicht korrekt:',\n    incorrectCollection: 'Falsche Sammlung',\n    insufficientClipboardPermissions:\n      'Zugriff auf die Zwischenablage verweigert. Bitte überprüfen Sie die Berechtigungen.',\n    invalidClipboardData: 'Ungültige Zwischenablagedaten.',\n    invalidFileType: 'Ungültiger Datei-Typ',\n    invalidFileTypeValue: 'Ungültiger Datei-Typ: {{value}}',\n    invalidRequestArgs: 'Ungültige Argumente in der Anfrage: {{args}}',\n    loadingDocument: 'Es gab ein Problem, das Dokument mit der ID {{id}} zu laden.',\n    localesNotSaved_one: 'Die folgende Sprache konnte nicht gespeichert werden:',\n    localesNotSaved_other: 'Die folgenden Sprachen konnten nicht gespeichert werden:',\n    logoutFailed: 'Abmeldung fehlgeschlagen.',\n    missingEmail: 'E-Mail-Adresse fehlt.',\n    missingIDOfDocument: 'ID des zu speichernden Dokuments fehlt.',\n    missingIDOfVersion: 'ID der Version fehlt.',\n    missingRequiredData: 'Erforderliche Daten fehlen.',\n    noFilesUploaded: 'Es wurden keine Dateien hochgeladen.',\n    noMatchedField: 'Kein übereinstimmendes Feld für \"{{label}}\" gefunden',\n    notAllowedToAccessPage: 'Du hast keine Berechtigung, auf diese Seite zuzugreifen.',\n    notAllowedToPerformAction: 'Du hast keine Berechtigung, diese Aktion auszuführen.',\n    notFound: 'Die angeforderte Ressource wurde nicht gefunden.',\n    noUser: 'Kein Benutzer',\n    previewing: 'Bei der Vorschau dieses Dokuments ist ein Fehler aufgetreten.',\n    problemUploadingFile: 'Beim Hochladen der Datei ist ein Fehler aufgetreten.',\n    restoringTitle:\n      'Es gab einen Fehler beim Wiederherstellen von {{title}}. Bitte überprüfen Sie Ihre Verbindung und versuchen Sie es erneut.',\n    tokenInvalidOrExpired: 'Token ist entweder ungültig oder abgelaufen.',\n    tokenNotProvided: 'Token nicht bereitgestellt.',\n    unableToCopy: 'Kopieren nicht möglich.',\n    unableToDeleteCount: '{{count}} von {{total}} {{label}} konnte nicht gelöscht werden.',\n    unableToReindexCollection:\n      'Fehler beim Neuindizieren der Sammlung {{collection}}. Vorgang abgebrochen.',\n    unableToUpdateCount: '{{count}} von {{total}} {{label}} konnten nicht aktualisiert werden.',\n    unauthorized: 'Nicht autorisiert - du musst angemeldet sein, um diese Anfrage zu stellen.',\n    unauthorizedAdmin: 'Nicht autorisiert, dieser Benutzer hat keinen Zugriff auf das Admin-Panel.',\n    unknown: 'Ein unbekannter Fehler ist aufgetreten.',\n    unPublishingDocument:\n      'Bei der Aufhebung der Veröffentlichung dieses Dokuments ist ein Fehler aufgetreten.',\n    unspecific: 'Ein Fehler ist aufgetreten.',\n    unverifiedEmail: 'Bitte verifiziere deine E-Mail, bevor du dich anmeldest.',\n    userEmailAlreadyRegistered: 'Ein Benutzer mit der angegebenen E-Mail ist bereits registriert.',\n    userLocked:\n      'Dieser Benutzer ist gesperrt, weil er zu viele fehlgeschlagene Anmeldeversuche hat.',\n    usernameAlreadyRegistered:\n      'Ein Benutzer mit dem angegebenen Benutzernamen ist bereits registriert.',\n    usernameOrPasswordIncorrect: 'Der angegebene Benutzername oder das Passwort ist falsch.',\n    valueMustBeUnique: 'Wert muss einzigartig sein',\n    verificationTokenInvalid: 'Verifizierungs-Token ist nicht korrekt.',\n  },\n  fields: {\n    addLabel: '{{label}} hinzufügen',\n    addLink: 'Link hinzufügen',\n    addNew: 'Neuen Eintrag hinzufügen',\n    addNewLabel: '{{label}} erstellen',\n    addRelationship: 'Verknüpfung hinzufügen',\n    addUpload: 'Neue Datei hochladen',\n    block: 'Block',\n    blocks: 'Blöcke',\n    blockType: 'Block-Typ',\n    chooseBetweenCustomTextOrDocument:\n      'Wähle zwischen der Eingabe einer benutzerdefinierten URL oder verknüpfe ein anderes Dokument.',\n    chooseDocumentToLink: 'Wähle ein Dokument, das du verlinken möchtest',\n    chooseFromExisting: 'Aus vorhandenen auswählen',\n    chooseLabel: '{{label}} auswählen',\n    collapseAll: 'Alle einklappen',\n    customURL: 'Eigene URL',\n    editLabelData: '{{label}} bearbeiten',\n    editLink: 'Link bearbeiten',\n    editRelationship: 'Verknüpfung bearbeiten',\n    enterURL: 'URL eingeben',\n    internalLink: 'Interne Verlinkung',\n    itemsAndMore: '{{items}} und {{count}} mehr',\n    labelRelationship: '{{label}}-Verknüpfung',\n    latitude: 'Breitengrad',\n    linkedTo: 'Verweist auf <0>{{label}}</0>',\n    linkType: 'Linktyp',\n    longitude: 'Längengrad',\n    newLabel: '{{label}} erstellen',\n    openInNewTab: 'In neuem Tab öffnen',\n    passwordsDoNotMatch: 'Passwörter stimmen nicht überein.',\n    relatedDocument: 'Verknüpftes Dokument',\n    relationTo: 'Verknüpfung zu',\n    removeRelationship: 'Verknüpfung entfernen',\n    removeUpload: 'Hochgeladene Datei löschen',\n    saveChanges: 'Änderungen speichern',\n    searchForBlock: 'Nach Block suchen',\n    selectExistingLabel: '{{label}} auswählen (vorhandene)',\n    selectFieldsToEdit: 'Wähle die zu bearbeitenden Felder aus',\n    showAll: 'Alle anzeigen',\n    swapRelationship: 'Verknüpfung tauschen',\n    swapUpload: 'Datei austauschen',\n    textToDisplay: 'Angezeigter Text',\n    toggleBlock: 'Block umschalten',\n    uploadNewLabel: '{{label}} neu hochladen',\n  },\n  folder: {\n    browseByFolder: 'Durchsuchen nach Ordner',\n    byFolder: 'Nach Ordner',\n    deleteFolder: 'Ordner löschen',\n    folderName: 'Ordnername',\n    folders: 'Ordner',\n    folderTypeDescription:\n      'Wählen Sie aus, welche Art von Sammlungsdokumenten in diesem Ordner zugelassen sein sollte.',\n    itemHasBeenMoved: '{{title}} wurde in {{folderName}} verschoben.',\n    itemHasBeenMovedToRoot: '{{title}} wurde in den Hauptordner verschoben',\n    itemsMovedToFolder: '{{title}} wurde in {{folderName}} verschoben.',\n    itemsMovedToRoot: '{{title}} wurde in den Stammordner verschoben',\n    moveFolder: 'Ordner verschieben',\n    moveItemsToFolderConfirmation:\n      'Sie sind dabei, <1>{{count}} {{label}}</1> nach <2>{{toFolder}}</2> zu verschieben. Sind Sie sicher?',\n    moveItemsToRootConfirmation:\n      'Sie sind dabei, <1>{{count}} {{label}}</1> in den Hauptordner zu verschieben. Sind Sie sicher?',\n    moveItemToFolderConfirmation:\n      'Sie sind dabei, <1>{{title}}</1> zu <2>{{toFolder}}</2> zu verschieben. Sind Sie sicher?',\n    moveItemToRootConfirmation:\n      'Sie sind dabei, <1>{{title}}</1> in den Hauptordner zu verschieben. Sind Sie sicher?',\n    movingFromFolder: 'Verschieben von {{title}} aus {{fromFolder}}',\n    newFolder: 'Neuer Ordner',\n    noFolder: 'Kein Ordner',\n    renameFolder: 'Ordner umbenennen',\n    searchByNameInFolder: 'Suche nach Name in {{folderName}}',\n    selectFolderForItem: 'Wählen Sie den Ordner für {{title}}',\n  },\n  general: {\n    name: 'Name',\n    aboutToDelete: 'Du bist dabei {{label}} <1>{{title}}</1> zu löschen. Bist du dir sicher?',\n    aboutToDeleteCount_many: 'Du bist dabei, {{count}} {{label}} zu löschen',\n    aboutToDeleteCount_one: 'Du bist dabei, {{count}} {{label}} zu löschen',\n    aboutToDeleteCount_other: 'Du bist dabei, {{count}} {{label}} zu löschen',\n    aboutToPermanentlyDelete:\n      'Sie sind im Begriff, das {{label}} <1>{{title}}</1> dauerhaft zu löschen. Sind Sie sicher?',\n    aboutToPermanentlyDeleteTrash:\n      'Sie sind dabei, <0>{{count}}</0> <1>{{label}}</1> endgültig aus dem Papierkorb zu löschen. Sind Sie sicher?',\n    aboutToRestore:\n      'Sie sind dabei, das {{label}} <1>{{title}}</1> wiederherzustellen. Sind Sie sicher?',\n    aboutToRestoreAsDraft:\n      'Sie sind dabei, das {{label}} <1>{{title}}</1> als Entwurf wiederherzustellen. Sind Sie sicher?',\n    aboutToRestoreAsDraftCount:\n      'Sie sind dabei, {{count}} {{label}} als Entwurf wiederherzustellen.',\n    aboutToRestoreCount: 'Sie sind dabei {{count}} {{label}} wiederherzustellen',\n    aboutToTrash:\n      'Sie sind dabei, das {{label}} <1>{{title}}</1> in den Papierkorb zu verschieben. Sind Sie sicher?',\n    aboutToTrashCount: 'Sie sind dabei, {{count}} {{label}} in den Papierkorb zu verschieben.',\n    addBelow: 'Unterhalb hinzufügen',\n    addFilter: 'Filter hinzufügen',\n    adminTheme: 'Admin-Erscheinungsbild',\n    all: 'Alle',\n    allCollections: 'Alle Sammlungen',\n    allLocales: 'Alle Standorte',\n    and: 'Und',\n    anotherUser: 'Ein anderer Benutzer',\n    anotherUserTakenOver: 'Ein anderer Benutzer hat die Bearbeitung dieses Dokuments übernommen.',\n    applyChanges: 'Änderungen anwenden',\n    ascending: 'Aufsteigend',\n    automatic: 'Automatisch',\n    backToDashboard: 'Zurück zur Übersicht',\n    cancel: 'Abbrechen',\n    changesNotSaved:\n      'Deine Änderungen wurden nicht gespeichert. Wenn du diese Seite verlässt, gehen deine Änderungen verloren.',\n    clear:\n      'Respektieren Sie die Bedeutung des ursprünglichen Textes im Kontext von Payload. Hier ist eine Liste von gängigen Payload-Begriffen, die sehr spezifische Bedeutungen tragen:\\n    - Sammlung: Eine Sammlung ist eine Gruppe von Dokumenten, die eine gemeinsame Struktur und Funktion teilen. Sammlungen werden verwendet, um Inhalte in Payload zu organisieren und zu verwalten.\\n    - Feld: Ein Feld ist ein spezifisches Datenstück innerhalb eines Dokuments in einer Sammlung. Felder definieren die Struktur und den Datentyp, der in einem Dokument gespeichert werden kann.\\n    -',\n    clearAll: 'Alles löschen',\n    close: 'Schließen',\n    collapse: 'Einklappen',\n    collections: 'Sammlungen',\n    columns: 'Spalten',\n    columnToSort: 'Spalten zum Sortieren',\n    confirm: 'Bestätigen',\n    confirmCopy: 'Kopie bestätigen',\n    confirmDeletion: 'Löschen bestätigen',\n    confirmDuplication: 'Duplizieren bestätigen',\n    confirmMove: 'Bestätigen Sie den Umzug.',\n    confirmReindex: 'Alle {{collections}} neu indizieren?',\n    confirmReindexAll: 'Alle Sammlungen neu indizieren?',\n    confirmReindexDescription:\n      'Dies entfernt bestehende Indizes und indiziert die Dokumente in den {{collections}}-Sammlungen neu.',\n    confirmReindexDescriptionAll:\n      'Dies entfernt bestehende Indizes und indiziert die Dokumente in allen Sammlungen neu.',\n    confirmRestoration: 'Bestätigen Sie die Wiederherstellung',\n    copied: 'Kopiert',\n    copy: 'Kopieren',\n    copyField: 'Feld kopieren',\n    copying: 'Kopieren',\n    copyRow: 'Zeile kopieren',\n    copyWarning:\n      'Du bist dabei, {{to}} mit {{from}} für {{label}} {{title}} zu überschreiben. Bist du dir sicher?',\n    create: 'Erstellen',\n    created: 'Erstellt',\n    createdAt: 'Erstellt am',\n    createNew: 'Neu erstellen',\n    createNewLabel: '{{label}} neu erstellen',\n    creating: 'Erstelle',\n    creatingNewLabel: 'Erstelle {{label}}',\n    currentlyEditing:\n      'bearbeitet gerade dieses Dokument. Wenn du übernimmst, wird die Bearbeitung blockiert und nicht gespeicherte Änderungen können verloren gehen.',\n    custom: 'Benutzerdefiniert',\n    dark: 'Dunkel',\n    dashboard: 'Übersicht',\n    delete: 'Löschen',\n    deleted: 'Gelöscht',\n    deletedAt: 'Gelöscht am',\n    deletedCountSuccessfully: '{{count}} {{label}} erfolgreich gelöscht.',\n    deletedSuccessfully: 'Erfolgreich gelöscht.',\n    deletePermanently: 'Überspringen Sie den Papierkorb und löschen Sie dauerhaft.',\n    deleting: 'Löschen...',\n    depth: 'Tiefe',\n    descending: 'Absteigend',\n    deselectAllRows: 'Alle Zeilen abwählen',\n    document: 'Dokument',\n    documentIsTrashed: 'Dieses {{label}} wurde gelöscht und ist nur lesbar.',\n    documentLocked: 'Dokument gesperrt',\n    documents: 'Dokumente',\n    duplicate: 'Duplizieren',\n    duplicateWithoutSaving: 'Duplizieren ohne Änderungen zu speichern',\n    edit: 'Bearbeiten',\n    editAll: 'Bearbeite alle',\n    editedSince: 'Bearbeitet seit',\n    editing: 'Bearbeite',\n    editingLabel_many: 'Bearbeiten von {{count}} {{label}}',\n    editingLabel_one: 'Bearbeiten von {{count}} {{label}}',\n    editingLabel_other: 'Bearbeiten von {{count}} {{label}}',\n    editingTakenOver: 'Bearbeitung übernommen',\n    editLabel: '{{label}} bearbeiten',\n    email: 'E-Mail',\n    emailAddress: 'E-Mail-Adresse',\n    emptyTrash: 'Papierkorb leeren',\n    emptyTrashLabel: 'Leeren Sie den {{label}} Papierkorb',\n    enterAValue: 'Gib einen Wert ein',\n    error: 'Fehler',\n    errors: 'Fehler',\n    exitLivePreview: 'Beenden Sie die Live-Vorschau',\n    export: 'Export',\n    fallbackToDefaultLocale: 'Auf die Standardsprache zurückfallen',\n    false: 'Falsch',\n    filter: 'Filter',\n    filters: 'Filter',\n    filterWhere: 'Filter {{label}}, wo',\n    globals: 'Globale Dokumente',\n    goBack: 'Zurück',\n    groupByLabel: 'Gruppieren nach {{label}}',\n    import: 'Importieren',\n    isEditing: 'bearbeitet gerade',\n    item: 'Artikel',\n    items: 'Artikel',\n    language: 'Sprache',\n    lastModified: 'Zuletzt geändert',\n    leaveAnyway: 'Trotzdem verlassen',\n    leaveWithoutSaving: 'Ohne speichern verlassen',\n    light: 'Hell',\n    livePreview: 'Live-Vorschau',\n    loading: 'Lädt',\n    locale: 'Sprache',\n    locales: 'Sprachen',\n    menu: 'Menü',\n    moreOptions: 'Mehr Optionen',\n    move: 'Bewegen',\n    moveConfirm:\n      'Sie sind dabei {{count}} {{label}} nach <1>{{destination}}</1> zu verschieben. Sind Sie sicher?',\n    moveCount: 'Bewegen Sie {{count}} {{label}}',\n    moveDown: 'Nach unten bewegen',\n    moveUp: 'Nach oben bewegen',\n    moving: 'Umziehen',\n    movingCount: 'Verschieben {{count}} {{label}}',\n    newPassword: 'Neues Passwort',\n    next: 'Nächste',\n    no: 'Nein',\n    noDateSelected: 'Kein Datum ausgewählt',\n    noFiltersSet: 'Keine Filter gesetzt',\n    noLabel: '<Kein {{label}}>',\n    none: 'Kein',\n    noOptions: 'Keine Optionen',\n    noResults:\n      'Keine {{label}} gefunden. Entweder es existieren keine {{label}} oder es gibt keine Übereinstimmung zu den von dir verwendeten Filtern.',\n    notFound: 'Nicht gefunden',\n    nothingFound: 'Keine Ergebnisse',\n    noTrashResults: 'Kein {{label}} im Papierkorb.',\n    noUpcomingEventsScheduled: 'Keine bevorstehenden Veranstaltungen geplant.',\n    noValue: 'Kein Wert',\n    of: 'von',\n    only: 'Nur',\n    open: 'Öffnen',\n    or: 'oder',\n    order: 'Reihenfolge',\n    overwriteExistingData: 'Vorhandene Eingaben überschreiben',\n    pageNotFound: 'Seite nicht gefunden',\n    password: 'Passwort',\n    pasteField: 'Feld einfügen',\n    pasteRow: 'Zeile einfügen',\n    payloadSettings: 'Payload-Einstellungen',\n    permanentlyDelete: 'Dauerhaft löschen',\n    permanentlyDeletedCountSuccessfully: '{{count}} {{label}} erfolgreich dauerhaft gelöscht.',\n    perPage: 'Pro Seite: {{limit}}',\n    previous: 'Vorherige',\n    reindex: 'Neuindizieren',\n    reindexingAll: 'Alle {{collections}} werden neu indiziert.',\n    remove: 'Entfernen',\n    rename: 'Umbenennen',\n    reset: 'Zurücksetzen',\n    resetPreferences: 'Präferenzen zurücksetzen',\n    resetPreferencesDescription: 'Alle Präferenzen werden auf die Standardwerte zurückgesetzt.',\n    resettingPreferences: 'Präferenzen werden zurückgesetzt.',\n    restore: 'Wiederherstellen',\n    restoreAsPublished: 'Wiederherstellen als veröffentlichte Version',\n    restoredCountSuccessfully: '{{count}} {{label}} erfolgreich wiederhergestellt.',\n    restoring:\n      'Respektieren Sie die Bedeutung des Originaltextes im Kontext von Payload. Hier ist eine Liste häufiger Payload-Begriffe, die sehr spezifische Bedeutungen haben:\\n    - Sammlung: Eine Sammlung ist eine Gruppe von Dokumenten, die eine gemeinsame Struktur und einen gemeinsamen Zweck teilen. Sammlungen werden verwendet, um Inhalte in Payload zu organisieren und zu verwalten.\\n    - Feld: Ein Feld ist ein spezifisches Datenstück innerhalb eines Dokuments in einer Sammlung. Felder definieren die Struktur und den Datentyp, der in einem Dokument gespeichert werden kann.\\n    - Dokument:',\n    row: 'Zeile',\n    rows: 'Zeilen',\n    save: 'Speichern',\n    saving: 'Speichern...',\n    schedulePublishFor: 'Plane die Veröffentlichung für {{title}}',\n    searchBy: 'Suche nach {{label}}',\n    select: 'Auswählen',\n    selectAll: 'Alle {{count}} {{label}} auswählen',\n    selectAllRows: 'Alle Zeilen auswählen',\n    selectedCount: '{{count}} {{label}} ausgewählt',\n    selectLabel: 'Wähle {{label}}',\n    selectValue: 'Wert auswählen',\n    showAllLabel: 'Zeige alle {{label}}',\n    sorryNotFound:\n      'Es tut uns leid, aber wir haben nichts gefunden, was deiner Anfrage entspricht.',\n    sort: 'Sortieren',\n    sortByLabelDirection: 'Sortieren nach {{label}} {{direction}}',\n    stayOnThisPage: 'Auf dieser Seite bleiben',\n    submissionSuccessful: 'Übermittlung erfolgreich.',\n    submit: 'Senden',\n    submitting: 'Wird aktualisiert...',\n    success: 'Erfolg',\n    successfullyCreated: '{{label}} erfolgreich erstellt.',\n    successfullyDuplicated: '{{label}} wurde erfolgreich dupliziert.',\n    successfullyReindexed:\n      'Erfolgreich {{count}} von {{total}} Dokumenten aus {{collections}} Sammlungen neu indiziert.',\n    takeOver: 'Übernehmen',\n    thisLanguage: 'Deutsch',\n    time: 'Zeit',\n    timezone: 'Zeitzone',\n    titleDeleted: '{{label}} {{title}} wurde erfolgreich gelöscht.',\n    titleRestored: '{{label}} \"{{title}}\" erfolgreich wiederhergestellt.',\n    titleTrashed: '{{label}} \"{{title}}\" wurde in den Papierkorb verschoben.',\n    trash: 'Müll',\n    trashedCountSuccessfully: '{{count}} {{label}} wurde in den Papierkorb verschoben.',\n    true: 'Wahr',\n    unauthorized: 'Nicht autorisiert',\n    unsavedChanges:\n      'Du hast ungespeicherte Änderungen. Speichern oder verwerfe sie, bevor du fortfahrst.',\n    unsavedChangesDuplicate:\n      'Du hast ungespeicherte Änderungen, möchtest du mit dem Duplizieren fortfahren?',\n    untitled: 'ohne Titel',\n    upcomingEvents: 'Bevorstehende Veranstaltungen',\n    updatedAt: 'Aktualisiert am',\n    updatedCountSuccessfully: '{{count}} {{label}} erfolgreich aktualisiert.',\n    updatedLabelSuccessfully: '{{label}} erfolgreich aktualisiert.',\n    updatedSuccessfully: 'Erfolgreich aktualisiert.',\n    updateForEveryone: 'Für alle aktualisieren',\n    updating: 'Aktualisierung',\n    uploading: 'Hochladen',\n    uploadingBulk: 'Hochladen von {{current}} von {{total}}',\n    user: 'Benutzer',\n    username: 'Benutzername',\n    users: 'Benutzer',\n    value: 'Wert',\n    viewing: 'Ansehen',\n    viewReadOnly: 'Nur-Lese-Ansicht',\n    welcome: 'Willkommen',\n    yes: 'Ja',\n  },\n  localization: {\n    cannotCopySameLocale: 'Kann nicht in dieselbe Sprache kopiert werden',\n    copyFrom: 'Kopieren von',\n    copyFromTo: 'Kopieren von {{from}} zu {{to}}',\n    copyTo: 'Kopieren nach',\n    copyToLocale: 'Erstelle Kopie für Sprach-Variante',\n    localeToPublish: 'Zu veröffentlichende Sprache',\n    selectLocaleToCopy: 'Wähle den Ort zum Kopieren aus',\n  },\n  operators: {\n    contains: 'enthält',\n    equals: 'gleich',\n    exists: 'existiert',\n    intersects: 'schneidet sich',\n    isGreaterThan: 'ist größer als',\n    isGreaterThanOrEqualTo: 'ist größer oder gleich',\n    isIn: 'ist drin',\n    isLessThan: 'ist kleiner als',\n    isLessThanOrEqualTo: 'ist kleiner oder gleich',\n    isLike: 'ist wie',\n    isNotEqualTo: 'ist nicht gleich',\n    isNotIn: 'ist nicht drin',\n    isNotLike: 'ist nicht wie',\n    near: 'in der Nähe',\n    within: 'innerhalb',\n  },\n  upload: {\n    addFile: 'Datei hinzufügen',\n    addFiles: 'Dateien hinzufügen',\n    bulkUpload: 'Mehrere Dateien hochladen',\n    crop: 'Zuschneiden',\n    cropToolDescription:\n      'Ziehe die Ecken des ausgewählten Bereichs, zeichne einen neuen Bereich oder passe die Werte unten an.',\n    download: 'Herunterladen',\n    dragAndDrop: 'Bewege eine Datei per Drag-and-Drop',\n    dragAndDropHere: 'oder lege eine Datei hier ab',\n    editImage: 'Bild bearbeiten',\n    fileName: 'Dateiname',\n    fileSize: 'Dateigröße',\n    filesToUpload: 'Dateien zum Hochladen',\n    fileToUpload: 'Datei zum Hochladen',\n    focalPoint: 'Brennpunkt',\n    focalPointDescription:\n      'Setze den Fokuspunkt direkt auf der Vorschau oder passe die Werte unten an.',\n    height: 'Höhe',\n    lessInfo: 'Weniger Info',\n    moreInfo: 'Mehr Info',\n    noFile: 'Keine Datei',\n    pasteURL: 'URL einfügen',\n    previewSizes: 'Vorschaugrößen',\n    selectCollectionToBrowse: 'Wähle eine Sammlung zum Durchsuchen aus',\n    selectFile: 'Datei auswählen',\n    setCropArea: 'Bereich zum Zuschneiden festlegen',\n    setFocalPoint: 'Fokuspunkt setzen',\n    sizes: 'Größen',\n    sizesFor: 'Größen für {{label}}',\n    width: 'Breite',\n  },\n  validation: {\n    emailAddress: 'Bitte gib eine korrekte E-Mail-Adresse an.',\n    enterNumber: 'Bitte gib eine gültige Nummer an.',\n    fieldHasNo: 'Dieses Feld hat kein {{label}}',\n    greaterThanMax: '{{value}} ist größer als der maximal erlaubte {{label}} von {{max}}.',\n    invalidInput: 'Dieses Feld hat einen inkorrekten Wert.',\n    invalidSelection: 'Dieses Feld hat eine inkorrekte Auswahl.',\n    invalidSelections: 'Dieses Feld enthält die folgenden inkorrekten Auswahlmöglichkeiten:',\n    lessThanMin: '{{value}} ist kleiner als der minimal erlaubte {{label}} von {{min}}.',\n    limitReached: 'Limit erreicht, es können nur {{max}} Elemente hinzugefügt werden.',\n    longerThanMin: 'Dieser Wert muss länger als die minimale Länge von {{minLength}} Zeichen sein.',\n    notValidDate: '\"{{value}}\" ist kein gültiges Datum.',\n    required: 'Pflichtfeld',\n    requiresAtLeast: 'Dieses Feld muss mindestens {{count}} {{label}} enthalten.',\n    requiresNoMoreThan: 'Dieses Feld kann nicht mehr als {{count}} {{label}} enthalten.',\n    requiresTwoNumbers: 'Dieses Feld muss zwei Zahlen enthalten.',\n    shorterThanMax: 'Dieser Wert muss kürzer als die maximale Länge von {{maxLength}} sein.',\n    timezoneRequired: 'Eine Zeitzone ist erforderlich.',\n    trueOrFalse: 'Dieses Feld kann nur wahr oder falsch sein.',\n    username:\n      'Bitte gib einen gültigen Benutzernamen ein. Dieser kann Buchstaben, Zahlen, Bindestriche, Punkte und Unterstriche enthalten.',\n    validUploadID: 'Dieses Feld enthält keine valide Upload-ID.',\n  },\n  version: {\n    type: 'Typ',\n    aboutToPublishSelection:\n      'Du bist dabei, alle {{label}} in der Auswahl zu veröffentlichen. Bist du dir sicher?',\n    aboutToRestore: 'Du bist dabei, {{label}} auf den Stand vom {{versionDate}} zurücksetzen.',\n    aboutToRestoreGlobal:\n      'Du bist dabei, das Globale Dokument {{label}} auf den Stand vom {{versionDate}} zurückzusetzen.',\n    aboutToRevertToPublished:\n      'Du bist dabei, dieses Dokument auf den Stand des ersten Veröffentlichungsdatums zurückzusetzen. Bist du sicher?',\n    aboutToUnpublish: 'Du bist dabei dieses Dokument auf Entwurf zu setzen. Bist du dir sicher?',\n    aboutToUnpublishSelection:\n      'Du bist dabei, die Veröffentlichung aller {{label}} in der Auswahl aufzuheben. Bist du dir sicher?',\n    autosave: 'Automatische Speicherung',\n    autosavedSuccessfully: 'Erfolgreich automatisch gespeichert.',\n    autosavedVersion: 'Automatisch gespeicherte Version',\n    changed: 'Geändert',\n    changedFieldsCount_one: '{{count}} geändertes Feld',\n    changedFieldsCount_other: '{{count}} geänderte Felder',\n    compareVersion: 'Vergleiche Version zu:',\n    compareVersions: 'Versionen vergleichen',\n    comparingAgainst: 'Im Vergleich zu',\n    confirmPublish: 'Veröffentlichung bestätigen',\n    confirmRevertToSaved: 'Zurücksetzen auf die letzte Speicherung bestätigen',\n    confirmUnpublish: 'Aufhebung der Veröffentlichung bestätigen',\n    confirmVersionRestoration: 'Wiederherstellung der Version bestätigen',\n    currentDocumentStatus: 'Aktueller Dokumentenstatus: {{docStatus}}',\n    currentDraft: 'Aktueller Entwurf',\n    currentlyPublished: 'Derzeit veröffentlicht',\n    currentlyViewing: 'Derzeitige Ansicht',\n    currentPublishedVersion: 'Aktuell veröffentlichte Version',\n    draft: 'Entwurf',\n    draftSavedSuccessfully: 'Entwurf erfolgreich gespeichert.',\n    lastSavedAgo: 'Zuletzt vor {{distance}} gespeichert',\n    modifiedOnly: 'Nur modifiziert',\n    moreVersions: 'Mehr Versionen...',\n    noFurtherVersionsFound: 'Keine weiteren Versionen vorhanden',\n    noRowsFound: 'Kein {{label}} gefunden',\n    noRowsSelected: 'Kein {{label}} ausgewählt',\n    preview: 'Vorschau',\n    previouslyDraft: 'Früher ein Entwurf',\n    previouslyPublished: 'Zuvor veröffentlicht',\n    previousVersion: 'Frühere Version',\n    problemRestoringVersion: 'Bei der Wiederherstellung der Version ist ein Fehler aufgetreten',\n    publish: 'Veröffentlichen',\n    publishAllLocales: 'Alle Sprachen veröffentlichen',\n    publishChanges: 'Änderungen veröffentlichen',\n    published: 'Veröffentlicht',\n    publishIn: 'Veröffentlichen auf {{locale}}',\n    publishing: 'Veröffentlichung',\n    restoreAsDraft: 'Als Entwurf wiederherstellen',\n    restoredSuccessfully: 'Erfolgreich wiederhergestellt.',\n    restoreThisVersion: 'Diese Version wiederherstellen',\n    restoring: 'Wiederherstellen...',\n    reverting: 'Zurücksetzen...',\n    revertToPublished: 'Auf veröffentlichte Version zurücksetzen',\n    saveDraft: 'Entwurf speichern',\n    scheduledSuccessfully: 'Erfolgreich geplant.',\n    schedulePublish: 'Veröffentlichungsplan',\n    selectLocales: 'Wähle anzuzeigende Sprachen',\n    selectVersionToCompare: 'Wähle Version zum Vergleich',\n    showingVersionsFor: 'Versionen anzeigen für:',\n    showLocales: 'Sprachen anzeigen:',\n    specificVersion: 'Spezifische Version',\n    status: 'Status',\n    unpublish: 'Veröffentlichung aufheben',\n    unpublishing: 'Veröffentlichung aufheben...',\n    version: 'Version',\n    versionAgo: 'vor {{distance}}',\n    versionCount_many: '{{count}} Versionen gefunden',\n    versionCount_none: 'Keine Versionen gefunden',\n    versionCount_one: '{{count}} Version gefunden',\n    versionCount_other: '{{count}} Versionen gefunden',\n    versionCreatedOn: '{{version}} erstellt am:',\n    versionID: 'Version-ID',\n    versions: 'Versionen',\n    viewingVersion: 'Version für {{entityLabel}} {{documentTitle}} ansehen',\n    viewingVersionGlobal: 'Version für das Globale Dokument {{entityLabel}} ansehen',\n    viewingVersions: 'Versionen für {{entityLabel}} {{documentTitle}} ansehen',\n    viewingVersionsGlobal: 'Versionen für das Globale Dokument {{entityLabel}} ansehen',\n  },\n}\n\nexport const de: Language = {\n  dateFNSKey: 'de',\n  translations: deTranslations,\n}\n"], "names": ["deTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "de", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,iBAA4C;IACvDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCACE;QACFC,WAAW;QACXC,WAAW;QACXC,UACE;QACFC,wBACE;QACFC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBACE;QACFC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BACE;IACJ;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eACE;QACFC,kBACE;QACFC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCACE;QACFC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBACE;QACFC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BACE;QACFC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBACE;QACFC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YACE;QACFC,2BACE;QACFC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCACE;QACFC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBACE;QACFC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BACE;QACFC,8BACE;QACFC,4BACE;QACFC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BACE;QACFC,+BACE;QACFC,gBACE;QACFC,uBACE;QACFC,4BACE;QACFC,qBAAqB;QACrBC,cACE;QACFC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBACE;QACFC,OACE;QACFC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BACE;QACFC,8BACE;QACFC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aACE;QACFC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aACE;QACFC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WACE;QACF9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WACE;QACFC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eACE;QACFC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBACE;QACFC,yBACE;QACFC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBACE;QACFC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBACE;QACFC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UACE;QACFga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBACE;QACF5R,gBAAgB;QAChB6R,sBACE;QACFC,0BACE;QACFC,kBAAkB;QAClBC,2BACE;QACFC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,KAAe;IAC1BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}