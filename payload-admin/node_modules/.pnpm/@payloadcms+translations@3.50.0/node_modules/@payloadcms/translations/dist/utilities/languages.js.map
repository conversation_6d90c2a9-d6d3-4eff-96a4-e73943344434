{"version": 3, "sources": ["../../src/utilities/languages.ts"], "sourcesContent": ["import type { AcceptedLanguages, LanguagePreference } from '../types.js'\n\nexport const rtlLanguages = ['ar', 'fa', 'he'] as const\n\nexport const acceptedLanguages = [\n  'ar',\n  'az',\n  'bg',\n  'bn-BD',\n  'bn-IN',\n  'ca',\n  'cs',\n  'bn-BD',\n  'bn-IN',\n  'da',\n  'de',\n  'en',\n  'es',\n  'et',\n  'fa',\n  'fr',\n  'he',\n  'hr',\n  'hu',\n  'hy',\n  'id',\n  'it',\n  'ja',\n  'ko',\n  'lt',\n  'lv',\n  'my',\n  'nb',\n  'nl',\n  'pl',\n  'pt',\n  'ro',\n  'rs',\n  'rs-latin',\n  'ru',\n  'sk',\n  'sl',\n  'sv',\n  'th',\n  'tr',\n  'uk',\n  'vi',\n  'zh',\n  'zh-TW',\n\n  /**\n   * Languages not implemented:\n   *\n   * 'af',\n   * 'am',\n   * 'ar-sa',\n   * 'as',\n   * 'az-latin',\n   * 'be',\n   * 'bs',\n   * 'ca-ES-valencia',\n   * 'cy',\n   * 'el',\n   * 'en-GB',\n   * 'en-US',\n   * 'es-ES',\n   * 'es-US',\n   * 'es-MX',\n   * 'eu',\n   * 'fi',\n   * 'fil-Latn',\n   * 'fr-FR',\n   * 'fr-CA',\n   * 'ga',\n   * 'gd-Latn',\n   * 'gl',\n   * 'gu',\n   * 'ha-Latn',\n   * 'hi',\n   * 'ig-Latn',\n   * 'is',\n   * 'it-it',\n   * 'ka',\n   * 'kk',\n   * 'km',\n   * 'kn',\n   * 'kok',\n   * 'ku-Arab',\n   * 'ky-Cyrl',\n   * 'lb',\n   * 'mi-Latn',\n   * 'mk',\n   * 'ml',\n   * 'mn-Cyrl',\n   * 'mr',\n   * 'ms',\n   * 'mt',\n   * 'ne',\n   * 'nl-BE',\n   * 'nn',\n   * 'nso',\n   * 'or',\n   * 'pa',\n   * 'pa-Arab',\n   * 'prs-Arab',\n   * 'pt-BR',\n   * 'pt-PT',\n   * 'qut-Latn',\n   * 'quz',\n   * 'rw',\n   * 'sd-Arab',\n   * 'si',\n   * 'sq',\n   * 'sr-Cyrl-BA',\n   * 'sr-Cyrl-RS',\n   * 'sr-Latn-RS',\n   * 'sw',\n   * 'ta',\n   * 'te',\n   * 'tg-Cyrl',\n   * 'ti',\n   * 'tk-Latn',\n   * 'tn',\n   * 'tt-Cyrl',\n   * 'ug-Arab',\n   * 'ur',\n   * 'uz-Latn',\n   * 'wo',\n   * 'xh',\n   * 'yo-Latn',\n   * 'zh-Hans',\n   * 'zh-Hant',\n   * 'zu',\n   */\n] as const\n\nfunction parseAcceptLanguage(acceptLanguageHeader: string): LanguagePreference[] {\n  return acceptLanguageHeader\n    .split(',')\n    .map((lang) => {\n      const [language, quality] = lang.trim().split(';q=') as [\n        AcceptedLanguages,\n        string | undefined,\n      ]\n      return {\n        language,\n        quality: quality ? parseFloat(quality) : 1,\n      }\n    })\n    .sort((a, b) => b.quality - a.quality) // Sort by quality, highest to lowest\n}\n\nexport function extractHeaderLanguage(acceptLanguageHeader: string): AcceptedLanguages | undefined {\n  const parsedHeader = parseAcceptLanguage(acceptLanguageHeader)\n\n  let matchedLanguage: AcceptedLanguages | undefined\n\n  for (const { language } of parsedHeader) {\n    if (!matchedLanguage && acceptedLanguages.includes(language)) {\n      matchedLanguage = language\n    }\n  }\n\n  return matchedLanguage\n}\n"], "names": ["rtlLanguages", "acceptedLanguages", "parseAcceptLanguage", "acceptLanguageHeader", "split", "map", "lang", "language", "quality", "trim", "parseFloat", "sort", "a", "b", "extractHeaderLanguage", "parsed<PERSON>eader", "matchedLanguage", "includes"], "mappings": "AAEA,OAAO,MAAMA,eAAe;IAAC;IAAM;IAAM;CAAK,CAAS;AAEvD,OAAO,MAAMC,oBAAoB;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CAsFD,CAAS;AAEV,SAASC,oBAAoBC,oBAA4B;IACvD,OAAOA,qBACJC,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC;QACJ,MAAM,CAACC,UAAUC,QAAQ,GAAGF,KAAKG,IAAI,GAAGL,KAAK,CAAC;QAI9C,OAAO;YACLG;YACAC,SAASA,UAAUE,WAAWF,WAAW;QAC3C;IACF,GACCG,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEL,OAAO,GAAGI,EAAEJ,OAAO,EAAE,qCAAqC;;AAChF;AAEA,OAAO,SAASM,sBAAsBX,oBAA4B;IAChE,MAAMY,eAAeb,oBAAoBC;IAEzC,IAAIa;IAEJ,KAAK,MAAM,EAAET,QAAQ,EAAE,IAAIQ,aAAc;QACvC,IAAI,CAACC,mBAAmBf,kBAAkBgB,QAAQ,CAACV,WAAW;YAC5DS,kBAAkBT;QACpB;IACF;IAEA,OAAOS;AACT"}