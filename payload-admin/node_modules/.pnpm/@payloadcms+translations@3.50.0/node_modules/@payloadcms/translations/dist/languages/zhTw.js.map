{"version": 3, "sources": ["../../src/languages/zhTw.ts"], "sourcesContent": ["import type { DefaultTranslationsObject, Language } from '../types.js'\n\nexport const zhTwTranslations: DefaultTranslationsObject = {\n  authentication: {\n    account: '帳戶',\n    accountOfCurrentUser: '目前使用者的帳戶',\n    accountVerified: '帳戶驗證成功。',\n    alreadyActivated: '已經啟用了',\n    alreadyLoggedIn: '已經登入了',\n    apiKey: 'API金鑰',\n    authenticated: '經過身份驗證的',\n    backToLogin: '返回登入頁面',\n    beginCreateFirstUser: '首先，請建立您的第一個使用者。',\n    changePassword: '更改密碼',\n    checkYourEmailForPasswordReset:\n      '如果此電子郵件地址已與一個帳戶相關聯，您將很快收到重設密碼的指示。如果您在收件箱中看不到該電子郵件，請檢查您的垃圾郵件或垃圾郵件夾。',\n    confirmGeneration: '確認生成',\n    confirmPassword: '確認密碼',\n    createFirstUser: '建立第一個使用者',\n    emailNotValid: '提供的電子郵件無效',\n    emailOrUsername: '電子郵件或使用者名稱',\n    emailSent: '電子郵件已寄出',\n    emailVerified: '電子郵件驗證成功。',\n    enableAPIKey: '啟用API金鑰',\n    failedToUnlock: '解鎖失敗',\n    forceUnlock: '強制解鎖',\n    forgotPassword: '忘記密碼',\n    forgotPasswordEmailInstructions:\n      '請在下方輸入您的電子郵件。您將收到一封有關如何重設密碼的說明電子郵件。',\n    forgotPasswordQuestion: '忘記密碼？',\n    forgotPasswordUsernameInstructions:\n      '請在下方輸入您的使用者名稱。關於如何重設密碼的指示將會發送到與您的使用者名稱相關的電子郵件地址。',\n    generate: '生成',\n    generateNewAPIKey: '生成新的API金鑰',\n    generatingNewAPIKeyWillInvalidate: '生成新的API金鑰將使之前的金鑰<1>失效</1>。您確定要繼續嗎？',\n    lockUntil: '鎖定直到',\n    logBackIn: '重新登入',\n    loggedIn: '要使用另一個使用者登入前，您需要先<0>登出</0>。',\n    loggedInChangePassword: '要更改您的密碼，請前往您的<0>帳戶</0>頁面並在那裡編輯您的密碼。',\n    loggedOutInactivity: '您由於不活躍而被登出了。',\n    loggedOutSuccessfully: '您已成功登出。',\n    loggingOut: '登出中...',\n    login: '登入',\n    loginAttempts: '登入次數',\n    loginUser: '登入使用者',\n    loginWithAnotherUser: '要使用另一個使用者登入前，您需要先<0>登出</0>。',\n    logOut: '登出',\n    logout: '登出',\n    logoutSuccessful: '成功登出。',\n    logoutUser: '登出使用者',\n    newAccountCreated:\n      '剛剛為您建立了一個可以存取 <a href=\"{{serverURL}}\">{{serverURL}}</a> 的新帳戶。請點擊以下連結或在瀏覽器中貼上以下網址以驗證您的電子郵件：<a href=\"{{verificationURL}}\">{{verificationURL}}</a><br> 驗證您的電子郵件後，您將能夠成功登入。',\n    newAPIKeyGenerated: '新的API金鑰已生成。',\n    newPassword: '新的密碼',\n    passed: '身份驗證通過',\n    passwordResetSuccessfully: '成功重設密碼。',\n    resetPassword: '重設密碼',\n    resetPasswordExpiration: '重設密碼的有效期',\n    resetPasswordToken: '重設密碼令牌',\n    resetYourPassword: '重設您的密碼',\n    stayLoggedIn: '保持登入狀態',\n    successfullyRegisteredFirstUser: '成功註冊了第一個使用者。',\n    successfullyUnlocked: '已成功解鎖',\n    tokenRefreshSuccessful: '令牌刷新成功。',\n    unableToVerify: '無法驗證',\n    username: '使用者名稱',\n    usernameNotValid: '提供的使用者名稱無效',\n    verified: '已驗證',\n    verifiedSuccessfully: '成功驗證',\n    verify: '驗證',\n    verifyUser: '驗證使用者',\n    verifyYourEmail: '驗證您的電子郵件',\n    youAreInactive:\n      '您已經有一段時間沒有活動了，為了您的安全，很快就會自動登出。您想保持登入狀態嗎？',\n    youAreReceivingResetPassword:\n      '您收到此郵件是因為您（或其他人）已請求重設您帳戶的密碼。請點擊以下連結，或將其貼上到您的瀏覽器中以完成該過程：',\n    youDidNotRequestPassword: '如果您沒有要求這樣做，請忽略這封郵件，您的密碼將保持不變。',\n  },\n  error: {\n    accountAlreadyActivated: '該帳戶已被啟用。',\n    autosaving: '自動儲存該文件時出現了問題。',\n    correctInvalidFields: '請更正無效區塊。',\n    deletingFile: '刪除文件時出現了錯誤。',\n    deletingTitle: '刪除{{title}}時出現了錯誤。請檢查您的網路連線並重試。',\n    documentNotFound: '找不到具有ID {{id}}的文件。可能已被刪除或從未存在，或者您可能無法訪問它。',\n    emailOrPasswordIncorrect: '提供的電子郵件或密碼不正確。',\n    followingFieldsInvalid_one: '下面的字串是無效的：',\n    followingFieldsInvalid_other: '以下字串是無效的：',\n    incorrectCollection: '不正確的集合',\n    insufficientClipboardPermissions: '剪貼簿訪問被拒絕。請檢查您的剪貼簿權限。',\n    invalidClipboardData: '剪貼簿資料無效。',\n    invalidFileType: '無效的文件類型',\n    invalidFileTypeValue: '無效的文件類型： {{value}}',\n    invalidRequestArgs: '請求中傳遞了無效的參數：{{args}}',\n    loadingDocument: '加載ID為{{id}}的文件時出現了問題。',\n    localesNotSaved_one: '以下的地區設定無法保存：',\n    localesNotSaved_other: '以下地區無法保存：',\n    logoutFailed: '登出失敗。',\n    missingEmail: '缺少電子郵件。',\n    missingIDOfDocument: '缺少需要更新的文檔的ID。',\n    missingIDOfVersion: '缺少版本的ID。',\n    missingRequiredData: '缺少必要的數據。',\n    noFilesUploaded: '沒有上傳文件。',\n    noMatchedField: '找不到與\"{{label}}\"匹配的字串',\n    notAllowedToAccessPage: '您沒有權限訪問此頁面。',\n    notAllowedToPerformAction: '您不被允許執行此操作。',\n    notFound: '沒有找到請求的資源。',\n    noUser: '沒有該使用者',\n    previewing: '預覽文件時出現了問題。',\n    problemUploadingFile: '上傳文件時出現了問題。',\n    restoringTitle: '在恢復 {{title}} 時發生了錯誤。請檢查您的連接並再試一次。',\n    tokenInvalidOrExpired: '令牌無效或已過期。',\n    tokenNotProvided: '未提供令牌。',\n    unableToCopy: '無法複製。',\n    unableToDeleteCount: '無法從 {{total}} 個中刪除 {{count}} 個 {{label}}。',\n    unableToReindexCollection: '重新索引集合 {{collection}} 時出現錯誤。操作已中止。',\n    unableToUpdateCount: '無法從 {{total}} 個中更新 {{count}} 個 {{label}}。',\n    unauthorized: '未經授權，您必須登錄才能提出這個請求。',\n    unauthorizedAdmin: '未經授權，此使用者無法訪問管理面板。',\n    unknown: '發生了一個未知的錯誤。',\n    unPublishingDocument: '取消發布此文件時出現了問題。',\n    unspecific: '發生了一個錯誤。',\n    unverifiedEmail: '請在登入前驗證您的電子郵件。',\n    userEmailAlreadyRegistered: '給定電子郵件的用戶已經註冊。',\n    userLocked: '該使用者由於有太多次失敗的登錄嘗試而被鎖定。',\n    usernameAlreadyRegistered: '已有使用者使用所提供的用戶名註冊。',\n    usernameOrPasswordIncorrect: '提供的使用者名稱或密碼不正確。',\n    valueMustBeUnique: '數值必須是唯一的',\n    verificationTokenInvalid: '驗證令牌無效。',\n  },\n  fields: {\n    addLabel: '新增{{label}}',\n    addLink: '新增連結',\n    addNew: '新增',\n    addNewLabel: '新增{{label}}',\n    addRelationship: '新增關聯',\n    addUpload: '上傳',\n    block: '區塊',\n    blocks: '區塊',\n    blockType: '區塊類型',\n    chooseBetweenCustomTextOrDocument: '選擇自定義文件或連結到另一個文件。',\n    chooseDocumentToLink: '選擇要連結的文件',\n    chooseFromExisting: '從現有的選擇',\n    chooseLabel: '選擇{{label}}',\n    collapseAll: '全部折疊',\n    customURL: '自定義連結',\n    editLabelData: '編輯{{label}}資料',\n    editLink: '編輯連結',\n    editRelationship: '編輯關聯',\n    enterURL: '輸入連結',\n    internalLink: '內部連結',\n    itemsAndMore: '{{items}} 個，還有 {{count}} 個',\n    labelRelationship: '{{label}}關聯',\n    latitude: '緯度',\n    linkedTo: '連結到<0>{{label}}</0>',\n    linkType: '連結類型',\n    longitude: '經度',\n    newLabel: '新的{{label}}',\n    openInNewTab: '在新標籤中打開',\n    passwordsDoNotMatch: '密碼不匹配。',\n    relatedDocument: '相關文件',\n    relationTo: '關聯到',\n    removeRelationship: '移除關聯',\n    removeUpload: '移除上傳',\n    saveChanges: '儲存變更',\n    searchForBlock: '搜尋一個區塊',\n    selectExistingLabel: '選擇現有的{{label}}',\n    selectFieldsToEdit: '選擇要編輯的字串',\n    showAll: '顯示全部',\n    swapRelationship: '替換關聯',\n    swapUpload: '替換上傳',\n    textToDisplay: '要顯示的文字',\n    toggleBlock: '切換區塊',\n    uploadNewLabel: '上傳新的{{label}}',\n  },\n  folder: {\n    browseByFolder: '按資料夾瀏覽',\n    byFolder: '按資料夾',\n    deleteFolder: '刪除資料夾',\n    folderName: '資料夾名稱',\n    folders: '資料夾',\n    folderTypeDescription: '在此文件夾中選擇應允許的集合文件類型。',\n    itemHasBeenMoved: '{{title}}已被移至{{folderName}}',\n    itemHasBeenMovedToRoot: '{{title}}已被移至根文件夾',\n    itemsMovedToFolder: '{{title}} 已移至 {{folderName}}',\n    itemsMovedToRoot: '{{title}}已經移至根資料夾',\n    moveFolder: '移動資料夾',\n    moveItemsToFolderConfirmation:\n      '您即將將 <1>{{count}} {{label}}</1> 移至 <2>{{toFolder}}</2>。您確定嗎？',\n    moveItemsToRootConfirmation: '您即將移動<1>{{count}} {{label}}</1>至根文件夾。您確定嗎？',\n    moveItemToFolderConfirmation: '您即將將<1>{{title}}</1>移至<2>{{toFolder}}</2>。您確定嗎？',\n    moveItemToRootConfirmation: '您即將把<1>{{title}}</1>移至根目錄。您確定嗎？',\n    movingFromFolder: '將 {{title}} 從 {{fromFolder}} 移出',\n    newFolder: '新資料夾',\n    noFolder: '沒有資料夾',\n    renameFolder: '重命名資料夾',\n    searchByNameInFolder: '在{{folderName}}中按名稱搜尋',\n    selectFolderForItem: '選擇{{title}}的資料夾',\n  },\n  general: {\n    name: '名稱',\n    aboutToDelete: '您即將刪除{{label}} <1>{{title}}</1>。您確定要繼續嗎？',\n    aboutToDeleteCount_many: '您即將刪除 {{count}} 個 {{label}}',\n    aboutToDeleteCount_one: '您即將刪除 {{count}} 個 {{label}}',\n    aboutToDeleteCount_other: '您即將刪除 {{count}} 個 {{label}}',\n    aboutToPermanentlyDelete: '您即將永久刪除 {{label}} <1>{{title}}</1>。你確定嗎？',\n    aboutToPermanentlyDeleteTrash:\n      '您即將從垃圾桶中永久刪除<0>{{count}}</0> <1>{{label}}</1>。你確定嗎？',\n    aboutToRestore: '您即將恢復 {{label}} <1>{{title}}</1>。您確定嗎？',\n    aboutToRestoreAsDraft: '您即將將 {{label}} <1>{{title}}</1> 恢復為草稿。您確定嗎？',\n    aboutToRestoreAsDraftCount: '您即將還原 {{count}} {{label}} 為草稿',\n    aboutToRestoreCount: '您即將恢復 {{count}} {{label}}',\n    aboutToTrash: '您即將將 {{label}} <1>{{title}}</1> 移到垃圾桶。你確定嗎？',\n    aboutToTrashCount: '您即將把 {{count}} {{label}} 移到垃圾桶',\n    addBelow: '新增到下方',\n    addFilter: '新增過濾器',\n    adminTheme: '管理頁面主題',\n    all: '所有',\n    allCollections: '所有集合',\n    allLocales: '所有地區',\n    and: '和',\n    anotherUser: '另一位使用者',\n    anotherUserTakenOver: '另一位使用者接管了此文件的編輯。',\n    applyChanges: '套用更改',\n    ascending: '升冪',\n    automatic: '自動',\n    backToDashboard: '返回到控制面板',\n    cancel: '取消',\n    changesNotSaved: '您還有尚未儲存的變更。您確定要離開嗎？',\n    clear: '清晰',\n    clearAll: '清除全部',\n    close: '關閉',\n    collapse: '折疊',\n    collections: '集合',\n    columns: '欄位',\n    columnToSort: '要排序的欄位',\n    confirm: '確認',\n    confirmCopy: '確認副本',\n    confirmDeletion: '確認刪除',\n    confirmDuplication: '確認複製',\n    confirmMove: '確認移動',\n    confirmReindex: '重新索引所有{{collections}}?',\n    confirmReindexAll: '重新索引所有集合?',\n    confirmReindexDescription: '此操作將刪除現有索引並重新索引{{collections}}集合中的文件。',\n    confirmReindexDescriptionAll: '此操作將刪除現有索引並重新索引所有集合中的文件。',\n    confirmRestoration: '確認恢復',\n    copied: '已複製',\n    copy: '複製',\n    copyField: '複製欄位',\n    copying: '複製',\n    copyRow: '複製列',\n    copyWarning: '您即將以{{from}}覆蓋{{to}}，這將影響{{label}} {{title}}。您確定要這麼做嗎？',\n    create: '建立',\n    created: '已建立',\n    createdAt: '建立於',\n    createNew: '建立新的',\n    createNewLabel: '建立新的{{label}}',\n    creating: '建立中',\n    creatingNewLabel: '正在建立新的{{label}}',\n    currentlyEditing:\n      '目前正在編輯此文件。如果您接管，他們將無法繼續編輯，並且可能會丟失未保存的更改。',\n    custom: '自訂',\n    dark: '深色',\n    dashboard: '控制面板',\n    delete: '刪除',\n    deleted: '已刪除',\n    deletedAt: '刪除於',\n    deletedCountSuccessfully: '已成功刪除 {{count}} 個 {{label}}。',\n    deletedSuccessfully: '已成功刪除。',\n    deletePermanently: '跳過垃圾桶並永久刪除',\n    deleting: '刪除中...',\n    depth: '深度',\n    descending: '降冪',\n    deselectAllRows: '取消選擇全部',\n    document: '文件',\n    documentIsTrashed: '此 {{label}} 已被丟入垃圾桶且只能讀取。',\n    documentLocked: '文件已鎖定',\n    documents: '文件',\n    duplicate: '複製',\n    duplicateWithoutSaving: '複製而不儲存變更。',\n    edit: '編輯',\n    editAll: '編輯全部',\n    editedSince: '自...以來編輯',\n    editing: '編輯中',\n    editingLabel_many: '編輯 {{count}} 個 {{label}}',\n    editingLabel_one: '編輯 {{count}} 個 {{label}}',\n    editingLabel_other: '編輯 {{count}} 個 {{label}}',\n    editingTakenOver: '編輯已被接管',\n    editLabel: '編輯{{label}}',\n    email: '電子郵件',\n    emailAddress: '電子郵件地址',\n    emptyTrash: '清空垃圾箱',\n    emptyTrashLabel: '清空 {{label}} 垃圾桶',\n    enterAValue: '輸入一個值',\n    error: '錯誤',\n    errors: '錯誤',\n    exitLivePreview: '退出即時預覽',\n    export: '出口',\n    fallbackToDefaultLocale: '回到預設的語言',\n    false: '假的',\n    filter: '過濾器',\n    filters: '過濾器',\n    filterWhere: '過濾{{label}}',\n    globals: '全域',\n    goBack: '返回',\n    groupByLabel: '按照 {{label}} 分類',\n    import: '進口',\n    isEditing: '正在編輯',\n    item: '物品',\n    items: '項目',\n    language: '語言',\n    lastModified: '最後修改',\n    leaveAnyway: '無論如何都要離開',\n    leaveWithoutSaving: '不儲存直接離開',\n    light: '亮色',\n    livePreview: '預覽',\n    loading: '載入中...',\n    locale: '語言環境',\n    locales: '語言環境',\n    menu: '菜單',\n    moreOptions: '更多選項',\n    move: '移動',\n    moveConfirm: '您即將移動 {{count}} {{label}} 到 <1>{{destination}}</1>。您確定嗎？',\n    moveCount: '移動 {{count}} {{label}}',\n    moveDown: '向下移動',\n    moveUp: '向上移動',\n    moving: '移動',\n    movingCount: '移動 {{count}} {{label}}',\n    newPassword: '新密碼',\n    next: '下一個',\n    no: '否',\n    noDateSelected: '未選擇日期',\n    noFiltersSet: '沒有設定過濾器',\n    noLabel: '<沒有{{label}}>',\n    none: '無',\n    noOptions: '沒有選項',\n    noResults: '沒有找到{{label}}。{{label}}並不存在或沒有符合您上面所指定的過濾器。',\n    notFound: '未找到',\n    nothingFound: '沒有找到任何東西',\n    noTrashResults: '垃圾桶中無{{label}}。',\n    noUpcomingEventsScheduled: '沒有即將到來的活動。',\n    noValue: '沒有數值',\n    of: '的',\n    only: '僅限',\n    open: '打開',\n    or: '或',\n    order: '排序',\n    overwriteExistingData: '覆蓋現有欄位資料',\n    pageNotFound: '未找到頁面',\n    password: '密碼',\n    pasteField: '貼上欄位',\n    pasteRow: '貼上列',\n    payloadSettings: 'Payload設定',\n    permanentlyDelete: '永久刪除',\n    permanentlyDeletedCountSuccessfully: '成功永久刪除 {{count}} {{label}}。',\n    perPage: '每一頁： {{limit}} 個',\n    previous: '先前的',\n    reindex: '重新索引',\n    reindexingAll: '正在重新索引所有{{collections}}。',\n    remove: '移除',\n    rename: '重新命名',\n    reset: '重設',\n    resetPreferences: '重設偏好設定',\n    resetPreferencesDescription: '這將把您的所有偏好設定恢復為預設值。',\n    resettingPreferences: '正在重設偏好設定。',\n    restore: '恢复',\n    restoreAsPublished: '恢复为已发布版本',\n    restoredCountSuccessfully: '成功恢復了 {{count}} {{label}}。',\n    restoring: '恢复中...',\n    row: '行',\n    rows: '行',\n    save: '儲存',\n    saving: '儲存中...',\n    schedulePublishFor: '為{{title}}設定發佈時間',\n    searchBy: '搜尋{{label}}',\n    select: '選擇',\n    selectAll: '選擇所有 {{count}} 個 {{label}}',\n    selectAllRows: '選擇所有行',\n    selectedCount: '已選擇 {{count}} 個 {{label}}',\n    selectLabel: '選擇 {{label}}',\n    selectValue: '選擇一個值',\n    showAllLabel: '顯示所有{{label}}',\n    sorryNotFound: '對不起，沒有找到您請求的東西。',\n    sort: '排序',\n    sortByLabelDirection: '按{{label}} {{direction}}排序',\n    stayOnThisPage: '停留在此頁面',\n    submissionSuccessful: '成功送出。',\n    submit: '送出',\n    submitting: '提交中...',\n    success: '成功',\n    successfullyCreated: '成功建立{{label}}',\n    successfullyDuplicated: '成功複製{{label}}',\n    successfullyReindexed:\n      '成功重新索引了 {{collections}} 集合中 {{total}} 個文檔中的 {{count}} 個。',\n    takeOver: '接管',\n    thisLanguage: '中文 (繁體)',\n    time: '時間',\n    timezone: '時區',\n    titleDeleted: '{{label}} \"{{title}}\"已被成功刪除。',\n    titleRestored: '\"{{label}}\" \"{{title}}\" 成功恢复。',\n    titleTrashed: '\"{{label}}\" \"{{title}}\" 已移至垃圾桶。',\n    trash: '垃圾',\n    trashedCountSuccessfully: '{{count}} {{label}} 已移至垃圾桶。',\n    true: '真實',\n    unauthorized: '未經授權',\n    unsavedChanges: '您有未保存的更改。繼續前請保存或放棄。',\n    unsavedChangesDuplicate: '您有還沒儲存的修改，確定要繼續複製嗎？',\n    untitled: '無標題',\n    upcomingEvents: '即將來臨的活動',\n    updatedAt: '更新於',\n    updatedCountSuccessfully: '已成功更新 {{count}} 個 {{label}}。',\n    updatedLabelSuccessfully: '成功更新了{{label}}。',\n    updatedSuccessfully: '更新成功。',\n    updateForEveryone: '給所有人的更新',\n    updating: '更新中',\n    uploading: '上傳中',\n    uploadingBulk: '正在上傳 {{current}} / {{total}}',\n    user: '使用者',\n    username: '使用者名稱',\n    users: '使用者',\n    value: '值',\n    viewing: '查看',\n    viewReadOnly: '僅檢視',\n    welcome: '歡迎',\n    yes: '是的',\n  },\n  localization: {\n    cannotCopySameLocale: '無法複製到相同的地區',\n    copyFrom: '從...複製',\n    copyFromTo: '從{{from}}複製到{{to}}',\n    copyTo: '複製到',\n    copyToLocale: '複製到區域設定',\n    localeToPublish: '發布地區',\n    selectLocaleToCopy: '選擇要複製的地區設定',\n  },\n  operators: {\n    contains: '包含',\n    equals: '等於',\n    exists: '存在',\n    intersects: '交叉點',\n    isGreaterThan: '大於',\n    isGreaterThanOrEqualTo: '大於等於',\n    isIn: '在',\n    isLessThan: '小於',\n    isLessThanOrEqualTo: '小於或等於',\n    isLike: '就像',\n    isNotEqualTo: '不等於',\n    isNotIn: '不在',\n    isNotLike: '不像',\n    near: '附近',\n    within: '在...之內',\n  },\n  upload: {\n    addFile: '添加文件',\n    addFiles: '添加檔案',\n    bulkUpload: '批量上傳',\n    crop: '裁剪',\n    cropToolDescription: '拖動所選區域的角落，繪製一個新區域或調整以下的值。',\n    download: '下載',\n    dragAndDrop: '拖放一個檔案',\n    dragAndDropHere: '或在這裡拖放一個檔案',\n    editImage: '編輯圖像',\n    fileName: '檔案名稱',\n    fileSize: '檔案大小',\n    filesToUpload: '上傳的文件',\n    fileToUpload: '上傳檔案',\n    focalPoint: '焦點',\n    focalPointDescription: '直接在預覽中拖動焦點或調整下面的值。',\n    height: '高度',\n    lessInfo: '更少資訊',\n    moreInfo: '更多資訊',\n    noFile: '沒有檔案',\n    pasteURL: '貼上網址',\n    previewSizes: '預覽尺寸',\n    selectCollectionToBrowse: '選擇一個要瀏覽的集合',\n    selectFile: '選擇一個文件',\n    setCropArea: '設置裁剪區域',\n    setFocalPoint: '設置焦點',\n    sizes: '尺寸',\n    sizesFor: '{{label}}的尺寸',\n    width: '寬度',\n  },\n  validation: {\n    emailAddress: '請輸入一個有效的電子郵件地址。',\n    enterNumber: '請輸入一個有效的數字。',\n    fieldHasNo: '這個字串沒有{{label}}',\n    greaterThanMax: '{{value}}超過了允許的最大{{label}}，該最大值為{{max}}。',\n    invalidInput: '這個字串有一個無效的輸入。',\n    invalidSelection: '這個字串有一個無效的選擇。',\n    invalidSelections: '這個字串有以下無效的選擇：',\n    lessThanMin: '{{value}}小於允許的最小{{label}}，該最小值為{{min}}。',\n    limitReached: '已達限制，只能添加{{max}}個項目。',\n    longerThanMin: '該值必須大於{{minLength}}字串的最小長度',\n    notValidDate: '\"{{value}}\"不是一個有效的日期。',\n    required: '該字串為必填項目。',\n    requiresAtLeast: '該字串至少需要 {{count}} 個 {{label}}。',\n    requiresNoMoreThan: '該字串要求不超過 {{count}} 個 {{label}。',\n    requiresTwoNumbers: '該字串需要兩個數字。',\n    shorterThanMax: '該值長度必須小於{{maxLength}}個字元',\n    timezoneRequired: '需要時間區。',\n    trueOrFalse: '該字串只能等於是或否。',\n    username: '請輸入有效的使用者名稱。可以包含字母、數字、連字號、句點和底線。',\n    validUploadID: '該字串不是有效的上傳ID。',\n  },\n  version: {\n    type: '類型',\n    aboutToPublishSelection: '您確定即將發佈所選的 {{label}} 嗎？',\n    aboutToRestore: '您將把這個文件{{label}}回復到{{versionDate}}時的狀態',\n    aboutToRestoreGlobal: '您要將痊域的{{label}}回復到{{versionDate}}時的狀態',\n    aboutToRevertToPublished: '您將要將這個文件的內容還原到它的發佈狀態。您確定嗎？',\n    aboutToUnpublish: '您即將取消發佈這個文件。您確定嗎？',\n    aboutToUnpublishSelection: '您即將取消發佈所選內容中的所有 {{label}}。您確定嗎？',\n    autosave: '自動儲存',\n    autosavedSuccessfully: '自動儲存成功。',\n    autosavedVersion: '自動儲存的版本',\n    changed: '已更改',\n    changedFieldsCount_one: '{{count}} 更改了字段',\n    changedFieldsCount_other: '{{count}}個已更改的欄位',\n    compareVersion: '對比版本：',\n    compareVersions: '比較版本',\n    comparingAgainst: '相對於',\n    confirmPublish: '確認發佈',\n    confirmRevertToSaved: '確認回復到儲存狀態',\n    confirmUnpublish: '確認取消發佈',\n    confirmVersionRestoration: '確認版本回復',\n    currentDocumentStatus: '目前{{docStatus}}文件',\n    currentDraft: '目前的草稿',\n    currentlyPublished: '目前已發布',\n    currentlyViewing: '目前正在查看',\n    currentPublishedVersion: '目前已發布的版本',\n    draft: '草稿',\n    draftSavedSuccessfully: '草稿儲存成功。',\n    lastSavedAgo: '上次儲存在{{distance}}之前',\n    modifiedOnly: '僅修改過的',\n    moreVersions: '更多版本...',\n    noFurtherVersionsFound: '沒有發現其他版本',\n    noRowsFound: '沒有發現{{label}}',\n    noRowsSelected: '未選擇 {{label}}',\n    preview: '預覽',\n    previouslyDraft: '先前為草案',\n    previouslyPublished: '先前出版過的',\n    previousVersion: '先前版本',\n    problemRestoringVersion: '回復這個版本時發生了問題',\n    publish: '發佈',\n    publishAllLocales: '發布所有地區設定',\n    publishChanges: '發佈修改',\n    published: '已發佈',\n    publishIn: '在 {{locale}} 發佈',\n    publishing: '發布',\n    restoreAsDraft: '恢復為草稿',\n    restoredSuccessfully: '回復成功。',\n    restoreThisVersion: '回復此版本',\n    restoring: '回復中...',\n    reverting: '還原中...',\n    revertToPublished: '還原到已發佈的版本',\n    saveDraft: '儲存草稿',\n    scheduledSuccessfully: '成功安排。',\n    schedulePublish: '排程發布',\n    selectLocales: '選擇要顯示的語言',\n    selectVersionToCompare: '選擇要比較的版本',\n    showingVersionsFor: '顯示版本為：',\n    showLocales: '顯示語言：',\n    specificVersion: '特定版本',\n    status: '狀態',\n    unpublish: '取消發佈',\n    unpublishing: '取消發佈中...',\n    version: '版本',\n    versionAgo: '{{distance}}前',\n    versionCount_many: '發現 {{count}}個版本',\n    versionCount_none: '沒有發現任何版本',\n    versionCount_one: '找到 {{count}} 個版本',\n    versionCount_other: '找到 {{count}} 個版本',\n    versionCreatedOn: '版本 {{version}} 建立於：',\n    versionID: '版本ID',\n    versions: '版本',\n    viewingVersion: '正在查看{{entityLabel}} {{documentTitle}}的版本',\n    viewingVersionGlobal: '正在查看全域{{entityLabel}}的版本',\n    viewingVersions: '正在查看{{entityLabel}} {{documentTitle}}的版本',\n    viewingVersionsGlobal: '正在查看全域{{entityLabel}}的版本',\n  },\n}\n\nexport const zhTw: Language = {\n  dateFNSKey: 'zh-TW',\n  translations: zhTwTranslations,\n}\n"], "names": ["zhTwTranslations", "authentication", "account", "accountOfCurrentUser", "accountVerified", "alreadyActivated", "alreadyLoggedIn", "<PERSON><PERSON><PERSON><PERSON>", "authenticated", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beginCreateFirstUser", "changePassword", "checkYourEmailForPasswordReset", "confirmGeneration", "confirmPassword", "createFirstUser", "emailNotValid", "emailOrUsername", "emailSent", "emailVerified", "enableAPIKey", "failedToUnlock", "forceUnlock", "forgotPassword", "forgotPasswordEmailInstructions", "forgotPasswordQuestion", "forgotPasswordUsernameInstructions", "generate", "generateNewAPIKey", "generatingNewAPIKeyWillInvalidate", "lockUntil", "logBackIn", "loggedIn", "loggedInChangePassword", "loggedOutInactivity", "loggedOutSuccessfully", "loggingOut", "login", "loginAttempts", "loginUser", "loginWithAnotherUser", "logOut", "logout", "logoutSuccessful", "logoutUser", "newAccountCreated", "newAPIKeyGenerated", "newPassword", "passed", "passwordResetSuccessfully", "resetPassword", "resetPasswordExpiration", "resetPasswordToken", "resetYourPassword", "stayLoggedIn", "successfullyRegisteredFirstUser", "successfullyUnlocked", "tokenRefreshSuccessful", "unableToVerify", "username", "usernameNotValid", "verified", "verifiedSuccessfully", "verify", "verifyUser", "verifyYourEmail", "youAreInactive", "youAreReceivingResetPassword", "youDidNotRequestPassword", "error", "accountAlreadyActivated", "autosaving", "<PERSON>In<PERSON><PERSON><PERSON><PERSON>s", "deletingFile", "deletingTitle", "documentNotFound", "emailOrPasswordIncorrect", "followingFieldsInvalid_one", "followingFieldsInvalid_other", "incorrectCollection", "insufficientClipboardPermissions", "invalidClipboardData", "invalidFileType", "invalidFileTypeValue", "invalidRequestArgs", "loadingDocument", "localesNotSaved_one", "localesNotSaved_other", "logoutFailed", "missingEmail", "missingIDOfDocument", "missingIDOfVersion", "missingRequiredData", "noFilesUploaded", "noMatchedField", "notAllowedToAccessPage", "notAllowedToPerformAction", "notFound", "noUser", "previewing", "problemUploadingFile", "restoringTitle", "tokenInvalidOrExpired", "tokenNotProvided", "unableToCopy", "unableToDeleteCount", "unableToReindexCollection", "unableToUpdateCount", "unauthorized", "unauthorizedAdmin", "unknown", "unPublishingDocument", "unspecific", "unverifiedEmail", "userEmailAlreadyRegistered", "userLocked", "usernameAlreadyRegistered", "usernameOrPasswordIncorrect", "valueMustBeUnique", "verificationTokenInvalid", "fields", "addLabel", "addLink", "addNew", "addNewLabel", "addRelationship", "addUpload", "block", "blocks", "blockType", "chooseBetweenCustomTextOrDocument", "chooseDocumentToLink", "chooseFromExisting", "<PERSON><PERSON><PERSON><PERSON>", "collapseAll", "customURL", "editLabelData", "editLink", "editRelationship", "enterURL", "internalLink", "itemsAndMore", "labelRelationship", "latitude", "linkedTo", "linkType", "longitude", "new<PERSON>abel", "openInNewTab", "passwordsDoNotMatch", "relatedDocument", "relationTo", "removeRelationship", "removeUpload", "saveChanges", "searchForBlock", "selectExistingLabel", "selectFieldsToEdit", "showAll", "swapRelationship", "swapUpload", "textToDisplay", "toggleBlock", "uploadNewLabel", "folder", "browseByFolder", "byFolder", "deleteFolder", "folderName", "folders", "folderTypeDescription", "itemHasBeenMoved", "itemHasBeenMovedToRoot", "itemsMovedToFolder", "itemsMovedToRoot", "moveFolder", "moveItemsToFolderConfirmation", "moveItemsToRootConfirmation", "moveItemToFolderConfirmation", "moveItemToRootConfirmation", "movingFromFolder", "newFolder", "noFolder", "renameFolder", "searchByNameInFolder", "selectFolderForItem", "general", "name", "aboutToDelete", "aboutToDeleteCount_many", "aboutToDeleteCount_one", "aboutToDeleteCount_other", "aboutToPermanentlyDelete", "aboutToPermanentlyDeleteTrash", "aboutToRestore", "aboutToRestoreAsDraft", "aboutToRestoreAsDraftCount", "aboutToRestoreCount", "aboutToTrash", "aboutToTrashCount", "addBelow", "addFilter", "adminTheme", "all", "allCollections", "allLocales", "and", "anotherUser", "anotherUserTakenOver", "applyChanges", "ascending", "automatic", "backToDashboard", "cancel", "changesNotSaved", "clear", "clearAll", "close", "collapse", "collections", "columns", "columnToSort", "confirm", "confirmCopy", "confirmDeletion", "confirmDuplication", "confirmMove", "confirmReindex", "confirmReindexAll", "confirmReindexDescription", "confirmReindexDescriptionAll", "confirmRestoration", "copied", "copy", "copyField", "copying", "copyRow", "copyWarning", "create", "created", "createdAt", "createNew", "createNewLabel", "creating", "creatingNewLabel", "currentlyEditing", "custom", "dark", "dashboard", "delete", "deleted", "deletedAt", "deletedCountSuccessfully", "deletedSuccessfully", "deletePermanently", "deleting", "depth", "descending", "deselectAllRows", "document", "documentIsTrashed", "documentLocked", "documents", "duplicate", "duplicateWithoutSaving", "edit", "editAll", "editedSince", "editing", "editingLabel_many", "editingLabel_one", "editingLabel_other", "editingTakenOver", "edit<PERSON><PERSON><PERSON>", "email", "emailAddress", "emptyTrash", "emptyTrashLabel", "enterAValue", "errors", "exitLivePreview", "export", "fallbackToDefaultLocale", "false", "filter", "filters", "filterWhere", "globals", "goBack", "groupByLabel", "import", "isEditing", "item", "items", "language", "lastModified", "leaveAnyway", "leaveWithoutSaving", "light", "livePreview", "loading", "locale", "locales", "menu", "moreOptions", "move", "moveConfirm", "moveCount", "moveDown", "moveUp", "moving", "movingCount", "next", "no", "noDateSelected", "noFiltersSet", "no<PERSON><PERSON><PERSON>", "none", "noOptions", "noResults", "nothingFound", "noTrashResults", "noUpcomingEventsScheduled", "noValue", "of", "only", "open", "or", "order", "overwriteExistingData", "pageNotFound", "password", "pasteField", "pasteRow", "payloadSettings", "permanentlyDelete", "permanentlyDeletedCountSuccessfully", "perPage", "previous", "reindex", "reindexingAll", "remove", "rename", "reset", "resetPreferences", "resetPreferencesDescription", "resettingPreferences", "restore", "restoreAsPublished", "restoredCountSuccessfully", "restoring", "row", "rows", "save", "saving", "schedulePublishFor", "searchBy", "select", "selectAll", "selectAllRows", "selectedCount", "selectLabel", "selectValue", "showAllLabel", "sorryNotFound", "sort", "sortByLabelDirection", "stayOnThisPage", "submissionSuccessful", "submit", "submitting", "success", "successfullyCreated", "successfullyDuplicated", "successfullyReindexed", "takeOver", "thisLanguage", "time", "timezone", "titleDeleted", "titleRestored", "titleTrashed", "trash", "trashedCountSuccessfully", "true", "unsavedChanges", "unsavedChangesDuplicate", "untitled", "upcomingEvents", "updatedAt", "updatedCountSuccessfully", "updatedLabelSuccessfully", "updatedSuccessfully", "updateForEveryone", "updating", "uploading", "uploadingBulk", "user", "users", "value", "viewing", "viewReadOnly", "welcome", "yes", "localization", "cannotCopySameLocale", "copyFrom", "copyFromTo", "copyTo", "copyToLocale", "localeToPublish", "selectLocaleToCopy", "operators", "contains", "equals", "exists", "intersects", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isGreaterThanOrEqualTo", "isIn", "is<PERSON><PERSON><PERSON><PERSON>", "isLessThanOrEqualTo", "isLike", "isNotEqualTo", "isNotIn", "isNotLike", "near", "within", "upload", "addFile", "addFiles", "bulkUpload", "crop", "cropToolDescription", "download", "dragAndDrop", "dragAndDropHere", "editImage", "fileName", "fileSize", "filesToUpload", "fileToUpload", "focalPoint", "focalPointDescription", "height", "lessInfo", "moreInfo", "noFile", "pasteURL", "previewSizes", "selectCollectionToBrowse", "selectFile", "setCropArea", "setFocalPoint", "sizes", "sizesFor", "width", "validation", "enterNumber", "fieldHasNo", "greaterThanMax", "invalidInput", "invalidSelection", "invalidSelections", "lessThanMin", "limitReached", "longerThanMin", "notValidDate", "required", "requiresAtLeast", "requiresNoMoreThan", "requiresTwoNumbers", "shorterThanMax", "timezoneRequired", "trueOrFalse", "validUploadID", "version", "type", "aboutToPublishSelection", "aboutToRestoreGlobal", "aboutToRevertToPublished", "aboutToUnpublish", "aboutToUnpublishSelection", "autosave", "autosavedSuccessfully", "autosavedVersion", "changed", "changedFieldsCount_one", "changedFieldsCount_other", "compareVersion", "compareVersions", "comparingAgainst", "confirmPublish", "confirmRevertToSaved", "confirmUnpublish", "confirmVersionRestoration", "currentDocumentStatus", "currentDraft", "currentlyPublished", "currentlyViewing", "currentPublishedVersion", "draft", "draftSavedSuccessfully", "lastSavedAgo", "modifiedOnly", "moreVersions", "noFurtherVersionsFound", "noRowsFound", "noRowsSelected", "preview", "previouslyDraft", "previouslyPublished", "previousVersion", "problemRestoringVersion", "publish", "publishAllLocales", "publishChanges", "published", "publishIn", "publishing", "restoreAsDraft", "restoredSuccessfully", "restoreThisVersion", "reverting", "revertToPublished", "saveDraft", "scheduledSuccessfully", "schedulePublish", "selectLocales", "selectVersionToCompare", "showingVersionsFor", "showLocales", "specificVersion", "status", "unpublish", "unpublishing", "versionAgo", "versionCount_many", "versionCount_none", "versionCount_one", "versionCount_other", "versionCreatedOn", "versionID", "versions", "viewingVersion", "viewingVersionGlobal", "viewingVersions", "viewingVersionsGlobal", "zhTw", "dateFNS<PERSON>ey", "translations"], "mappings": "AAEA,OAAO,MAAMA,mBAA8C;IACzDC,gBAAgB;QACdC,SAAS;QACTC,sBAAsB;QACtBC,iBAAiB;QACjBC,kBAAkB;QAClBC,iBAAiB;QACjBC,QAAQ;QACRC,eAAe;QACfC,aAAa;QACbC,sBAAsB;QACtBC,gBAAgB;QAChBC,gCACE;QACFC,mBAAmB;QACnBC,iBAAiB;QACjBC,iBAAiB;QACjBC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,eAAe;QACfC,cAAc;QACdC,gBAAgB;QAChBC,aAAa;QACbC,gBAAgB;QAChBC,iCACE;QACFC,wBAAwB;QACxBC,oCACE;QACFC,UAAU;QACVC,mBAAmB;QACnBC,mCAAmC;QACnCC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,wBAAwB;QACxBC,qBAAqB;QACrBC,uBAAuB;QACvBC,YAAY;QACZC,OAAO;QACPC,eAAe;QACfC,WAAW;QACXC,sBAAsB;QACtBC,QAAQ;QACRC,QAAQ;QACRC,kBAAkB;QAClBC,YAAY;QACZC,mBACE;QACFC,oBAAoB;QACpBC,aAAa;QACbC,QAAQ;QACRC,2BAA2B;QAC3BC,eAAe;QACfC,yBAAyB;QACzBC,oBAAoB;QACpBC,mBAAmB;QACnBC,cAAc;QACdC,iCAAiC;QACjCC,sBAAsB;QACtBC,wBAAwB;QACxBC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,iBAAiB;QACjBC,gBACE;QACFC,8BACE;QACFC,0BAA0B;IAC5B;IACAC,OAAO;QACLC,yBAAyB;QACzBC,YAAY;QACZC,sBAAsB;QACtBC,cAAc;QACdC,eAAe;QACfC,kBAAkB;QAClBC,0BAA0B;QAC1BC,4BAA4B;QAC5BC,8BAA8B;QAC9BC,qBAAqB;QACrBC,kCAAkC;QAClCC,sBAAsB;QACtBC,iBAAiB;QACjBC,sBAAsB;QACtBC,oBAAoB;QACpBC,iBAAiB;QACjBC,qBAAqB;QACrBC,uBAAuB;QACvBC,cAAc;QACdC,cAAc;QACdC,qBAAqB;QACrBC,oBAAoB;QACpBC,qBAAqB;QACrBC,iBAAiB;QACjBC,gBAAgB;QAChBC,wBAAwB;QACxBC,2BAA2B;QAC3BC,UAAU;QACVC,QAAQ;QACRC,YAAY;QACZC,sBAAsB;QACtBC,gBAAgB;QAChBC,uBAAuB;QACvBC,kBAAkB;QAClBC,cAAc;QACdC,qBAAqB;QACrBC,2BAA2B;QAC3BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,SAAS;QACTC,sBAAsB;QACtBC,YAAY;QACZC,iBAAiB;QACjBC,4BAA4B;QAC5BC,YAAY;QACZC,2BAA2B;QAC3BC,6BAA6B;QAC7BC,mBAAmB;QACnBC,0BAA0B;IAC5B;IACAC,QAAQ;QACNC,UAAU;QACVC,SAAS;QACTC,QAAQ;QACRC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,QAAQ;QACRC,WAAW;QACXC,mCAAmC;QACnCC,sBAAsB;QACtBC,oBAAoB;QACpBC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXC,eAAe;QACfC,UAAU;QACVC,kBAAkB;QAClBC,UAAU;QACVC,cAAc;QACdC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,qBAAqB;QACrBC,iBAAiB;QACjBC,YAAY;QACZC,oBAAoB;QACpBC,cAAc;QACdC,aAAa;QACbC,gBAAgB;QAChBC,qBAAqB;QACrBC,oBAAoB;QACpBC,SAAS;QACTC,kBAAkB;QAClBC,YAAY;QACZC,eAAe;QACfC,aAAa;QACbC,gBAAgB;IAClB;IACAC,QAAQ;QACNC,gBAAgB;QAChBC,UAAU;QACVC,cAAc;QACdC,YAAY;QACZC,SAAS;QACTC,uBAAuB;QACvBC,kBAAkB;QAClBC,wBAAwB;QACxBC,oBAAoB;QACpBC,kBAAkB;QAClBC,YAAY;QACZC,+BACE;QACFC,6BAA6B;QAC7BC,8BAA8B;QAC9BC,4BAA4B;QAC5BC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,cAAc;QACdC,sBAAsB;QACtBC,qBAAqB;IACvB;IACAC,SAAS;QACPC,MAAM;QACNC,eAAe;QACfC,yBAAyB;QACzBC,wBAAwB;QACxBC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,+BACE;QACFC,gBAAgB;QAChBC,uBAAuB;QACvBC,4BAA4B;QAC5BC,qBAAqB;QACrBC,cAAc;QACdC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,YAAY;QACZC,KAAK;QACLC,gBAAgB;QAChBC,YAAY;QACZC,KAAK;QACLC,aAAa;QACbC,sBAAsB;QACtBC,cAAc;QACdC,WAAW;QACXC,WAAW;QACXC,iBAAiB;QACjBC,QAAQ;QACRC,iBAAiB;QACjBC,OAAO;QACPC,UAAU;QACVC,OAAO;QACPC,UAAU;QACVC,aAAa;QACbC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,aAAa;QACbC,iBAAiB;QACjBC,oBAAoB;QACpBC,aAAa;QACbC,gBAAgB;QAChBC,mBAAmB;QACnBC,2BAA2B;QAC3BC,8BAA8B;QAC9BC,oBAAoB;QACpBC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,SAAS;QACTC,SAAS;QACTC,aAAa;QACbC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,WAAW;QACXC,gBAAgB;QAChBC,UAAU;QACVC,kBAAkB;QAClBC,kBACE;QACFC,QAAQ;QACRC,MAAM;QACNC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,WAAW;QACXC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,OAAO;QACPC,YAAY;QACZC,iBAAiB;QACjBC,UAAU;QACVC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,wBAAwB;QACxBC,MAAM;QACNC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,OAAO;QACPC,cAAc;QACdC,YAAY;QACZC,iBAAiB;QACjBC,aAAa;QACbjN,OAAO;QACPkN,QAAQ;QACRC,iBAAiB;QACjBC,QAAQ;QACRC,yBAAyB;QACzBC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,oBAAoB;QACpBC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,QAAQ;QACRC,SAAS;QACTC,MAAM;QACNC,aAAa;QACbC,MAAM;QACNC,aAAa;QACbC,WAAW;QACXC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,aAAa;QACbxQ,aAAa;QACbyQ,MAAM;QACNC,IAAI;QACJC,gBAAgB;QAChBC,cAAc;QACdC,SAAS;QACTC,MAAM;QACNC,WAAW;QACXC,WAAW;QACX9N,UAAU;QACV+N,cAAc;QACdC,gBAAgB;QAChBC,2BAA2B;QAC3BC,SAAS;QACTC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,IAAI;QACJC,OAAO;QACPC,uBAAuB;QACvBC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,UAAU;QACVC,iBAAiB;QACjBC,mBAAmB;QACnBC,qCAAqC;QACrCC,SAAS;QACTC,UAAU;QACVC,SAAS;QACTC,eAAe;QACfC,QAAQ;QACRC,QAAQ;QACRC,OAAO;QACPC,kBAAkB;QAClBC,6BAA6B;QAC7BC,sBAAsB;QACtBC,SAAS;QACTC,oBAAoB;QACpBC,2BAA2B;QAC3BC,WAAW;QACXC,KAAK;QACLC,MAAM;QACNC,MAAM;QACNC,QAAQ;QACRC,oBAAoB;QACpBC,UAAU;QACVC,QAAQ;QACRC,WAAW;QACXC,eAAe;QACfC,eAAe;QACfC,aAAa;QACbC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,MAAM;QACNC,sBAAsB;QACtBC,gBAAgB;QAChBC,sBAAsB;QACtBC,QAAQ;QACRC,YAAY;QACZC,SAAS;QACTC,qBAAqB;QACrBC,wBAAwB;QACxBC,uBACE;QACFC,UAAU;QACVC,cAAc;QACdC,MAAM;QACNC,UAAU;QACVC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,OAAO;QACPC,0BAA0B;QAC1BC,MAAM;QACNpR,cAAc;QACdqR,gBAAgB;QAChBC,yBAAyB;QACzBC,UAAU;QACVC,gBAAgB;QAChBC,WAAW;QACXC,0BAA0B;QAC1BC,0BAA0B;QAC1BC,qBAAqB;QACrBC,mBAAmB;QACnBC,UAAU;QACVC,WAAW;QACXC,eAAe;QACfC,MAAM;QACNlV,UAAU;QACVmV,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,cAAc;QACdC,SAAS;QACTC,KAAK;IACP;IACAC,cAAc;QACZC,sBAAsB;QACtBC,UAAU;QACVC,YAAY;QACZC,QAAQ;QACRC,cAAc;QACdC,iBAAiB;QACjBC,oBAAoB;IACtB;IACAC,WAAW;QACTC,UAAU;QACVC,QAAQ;QACRC,QAAQ;QACRC,YAAY;QACZC,eAAe;QACfC,wBAAwB;QACxBC,MAAM;QACNC,YAAY;QACZC,qBAAqB;QACrBC,QAAQ;QACRC,cAAc;QACdC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,QAAQ;IACV;IACAC,QAAQ;QACNC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,MAAM;QACNC,qBAAqB;QACrBC,UAAU;QACVC,aAAa;QACbC,iBAAiB;QACjBC,WAAW;QACXC,UAAU;QACVC,UAAU;QACVC,eAAe;QACfC,cAAc;QACdC,YAAY;QACZC,uBAAuB;QACvBC,QAAQ;QACRC,UAAU;QACVC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,cAAc;QACdC,0BAA0B;QAC1BC,YAAY;QACZC,aAAa;QACbC,eAAe;QACfC,OAAO;QACPC,UAAU;QACVC,OAAO;IACT;IACAC,YAAY;QACVtL,cAAc;QACduL,aAAa;QACbC,YAAY;QACZC,gBAAgB;QAChBC,cAAc;QACdC,kBAAkB;QAClBC,mBAAmB;QACnBC,aAAa;QACbC,cAAc;QACdC,eAAe;QACfC,cAAc;QACdC,UAAU;QACVC,iBAAiB;QACjBC,oBAAoB;QACpBC,oBAAoB;QACpBC,gBAAgB;QAChBC,kBAAkB;QAClBC,aAAa;QACb/Z,UAAU;QACVga,eAAe;IACjB;IACAC,SAAS;QACPC,MAAM;QACNC,yBAAyB;QACzB5R,gBAAgB;QAChB6R,sBAAsB;QACtBC,0BAA0B;QAC1BC,kBAAkB;QAClBC,2BAA2B;QAC3BC,UAAU;QACVC,uBAAuB;QACvBC,kBAAkB;QAClBC,SAAS;QACTC,wBAAwB;QACxBC,0BAA0B;QAC1BC,gBAAgB;QAChBC,iBAAiB;QACjBC,kBAAkB;QAClBC,gBAAgB;QAChBC,sBAAsB;QACtBC,kBAAkB;QAClBC,2BAA2B;QAC3BC,uBAAuB;QACvBC,cAAc;QACdC,oBAAoB;QACpBC,kBAAkB;QAClBC,yBAAyB;QACzBC,OAAO;QACPC,wBAAwB;QACxBC,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,wBAAwB;QACxBC,aAAa;QACbC,gBAAgB;QAChBC,SAAS;QACTC,iBAAiB;QACjBC,qBAAqB;QACrBC,iBAAiB;QACjBC,yBAAyB;QACzBC,SAAS;QACTC,mBAAmB;QACnBC,gBAAgB;QAChBC,WAAW;QACXC,WAAW;QACXC,YAAY;QACZC,gBAAgB;QAChBC,sBAAsB;QACtBC,oBAAoB;QACpB5K,WAAW;QACX6K,WAAW;QACXC,mBAAmB;QACnBC,WAAW;QACXC,uBAAuB;QACvBC,iBAAiB;QACjBC,eAAe;QACfC,wBAAwB;QACxBC,oBAAoB;QACpBC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,WAAW;QACXC,cAAc;QACd3D,SAAS;QACT4D,YAAY;QACZC,mBAAmB;QACnBC,mBAAmB;QACnBC,kBAAkB;QAClBC,oBAAoB;QACpBC,kBAAkB;QAClBC,WAAW;QACXC,UAAU;QACVC,gBAAgB;QAChBC,sBAAsB;QACtBC,iBAAiB;QACjBC,uBAAuB;IACzB;AACF,EAAC;AAED,OAAO,MAAMC,OAAiB;IAC5BC,YAAY;IACZC,cAActiB;AAChB,EAAC"}