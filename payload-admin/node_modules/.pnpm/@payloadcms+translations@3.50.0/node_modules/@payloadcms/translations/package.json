{"name": "@payloadcms/translations", "version": "3.50.0", "homepage": "https://payloadcms.com", "repository": {"type": "git", "url": "https://github.com/payloadcms/payload.git", "directory": "packages/translations"}, "license": "MIT", "author": "Payload <<EMAIL>> (https://payloadcms.com)", "maintainers": [{"name": "Payload", "email": "<EMAIL>", "url": "https://payloadcms.com"}], "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/exports/index.js", "types": "./dist/exports/index.d.ts", "default": "./dist/exports/index.js"}, "./all": {"import": "./dist/exports/all.js", "types": "./dist/exports/all.d.ts", "default": "./dist/exports/all.js"}, "./utilities": {"import": "./dist/exports/utilities.js", "types": "./dist/exports/utilities.d.ts", "default": "./dist/exports/utilities.js"}, "./languages/*": {"import": "./dist/languages/*.js", "types": "./dist/languages/*.d.ts", "default": "./dist/languages/*.js"}}, "main": "./dist/exports/index.js", "types": "./dist/exports/index.d.ts", "files": ["dist"], "dependencies": {"date-fns": "4.1.0"}, "devDependencies": {"@swc/core": "1.11.29", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "dotenv": "16.4.7", "prettier": "3.5.3", "typescript": "5.7.3", "@payloadcms/eslint-config": "3.28.0"}, "scripts": {"build": "pnpm build:types && pnpm build:swc", "build:swc": "swc ./src -d ./dist --config-file .swcrc --strip-leading-paths", "build:types": "tsc --emitDeclarationOnly --outDir dist", "clean": "rimraf -g {dist,*.tsbuildinfo}", "lint": "eslint .", "lint:fix": "eslint . --fix"}}